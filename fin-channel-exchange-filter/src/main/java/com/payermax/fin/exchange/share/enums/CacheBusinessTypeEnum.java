package com.payermax.fin.exchange.share.enums;

import lombok.Getter;

public enum CacheBusinessTypeEnum {

    CONFIG_MAPPING("configMapping", "configMapping"),
    STEP_API("stepApi", "stepApi"),
    INFO("info", "info"),
    ALL_INFO("allInfo", "allInfo"),
    GET_CHANNEL_LIMIT_LIST("getChannelLimitList", "getChannelLimitList"),
    GET_ALL_VALID_CHANNEL_LIMIT("getAllValidChannelLimit", "getAllValidChannelLimit"),
    MID_WITHOUT_STATUS("mid_withoutStatus", "mid_withoutStatus"),
    MID("mid", "mid"),
    MID_BY_CHANNEL_CODE("mid_by_channelCode", "mid_by_channelCode"),
    CONFIG("config", "config"),
    ALL_CONFIG("allConfig", "allConfig"),
    STEP("step", "step"),
    SPECIAL_MAPPING("specialMapping", "specialMapping"),
    PAYMENT_FIELD_ENTITY_LIST("paymentFieldEntityList", "paymentFieldEntityList"),
    VALIDATE_RULE_ENTITY_LIST_BY_RULE_IDS("ValidateRuleEntityListByRuleIds", "ValidateRuleEntityListByRuleIds"),
    EXT_CONFIG_LIST("extConfigList", "extConfigList"),
    SPECIAL_MAPPING_CONFIG("specialMappingConfig", "specialMappingConfig"),
    MERCHANT_WITH_EXCLUSIVE_MID("merchantWithExclusiveMid", "merchantWithExclusiveMid"),
    CODE_MAP("codeMap", "codeMap"),
    CODE("code", "code"),
    PAYOUT_PENDING_NOTIFY("payoutPendingNotify", "payoutPendingNotify"),
    ALERT("alert", "alert"),
    ACCOUNT("account", "account"),
    UPDATE_ORDER_SENSITIVE_INFO("UpdateOrderSensitiveInfo", "UpdateOrderSensitiveInfo"),
    ;

    CacheBusinessTypeEnum(String cacheBusinessType, String name) {
        this.cacheBusinessType = cacheBusinessType;
        this.name = name;
    }

    @Getter
    private String cacheBusinessType;

    @Getter
    private String name;

    public static CacheBusinessTypeEnum getValueByKey(String cacheBusinessType) {
        for (CacheBusinessTypeEnum current : values()) {
            if (current.getCacheBusinessType().equals(cacheBusinessType)) {
                return current;
            }
        }
        return null;
    }
}
