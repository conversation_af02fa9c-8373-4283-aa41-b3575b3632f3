package com.payermax.fin.exchange.domainservice.newExchange;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.payermax.card.security.api.provider.CardChannelTokenFacade;
import com.payermax.card.security.api.request.CardChannelTokenAddRequest;
import com.payermax.card.security.api.response.CardChannelTokenAddResponse;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.fin.exchange.common.enums.ApiModeEnum;
import com.payermax.fin.exchange.common.enums.ExtParamEnum;
import com.payermax.fin.exchange.common.enums.OrderStatusEnum;
import com.payermax.fin.exchange.common.enums.RequestTypeEnum;
import com.payermax.fin.exchange.domain.trans.RequestOrderDO;
import com.payermax.fin.exchange.domain.trans.token.ChannelTokenIdResponseOrderDO;
import com.payermax.fin.exchange.domainservice.bo.BizContextBO;
import com.payermax.fin.exchange.domainservice.bo.TransactionBO;
import com.payermax.fin.exchange.integration.rpc.constants.InternalUrlConstants;
import com.payermax.fin.exchange.integration.rpc.request.ChannelTokenIdFrontRequest;
import com.payermax.fin.exchange.integration.rpc.response.ChannelTokenIdFrontResponse;
import com.payermax.fin.exchange.share.enums.ChannelTokenEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Map;

/**
 * ChannelTokenId请求模版
 *
 * <AUTHOR>
 * @date 2024/12/2 22:01
 */
@Slf4j
@Component
public class ChannelTokenIdExchangeTemplate extends CommonExchangeTemplate<RequestOrderDO, ChannelTokenIdResponseOrderDO, ChannelTokenIdFrontRequest, ChannelTokenIdFrontResponse> {

    @DubboReference(version = "1.0", timeout = 100)
    private CardChannelTokenFacade cardChannelTokenFacade;

    @Override
    public ChannelTokenIdFrontRequest initFrontRequest() {
        return new ChannelTokenIdFrontRequest();
    }

    @Override
    public String getApiMode() {
        return ApiModeEnum.PAST_FRONT.name();
    }

    @Override
    public String getApiName() {
        return InternalUrlConstants.FRONT_URL_CHANNELTOKENAPPLY;
    }

    @Override
    public ChannelTokenIdFrontRequest exchangeRequest(BizContextBO<RequestOrderDO, ChannelTokenIdResponseOrderDO> bizContext) {
        ChannelTokenIdFrontRequest frontRequest = super.exchangeRequest(bizContext);
        RequestOrderDO requestOrderDO = bizContext.getIn();
        Map<String, String> params = requestOrderDO.getParams();
        frontRequest.setCardToken(params.get(ExtParamEnum.CARD_TOKEN.getCode()));
        frontRequest.setCardIdentifierNo(params.get(ExtParamEnum.CARD_IDENTIFIER_NO.getCode()));
        frontRequest.setCvvToken(params.get(ExtParamEnum.CVV_TOEKN.getCode()));
        frontRequest.setApi(InternalUrlConstants.CHANNEL_GATEWAY_API_CHANNELTOKENAPPLY);
        return frontRequest;
    }

    /**
     * 请求成功后的Response处理
     * @param response
     * @param bizContext
     * @return
     */
    @Override
    public ChannelTokenIdResponseOrderDO exchangeResponse(ChannelTokenIdFrontResponse response, BizContextBO<RequestOrderDO, ChannelTokenIdResponseOrderDO> bizContext) {
        ChannelTokenIdResponseOrderDO channelTokenIdResponseOrderDO = super.exchangeResponse(response, bizContext);
        TransactionBO transactionBO = getTransactionBO(bizContext);

        String networkTokenId = response.getNetworkTokenId();
        channelTokenIdResponseOrderDO.setTokenId(networkTokenId);
        channelTokenIdResponseOrderDO.setAuthenticationMethodTypes(response.getAuthenticationMethodTypes());
        channelTokenIdResponseOrderDO.setTokenStatus(response.getTokenStatus());
        channelTokenIdResponseOrderDO.setTokenChannel(transactionBO.getChannelCode());
        channelTokenIdResponseOrderDO.setTokenChannelMid(transactionBO.getChannelMerchantCode());
        channelTokenIdResponseOrderDO.setTokenType(response.getTokenType());
        //添加到卡服务
        addToCardServer(bizContext.getIn(), transactionBO, channelTokenIdResponseOrderDO);
        return channelTokenIdResponseOrderDO;
    }

    /**
     * 添加到卡服务
     * @param request
     * @param transactionBO
     * @param response
     */
    private void addToCardServer(RequestOrderDO request, TransactionBO transactionBO, ChannelTokenIdResponseOrderDO response) {
        if (RequestTypeEnum.APPLY_PAYMENT_TOKEN_ID != transactionBO.getRequestType() || response == null || !OrderStatusEnum.SUCCESS.equals(response.getStatus())) {
            return;
        }
        if (StringUtils.isBlank(response.getTokenId())) {
            return;
        }
        try {
            CardChannelTokenAddRequest cardChannelTokenAddRequest = new CardChannelTokenAddRequest();
            cardChannelTokenAddRequest.setTokenChannel(transactionBO.getChannelCode());
            cardChannelTokenAddRequest.setTokenChannelMid(transactionBO.getChannelMerchantCode());
            cardChannelTokenAddRequest.setTokenType(ChannelTokenEnum.NETWORK_TOKEN.getCode());

            Map<String, String> params = request.getParams();
            cardChannelTokenAddRequest.setCardIdentityNo(params.get(ExtParamEnum.CARD_IDENTIFIER_NO.getCode()));
            cardChannelTokenAddRequest.setTokenValue(response.getTokenId());
            cardChannelTokenAddRequest.setStatus(response.getTokenStatus());
            if (CollectionUtils.isNotEmpty(response.getAuthenticationMethodTypes())) {//认证类型可能为空
                HashSet<String> authenticationMethodTypes = CollUtil.newHashSet(response.getAuthenticationMethodTypes());
                cardChannelTokenAddRequest.setAuthenticationMethodTypes(authenticationMethodTypes);
            }
            Result<CardChannelTokenAddResponse> cardChannelTokenAddResponseResult = cardChannelTokenFacade.addCardChannelToken(cardChannelTokenAddRequest);
            if (!cardChannelTokenAddResponseResult.isSuccess()) {
                log.warn("ChannelTokenIdExchangeTemplate.addCardChannelToken fail,request:{}", JSONObject.toJSONString(request));
            }
        } catch (Exception e) {
            log.warn("ChannelTokenIdExchangeTemplate.addCardChannelToken error,request:{}", JSONObject.toJSONString(response));
        }
    }

}
