package com.payermax.fin.exchange.domainservice.newExchange;

import com.payermax.fin.exchange.common.enums.OrderStatusEnum;
import com.payermax.fin.exchange.dal.entity.ChannelCommitOrderEntity;
import com.payermax.fin.exchange.dal.entity.ChannelConfigMappingEntity;
import com.payermax.fin.exchange.domain.inquiry.CompensateInquiryRequestDO;
import com.payermax.fin.exchange.domainservice.bo.BizContextBO;
import com.payermax.fin.exchange.domainservice.bo.CompensateTransactionBO;
import com.payermax.fin.exchange.domainservice.repository.IChannelConfigMappingService;
import com.payermax.fin.exchange.domainservice.repository.ITargetMerchantService;
import com.payermax.fin.exchange.integration.rpc.constants.InternalUrlConstants;
import com.payermax.fin.exchange.integration.rpc.request.CommonCompensateFrontRequest;
import com.payermax.fin.exchange.integration.rpc.response.CommonCompensateFrontResponse;
import com.payermax.fin.exchange.share.constants.ShareConstants;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 通用补偿查询Exchange Template
 * <AUTHOR>
 * @date 2024/12/14 17:49
 */
@Component
public class CommonCompensateExchangeTemplate extends AbstractCompensateExchangeTemplate<CompensateInquiryRequestDO, CommonCompensateFrontRequest, CommonCompensateFrontResponse> {

    @Autowired
    protected ITargetMerchantService targetMerchantService;

    @Autowired
    protected IChannelConfigMappingService channelConfigMappingService;

    @Override
    public Class<CommonCompensateFrontRequest> frontRequestClass() {
        return CommonCompensateFrontRequest.class;
    }

    @Override
    public String getApiName() {
        return String.format("%s_%s", ShareConstants.STAR, InternalUrlConstants.SUFFIX_INQUIRY);
    }

    @Override
    public CommonCompensateFrontRequest exchangeRequest(BizContextBO<CompensateInquiryRequestDO, OrderStatusEnum> bizContext) {
        CompensateTransactionBO transactionBO = getTransactionBO(bizContext);
        String channelMethodCode = transactionBO.getChannelMethodCode();

        CommonCompensateFrontRequest frontRequest = super.exchangeRequest(bizContext);
        ChannelConfigMappingEntity configMapping = channelConfigMappingService.getByConfigCode(channelMethodCode);
        if (configMapping != null) {
            frontRequest.setChannelCode(configMapping.getOldChannelCode());
        }
        //设置二级商户信息
        frontRequest.setTargetMerchantDetail(targetMerchantService.getAndCheckTargetMerchant(
                channelMethodCode, frontRequest.getChannelMerchantCode(), frontRequest.getMerchantId(), frontRequest.getTargetSubMerchantId()));
        return frontRequest;
    }

    @Override
    public void fillCommitOrderAfterExchange(ChannelCommitOrderEntity commitOrder, BizContextBO<CompensateInquiryRequestDO, OrderStatusEnum> bizContext) {
        CompensateTransactionBO transactionBO = getTransactionBO(bizContext);

        CommonCompensateFrontResponse frontResponse = (CommonCompensateFrontResponse) transactionBO.getInternalBaseResponse();
        commitOrder.setThirdOrgOrderNo(StringUtils.defaultIfBlank(frontResponse.getChannelTransactionId(), commitOrder.getThirdOrgOrderNo()));
        commitOrder.setFourthOrgOrderNo(StringUtils.defaultIfBlank(frontResponse.getChannelThirdPartyTxnId(), commitOrder.getFourthOrgOrderNo()));
        commitOrder.setAddField1(StringUtils.defaultIfBlank(frontResponse.getAddField1(), commitOrder.getAddField1()));
        commitOrder.setAddField2(StringUtils.defaultIfBlank(frontResponse.getAddField2(), commitOrder.getAddField2()));
        commitOrder.setAddField3(StringUtils.defaultIfBlank(frontResponse.getAddField3(), commitOrder.getAddField3()));
        commitOrder.setAddField4(StringUtils.defaultIfBlank(frontResponse.getAddField4(), commitOrder.getAddField4()));
        commitOrder.setAddField5(StringUtils.defaultIfBlank(frontResponse.getAddField5(), commitOrder.getAddField5()));
        commitOrder.setAddField6(StringUtils.defaultIfBlank(frontResponse.getAddField6(), commitOrder.getAddField6()));
    }

}
