package com.payermax.fin.exchange.domainservice.newExchange;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONPath;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.util.JsonUtils;
import com.payermax.fin.exchange.common.constants.CommonConstants;
import com.payermax.fin.exchange.common.enums.*;
import com.payermax.fin.exchange.dal.entity.*;
import com.payermax.fin.exchange.domain.trans.RequestOrderDO;
import com.payermax.fin.exchange.domain.trans.ResponseOrderDO;
import com.payermax.fin.exchange.domain.trans.ReturnParamDO;
import com.payermax.fin.exchange.domain.trans.pay.PayRequestOrderDO;
import com.payermax.fin.exchange.domain.trans.pay.PayResponseOrderDO;
import com.payermax.fin.exchange.domainservice.bo.BizContextBO;
import com.payermax.fin.exchange.domainservice.bo.ChannelConfigJsonBO;
import com.payermax.fin.exchange.domainservice.bo.TransactionBO;
import com.payermax.fin.exchange.domainservice.expression.ExpressionConstant;
import com.payermax.fin.exchange.domainservice.expression.ExpressionUtils;
import com.payermax.fin.exchange.domainservice.expression.JSONPathExecutor;
import com.payermax.fin.exchange.domainservice.repository.IAuthorizationService;
import com.payermax.fin.exchange.domainservice.repository.IChannelInfoService;
import com.payermax.fin.exchange.domainservice.repository.IChannelMethodService;
import com.payermax.fin.exchange.domainservice.repository.ITargetMerchantService;
import com.payermax.fin.exchange.domainservice.service.validation.impl.CommonCheck;
import com.payermax.fin.exchange.domainservice.util.AmountUtils;
import com.payermax.fin.exchange.domainservice.util.ObjectOperateUtils;
import com.payermax.fin.exchange.integration.rpc.constants.InternalUrlConstants;
import com.payermax.fin.exchange.integration.rpc.request.PayFrontRequest;
import com.payermax.fin.exchange.integration.rpc.response.PayFrontResponse;
import com.payermax.fin.exchange.service.enums.ResultTypeEnum;
import com.payermax.fin.exchange.share.constants.ShareConstants;
import com.payermax.fin.exchange.share.domain.EnvInfoDO;
import com.payermax.fin.exchange.share.domain.OrderInfoDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/3/17 15:28
 */
@Slf4j
@Component
public class FrontPayApplyExchangeTemplate extends AbstractExchangeTemplate<RequestOrderDO, ResponseOrderDO, PayFrontRequest, PayFrontResponse> {

    @NacosValue(value = "#{${terminal.relation.map}}", autoRefreshed = true)
    private Map<String, String> terminalRelationMap;

    @NacosValue(value = "#{'${exchange.isUseOriginalCheckoutUrl.channelCodes:ALL}'.split(',')}", autoRefreshed = true)
    private List<String> useOriginalUrlChannelCodes;

    @Autowired
    private IChannelInfoService channelInfoService;

    @Autowired
    private IChannelMethodService channelMethodService;

    @Autowired
    private ITargetMerchantService targetMerchantService;

    @Autowired
    private IAuthorizationService authorizationService;

    @Autowired
    private CommonCheck commonCheck;

    @Override
    public String getApiMode() {
        return ApiModeEnum.PAST_FRONT.name();
    }

    @Override
    public String getApiName() {
        return InternalUrlConstants.FRONT_URL_PAY;
    }

    @Override
    protected Map<String, String> defaultRequestMapping() {
        Map<String, String> requestMapping = super.defaultRequestMapping();
        requestMapping.put("paymentIntegration", "paymentIntegration");
        requestMapping.put("additionalPaymentTag", "additionalPaymentTag");
        return requestMapping;
    }

    @Override
    public PayFrontRequest exchangeRequest(BizContextBO<RequestOrderDO, ResponseOrderDO> bizContext) {
        RequestOrderDO payRequest = bizContext.getIn();
        OrderInfoDO orderInfo = payRequest.getOrderInfo();
        EnvInfoDO envInfo = orderInfo.getEnvInfo();

        TransactionBO transactionBO = getTransactionBO(bizContext);
        ChannelConfigMappingEntity channelConfigMapping = transactionBO.getChannelConfigMappingEntity();
        ChannelStepApiMappingEntity currentStepApiMapping = transactionBO.getCurrentStepApiMapping();
        ChannelRequestOrderEntity channelRequestOrder = transactionBO.getChannelRequestOrder();
        ChannelCommitOrderEntity channelCommitOrder = transactionBO.getChannelCommitOrder();
        ChannelInfoEntity channelInfo = transactionBO.getChannelInfoEntity();
        Object preResponse = transactionBO.getPreResponse();

        PayFrontRequest payFrontRequest = new PayFrontRequest();

        payFrontRequest.setIdcRegion(payRequest.getIdcRegion());
        payFrontRequest.setChannelMerchantCode(transactionBO.getChannelMerchantCode());
        payFrontRequest.setChannelCertificationNo(transactionBO.getChannelPayCommitNo());
        payFrontRequest.setChannelOrderNo(transactionBO.getChannelPayCommitNo());
        payFrontRequest.setChannelRequestOrderNo(channelRequestOrder.getChannelPayRequestNo());
        payFrontRequest.setPayOrderNo(channelRequestOrder.getBizOrderNo());
        // 使用提交单上的金额
        payFrontRequest.setAmount(AmountUtils.toLeastTwoScale(channelCommitOrder.getAmount()));
        payFrontRequest.setCountry(channelRequestOrder.getCountry());
        payFrontRequest.setCurrency(channelRequestOrder.getCurrency());

        String customerType = Objects.toString(JSONPath.eval(channelRequestOrder.getRequestBody(), FrontParamEnum.customerType.getCode()), "");
        payFrontRequest.setCustomerType(customerType);

        if (StringUtils.isNotBlank(channelRequestOrder.getUserMemberId())) {
            payFrontRequest.setUserId(channelRequestOrder.getUserMemberId());
        } else {
            payFrontRequest.setUserId(channelRequestOrder.getOutUserId());
        }
        if (null != orderInfo.getOrderExpireTimeStamp()) {
            payFrontRequest.setOrderExpireTimeStamp(orderInfo.getOrderExpireTimeStamp());
        }
        payFrontRequest.setPurchaseInfo(orderInfo.getPurchaseInfo());
        payFrontRequest.setRemark(orderInfo.getRemark());
        payFrontRequest.setMerchantTxnId(channelRequestOrder.getBizOrderNo());
        payFrontRequest.setMerchantId(channelRequestOrder.getMerchantIdSendToChannel());
        payFrontRequest.setChannelCode(transactionBO.getChannelCode());
        // 设置老系统渠道编码等信息
        if (Objects.nonNull(channelConfigMapping)) {
            payFrontRequest.setProductCode(channelConfigMapping.getOldProductCode());
            payFrontRequest.setChannelCode(channelConfigMapping.getOldChannelCode());
            payFrontRequest.setMethodCode(channelConfigMapping.getOldMethodCode());
            payFrontRequest.setMethodSubCode(channelConfigMapping.getOldMethodSubCode());
        }
        // 设置新系统渠道编码等信息
        if (Objects.nonNull(channelInfo)) {
            ChannelConfigJsonBO channelConfigJson = channelInfoService.getConfigJsonByChannel(channelInfo);
            payFrontRequest.setTechnicalOrg(channelInfo.getTechnicalOrg());
            payFrontRequest.setTargetService(channelConfigJson.getTargetService());
        }
        payFrontRequest.setNewChannelCode(transactionBO.getChannelCode());
        payFrontRequest.setChannelMethodCode(transactionBO.getChannelMethodCode());
        payFrontRequest.setPaymentMethodType(channelRequestOrder.getPaymentMethodType());
        payFrontRequest.setTargetOrCardOrg(channelRequestOrder.getTargetOrCardOrg());
        payFrontRequest.setBankCode(channelRequestOrder.getExtJsonBo().getBankCode());

        // 设置支付方式维度透传到下游的配置
        ChannelMethodEntity channelConfigEntity = transactionBO.getChannelConfigEntity();
        payFrontRequest.setChannelMethodExtConfig(channelMethodService.getChannelExtConfigByConfigJson(channelConfigEntity.getConfigJson()));

        // 设置上一步渠道编码
        payFrontRequest.setPreStepChannelCode(transactionBO.getPreStepChannelCode());
        payFrontRequest.setShopId(orderInfo.getShopId());
        payFrontRequest.setSubMerchantNo(channelRequestOrder.getExtJsonBo().getSubMerchantNo());
        // 二级商户号：先根据渠道维度的配置字段值作为二级商户号，如果无值，则取subMerchantNo
        String targetSubMerchantId = targetMerchantService.getSubMerchantIdByRequestOrder(transactionBO.getChannelCode(), channelRequestOrder.getRequestBody());
        if (StringUtils.isBlank(targetSubMerchantId)) {
            targetSubMerchantId = orderInfo.gainSubMerchantNo();
        }
        TargetMerchantEntity targetMerchant = targetMerchantService.getAndCheckTargetMerchant(
                transactionBO.getChannelMethodCode(), transactionBO.getChannelMerchantCode(), orderInfo.getMerchantNo(), targetSubMerchantId);
        targetSubMerchantId = null == targetMerchant ? null : targetMerchant.getSubMerchantId();
        payFrontRequest.setTargetSubMerchantId(targetSubMerchantId);
        payFrontRequest.setTargetMerchantDetail(targetMerchant);

        // 付款方扩展信息
        PayFrontRequest.PayerExtraDetails payerExtraDetails = new PayFrontRequest.PayerExtraDetails();
        payFrontRequest.setPayerExtraDetails(payerExtraDetails);
        // 用户IP
        if (envInfo != null) {
            payFrontRequest.setPayerRemoteIp(envInfo.getClientIp());
            payerExtraDetails.setPayerRemoteIp(envInfo.getClientIp());
            payFrontRequest.setOsType(envInfo.getOsType());
            payFrontRequest.setAcceptHeader(envInfo.getAcceptHeader());
            payFrontRequest.setUserAgentHeader(envInfo.getUserAgentHeader());
            payFrontRequest.setBrowserLanguage(envInfo.getBrowserLanguage());
            payFrontRequest.setBrowserJavaEnabled(envInfo.getBrowserJavaEnabled());
            payFrontRequest.setBrowserColorDepth(envInfo.getBrowserColorDepth());
            payFrontRequest.setBrowserScreenHeight(envInfo.getBrowserScreenHeight());
            payFrontRequest.setBrowserScreenWidth(envInfo.getBrowserScreenWidth());
            payFrontRequest.setBrowserTZ(envInfo.getBrowserTZ());
            String terminalType = envInfo.getTerminalType();
            if (terminalRelationMap != null && terminalRelationMap.get(terminalType) != null) {
                terminalType = terminalRelationMap.get(terminalType);
            }
            payFrontRequest.setTerminalType(terminalType);
        }
        // 授权支付，设置相关信息
        if (RequestTypeEnum.AUTHORIZATION_PAY == transactionBO.getRequestType() && payRequest instanceof PayRequestOrderDO) {
            PayRequestOrderDO payRequestOrderDO = (PayRequestOrderDO) payRequest;
            String internalAuthCode = Objects.requireNonNull(payRequestOrderDO.getAuthorizeCode());
            AuthorizationEntity authorizationEntity = Objects.requireNonNull(authorizationService.getByInternalAuthCode(internalAuthCode));
            payFrontRequest.setAuthorizeCode(authorizationEntity.getChannelAuthCode());
            payFrontRequest.setInternalAuthCode(payRequestOrderDO.getAuthorizeCode());
        }

        // 以下逻辑为：根据数据库参数映射把新系统采集参数映射成老系统字段
        Map allParams = payRequest.getAllParams();
        allParams.put(ExtParamEnum.MCC.getCode(), orderInfo.getMcc());
        // 重新设置ddcSessionId

        this.fillDdcSessionId(bizContext, allParams);
        // 卡标识
        Object cardIdentifierNo = allParams.get(ExtParamEnum.CARD_IDENTIFIER_NO.getCode());
        if (cardIdentifierNo != null) {
            payerExtraDetails.setCardIdentifierNo(String.valueOf(cardIdentifierNo));
            payFrontRequest.setCardIdentifierNo(String.valueOf(cardIdentifierNo));
        }
        Object cryptogram = allParams.get(ExtParamEnum.CRYPTOGRAM.getCode());
        if (cryptogram != null) {
            payFrontRequest.setCryptogram(String.valueOf(cryptogram));
        }
        Object transactionIdentifier = allParams.get(ExtParamEnum.TRANSACTION_IDENTIFIER.getCode());
        if (transactionIdentifier != null) {
            payFrontRequest.setTransactionIdentifier(String.valueOf(transactionIdentifier));
        }
        Map<String, String> tmpParamsMapping = null;
        if (currentStepApiMapping != null && StringUtils.isNotBlank(currentStepApiMapping.getParamsMapping())) {
            tmpParamsMapping = JsonUtils.toBean(Map.class, currentStepApiMapping.getParamsMapping());
        }
        // 根据配置进行参数映射
        if (tmpParamsMapping != null && !tmpParamsMapping.isEmpty()) {
            Map passInfoToChannelMap = new HashMap();
            // 定义数据解析本地缓存Map
            Map<String, Object> resolveMapCache = new HashMap<>();
            tmpParamsMapping.forEach((tempField, valKey) -> {
                try {
                    if (ExpressionUtils.isMatch(valKey, ExpressionConstant.REQUEST_PREFIX)) { // 从requestOrderDO中取值
                        String tempValKey = ExpressionUtils.delPrefix(valKey, ExpressionConstant.REQUEST_PREFIX);
                        Object tempVal = ExpressionUtils.executeNew(tempValKey, payRequest);
                        JSONPathExecutor.set(payFrontRequest, tempField, tempVal);
                        return;
                    }
                    Object value = null;
                    boolean result = ExpressionUtils.isMatch(valKey, ExpressionConstant.RESPONSE_PREFIX);
                    if (result) { // 从上一步的结果中取值
                        String tempValKey = ExpressionUtils.delPrefix(valKey, ExpressionConstant.RESPONSE_PREFIX);
                        value = ExpressionUtils.execute(tempValKey, preResponse, resolveMapCache);
                    } else {
                        value = ExpressionUtils.exeWithMap(valKey, allParams);
                    }
                    if (value == null) {
                        return;
                    }
                    if (tempField.lastIndexOf('.') < 0) {
                        // 统一使用JSONPath赋值
                        JSONPathExecutor.set(payFrontRequest, tempField, value);
                        return;
                    }
                    // 以下为兼容以前老配置
                    String prefix = tempField.substring(0, tempField.lastIndexOf('.'));
                    String field = tempField.substring(tempField.lastIndexOf('.') + 1);
                    // payFrontRequest中的字段
                    if (CommonConstants.PREFIX_PAY_FRONT_REQUEST.equals(prefix)) {
                        ReflectUtil.setFieldValue(payFrontRequest, field, value);
                        return;
                    }
                    // payFrontRequest.payerExtraDetails 中的字段
                    if (CommonConstants.PREFIX_PAY_FRONT_REQUEST_PAYER_EXTRA_DETAILS.equals(prefix)) {
                        ReflectUtil.setFieldValue(payerExtraDetails, field, value);
                        return;
                    }
                    if (CommonConstants.PREFIX_PAY_FRONT_REQUEST_SHIPPING_INFO.equals(prefix)) {
                        if (payFrontRequest.getShippingInfo() == null) {
                            payFrontRequest.setShippingInfo(new PayFrontRequest.ShippingInfo());
                        }
                        ReflectUtil.setFieldValue(payFrontRequest.getShippingInfo(), field, value);
                        return;
                    }
                    if (CommonConstants.PREFIX_PAY_FRONT_REQUEST_3DS_INFO.equals(prefix)) {
                        if (payFrontRequest.getThreeDSecureInfo() == null) {
                            payFrontRequest.setThreeDSecureInfo(new PayFrontRequest.ThreeDSecureInfo());
                        }
                        ReflectUtil.setFieldValue(payFrontRequest.getThreeDSecureInfo(), field, value);
                        return;
                    }
                    if (CommonConstants.PREFIX_PAY_FRONT_TARGET_MERCHANT_INFO.equals(prefix)) {
                        if (payFrontRequest.getTargetMerchantDetail() == null) {
                            payFrontRequest.setTargetMerchantDetail(new TargetMerchantEntity());
                        }
                        ReflectUtil.setFieldValue(payFrontRequest.getTargetMerchantDetail(), field, value);
                        return;
                    }
                    // payFrontRequest.passInfoToChannelByPayment 中的字段
                    if (CommonConstants.PREFIX_PAY_FRONT_REQUEST_PASS_INFO_TO_CHANNEL_BY_PAYMENT.equals(prefix)) {
                        passInfoToChannelMap.put(field, value);
                        if (payFrontRequest.getPassInfoToChannelByPayment() == null) {
                            payFrontRequest.setPassInfoToChannelByPayment(new PayFrontRequest.PassInfoToChannelByPayment());
                        }
                        ReflectUtil.setFieldValue(payFrontRequest.getPassInfoToChannelByPayment(), field, value);
                        return;
                    }
                    // payFrontRequest.passInfoToChannel 中的字段
                    if (CommonConstants.PREFIX_PAY_FRONT_REQUEST_PASS_INFO_TO_CHANNEL.equals(prefix)) {
                        passInfoToChannelMap.put(field, value);
                        return;
                    }
                    // payFrontRequest.passInfoToChannelByPayment.payerExtraDetails 中的字段
                    if (CommonConstants.PREFIX_PAY_FRONT_REQUEST_PASS_INFO_TO_CHANNEL_BY_PAYMENT_PAYER_EXTRA_DETAILS.equals(prefix)) {
                        if (payFrontRequest.getPassInfoToChannelByPayment() == null) {
                            payFrontRequest.setPassInfoToChannelByPayment(new PayFrontRequest.PassInfoToChannelByPayment());
                        }
                        if (payFrontRequest.getPassInfoToChannelByPayment().getPayerExtraDetails() == null) {
                            payFrontRequest.getPassInfoToChannelByPayment().setPayerExtraDetails(new PayFrontRequest.PassInfoToChannelByPayment.PayerExtraDetails());
                        }
                        ReflectUtil.setFieldValue(payFrontRequest.getPassInfoToChannelByPayment().getPayerExtraDetails(), field, value);
                        return;
                    }
                    // 其它情况统一使用JSONPath赋值
                    JSONPathExecutor.set(payFrontRequest, tempField, value);
                } catch (Exception e) {
                    log.warn("采集参数(valKey:{}, Field:{})映射到Front系统入参错误，需排查！", valKey, tempField, e);
                }
            });

            if (!passInfoToChannelMap.isEmpty()) {
                payFrontRequest.setPassInfoToChannel(JsonUtils.toJson(passInfoToChannelMap));
            }
            // 设置cvv
            if (StringUtils.isNotBlank(payerExtraDetails.getCvv())) {
                payFrontRequest.setCvv(payerExtraDetails.getCvv());
            }
        }
        //用户使用语言处理
        if (null == payFrontRequest.getPassInfoToChannelByPayment()) {
            payFrontRequest.setPassInfoToChannelByPayment(new PayFrontRequest.PassInfoToChannelByPayment());
        }
        payFrontRequest.getPassInfoToChannelByPayment().setLanguage(orderInfo.getUserLanguage());
        payFrontRequest.setLanguage(orderInfo.getUserLanguage());
        //上送请求类型
        payFrontRequest.setRequestType(channelRequestOrder.getRequestType());
        // 对请求参数进行校验
        PayFrontRequest tmpPayFrontRequest = this.extraParamValidation(payFrontRequest);
        //校验后交易备注和消费信息回填到请求对象(更新请求单)
        channelRequestOrder.setRemark(tmpPayFrontRequest.getRemark());
        channelRequestOrder.setPurchaseInfo(tmpPayFrontRequest.getPurchaseInfo());
        tmpPayFrontRequest.setApi(InternalUrlConstants.CHANNEL_GATEWAY_API_PAY);
        return tmpPayFrontRequest;
    }

    @Override
    public void fillCommitOrderAfterExchange(ChannelCommitOrderEntity commitOrder, BizContextBO<RequestOrderDO, ResponseOrderDO> bizContext) {
        TransactionBO transactionBO = getTransactionBO(bizContext);
        // 正确响应的前提下，更新提交单&请求单
        PayFrontResponse payFrontResponse = (PayFrontResponse) transactionBO.getInternalBaseResponse();

        ChannelStepApiMappingEntity currentStepApiMapping = transactionBO.getCurrentStepApiMapping();
        String backendApiUrl = currentStepApiMapping == null ? "" : currentStepApiMapping.getBackendApiUrl();

        commitOrder.setThirdOrgOrderNo(payFrontResponse.getChannelTransactionId());
        commitOrder.setFourthOrgOrderNo(payFrontResponse.getChannelThirdPartyTxnId());
        // 申请发送otp接口 1. 设置三方单号 2. 订单状态置为初始化（OTP验证时才置为pending，解决resend的问题）
        if (InternalUrlConstants.FRONT_URL_CERTIFICATION_APPLY.equals(backendApiUrl)) {
            commitOrder.setThirdOrgOrderNo(payFrontResponse.getThirdCertificationNo());
            commitOrder.setFourthOrgOrderNo(payFrontResponse.getFourthPartyCertificationNo());
            // 如果订单状态不为失败，置为初始化（OTP验证时才置为pending，解决resend的问题）
            if (transactionBO.getOrderStatusEnum() != OrderStatusEnum.FAILED) {
                transactionBO.setOrderStatusEnum(OrderStatusEnum.INITIATE);
            }
        }

        commitOrder.setAddField1(payFrontResponse.getAddField1());
        commitOrder.setAddField2(payFrontResponse.getAddField2());
        commitOrder.setAddField3(payFrontResponse.getAddField3());
        commitOrder.setAddField4(payFrontResponse.getAddField4());
        commitOrder.setAddField5(payFrontResponse.getAddField5());
        commitOrder.setAddField6(payFrontResponse.getAddField6());
        // 设置ARN和RRN
        Optional.ofNullable(payFrontResponse.getArn()).ifPresent(arn -> commitOrder.getExtJsonBo().setArn(arn));
        Optional.ofNullable(payFrontResponse.getRrn()).ifPresent(rrn -> commitOrder.getExtJsonBo().setRrn(rrn));
        Optional.ofNullable(payFrontResponse.getAvsResultCode()).ifPresent(avs -> commitOrder.getExtJsonBo().setAvsResultCode(avs));

        // 3ds请求 && 状态为pending，则保存需要challenge
        if (StepApiMappingStepCodeEnum.PAY3DS.getCode().equals(commitOrder.getRequestType())
                && OrderStatusEnum.PENDING == transactionBO.getOrderStatusEnum()) {
            ChannelCommitOrderEntity.ExtJsonBO extJsonBO = commitOrder.getExtJsonBo();
            extJsonBO.setIsChallenge(ShareConstants.YES_FLAG);
        }
    }

    @Override
    public ResponseOrderDO exchangeResponse(PayFrontResponse response, BizContextBO<RequestOrderDO, ResponseOrderDO> bizContext) {
        // 执行通用响应映射
        super.exchangeResponse(response, bizContext);
        ResponseOrderDO responseOrderDO = bizContext.getOut();
        // 如果是PayResponseOrderDO类型，则兼容
        if (responseOrderDO instanceof PayResponseOrderDO) {
            return buildPayResponseOrderDO(response, (PayResponseOrderDO) responseOrderDO, bizContext);
        }
        return responseOrderDO;
    }

    /**
     * 填充ddcSessionId*
     * *
     *
     * @param bizContext
     * @return
     */
    private void fillDdcSessionId(BizContextBO<RequestOrderDO, ResponseOrderDO> bizContext, Map allParams) {
        RequestOrderDO request = bizContext.getIn();
        Pair<String, String> ddcOrgSessionPair = request.getDdcOrgSessionPair();
        String ddcSessionId = null;
        if (ddcOrgSessionPair != null) {
            ddcSessionId = ddcOrgSessionPair.getValue();
        }
        // 重置ddcSessionId
        allParams.put(ExtParamEnum.DDC_SESSION_ID.getCode(), ddcSessionId);
    }

    /**
     * 渠道请求入参校验
     *
     * @param payFrontRequest
     * @return
     */
    private PayFrontRequest extraParamValidation(PayFrontRequest payFrontRequest) {
        try {
            payFrontRequest = commonCheck.checkTradeFilter(payFrontRequest, PayFrontRequest.class);
        } catch (Exception e) {
            throw new BusinessException(ErrorCodeEnum.PARAMETER_INVALID.getCode(), e.getMessage());
        }
        return payFrontRequest;
    }

    /**
     * 构建PayResponseOrderDO类型的响应
     *
     * @param payFrontResponse
     * @param payResponseDTO
     * @param bizContext
     * @return
     */
    private PayResponseOrderDO buildPayResponseOrderDO(PayFrontResponse payFrontResponse, PayResponseOrderDO payResponseDTO, BizContextBO<RequestOrderDO, ResponseOrderDO> bizContext) {
        TransactionBO transactionBO = getTransactionBO(bizContext);
        String channelCode = transactionBO.getChannelCode();
        ChannelStepApiMappingEntity currentStepApiMapping = transactionBO.getCurrentStepApiMapping();
        ChannelRequestOrderEntity channelRequestOrder = transactionBO.getChannelRequestOrder();
        ChannelCommitOrderEntity channelCommitOrder = transactionBO.getChannelCommitOrder();

        // 构建响应报文
        ObjectOperateUtils.fillPayResponseOrderDO(payResponseDTO, channelRequestOrder, channelCommitOrder);

        ReturnParamDO returnParam = new ReturnParamDO();
        // 设置extraInfo
        String extraInfo = payFrontResponse.getExtraInfo();
        if (StringUtils.isNotBlank(extraInfo)) {
            Map<String, Object> extraInfoMap = JsonUtils.toBean(Map.class, extraInfo);
            returnParam.setExtraInfo(extraInfoMap);
            if (extraInfoMap != null && extraInfoMap.get("vaid") != null) {
                returnParam.setVirtualAccount(String.valueOf(extraInfoMap.get("vaid")));
            }
            payResponseDTO.setReturnParam(returnParam);
        }

        String interactiveMode = payFrontResponse.getInteractiveMode();

        // API
        if (PayFrontResponse.InteractiveModeEnum.API.name().equals(payFrontResponse.getInteractiveMode())
                || PayFrontResponse.InteractiveModeEnum.THREE_DS_DIRECT.name().equals(interactiveMode)) {
            payResponseDTO.setResultType(ResultTypeEnum.API.getType());
            return payResponseDTO;
        }

        // REDIRECT
        if (PayFrontResponse.InteractiveModeEnum.REDIRECT.name().equals(interactiveMode)
                || PayFrontResponse.InteractiveModeEnum.THREE_DS_REDIRECT.name().equals(interactiveMode)) {
            payResponseDTO.setResultType(ResultTypeEnum.REDIRECT.getType());
            // 下游指定交互模式，则先根据数据库步骤配置，进行结果映射，则取固定值
            if (Objects.nonNull(currentStepApiMapping)) {
                dealResultParamMapping(payFrontResponse, returnParam, currentStepApiMapping.getResultMapping());
            }
            String requestUrl = payFrontResponse.getRequestUrl();
            if (StringUtils.isNotBlank(requestUrl)) {
                returnParam.setRedirectUrl(requestUrl);
            }
            returnParam.setRedirectPageInfo(payFrontResponse.getRedirect2PaymentChannelPageInfo());
            // 解析渠道原始url
            String origRedirectUrl = this.parseOrigRedirectUrl(channelCode, payFrontResponse.getRedirect2PaymentChannelPageInfo());
            returnParam.setOrigRedirectUrl(StringUtils.isBlank(origRedirectUrl) ? requestUrl : origRedirectUrl);
            payResponseDTO.setReturnParam(returnParam);
            return payResponseDTO;
        }

        // RENDER - with qr_code
        if (PayFrontResponse.InteractiveModeEnum.QRCODE_INFO.name().equals(interactiveMode)) {
            payResponseDTO.setResultType(ResultTypeEnum.RENDER.getType());
            payResponseDTO.setReturnParam(returnParam);
            // 下游指定交互模式，则先根据数据库步骤配置，进行结果映射，则取固定值
            if (Objects.nonNull(currentStepApiMapping)) {
                dealResultParamMapping(payFrontResponse, returnParam, currentStepApiMapping.getResultMapping());
            }
            String paymentInfoStr = payFrontResponse.getPaymentInfo();
            if (StringUtils.isBlank(paymentInfoStr)) {
                return payResponseDTO;
            }
            PayFrontResponse.PaymentInfo paymentInfo = JsonUtils.toBean(PayFrontResponse.PaymentInfo.class, paymentInfoStr);
            if (Objects.isNull(paymentInfo)) {
                return payResponseDTO;
            }
            if (StringUtils.isNotBlank(paymentInfo.getQrInfo())) {
                returnParam.setQrCode(paymentInfo.getQrInfo());
            }
            if (StringUtils.isNotBlank(paymentInfo.getUrlInfo())) {
                returnParam.setQrCodeUrl(paymentInfo.getUrlInfo());
            }
            return payResponseDTO;
        }

        // RENDER - with qr_url
        if (PayFrontResponse.InteractiveModeEnum.QRCODE_URL.name().equals(interactiveMode)) {
            payResponseDTO.setResultType(ResultTypeEnum.RENDER.getType());
            payResponseDTO.setReturnParam(returnParam);
            // 下游指定交互模式，则先根据数据库步骤配置，进行结果映射，则取固定值
            if (Objects.nonNull(currentStepApiMapping)) {
                dealResultParamMapping(payFrontResponse, returnParam, currentStepApiMapping.getResultMapping());
            }
            if (StringUtils.isNotBlank(payFrontResponse.getQrCodeUrl())) {
                returnParam.setQrCodeUrl(payFrontResponse.getQrCodeUrl());
            }
            if (StringUtils.isNotBlank(returnParam.getQrCodeUrl()) || StringUtils.isBlank(payFrontResponse.getPaymentInfo())) {
                return payResponseDTO;
            }
            PayFrontResponse.PaymentInfo paymentInfo = JsonUtils.toBean(PayFrontResponse.PaymentInfo.class, payFrontResponse.getPaymentInfo());
            if (Objects.nonNull(paymentInfo) && StringUtils.isNotBlank(paymentInfo.getQrInfo())) {
                returnParam.setQrCodeUrl(paymentInfo.getQrInfo());
            }
            if (Objects.nonNull(paymentInfo) && StringUtils.isNotBlank(paymentInfo.getUrlInfo())) {
                returnParam.setQrCodeUrl(paymentInfo.getUrlInfo());
            }
            return payResponseDTO;
        }

        // interactiveMode 为空
        if (StringUtils.isBlank(interactiveMode)) {
            String requestUrl = payFrontResponse.getRequestUrl();
            // 如果requestUrl非空，按跳转类处理
            if (StringUtils.isNotBlank(requestUrl)) {
                payResponseDTO.setResultType(ResultTypeEnum.REDIRECT.getType());
                returnParam.setRedirectUrl(requestUrl);
                returnParam.setRedirectPageInfo(payFrontResponse.getRedirect2PaymentChannelPageInfo());
                // 解析渠道原始url
                String origRedirectUrl = this.parseOrigRedirectUrl(channelCode, payFrontResponse.getRedirect2PaymentChannelPageInfo());
                returnParam.setOrigRedirectUrl(StringUtils.isBlank(origRedirectUrl) ? requestUrl : origRedirectUrl);
                payResponseDTO.setReturnParam(returnParam);
            }
        }

        // 需配置映射关系
        if (Objects.nonNull(currentStepApiMapping)) {
            // resultType
            String resultType = currentStepApiMapping.getResultType();
            if (StringUtils.isNotBlank(resultType)) {
                payResponseDTO.setResultType(resultType);
            }
            // 结果映射
            dealResultParamMapping(payFrontResponse, returnParam, currentStepApiMapping.getResultMapping());
            payResponseDTO.setReturnParam(returnParam);
        }
        return payResponseDTO;
    }

    /**
     * 处理结果映射
     *
     * @param response
     * @param returnParam
     * @param resultMapping
     */
    protected void dealResultParamMapping(Object response, ReturnParamDO returnParam, String resultMapping) {
        Map<String, Object> extraInfoMap = returnParam.getExtraInfo();
        if (extraInfoMap == null) {
            extraInfoMap = new HashMap<>();
            returnParam.setExtraInfo(extraInfoMap);
        }
        Map<String, Object> finalExtraInfoMap = extraInfoMap;

        // 如果结果映射字段为空，则返回
        if (StringUtils.isBlank(resultMapping)) {
            return;
        }
        Map<String, String> resultMappingMap = JsonUtils.toBean(Map.class, resultMapping);
        // 定义数据解析本地缓存Map
        Map<String, Object> resolveMapCache = new HashMap<>();
        resultMappingMap.forEach((key, val) -> {
            // 如果key或value为空，则忽略
            if (StringUtils.isBlank(key) || StringUtils.isBlank(val)) {
                return;
            }
            Object elementVal = null;
            // 如果value中存在逗号，说明值为多个
            String[] vals = val.split(",");
            if (vals.length == 1) {
                elementVal = ExpressionUtils.execute(val, response, resolveMapCache);
            } else {
                List<String> elementList = new ArrayList<>();
                for (String tmpVal : vals) {
                    if (StringUtils.isBlank(tmpVal)) {
                        continue;
                    }
                    Object empElementVal = ExpressionUtils.execute(tmpVal, response, resolveMapCache);
                    if (empElementVal != null) {
                        elementList.add(String.valueOf(empElementVal));
                    }
                }
                elementVal = elementList;
            }
            if (elementVal != null) {
                int tmpIndex = key.indexOf('.');
                if (tmpIndex >= 0 && key.startsWith(CommonConstants.PREFIX_PAY_FRONT_RESPONSE_EXTRAINFO)) {
                    String tmpKey = key.substring(tmpIndex + 1);
                    finalExtraInfoMap.put(tmpKey, elementVal);
                } else {
                    try {
                        ReflectUtil.setFieldValue(returnParam, key, elementVal);
                    } catch (Exception e) {
                        // returnParam里面无属性，则忽略
                    }
                }
            }
        });
    }

    /**
     * 解析渠道原始跳转url
     *
     * @param channelCode
     * @param redirect2PaymentChannelPageInfo
     * @return
     */
    private String parseOrigRedirectUrl(String channelCode, String redirect2PaymentChannelPageInfo) {
        try {
            if (StringUtils.isBlank(redirect2PaymentChannelPageInfo)) {
                return null;
            }
            PayFrontResponse.Redirect2PaymentChannelPageInfo pageInfo = JSON.parseObject(redirect2PaymentChannelPageInfo, PayFrontResponse.Redirect2PaymentChannelPageInfo.class);
            if (pageInfo == null) {
                return null;
            }
            PayFrontResponse.Redirect2PaymentChannelInfo redirect2PaymentChannelInfo = pageInfo.getTemplatePageParamsJson();
            if (redirect2PaymentChannelInfo == null) {
                return null;
            }
            // 不在配置内的渠道不支持（可以配置支持所有渠道）
            if (CollectionUtils.isEmpty(useOriginalUrlChannelCodes) ||
                    !(useOriginalUrlChannelCodes.contains(ShareConstants.ALL) || useOriginalUrlChannelCodes.contains(channelCode))) {
                return null;
            }
            String requestUrl = redirect2PaymentChannelInfo.getRequestUrl();
            String methodType = redirect2PaymentChannelInfo.getMethodType();
            Map params = redirect2PaymentChannelInfo.getParams();
            // 请求url为空 或者 明确非GET请求，则返回
            if (StringUtils.isBlank(requestUrl) || !(StringUtils.isBlank(methodType) || RequestMethod.GET.name().equals(methodType))) {
                return null;
            }
            // 如果GET请求，有请求参数，则拼接到url后面
            if (MapUtil.isNotEmpty(params)) {
                requestUrl = HttpUtil.urlWithForm(requestUrl, params, CharsetUtil.CHARSET_UTF_8, false);
            }
            return requestUrl;
        } catch (Exception e) {
            log.error("Parse orig request url error.", e);
        }
        return null;
    }

}
