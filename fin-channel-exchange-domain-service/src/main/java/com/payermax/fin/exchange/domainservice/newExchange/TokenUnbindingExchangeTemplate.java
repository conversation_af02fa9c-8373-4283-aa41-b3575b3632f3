package com.payermax.fin.exchange.domainservice.newExchange;

import com.payermax.fin.exchange.common.enums.ApiModeEnum;
import com.payermax.fin.exchange.domain.trans.applyToken.TokenUnbindingRequestOrderDO;
import com.payermax.fin.exchange.domain.trans.applyToken.TokenUnbindingResponseOrderDO;
import com.payermax.fin.exchange.domainservice.bo.BizContextBO;
import com.payermax.fin.exchange.integration.rpc.constants.InternalUrlConstants;
import com.payermax.fin.exchange.integration.rpc.request.TokenUnbindingFrontRequest;
import com.payermax.fin.exchange.integration.rpc.response.TokenUnbindingFrontResponse;
import org.springframework.stereotype.Component;

/**
 * tokenUnbinding模版
 */
@Component
public class TokenUnbindingExchangeTemplate extends CommonExchangeTemplate<TokenUnbindingRequestOrderDO, TokenUnbindingResponseOrderDO, TokenUnbindingFrontRequest, TokenUnbindingFrontResponse> {

    @Override
    public TokenUnbindingFrontRequest initFrontRequest() {
        return new TokenUnbindingFrontRequest();
    }

    @Override
    public String getApiMode() {
        return ApiModeEnum.PAST_FRONT.name();
    }

    @Override
    public String getApiName() {
        return InternalUrlConstants.FRONT_URL_CHANNELTOKENDELETE;
    }

    @Override
    public TokenUnbindingFrontRequest exchangeRequest(BizContextBO<TokenUnbindingRequestOrderDO, TokenUnbindingResponseOrderDO> bizContext) {
        TokenUnbindingFrontRequest tokenUnbindingFrontRequest = super.exchangeRequest(bizContext);

        TokenUnbindingRequestOrderDO in = bizContext.getIn();
        tokenUnbindingFrontRequest.setNetworkTokenId(in.getTokenId());
        tokenUnbindingFrontRequest.setApi(InternalUrlConstants.CHANNEL_GATEWAY_API_CHANNELTOKENDELETE);
        return tokenUnbindingFrontRequest;
    }
}
