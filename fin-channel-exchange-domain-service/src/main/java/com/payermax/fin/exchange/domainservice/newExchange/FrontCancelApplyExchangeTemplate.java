package com.payermax.fin.exchange.domainservice.newExchange;

import com.payermax.fin.exchange.domain.trans.RequestOrderDO;
import com.payermax.fin.exchange.domain.trans.ResponseOrderDO;
import com.payermax.fin.exchange.domainservice.bo.BizContextBO;
import com.payermax.fin.exchange.integration.rpc.constants.InternalUrlConstants;
import com.payermax.fin.exchange.integration.rpc.request.RefundFrontRequest;
import org.springframework.stereotype.Component;

/**
 * 撤销功能front处理模版
 *
 * <AUTHOR>
 * @date 2024/3/18 14:10
 */
@Component
public class FrontCancelApplyExchangeTemplate extends FrontRefundApplyExchangeTemplate {

    @Override
    public String getApiName() {
        return InternalUrlConstants.FRONT_URL_CANCEL_PAY;
    }

    @Override
    public RefundFrontRequest exchangeRequest(BizContextBO<RequestOrderDO, ResponseOrderDO> bizContext) {
        RefundFrontRequest frontRequest = super.exchangeRequest(bizContext);
        frontRequest.setApi(InternalUrlConstants.CHANNEL_GATEWAY_API_CANCEL_PAY);
        return frontRequest;
    }
}
