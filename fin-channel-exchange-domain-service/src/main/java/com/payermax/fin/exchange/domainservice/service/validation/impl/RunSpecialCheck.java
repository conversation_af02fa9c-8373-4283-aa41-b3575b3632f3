package com.payermax.fin.exchange.domainservice.service.validation.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.fin.exchange.domainservice.util.ExStringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/10/15 14:10
 */
@Service
@Slf4j
public class RunSpecialCheck {

    private JSONObject supportCheckJson;

    @NacosValue(value = "${support.checkJsonStr}", autoRefreshed = true)
    public void setCheckJsonStr(String checkJsonStr) {
        try {
            if (StringUtils.isBlank(checkJsonStr)) {
                return;
            }
            supportCheckJson = JSONObject.parseObject(checkJsonStr);
        } catch (Exception e) {
            log.error("setCheckJsonStr error", e);
        }
    }

    /**
     * 必填校验
     *
     * @param fieldName
     * @param checkField
     * @return
     */
    public String checkIsEmpty(String fieldName, String checkField, JSONObject fieldValues, String specialCheckValue) {
        if (StringUtils.isEmpty(checkField)) {
            return fieldName + " is mandatory !";
        }
        return null;
    }

    /**
     * 当开头不为0时补0
     *
     * @param fieldName
     * @param checkField
     * @return
     */
    public String checkIsStartWith0(String fieldName, String checkField, JSONObject fieldValues, String specialCheckValue) {
        if (!checkField.startsWith("0")) {
            checkField = "0" + checkField;
            CommonCheck.setValueInFieldValue(fieldName, checkField, fieldValues);
        }
        return null;
    }

    /**
     * 当开头为0时去掉0
     *
     * @param fieldName
     * @param checkField
     * @param fieldValues
     * @return
     */
    public String removeIfStartWith0(String fieldName, String checkField, JSONObject fieldValues, String specialCheckValue) {
        if (checkField.startsWith("0")) {
            checkField = checkField.substring(1);
            CommonCheck.setValueInFieldValue(fieldName, checkField, fieldValues);
        }
        return null;
    }

    /**
     * 只能以03开头
     *
     * @param fieldName
     * @param checkField
     * @return
     */
    public String checkIsStartWith03(String fieldName, String checkField, JSONObject fieldValues, String specialCheckValue) {
        if (!checkField.startsWith("03")) {
            return fieldName + " must start with '03' !";
        }
        return null;
    }

    /**
     * 只能以0开头
     *
     * @param fieldName
     * @param checkField
     * @return
     */
    public String checkIsStartWithZero(String fieldName, String checkField, JSONObject fieldValues, String specialCheckValue) {
        if (!checkField.startsWith("0")) {
            return fieldName + " must start with '0' !";
        }
        return null;
    }

    /**
     * 当开头为0时去掉0
     *
     * @param fieldName
     * @param checkField
     * @param fieldValues
     * @return
     */
    public String removeIfStartWith60(String fieldName, String checkField, JSONObject fieldValues, String specialCheckValue) {
        if (checkField.startsWith("60")) {
            checkField = checkField.substring(2);
            CommonCheck.setValueInFieldValue(fieldName, checkField, fieldValues);
        }
        return null;
    }

    /**
     * 只能以X开头
     *
     * @param fieldName
     * @param checkField
     * @return
     */
    public String checkIsStartWithX(String fieldName, String checkField, JSONObject fieldValues, String specialCheckValue) {
        if (!checkField.startsWith(specialCheckValue)) {
            return fieldName + " must start with " + specialCheckValue + " !";
        }
        return null;
    }

    /**
     * 当开头不为X时加X
     *
     * @param fieldName
     * @param checkField
     * @return
     */
    public String addXBeforeField(String fieldName, String checkField, JSONObject fieldValues, String specialCheckValue) {
        String prefix = specialCheckValue.substring(0, 1);
        String body = specialCheckValue.substring(1);
        if (!checkField.startsWith(prefix)) {
            if (!checkField.startsWith(body)) {
                checkField = specialCheckValue + checkField;
            } else {
                checkField = prefix + checkField;
            }
            CommonCheck.setValueInFieldValue(fieldName, checkField, fieldValues);
        }
        return null;
    }

    /**
     * 当开头为X时去掉X
     *
     * @param fieldName         key
     * @param checkField        value
     * @param fieldValues       JSONObject
     * @param specialCheckValue X
     * @return
     */
    private String removeIfStartWithX(String fieldName, String checkField, JSONObject fieldValues, String specialCheckValue) {
        if (StringUtils.isNotBlank(specialCheckValue) && checkField.startsWith(specialCheckValue)){
            checkField = checkField.substring(specialCheckValue.length());
            CommonCheck.setValueInFieldValue(fieldName, checkField, fieldValues);
        }
        return null;
    }

    /**
     * 检查是否Parara账户
     *
     * @param fieldName
     * @param checkField
     * @param fieldValues
     * @param specialCheckValue
     * @return
     */
    public String checkPaparaAccount(String fieldName, String checkField, JSONObject fieldValues, String specialCheckValue) {
        if (StringUtils.startsWith(checkField, "PL")) {
            checkField = checkField.substring(2);
        }
        if (!checkField.matches("^\\d{10}$")) {
            return "You have entered an invalid papara number.";
        }
        return null;
    }

    /**
     * 替换特殊符号为空格
     *
     * @param fieldName
     * @param checkField
     * @return
     */
    public String replaceWithSpaces(String fieldName, String checkField, JSONObject fieldValues, String specialCheckValue) {
        if (StringUtils.isNotBlank(checkField)) {
            checkField = ExStringUtils.regularReplacement(checkField,
                    "[.*[`~!@#$%^&*()+=|{}':;',\\\\[\\\\].<>/?~！@#￥%……&*（）——+|{}_【】‘；：”“’。，、？\\\\\\\\]+.*-]", " ")
                    .trim().replaceAll("\\s+", " ");
            CommonCheck.setValueInFieldValue(fieldName, checkField, fieldValues);
        }
        return null;
    }

    public String replaceNewlineWithSpace(String fieldName, String checkField, JSONObject fieldValues, String specialCheckValue) {
        if (StringUtils.isNotBlank(checkField)) {
            checkField = ExStringUtils.regularReplacement(checkField, "\n+|\\s+", " ").trim();
            CommonCheck.setValueInFieldValue(fieldName, checkField, fieldValues);
        }
        return null;
    }

    /**
     * 将空值处理成指定值
     *
     * @param fieldName
     * @param checkField
     * @param fieldValues
     * @param specialCheckValue
     * @return
     */
    public String handleEmptyValue(String fieldName, String checkField, JSONObject fieldValues, String specialCheckValue) {
        if (StringUtils.isBlank(checkField)) {
            JSONPath.set(fieldValues, fieldName, specialCheckValue);
        }
        return null;
    }

    /**
     * 替换指定符号为空格（t_validate_rule 65）
     *
     * @param fieldName
     * @param checkField
     * @param fieldValues
     * @param specialCheckValue
     * @return
     */
    public String replaceSymbolWithSpaces(String fieldName, String checkField, JSONObject fieldValues, String specialCheckValue) {
        if (StringUtils.isNotBlank(checkField)) {
            checkField = ExStringUtils.regularReplacement(checkField, specialCheckValue, " ").trim();
            CommonCheck.setValueInFieldValue(fieldName, checkField, fieldValues);
        }
        return null;
    }

    /**
     * 检查字段是否在指定列表内
     *
     * @param fieldName
     * @param checkField
     * @param fieldValues
     * @param specialCheckValue
     * @return
     */
    public String checkIsInTheList(String fieldName, String checkField, JSONObject fieldValues, String specialCheckValue) {
        if (StringUtils.isBlank(checkField)) {
            return fieldName + " is mandatory";
        }
        JSONArray arrays = supportCheckJson.getJSONArray(specialCheckValue);
        if (Objects.nonNull(arrays) && !arrays.contains(checkField)) {
            return checkField + " is invalid";
        }
        return null;
    }

    /**
     * 替换指定值
     * @return
     */
    public String replaceWithParam(String fieldName, String checkField, JSONObject fieldValues, String specialCheckValue) {
        if (StringUtils.isBlank(checkField)) return null;

        JSONArray array = JSONArray.parseArray(specialCheckValue);
        String searchString = array.getString(0);
        String replacement = array.getString(1);
        if (StringUtils.isNotBlank(fieldName)) {
            String replace = StringUtils.replace(checkField, searchString, replacement);
            CommonCheck.setValueInFieldValue(fieldName, replace, fieldValues);
        }
        return null;
    }

    /**
     * 使用正则表达式替换指定值
     * @return
     */
    public String replaceWithRegex(String fieldName, String checkField, JSONObject fieldValues, String specialCheckValue) {
        if (StringUtils.isBlank(checkField)) return null;

        JSONArray array = JSONArray.parseArray(specialCheckValue);
        String regex = array.getString(0);
        String replacement = array.getString(1);
        if (StringUtils.isNotBlank(fieldName)) {
            String replace = checkField.replaceAll(regex, replacement);
            CommonCheck.setValueInFieldValue(fieldName, replace, fieldValues);
        }
        return null;
    }
}
