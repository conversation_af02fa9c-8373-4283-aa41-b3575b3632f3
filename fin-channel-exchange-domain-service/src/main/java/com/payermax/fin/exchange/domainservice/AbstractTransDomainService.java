package com.payermax.fin.exchange.domainservice;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.components.tracer.TraceContextUtil;
import com.payermax.fin.exchange.common.constants.CommonConstants;
import com.payermax.fin.exchange.common.enums.*;
import com.payermax.fin.exchange.common.util.ValidationUtils;
import com.payermax.fin.exchange.dal.entity.*;
import com.payermax.fin.exchange.dal.entity.query.ChannelCommitOrderEntityQuery;
import com.payermax.fin.exchange.domain.trans.RequestOrderDO;
import com.payermax.fin.exchange.domain.trans.ResponseOrderDO;
import com.payermax.fin.exchange.domainservice.bo.*;
import com.payermax.fin.exchange.domainservice.exchange.ExchangeTemplate;
import com.payermax.fin.exchange.domainservice.expression.ExpressionConstant;
import com.payermax.fin.exchange.domainservice.expression.ExpressionUtils;
import com.payermax.fin.exchange.domainservice.expression.ExpressionVarBO;
import com.payermax.fin.exchange.domainservice.repository.*;
import com.payermax.fin.exchange.domainservice.rule.channelgatewaygary.ChannelGatewayGrayExecutor;
import com.payermax.fin.exchange.domainservice.service.orderNo.IOrderNoService;
import com.payermax.fin.exchange.domainservice.service.route.IRoutingService;
import com.payermax.fin.exchange.domainservice.state.StateMachineExecutor;
import com.payermax.fin.exchange.domainservice.state.StateRequest;
import com.payermax.fin.exchange.domainservice.util.DDCUtils;
import com.payermax.fin.exchange.domainservice.util.ObjectOperateUtils;
import com.payermax.fin.exchange.domainservice.util.OrderCompensateUtils;
import com.payermax.fin.exchange.integration.rpc.proxy.RiskEngineClientProxy;
import com.payermax.fin.exchange.integration.rpc.request.InternalBaseRequest;
import com.payermax.fin.exchange.integration.rpc.response.ResultDTO;
import com.payermax.fin.exchange.share.constants.ShareConstants;
import com.payermax.fin.exchange.share.domain.ChannelInstanceDO;
import com.payermax.fin.exchange.share.domain.OrderInfoDO;
import com.payermax.fin.exchange.share.domain.PaymentMethodDO;
import com.payermax.fin.exchange.share.enums.FactorKeyEnum;
import com.payermax.fin.exchange.share.enums.PaymentTypeEnum;
import com.payermax.fin.exchange.share.utils.ExtendPropertyUtils;
import com.payermax.infra.ionia.log.digest.core.dto.DigestLogDTO;
import com.payermax.risk.engine.api.enums.EventResultEnum;
import com.ushareit.fintech.common.model.dto.Money;
import com.payermax.common.lang.util.JsonUtils;
import com.ushareit.fintech.components.enums.common.TraceCorrelationEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;

import java.math.BigDecimal;
import java.util.*;

import static com.payermax.fin.exchange.common.enums.ErrorCodeEnum.ORDER_TXN_IS_EXIST;

/**
 * 交易领域服务抽象实现类
 *
 * <AUTHOR>
 * @date 2021/12/12 22:01
 */
public abstract class AbstractTransDomainService<IN extends RequestOrderDO, OUT extends ResponseOrderDO> extends AbstractDomainService<IN, OUT> {

    @Autowired
    protected IChannelRequestOrderService channelRequestOrderService;

    @Autowired
    protected IChannelCommitOrderService channelCommitOrderService;

    @Autowired
    protected IChannelResultOrderService channelResultOrderService;

    @Autowired
    protected IChannelStepService channelStepService;

    @Autowired
    protected IChannelMethodService channelMethodService;

    @Autowired
    protected IChannelInfoService channelInfoService;

    @Autowired
    protected IChannelConfigMappingService channelConfigMappingService;

    @Autowired
    protected IOrderNoService orderNoService;

    @Autowired
    protected IChannelOrderCompensateService channelOrderCompensateService;

    @Autowired
    protected IChannelStepApiMappingService channelStepApiMappingService;

    @Autowired
    @Qualifier("defaultRoutingService")
    protected IRoutingService routingService;

    @Autowired
    protected IChannelRetryConfigService channelRetryConfigService;

    @Autowired
    protected StateMachineExecutor stateMachineExecutor;

    @Autowired
    protected RiskEngineClientProxy riskEngineClientProxy;

    @Value("${project.env}")
    private String env;

    protected Map<String, Integer> maxRetryCountMap;

    @NacosValue(value = "#{${channel.retry.max-count.map}}", autoRefreshed = true)
    public void setMaxRetryCountMap(Map<String, Integer> maxRetryCountMap) {
        this.maxRetryCountMap = maxRetryCountMap;
    }

    @Override
    protected void paramCheckSelf(BizContextBO<IN, OUT> bizContext) {
        super.paramCheckSelf(bizContext);

        RequestOrderDO requestOrderDO = bizContext.getIn();
        ValidationUtils.notNullCheck(requestOrderDO.getRequestType(), "[requestType] is mandatory");
        RequestTypeEnum requestTypeEnum = RequestTypeEnum.getRequestTypeByCode(requestOrderDO.getRequestType());
        ValidationUtils.notNullCheck(requestTypeEnum, "[requestType] is invalid");

        PaymentMethodDO paymentMethod = requestOrderDO.getPaymentMethod();
        if (paymentMethod != null && paymentMethod.getAmount() != null) {
            ValidationUtils.notBlankCheck(paymentMethod.getAmount().getCurrency(), "[amount.currency] is mandatory");
            if (!(BigDecimal.ZERO.compareTo(paymentMethod.getAmount().getValue()) == 0 && RequestTypeEnum.AUTH.getCode().equals(requestOrderDO.getRequestType()))) {
                ValidationUtils.minNumberCheck(paymentMethod.getAmount().getValue(), 0, false, "[amount.value] must be greater than zero");
            }
        }
    }

    @Override
    protected void beforeBusiness(BizContextBO<IN, OUT> bizContext) {
        RequestOrderDO requestOrderDO = bizContext.getIn();
        // 设置上下文中间数据对象
        TransactionBO transactionBO = getTransactionBO(bizContext);
        bizContext.setBaseBo(transactionBO);
        // 获取请求类型
        RequestTypeEnum requestTypeEnum = RequestTypeEnum.getRequestTypeByCode(requestOrderDO.getRequestType());
        transactionBO.setRequestType(requestTypeEnum);
        // 路由前数据校验&填充（主要针对是否需要进行路由）
        checkAndFillBeforeRouting(bizContext);
        // 创建请求单
        createChannelRequestOrder(bizContext);
        // 渠道筛选&路由
        channelRouting(bizContext);
    }

    @Override
    protected void executeBusiness(BizContextBO<IN, OUT> bizContext) throws Exception {
        // 初始化订单补偿对象，用于订单补偿
        initOrderCompensate(bizContext);
        // 调用下游
        ResultDTO<OUT> resultDTO = loopExchange(bizContext);
        // 更新请求单
        updateChannelRequestOrder(bizContext);
        // 结果判断
        if (resultDTO != null) {
            // 设置结果
            bizContext.setOut(resultDTO.getData());
            // 填充订单完成时间
            fillOrderCompleteTime(bizContext);
            // 填充自定义out
            fillCustomOut(bizContext);
            // 是否成功
            if (!resultDTO.isRespSuccess()) {
                throw new BusinessException(resultDTO.getBizCode(), resultDTO.getMessage());
            }
        }
    }

    /**
     * 获取上下文中间数据对象
     *
     * @param bizContext
     * @return
     */
    protected TransactionBO getTransactionBO(BizContextBO<IN, OUT> bizContext) {
        Object baseBo = bizContext.getBaseBo();
        if (baseBo == null) {
            return new TransactionBO();
        }
        if (!(baseBo instanceof TransactionBO)) {
            throw new BusinessException(ErrorCodeEnum.INNER_ERROR.getCode(), "baseBo no instanceof TransactionBO");
        }
        return (TransactionBO) baseBo;
    }

    /**
     * 路由前数据校验&填充
     *
     * @param bizContext 交易信息上下文
     * @apiNote 主要针对是否需要进行路由
     */
    protected void checkAndFillBeforeRouting(BizContextBO<IN, OUT> bizContext) {
        TransactionBO transactionBO = getTransactionBO(bizContext);
        RequestOrderDO request = bizContext.getIn();
        RequestOrderDO.InnerUseInfo innerUseInfo = request.getInnerUseInfo();
        if (innerUseInfo == null) {
            return;
        }
        transactionBO.setChannelCode(innerUseInfo.getChannelCode());
        transactionBO.setChannelMerchantCode(innerUseInfo.getChannelMerchantCode());
        transactionBO.setChannelMethodCode(innerUseInfo.getChannelMethodCode());
        transactionBO.setChannelPayRequestNo(innerUseInfo.getChannelPayRequestNo());
    }

    /**
     * 渠道路由（暂时输出最终选定的渠道）
     *
     * @param bizContext 交易信息上下文
     */
    protected void channelRouting(BizContextBO<IN, OUT> bizContext) {
        TransactionBO transactionBO = getTransactionBO(bizContext);
        // 渠道已存在，则无需路由
        if (StringUtils.isNotBlank(transactionBO.getChannelCode()) && StringUtils.isNotBlank(transactionBO.getChannelMethodCode())) {
            return;
        }
        IRoutingService routingService = getRoutingService();
        if (routingService == null) {
            return;
        }
        List<ChannelInstanceDO> paymentMethodBOList = routingService.routing(bizContext.getIn());
        if (CollectionUtils.isEmpty(paymentMethodBOList)) {
            throw new BusinessException(ErrorCodeEnum.NO_CHANNEL_SUPPORT.getCode(), "无可用支付方式");
        }

        transactionBO.setPaymentMethodBOList(paymentMethodBOList);
        // 设置将要走的资金渠道信息
        ChannelInstanceDO finalPaymentMethod = paymentMethodBOList.get(0);//NO_CHECK
        transactionBO.setChannelInstance(finalPaymentMethod);
        transactionBO.setChannelCode(finalPaymentMethod.getChannelCode());
        transactionBO.setChannelMerchantCode(finalPaymentMethod.getChannelMerchantCode());
        transactionBO.setChannelMethodCode(finalPaymentMethod.getChannelMethodCode());

        // 路由结果记录摘要日志
        try {
            Optional.ofNullable(transactionBO.getChannelCode())
                .ifPresent(c -> DigestLogDTO.addBusinessInfo(CommonConstants.FINAL_CHANNEL_CODE, c));
            Optional.ofNullable(transactionBO.getChannelMethodCode())
                .ifPresent(c -> DigestLogDTO.addBusinessInfo(CommonConstants.FINAL_CHANNEL_METHOD_CODE, c));
            Optional.ofNullable(transactionBO.getChannelMerchantCode())
                .ifPresent(c -> DigestLogDTO.addBusinessInfo(CommonConstants.FINAL_CHANNEL_MERCHANT_CODE, c));
        } catch (Exception e) {
            log.error("AbstractTransDomainService.channelRouting记录摘要日志异常", e);
        }
    }

    /**
     * 路由后校验&填充（主要针对路由出来的渠道进行二次校验）
     *
     * @param bizContext 交易信息上下文
     */
    protected void checkAndFillAfterRouting(BizContextBO<IN, OUT> bizContext) {
        TransactionBO transactionBO = getTransactionBO(bizContext);
        String channelMethodCode = transactionBO.getChannelMethodCode();

        transactionBO.setApiModeEnum(ApiModeEnum.PAST_FRONT);
        // 查询渠道能力配置信息
        ChannelMethodEntity channelConfig = this.getChannelMethodByMethodCode(channelMethodCode);
        // 支付方式已关闭，抛出异常
        if (channelConfig == null) {
            throw new BusinessException(ErrorCodeEnum.CHANNEL_IS_CLOSED.getCode(), ErrorCodeEnum.CHANNEL_IS_CLOSED.getMsg());
        }
        if (StringUtils.isNotBlank(channelConfig.getConfigJson())) {
            PaymentMethodConfigJsonBO paymentMethodConfigJson = JsonUtils.toBean(PaymentMethodConfigJsonBO.class, channelConfig.getConfigJson());
            transactionBO.setPaymentMethodConfigJson(paymentMethodConfigJson);
            if (StringUtils.isNotBlank(paymentMethodConfigJson.getApiModeEnum())) {
                transactionBO.setApiModeEnum(ApiModeEnum.valueOf(paymentMethodConfigJson.getApiModeEnum()));
            }
        }
        transactionBO.setChannelConfigEntity(channelConfig);

        // 查询渠道信息
        ChannelInfoEntity channelInfo = this.getChannelInfoByChannelCode(transactionBO.getChannelCode());
        // 渠道已关闭，抛出异常
        if (channelInfo == null) {
            throw new BusinessException(ErrorCodeEnum.CHANNEL_IS_CLOSED.getCode(), ErrorCodeEnum.CHANNEL_IS_CLOSED.getMsg());
        }
        if (StringUtils.isNotBlank(channelInfo.getConfigJson())) {
            ChannelConfigJsonBO channelConfigJson = JsonUtils.toBean(ChannelConfigJsonBO.class, channelInfo.getConfigJson());
            transactionBO.setChannelConfigJson(channelConfigJson);
        }
        transactionBO.setChannelInfoEntity(channelInfo);

        // 如果是调用front模式，则查询老系统映射数据
        if (ApiModeEnum.PAST_FRONT == transactionBO.getApiModeEnum()) {
            ChannelConfigMappingEntity channelConfigMapping = channelConfigMappingService.getByConfigCode(channelMethodCode);
            transactionBO.setChannelConfigMappingEntity(channelConfigMapping);
        }
    }

    /**
     * 获取支付方式信息
     * @param channelMethodCode
     * @return
     */
    protected ChannelMethodEntity getChannelMethodByMethodCode(String channelMethodCode) {
        return channelMethodService.getChannelMethodByMethodCode(channelMethodCode);
    }

    /**
     * 获取渠道信息
     * @param channelCode
     * @return
     */
    protected ChannelInfoEntity getChannelInfoByChannelCode(String channelCode) {
        return channelInfoService.getByChannelCode(channelCode);
    }

    /**
     * 调用下游，含换号重试
     *
     * @param bizContext
     * @return
     */
    protected ResultDTO<OUT> loopExchange(BizContextBO<IN, OUT> bizContext) {
        ResultDTO<OUT> resultDTO;
        try {
            resultDTO = exchange(bizContext);
        } catch (Exception e) {
            // TODO 判断错误错误信息，是否重抛，暂无业务，留下口子
            throw e;
        }
        // 换号重试
        resultDTO = retryExchange(bizContext, resultDTO);

        return resultDTO;
    }

    /**
     * 调用下游
     *
     * @param bizContext 交易信息上下文
     * @return ResultDTO<OUT>
     */
    protected ResultDTO<OUT> exchange(BizContextBO<IN, OUT> bizContext) {
        // 路由后校验&填充
        checkAndFillAfterRouting(bizContext);

        TransactionBO transactionBO = getTransactionBO(bizContext);
        ApiModeEnum apiModeEnum = transactionBO.getApiModeEnum();
        // 获取执行器
        ExchangeTemplate<IN, OUT> exchangeTemplate = getExchangeTemplate(apiModeEnum);
        ResultDTO<OUT> resultDTO = exchangeTemplate.exchange(bizContext);
        return resultDTO;
    }

    /**
     * 调用下游-多步
     *
     * @param bizContext
     * @return
     */
    protected ResultDTO<OUT> exchangeMultiStep(BizContextBO<IN, OUT> bizContext) {
        // 路由后校验&填充
        checkAndFillAfterRouting(bizContext);
        // 构建渠道支付步骤
        buildChannelStep(bizContext);

        TransactionBO transactionBO = getTransactionBO(bizContext);
        IN request = bizContext.getIn();
        ApiModeEnum apiModeEnum = transactionBO.getApiModeEnum();
        String channelCode = transactionBO.getChannelCode();
        String channelMethodCode = transactionBO.getChannelMethodCode();
        String channelMerchantCode = transactionBO.getChannelMerchantCode();
        RequestTypeEnum requestTypeEnum = transactionBO.getRequestType();

        ResultDTO<OUT> resultDTO = null;
        // 查询支付步骤（按照步骤有序）
        List<ChannelStepApiMappingEntity> stepApiMappingList = channelStepApiMappingService.getStepApiMappingByCode(channelCode, channelMethodCode, requestTypeEnum.getCode());
        // 步骤配置不存在，则执行单步调用（单步可以不配置）
        if (CollectionUtils.isEmpty(stepApiMappingList)) {
            // 获取执行器
            ExchangeTemplate<IN, OUT> exchangeTemplate = getExchangeTemplate(apiModeEnum);
            resultDTO = exchangeTemplate.exchange(bizContext);
            return resultDTO;
        }

        // 设置前步骤响应：使用当前请求参数作为前一步响应，兼容商户自主3ds时，通过请求参数上送3ds信息的情况
        transactionBO.setPreResponse(request);

        ExpressionVarBO expVarBo = new ExpressionVarBO();
        transactionBO.setExpressionVarBO(expVarBo);
        expVarBo.setRequest(request);
        // 选择此交易将使用的DDC机构，并设置到请求中
        Pair<String, String> ddcOrgSessionPair = DDCUtils.getDdcOrgAndSession(channelCode, channelMethodCode, channelMerchantCode, request);
        expVarBo.setDdcOrg(ddcOrgSessionPair.getKey());
        request.setDdcOrgSessionPair(ddcOrgSessionPair);
        RequestOrderDO.InnerUseInfo innerUseInfo = request.getInnerUseInfo();
        // 检查是否重发交易
        boolean retryTrans = false;
        if (Objects.nonNull(innerUseInfo) && StringUtils.isNotBlank(innerUseInfo.getChannelPayCommitNo())) {
            retryTrans = true;
        }
        // 判断是否已经有前步骤（中间步骤callback的情况），如果有，则执行前步骤的后续步骤
        if (innerUseInfo != null && StringUtils.isNotBlank(innerUseInfo.getPreApiStepCode())) {
            List<ChannelStepApiMappingEntity> tmpStepApiMappingList = new ArrayList<>();
            boolean isAdd = false;
            for (ChannelStepApiMappingEntity stepApiMapping : stepApiMappingList) {
                if (innerUseInfo.getPreApiStepCode().equals(stepApiMapping.getStepCode())) {
                    // 重发步骤需要重新添加
                    if (retryTrans) {
                        tmpStepApiMappingList.add(stepApiMapping);
                    }
                    isAdd = true;
                    continue;
                }
                if (isAdd) {
                    tmpStepApiMappingList.add(stepApiMapping);
                }
            }
            // 如果上述没有后续步骤，并且当前步骤为风控步骤，则说明此渠道没有显示配置风控步骤，则直接使用原步骤
            if (CollectionUtils.isEmpty(tmpStepApiMappingList) && RequestTypeEnum.PAYOUT_RISK.getCode().equals(innerUseInfo.getPreApiStepCode())) {
                tmpStepApiMappingList = stepApiMappingList;
            }
            stepApiMappingList = tmpStepApiMappingList;
            // 设置前步骤响应
            transactionBO.setPreResponse(innerUseInfo.getPreResponse());
            Object commitOrder = innerUseInfo.getCommitOrder();
            if(Objects.nonNull(commitOrder) && commitOrder instanceof ChannelCommitOrderEntity){
                transactionBO.setPreStepChannelCode(((ChannelCommitOrderEntity)commitOrder).getChannelCode());
            }
            // 设置表达式参数
            expVarBo.setPreResponse(innerUseInfo.getPreResponse());
            expVarBo.setPreStatus(innerUseInfo.getPreStatus());
            expVarBo.setRunPreStep(true);
            expVarBo.setPreCommitOrder(innerUseInfo.getCommitOrder());
        }

        //当前步骤是否需要检查
        boolean isCheck = true;
        // 循环调用对应步骤
        for (ChannelStepApiMappingEntity stepApiMapping : stepApiMappingList) {
            //判断步骤是否是仅需要执行一次的
            if(isCheck && ShareConstants.NUMBER_ONE.equals(stepApiMapping.getExecuteStrategy()) && innerUseInfo.isCheckMultiStep()){
                //根据请求类型查询提交单，如果当前步骤已经存在并且成功，则跳过当前步骤
                List<ChannelCommitOrderEntity> commitOrderList = getCommitOrderByConditions(request.getChannelPayRequestNo(), request.getPaymentType().getValue(), channelCode, stepApiMapping.getStepCode());
                if(CollectionUtils.isNotEmpty(commitOrderList)){
                    ChannelCommitOrderEntity commitOrder = commitOrderList.get(0);//NO_CHECK
                    if(OrderStatusEnum.SUCCESS.getStatus().equals(commitOrder.getStatus())){
                        expVarBo.setPreStatus(commitOrder.getStatus());
                        expVarBo.setPreCommitOrder(commitOrder);
                        continue;
                    }
                }
            }
            //如果有步骤执行过，则后面步骤都不需要校验，必须执行后续所有步骤
            isCheck = false;
            // 设置当前步骤
            transactionBO.setCurrentStepApiMapping(stepApiMapping);
            // 执行步骤触发条件
            String resultCode = ExpressionConstant.CODE_SUCCESS;
            if (!retryTrans) {
                resultCode = ExpressionUtils.exeToString(stepApiMapping.getTriggerCondition(), expVarBo);
                // 处重发步骤外，其他步骤正常执行
                if (Objects.nonNull(innerUseInfo)) {
                    innerUseInfo.setChannelPayCommitNo(null);
                }
            }
            retryTrans = false;
            // 等待
            if (ExpressionConstant.CODE_WAIT.equals(resultCode)) {
                transactionBO.setRequestOrderStatus(OrderStatusEnum.PENDING.getStatus());
                break;
            }
            // 此步骤放弃
            if (ExpressionConstant.CODE_ABANDON.equals(resultCode)) {
                continue;
            }
            ChannelCommitOrderEntity channelCommitOrder = transactionBO.getChannelCommitOrder();
            // 把结果置空：防止后面步骤执行不成功，返回前面步骤的结果
            bizContext.setOut(null);
            // 重置渠道提交单
            transactionBO.setChannelCommitOrder(null);
            // 失败
            if (ExpressionConstant.CODE_FAIL.equals(resultCode)) {
                // 不是非必需步骤，则跳过
                if (ShareConstants.NUMBER_ONE.equals(stepApiMapping.getIsOptional())) {
                    expVarBo.setPreStatus(OrderStatusEnum.SUCCESS.getStatus());
                    expVarBo.setRunPreStep(false);
                    continue;
                } else { // 必需步骤，则失败
                    String errorCode = ErrorCodeEnum.TRIGGER_CONDITION_FAILED.getCode();
                    String errorMsg = ErrorCodeEnum.TRIGGER_CONDITION_FAILED.getMsg();
                    // 如果有提交单，则从提交单获取错误信息
                    if (Objects.nonNull(channelCommitOrder)) {
                        errorCode = channelCommitOrder.getMappingCode();
                        errorMsg = channelCommitOrder.getMappingMsg();
                    }
                    throw new BusinessException(errorCode, errorMsg);
                }
            }
            expVarBo.setRunPreStep(true);

            // 查看当前步骤映射是否有对接模式
            Integer apiMode = stepApiMapping.getApiMode();
            ApiModeEnum curStepApiMode = ApiModeEnum.getApiModeEnum(apiMode);

            // 兼容入款老逻辑
            if (Objects.isNull(curStepApiMode) && MainRequestTypeEnum.RISK.getValue().equals(stepApiMapping.getStepType())) {
                curStepApiMode = ApiModeEnum.RISK_QUERY;
            }

            ApiModeEnum curApiModeEnum = Objects.nonNull(curStepApiMode) ? curStepApiMode : apiModeEnum;
            transactionBO.setApiModeEnum(curApiModeEnum);

            // 获取执行器
            ExchangeTemplate<IN, OUT> exchangeTemplate = getExchangeTemplate(curApiModeEnum);
            resultDTO = exchangeTemplate.exchange(bizContext);
            // 设置响应
            expVarBo.setPreResponse(transactionBO.getPreResponse());
            expVarBo.setPreStatus(transactionBO.getChannelCommitOrder().getStatus());
            expVarBo.setPreCommitOrder(transactionBO.getChannelCommitOrder());
        }
        return resultDTO;
    }

    /**
     * 换号重发*
     * *
     *
     * @param bizContext
     * @param sourceResultDTO
     * @return
     */
    protected ResultDTO<OUT> retryExchange(BizContextBO<IN, OUT> bizContext, ResultDTO<OUT> sourceResultDTO) {
        RequestOrderDO request = bizContext.getIn();
        TransactionBO transactionBO = getTransactionBO(bizContext);
        ChannelRequestOrderEntity channelRequestOrder = transactionBO.getChannelRequestOrder();
        ChannelCommitOrderEntity channelCommitOrder = transactionBO.getChannelCommitOrder();
        String requestOrderNo = channelRequestOrder.getBizOrderNo();

        try {
            // 1. 判断是否允许重试
            boolean isAllow = this.checkAndFillRetry(bizContext);
            if (!isAllow) {
                return sourceResultDTO;
            }
            log.info("Check retry exchange result is true. requestOrderNo = {}", requestOrderNo);

            // 2. 重新进行渠道筛选&路由
            this.channelRouting(bizContext);
        } catch (Exception e) {
            // 出现异常，则忽略重试，返回原结果
            // 正常业务异常场景：重新路由无可用渠道
            log.warn("Retry exchange check error. requestOrderNo = {}", requestOrderNo, e);
            return sourceResultDTO;
        }

        // 调用新渠道
        ResultDTO<OUT> newResultDTO = null;
        try {
            // 重试次数+1
            String addField2 = channelRequestOrder.getAddField2();
            int retryCount = Integer.parseInt(StringUtils.defaultIfBlank(addField2, "0"));
            channelRequestOrder.setAddField2(String.valueOf(retryCount + 1));

            // 设置风控是否出3DS标识：重试情况下不能走3DS流程，因为第一次3DS情况下，支付可能是异步的，此时和用户无法交互了；
            // 而重试情况下，再走3DS流程，可能出challenge，需要用户参与
            request.getInnerUseInfo().setRiskResult(EventResultEnum.PASS.getCode());
            // 重置步骤
            request.getInnerUseInfo().setPreApiStepCode(null);

            // 3. 执行调用
            newResultDTO = loopExchange(bizContext);
        } catch (Exception e) {
            log.error("Retry exchange execute error.", e);
            // 正常业务异常场景：渠道限流异常
            // 判断上下文中的新渠道提交单和原渠道提交单是否一致
            // 如果新渠道提交单为空 或 新老一致，则说明没产生新的提交单，则使用原结果
            ChannelCommitOrderEntity newChannelCommitOrder = transactionBO.getChannelCommitOrder();
            if (newChannelCommitOrder == null || newChannelCommitOrder == channelCommitOrder) {
                transactionBO.setChannelCommitOrder(channelCommitOrder);
                return sourceResultDTO;
            }
        }

        return newResultDTO;
    }

    /**
     * 校验是否允许重试 && 填充重试数据*
     * *
     *
     * @param bizContext
     * @return
     */
    protected boolean checkAndFillRetry(BizContextBO<IN, OUT> bizContext) {
        RequestOrderDO request = bizContext.getIn();
        TransactionBO transactionBO = getTransactionBO(bizContext);
        ChannelRequestOrderEntity channelRequestOrder = transactionBO.getChannelRequestOrder();
        ChannelCommitOrderEntity channelCommitOrder = transactionBO.getChannelCommitOrder();

        // 如果需要忽略重试，则不允许重试
        if (transactionBO.isIgnoreRetry()) {
            return false;
        }
        // 退款和出款不进行同步重试
        if (StringUtils.equals(channelRequestOrder.getPaymentType(), PaymentTypeEnum.REFUND.getValue()) 
        || StringUtils.equals(channelRequestOrder.getPaymentType(), PaymentTypeEnum.PAYOUT.getValue())) {
            return false;
        }
        // 1. 判断重试次数：重试次数大于最大重试次数，则不允许重试
        String paymentType = channelRequestOrder.getPaymentType();
        String addField2 = channelRequestOrder.getAddField2();
        int retryCount = Integer.parseInt(StringUtils.defaultIfBlank(addField2, "0"));
        int maxRetryCount = maxRetryCountMap.getOrDefault(paymentType, 0);
        if (retryCount >= maxRetryCount) {
            return false;
        }

        // 2. 判断提交单：提交单不为空 && 是支付类订单 && 提交单状态为失败
        boolean isAllow = channelCommitOrder != null
                && MainRequestTypeEnum.TRANSACTION.getValue().equals(channelCommitOrder.getMainRequestType())
                && OrderStatusEnum.FAILED.getStatus().equals(channelCommitOrder.getStatus());
        if (!isAllow) {
            return false;
        }

        String channelCode = channelCommitOrder.getChannelCode();
        String channelMethodCode = channelCommitOrder.getChannelMethodCode();
        String responseCode = channelCommitOrder.getResponseCode();
        String responseMsg = channelCommitOrder.getResponseMsg();
        // 3. 判断重试配置：查询渠道重试配置
        ChannelRetryConfigEntity retryConfig = channelRetryConfigService.get(paymentType, channelCode,channelMethodCode,responseCode,responseMsg,channelRequestOrder.getRequestBody());
        // 无重试配置
        if (retryConfig == null) {
            return false;
        }

        // 以上条件校验通过，则允许重试，下面设置渠道路由条件：过滤掉当前渠道 && 设置重试渠道
        // 设置当前渠道到需排除列表中
        if (request.getInnerUseInfo() == null) {
            request.setInnerUseInfo(new RequestOrderDO.InnerUseInfo());
        }
        List<String> excludeChannelCodes = request.getInnerUseInfo().getExcludeChannelCodes();
        if (excludeChannelCodes == null) {
            excludeChannelCodes = new ArrayList<>();
            request.getInnerUseInfo().setExcludeChannelCodes(excludeChannelCodes);
        }
        excludeChannelCodes.add(channelCode);
        // 设置重试渠道：可为空，为空时，则按照原路由顺序选择渠道
        request.getInnerUseInfo().setChannelCode(retryConfig.getRetryChannelCode());
        request.getInnerUseInfo().setChannelMethodCode(null);
        // 防止失败的提交单被重发
        request.getInnerUseInfo().setChannelPayCommitNo(null);
        request.getInnerUseInfo().setIsIgnoreTokenPay(ShareConstants.NUMBER_ONE.equals(retryConfig.getIgnoreTokenPay()) ? ShareConstants.YES_FLAG : ShareConstants.NO_FLAG);
        // 重置原渠道编码和渠道支付方式编码
        transactionBO.setChannelCode(null);
        transactionBO.setChannelMerchantCode(null);
        transactionBO.setChannelMethodCode(null);
        return true;
    }

    /**
     * 构建渠道请求单对象
     *
     * @param bizContext
     * @return
     */
    protected ChannelRequestOrderEntity buildChannelRequestOrder(BizContextBO<IN, OUT> bizContext) {
        RequestOrderDO request = bizContext.getIn();
        TransactionBO transactionBO = getTransactionBO(bizContext);

        ChannelRequestOrderEntity channelRequestOrder = new ChannelRequestOrderEntity();
        channelRequestOrder.setBizSource(request.getBizSource());
        channelRequestOrder.setSourceBizOrderNo(request.getSourceBizOrderNo());
        channelRequestOrder.setBizOrderNo(request.getBizOrderNo());
        channelRequestOrder.setMainRequestType(MainRequestTypeEnum.TRANSACTION.getValue());
        channelRequestOrder.setRequestType(request.getRequestType());
        channelRequestOrder.setStatus(OrderStatusEnum.INITIATE.getStatus());
        channelRequestOrder.setRequestBody(JsonUtils.toJson(request));
        // 支付逻辑：支付单号作为请求单号
        if (StringUtils.isNotBlank(request.getChannelPayRequestNo())) {
            channelRequestOrder.setChannelPayRequestNo(request.getChannelPayRequestNo());
        } else {
            channelRequestOrder.setChannelPayRequestNo(request.getBizOrderNo());
        }
        if (StringUtils.isBlank(request.getBizOrderNo())) {
            channelRequestOrder.setBizOrderNo(channelRequestOrder.getChannelPayRequestNo());
        }

        ChannelRequestOrderEntity quoteRequestOrder = transactionBO.getQuoteRequestOrder();
        if (quoteRequestOrder != null) {
            // 根据原渠道支付请求单，填充数据
            channelRequestOrder.setServiceMode(quoteRequestOrder.getServiceMode());
            channelRequestOrder.setServiceEntity(quoteRequestOrder.getServiceEntity());
            channelRequestOrder.setQuoteChannelPayRequestNo(quoteRequestOrder.getChannelPayRequestNo());
            channelRequestOrder.setPaymentMethodType(quoteRequestOrder.getPaymentMethodType());
            channelRequestOrder.setTargetOrg(quoteRequestOrder.getTargetOrg());
            channelRequestOrder.setCardOrg(quoteRequestOrder.getCardOrg());
            channelRequestOrder.setCountry(quoteRequestOrder.getCountry());
            channelRequestOrder.setCurrency(quoteRequestOrder.getCurrency());
            channelRequestOrder.setMerchantNo(quoteRequestOrder.getMerchantNo());
            channelRequestOrder.setSubMerchantNo(quoteRequestOrder.getSubMerchantNo());
            channelRequestOrder.getExtJsonBo().setSubMerchantNo(quoteRequestOrder.getExtJsonBo().getSubMerchantNo());
            channelRequestOrder.getExtJsonBo().setBankCode(quoteRequestOrder.getExtJsonBo().getBankCode());
            channelRequestOrder.setProductCode(quoteRequestOrder.getProductCode());
            channelRequestOrder.setBizIdentify(quoteRequestOrder.getBizIdentify());
            channelRequestOrder.setOutUserId(quoteRequestOrder.getOutUserId());
            channelRequestOrder.setUserMemberId(quoteRequestOrder.getUserMemberId());
        }
        // 订单信息上的属性
        OrderInfoDO orderInfo = request.getOrderInfo();
        if (orderInfo != null) {
            channelRequestOrder.setServiceMode(orderInfo.getServiceMode());
            channelRequestOrder.setServiceEntity(orderInfo.getServiceEntity());
            channelRequestOrder.setMerchantNo(orderInfo.getMerchantNo());
            channelRequestOrder.setSubMerchantNo(orderInfo.getInnerSubMerchantNo());
            channelRequestOrder.getExtJsonBo().setSubMerchantNo(orderInfo.getOuterSubMerchantNo());
            channelRequestOrder.setOutUserId(orderInfo.getOutUserId());
            channelRequestOrder.setUserMemberId(orderInfo.getUserMemberId());
            channelRequestOrder.setPurchaseInfo(orderInfo.getPurchaseInfo());
            channelRequestOrder.setRemark(orderInfo.getRemark());
            //设置扩展字段realCardBin
            channelRequestOrder.setAddField5(orderInfo.getRealCardBin());
            // 存储交易步骤
            channelRequestOrder.setAddField7(orderInfo.getTransStep());
        }
        // 支付方式上的属性
        PaymentMethodDO paymentMethod = request.getPaymentMethod();
        if (paymentMethod != null) {
            channelRequestOrder.setPaymentType(paymentMethod.getPaymentType());
            channelRequestOrder.setCountry(paymentMethod.getCountry());
            channelRequestOrder.setTargetOrg(paymentMethod.getTargetOrg());
            channelRequestOrder.getExtJsonBo().setBankCode(paymentMethod.getBankCode());
            channelRequestOrder.setBizIdentify(paymentMethod.getBizIdentify());
            channelRequestOrder.setProductCode(paymentMethod.getProductCode());
            channelRequestOrder.setPaymentMethodType(paymentMethod.getPaymentMethodType());
            Money amount = paymentMethod.getAmount();
            if (amount != null) {
                channelRequestOrder.setCurrency(amount.getCurrency());
                channelRequestOrder.setAmount(amount.getValue());
            }
            // 卡组织
            String cardOrg = ExtendPropertyUtils.getExtProperty(paymentMethod.getExtendProperties(), FactorKeyEnum.SUPPORT_CARD_ORG.name());
            channelRequestOrder.setCardOrg(cardOrg);
        }
        // 如果paymentType为空，则设置
        if (StringUtils.isBlank(channelRequestOrder.getPaymentType())) {
            channelRequestOrder.setPaymentType(request.getPaymentType().getValue());
        }
        return channelRequestOrder;
    }

    /**
     * 创建请求单
     *
     * @param bizContext
     */
    protected void createChannelRequestOrder(BizContextBO<IN, OUT> bizContext) {
        TransactionBO transactionBO = getTransactionBO(bizContext);
        // 校验并获取请求单信息
        ChannelRequestOrderEntity orderEntity = checkAndGetChannelRequestOrder(bizContext);
        if (orderEntity != null) {
            // 把请求单对象放到上下文
            transactionBO.setRequestOrderInitStatus(orderEntity.getStatus());
            transactionBO.setChannelRequestOrder(orderEntity);
            return;
        }
        // 构建渠道请求单对象
        orderEntity = this.buildChannelRequestOrder(bizContext);
        // requestType不允许为空
        if (StringUtils.isBlank(orderEntity.getRequestType())) {
            orderEntity.setRequestType(RequestTypeEnum.NA.getCode());
        }

        try {
            // 新增渠道请求单
            channelRequestOrderService.add(orderEntity);
        } catch (DuplicateKeyException e) {
            throw new BusinessException(ORDER_TXN_IS_EXIST.getCode(), ORDER_TXN_IS_EXIST.getMsg(), e.getCause());
        } finally {
            // 把请求单对象放到上下文
            transactionBO.setRequestOrderInitStatus(orderEntity.getStatus());
            transactionBO.setChannelPayRequestNo(orderEntity.getChannelPayRequestNo());
            transactionBO.setChannelRequestOrder(orderEntity);
            // 摘要日志记录
            TraceContextUtil.putCorrelation(TraceCorrelationEnum.TARGET_ORG, orderEntity.getTargetOrg());
            if (Objects.nonNull(DigestLogDTO.getCurrentInDigestLog())) {
                DigestLogDTO.addBusinessInfo(CommonConstants.CHANNEL_PAY_REQUEST_ORDER_NO, orderEntity.getChannelPayRequestNo());
            }
        }
    }

    /**
     * 更新提交单&请求单
     *
     * @param bizContext
     */
    protected void updateChannelRequestOrder(BizContextBO<IN, OUT> bizContext) {
        RequestOrderDO request = bizContext.getIn();
        TransactionBO transactionBO = getTransactionBO(bizContext);
        ChannelCommitOrderEntity channelCommitOrder = transactionBO.getChannelCommitOrder();
        ChannelRequestOrderEntity channelRequestOrder = transactionBO.getChannelRequestOrder();
        ChannelOrderCompensateEntity orderCompensate = transactionBO.getOrderCompensate();
        Integer initStatus = transactionBO.getRequestOrderInitStatus();

        // 更新请求单
        channelRequestOrder.setChannelPayRequestNo(transactionBO.getChannelPayRequestNo());
        Integer requestOrderStatus = transactionBO.getRequestOrderStatus();
        Date completeTime = new Date();
        // 如果请求单状态为null，则取commitOrder的信息
        if (requestOrderStatus == null && channelCommitOrder != null) {
            channelRequestOrder.setMappingCode(channelCommitOrder.getMappingCode());
            channelRequestOrder.setMappingMsg(channelCommitOrder.getMappingMsg());
            requestOrderStatus = channelCommitOrder.getStatus();
            completeTime = channelCommitOrder.getCompleteTime();
        }
        // 判断是否终态
        if (OrderStatusEnum.isFinalStatus(requestOrderStatus)) {
            channelRequestOrder.setStatus(requestOrderStatus);
            channelRequestOrder.setCompleteTime(completeTime);
        } else {
            channelRequestOrder.setStatus(OrderStatusEnum.PENDING.getStatus());
        }
        if (request.getInnerUseInfo() != null) {
            // 重新设置requestBody
            boolean isChangeRequestParam = request.getInnerUseInfo().isChangeRequestParam();
            if (isChangeRequestParam) {
                channelRequestOrder.setChangeRequestParam(true);
                channelRequestOrder.setRequestBody(JsonUtils.toJson(request));
            }
        }
        if (request.getInnerUseInfo() != null && request.getInnerUseInfo().getTokenPayInfo() != null) {
            //更新ExtJsonBo,存到数据会通过ExtJsonBo.toJson
            ChannelRequestOrderEntity.ExtJsonBO extJsonBo = channelRequestOrder.getExtJsonBo();
            extJsonBo.setTokenPayInfo(JSONObject.toJSONString(request.getInnerUseInfo().getTokenPayInfo()));
        }
        try {
            // 通过状态机更新状态
            StateRequest stateRequest = new StateRequest(initStatus, OrderEventEnum.APPLY, channelRequestOrder);
            stateMachineExecutor.changeRequestState(stateRequest);
        } catch (BusinessException e) {
            // 如果是乐观锁导致的更新失败：说明订单状态有变更
            if (!ErrorCodeEnum.UPDATE_ORDER_FAIL.getCode().equals(e.getErrCode())) {
                throw e;
            }
        }

        // 更新成功，如果是支付单，则更新渠道补偿信息
        if (orderCompensate != null && MainRequestTypeEnum.isTransactionType(channelRequestOrder.getMainRequestType())) {
            orderCompensate.setRetryRequestStatus(channelRequestOrder.getStatus());
        }
    }

    /**
     * 填充自定义out
     *
     * @param bizContext
     */
    protected void fillCustomOut(BizContextBO<IN, OUT> bizContext) {
    }

    /**
     * 获取请求下游模板
     *
     * @param apiModeEnum 请求下游的交换模式枚举
     * @return 请求下游的交换模板
     */
    protected abstract ExchangeTemplate<IN, OUT> getExchangeTemplate(ApiModeEnum apiModeEnum);

    @Override
    protected void afterBusiness(BizContextBO<IN, OUT> bizContext) {
        // 父类
        super.afterBusiness(bizContext);
        // 失败情况下，更新请求单
        this.updateRequestOrderWhenError(bizContext);
        // 保存订单补偿信息
        this.saveOrderCompensate(bizContext);
        // 重发更新补偿查询单
        this.updateOrderCompensate(bizContext);
    }

    /**
     * 当流程异常时，更新请求单为失败
     *
     * @param bizContext
     */
    protected void updateRequestOrderWhenError(BizContextBO<IN, OUT> bizContext) {
        TransactionBO transactionBO = getTransactionBO(bizContext);
        ChannelRequestOrderEntity requestOrder = transactionBO.getChannelRequestOrder();
        ChannelCommitOrderEntity commitOrder = transactionBO.getChannelCommitOrder();
        Integer initStatus = transactionBO.getRequestOrderInitStatus();
        try {
            ErrorMsgBO errorMsgBo = bizContext.getErrorMsgBo();
            // 无异常，则不处理
            if (errorMsgBo == null) {
                return;
            }
            // 提交单已存在，说明到了调用下游步骤，则不处理
            if (commitOrder != null) {
                return;
            }
            // 设置响应对象
            OUT out = buildErrorOut(bizContext);
            bizContext.setOut(out);
            // 可忽略异常，则不更新请求单
            if (isIgnoreError(errorMsgBo)) {
                return;
            }
            // 更新订单状态
            if (requestOrder != null) {
                // 设置请求单状态
                transactionBO.setRequestOrderStatus(out.getStatus().getStatus());

                requestOrder.setMappingCode(errorMsgBo.getCode());
                requestOrder.setMappingMsg(errorMsgBo.getMsg());
                // 通过更新方法更新订单（包含异步达到终态通知上游逻辑）
                updateChannelRequestOrder(bizContext);
            }
        } catch (Exception e) {
            log.error("Update request order status to FAILED error.", e);
        }
    }

    /**
     * 是否可忽略异常
     *
     * @param errorMsgBo
     * @return
     */
    protected boolean isIgnoreError(ErrorMsgBO errorMsgBo) {
        boolean isIgnoreError = ErrorCodeEnum.ORDER_TXN_IS_EXIST.getCode().equals(errorMsgBo.getCode());
        return isIgnoreError;
    }

    /**
     * 构建异常情况下的OUT
     *
     * @param bizContext
     * @return
     */
    protected OUT buildErrorOut(BizContextBO<IN, OUT> bizContext) {
        TransactionBO transactionBO = getTransactionBO(bizContext);
        ErrorMsgBO errorMsgBo = bizContext.getErrorMsgBo();
        ChannelRequestOrderEntity requestOrder = transactionBO.getChannelRequestOrder();
        // 幂等错误
        if (ErrorCodeEnum.ORDER_TXN_IS_EXIST.getCode().equals(errorMsgBo.getCode())) {
            OUT out = buildIdempotentOut(bizContext);
            return out;
        }

        OUT out = bizContext.getOut();
        if (out == null) {
            out = initOut();
        }
        out.setStatus(OrderStatusEnum.FAILED);
        if (requestOrder != null) {
            out.setBizOrderNo(requestOrder.getBizOrderNo());
            out.setChannelPayRequestNo(requestOrder.getChannelPayRequestNo());
        }
        if (errorMsgBo != null) {
            out.setMappingCode(errorMsgBo.getCode());
            out.setMappingMsg(errorMsgBo.getMsg());
        }
        return out;
    }

    /**
     * 初始化响应
     *
     * @return
     */
    protected abstract OUT initOut();

    /**
     * 构建幂等响应
     * *
     *
     * @param bizContext
     * @return
     */
    protected OUT buildIdempotentOut(BizContextBO<IN, OUT> bizContext) {
        OUT responseDTO = initOut();

        TransactionBO transactionBO = getTransactionBO(bizContext);
        String payRequestNo = transactionBO.getChannelPayRequestNo();
        // 查询请求单
        ChannelRequestOrderEntity requestOrder = channelRequestOrderService.getByRequestOrderNoFromMaster(payRequestNo);
        if (requestOrder == null) {
            return responseDTO;
        }
        // 查询提交单
        ChannelCommitOrderEntity commitOrder = channelCommitOrderService.getByRequestNoAndTypeFromMaster(payRequestNo,
                MainRequestTypeEnum.TRANSACTION.getValue(), requestOrder.getPaymentType());
        // 放到上下文
        transactionBO.setChannelRequestOrder(requestOrder);
        transactionBO.setChannelCommitOrder(commitOrder);

        // 填充同步申请响应
        ObjectOperateUtils.fillResponseOrderDO(responseDTO, requestOrder, commitOrder);
        responseDTO.setStatus(OrderStatusEnum.getOrderStatusByStatus(requestOrder.getStatus()));
        return responseDTO;
    }

    @Override
    protected boolean isReturnDataWhenError() {
        return true;
    }

    /**
     * 获取渠道路由Service
     *
     * @return
     */
    private IRoutingService getRoutingService() {
        return routingService;
    }

    /**
     * 构建渠道支付步骤
     *
     * @param bizContext
     */
    private void buildChannelStep(BizContextBO<IN, OUT> bizContext) {
        TransactionBO transactionBO = getTransactionBO(bizContext);
        RequestTypeEnum requestTypeEnum = transactionBO.getRequestType();
        String channelCode = transactionBO.getChannelCode();
        String channelMethodCode = transactionBO.getChannelMethodCode();

        // 获取支付步骤上的请求标识
        Map<Integer, ChannelStepEntity> channelStepMap = channelStepService.getChannelStepByCode(channelCode, channelMethodCode);
        // 没有配置配置，则默认第一步为支付（只有一步并且无采集参数配置的渠道可以不配置步骤）
        if (channelStepMap == null || channelStepMap.isEmpty()) {
            return;
        }
        if (requestTypeEnum != null) {
            for (ChannelStepEntity step : channelStepMap.values()) {
                if (requestTypeEnum.getCode().equals(step.getStepCode())) {
                    // 设置上下文
                    transactionBO.setCurrentChannelStep(step);
                    if (step.getStep() != null && step.getStep() > 0) {
                        transactionBO.setNextChannelStep(channelStepMap.get(step.getStep() + 1));
                    }
                }
            }
        } else {
            ChannelStepEntity currentChannelStep = channelStepMap.get(1);
            String stepCode = currentChannelStep.getStepCode();
            requestTypeEnum = RequestTypeEnum.getRequestTypeByCode(stepCode);
            if (requestTypeEnum == null) {
                log.error("渠道支付步骤配置中存在不支持的请求类型. channelCode={}, channelMethodCode={}, stepCode={}", channelCode, channelMethodCode, stepCode);
                throw new BusinessException(ErrorCodeEnum.PARAMETER_INVALID.getCode(), "不支持的请求类型");
            }
            // 设置上下文
            transactionBO.setCurrentChannelStep(currentChannelStep);
            transactionBO.setNextChannelStep(channelStepMap.get(2));
        }
        transactionBO.setRequestType(requestTypeEnum);
        if (transactionBO.getCurrentChannelStep() == null) {
            log.error("渠道支付步骤未配置. channelCode={}, channelMethodCode={}", channelCode, channelMethodCode);
            throw new BusinessException(ErrorCodeEnum.PAYMENT_METHOD_CONFIG_ERROR.getCode(), "渠道支付步骤未配置");
        }
    }

    /**
     * 校验并获取请求单信息
     *
     * @param bizContext
     * @return
     */
    private ChannelRequestOrderEntity checkAndGetChannelRequestOrder(BizContextBO<IN, OUT> bizContext) {
        TransactionBO transactionBO = getTransactionBO(bizContext);
        String channelPayRequestNo = transactionBO.getChannelPayRequestNo();
        if (StringUtils.isBlank(channelPayRequestNo)) {
            return null;
        }
        ChannelRequestOrderEntity orderEntity = channelRequestOrderService.getByRequestOrderNoFromMaster(channelPayRequestNo);
        if (orderEntity == null) {
            throw new BusinessException(ErrorCodeEnum.CHANNEL_ORDER_IS_NOT_EXIST.getCode(), "渠道请求单不存在");
        }
        if (OrderStatusEnum.isFinalStatus(orderEntity.getStatus())) {
            throw new BusinessException(ErrorCodeEnum.CHANNEL_ORDER_IS_FINAL.getCode(), "渠道请求单已为终态，不能操作");
        }
        return orderEntity;
    }

    /**
     * 初始化订单补偿对象
     *
     * @param bizContext
     */
    protected void initOrderCompensate(BizContextBO<IN, OUT> bizContext) {
        TransactionBO transactionBO = getTransactionBO(bizContext);
        ChannelRequestOrderEntity requestOrder = transactionBO.getChannelRequestOrder();

        // 初始化
        ChannelOrderCompensateEntity orderCompensate = new ChannelOrderCompensateEntity();
        transactionBO.setOrderCompensate(orderCompensate);
        // 如果为非支付单，则不更新
        if (!MainRequestTypeEnum.isTransactionType(requestOrder.getMainRequestType())) {
            return;
        }
        orderCompensate.setPaymentType(requestOrder.getPaymentType());
        orderCompensate.setChannelPayRequestNo(requestOrder.getChannelPayRequestNo());
        orderCompensate.setAmount(requestOrder.getAmount());
        orderCompensate.setRetryRequestStatus(requestOrder.getStatus());
        transactionBO.setOrderCompensate(orderCompensate);
    }

    /**
     * 保存订单补偿单
     *
     * @param bizContext
     */
    private void saveOrderCompensate(BizContextBO<IN, OUT> bizContext) {
        TransactionBO transactionBO = getTransactionBO(bizContext);
        ChannelRequestOrderEntity requestOrder = transactionBO.getChannelRequestOrder();
        ChannelCommitOrderEntity commitOrder = transactionBO.getChannelCommitOrder();
        ChannelRequestOrderEntity quoteRequestOrder = transactionBO.getQuoteRequestOrder();
        ChannelCommitOrderEntity quoteCommitOrder = transactionBO.getQuoteCommitOrder();
        ApiModeEnum apiModeEnum = transactionBO.getApiModeEnum();
        ChannelOrderCompensateEntity orderCompensate = transactionBO.getOrderCompensate();
        ChannelStepEntity currentChannelStep = transactionBO.getCurrentChannelStep();
        ChannelStepEntity nextChannelStep = transactionBO.getNextChannelStep();
        ChannelConfigJsonBO channelConfigJson = transactionBO.getChannelConfigJson();
        PaymentMethodConfigJsonBO paymentMethodConfigJsonBO = transactionBO.getPaymentMethodConfigJson();
        try {
            // check数据完整性
            boolean isResult = orderCompensate == null || StringUtils.isBlank(orderCompensate.getChannelPayRequestNo())
                    || StringUtils.isBlank(orderCompensate.getChannelPayCommitNo())
                    || StringUtils.isBlank(orderCompensate.getPaymentType()) || StringUtils.isBlank(orderCompensate.getApiMode());
            if (isResult) {
                return;
            }
            // check订单状态：请求单和提交单均为终态，则无需补偿
            isResult = OrderStatusEnum.isFinalStatus(orderCompensate.getRetryRequestStatus())
                    && OrderStatusEnum.isFinalStatus(orderCompensate.getRetryCommitStatus());
            if (isResult) {
                return;
            }
            // 如果当前步骤为空或小于0，说明不是主流程步骤，无需补偿
            if (currentChannelStep != null && (currentChannelStep.getStep() == null || currentChannelStep.getStep() < 0)) {
                return;
            }
            // 存在下一步，说明不是最后一步，无需补偿
            if (nextChannelStep != null) {
                return;
            }
            boolean notSupportInquiry;
            if (null != paymentMethodConfigJsonBO && StringUtils.isNotBlank(paymentMethodConfigJsonBO.getIsSupportInquiry())) {
                // 优先渠道支付方式维度是否支持补偿查询
                notSupportInquiry = ShareConstants.NO_FLAG.equals(paymentMethodConfigJsonBO.getIsSupportInquiry());
            } else {
                // 渠道方式维度是否支持补偿查询
                notSupportInquiry = Objects.nonNull(channelConfigJson)
                        && ShareConstants.NO_FLAG.equals(channelConfigJson.getIsSupportInquiry());
            }
            // 退款查询暂时只配置渠道维度
            boolean notSupportRefundInquiry = Objects.nonNull(channelConfigJson)
                    && PaymentTypeEnum.REFUND.getValue().equals(requestOrder.getPaymentType())
                    && ShareConstants.NO_FLAG.equals(channelConfigJson.getIsSupportRefundInquiry());
            boolean notFinalStatus = !OrderStatusEnum.isFinalStatus(orderCompensate.getRetryRequestStatus())
                    && !OrderStatusEnum.isFinalStatus(orderCompensate.getRetryCommitStatus());
            // (不支持补偿查询 或者 退款交易不支持退款补偿查询) 并且 渠道请求单和提交单都是非终态（如果提交单为状态，请求单为非终态，也需要补偿请求单），则无需补偿
            if ((notSupportInquiry || notSupportRefundInquiry) && notFinalStatus) {
                return;
            }

            orderCompensate.setRetryFlag(ShareConstants.NUMBER_ONE);
            // 重试类型：默认为查询
            String retryType = orderCompensate.getRetryType();
            if (StringUtils.isBlank(retryType)) {
                orderCompensate.setRetryType(RetryTypeEnum.INQUIRY.name());
            }
            orderCompensate.setRetryTime(OrderCompensateUtils.calculateNextRetryTime(orderCompensate));
            // 构建补偿请求报文（仅补偿查询需要）
            if (RetryTypeEnum.INQUIRY.name().equals(orderCompensate.getRetryType())) {
                InternalBaseRequest baseRequest = null;
                if (orderCompensate.getChannelPayRequestNo().equals(requestOrder.getChannelPayRequestNo())) {
                    baseRequest = OrderCompensateUtils.buildRequestBody(apiModeEnum, requestOrder, commitOrder);
                } else if (orderCompensate.getChannelPayRequestNo().equals(quoteRequestOrder.getChannelPayRequestNo())) {
                    baseRequest = OrderCompensateUtils.buildRequestBody(apiModeEnum, quoteRequestOrder, quoteCommitOrder);
                }
                if (baseRequest != null) {
                    orderCompensate.setRetryRequestBody(JsonUtils.toString(baseRequest));
                }
            }
            orderCompensate.setChannelMerchantCode(commitOrder.getChannelMerchantCode());
            orderCompensate.setMerchantNo(requestOrder.getMerchantNo());
            // TODO 可考虑通过MQ消息异步保存
            channelOrderCompensateService.add(orderCompensate);
        } catch (Exception e) {
            log.error("Save channel order compensate data error. orderCompensate = {}", JsonUtils.toString(orderCompensate), e);
        }
    }

    /**
     * 原单重发，更新补偿查询单时间
     *
     * @param bizContext
     */
    private void updateOrderCompensate(BizContextBO<IN, OUT> bizContext) {
        TransactionBO transactionBO = getTransactionBO(bizContext);
        ChannelCommitOrderEntity commitOrder = transactionBO.getChannelCommitOrder();
        RequestOrderDO request = bizContext.getIn();
        RequestOrderDO.InnerUseInfo innerUseInfo = request.getInnerUseInfo();
        if (Objects.isNull(commitOrder)) {
            return;
        }
        // check订单状态：提交单为终态，则跳过
        boolean isResult = OrderStatusEnum.isFinalStatus(commitOrder.getStatus());
        if (isResult) {
            return;
        }
        ErrorMsgBO errorMsgBo = bizContext.getErrorMsgBo();
        // 如果是订单手动锁定异常，说明订单已经人工介入，则不重置补偿查询记录的重试次数
        if (errorMsgBo != null && ErrorCodeEnum.ORDER_MANUALLY_LOCKED.getCode().equals(errorMsgBo.getCode())) {
            return;
        }
        // 原单重发交易，则重置原有的补偿查询单信息
        if (Objects.nonNull(innerUseInfo) && StringUtils.isNotBlank(innerUseInfo.getChannelPayCommitNo())) {
            ChannelOrderCompensateEntity orderCompensate = new ChannelOrderCompensateEntity();
            orderCompensate.setChannelCode(commitOrder.getChannelCode());
            orderCompensate.setPaymentType(commitOrder.getPaymentType());
            orderCompensate.setChannelMethodCode(commitOrder.getChannelMethodCode());
            orderCompensate.setChannelPayRequestNo(commitOrder.getChannelPayRequestNo());
            orderCompensate.setChannelPayCommitNo(commitOrder.getChannelPayCommitNo());
            orderCompensate.setRetryType(RetryTypeEnum.INQUIRY.name());
            orderCompensate.setAddField4(-1);
            orderCompensate.setRetryTime(OrderCompensateUtils.calculateNextRetryTime(orderCompensate));
            channelOrderCompensateService.updateAvailableCompensateOrder(orderCompensate);
        }
    }

    /**
     * 填充订单完成时间
     * *
     *
     * @param bizContext
     */
    private void fillOrderCompleteTime(BizContextBO<IN, OUT> bizContext) {
        OUT out = bizContext.getOut();
        if (out == null) {
            return;
        }
        TransactionBO transactionBO = getTransactionBO(bizContext);
        ChannelRequestOrderEntity requestOrder = transactionBO.getChannelRequestOrder();
        if (requestOrder == null || requestOrder.getCompleteTime() == null) {
            return;
        }
        // 已请求单的完成时间作为响应时间：覆盖ExchangeTemplate里设置的提交单上完成时间
        out.setCompleteTime(requestOrder.getCompleteTime().getTime());
    }

    /**
     * 根据条件查询提交单
     * @param requestNo
     * @param paymentType
     * @param stepCode
     */
    protected List<ChannelCommitOrderEntity>  getCommitOrderByConditions(String requestNo, String paymentType, String channelCode, String stepCode) {
        ChannelCommitOrderEntityQuery channelCommitOrderEntityQuery = new ChannelCommitOrderEntityQuery();
        channelCommitOrderEntityQuery.setChannelPayRequestNo(requestNo);
        channelCommitOrderEntityQuery.setPaymentType(paymentType);
        channelCommitOrderEntityQuery.setChannelCode(channelCode);
        channelCommitOrderEntityQuery.setRequestType(stepCode);
        return channelCommitOrderService.getByEntityQueryFromMaster(channelCommitOrderEntityQuery);
    }


}
