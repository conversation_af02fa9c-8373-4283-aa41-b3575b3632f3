package com.payermax.fin.exchange.domainservice;

import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.JsonUtils;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.fin.exchange.common.enums.ErrorCodeEnum;
import com.payermax.fin.exchange.common.exception.ExchangeException;
import com.payermax.fin.exchange.common.util.ValidationUtils;
import com.payermax.fin.exchange.domainservice.bo.BizContextBO;
import com.payermax.fin.exchange.domainservice.bo.ErrorMsgBO;
import com.payermax.fin.exchange.domainservice.util.ErrorUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

/**
 * 基础领域服务抽象实现类
 *
 * <AUTHOR>
 * @date 2021/12/12 21:55
 */
public abstract class AbstractDomainService<IN, OUT> implements IDomainService<IN, OUT>, IV2DomainService<IN, OUT> {

    protected Logger log = LoggerFactory.getLogger(this.getClass());

    @Value("${spring.application.name}")
    private String appName;

    @Override
    public OUT execute(IN in) {
        // 构造BizContext
        BizContextBO<IN, OUT> bizContext = buildBizContext(in);
        // 执行
        return execute(bizContext);
    }

    @Override
    public OUT execute(IN in, Class<OUT> outClazz) {
        // 构造BizContext
        BizContextBO<IN, OUT> bizContext = buildBizContext(in);
        bizContext.setOutClazz(outClazz);
        bizContext.setOut(initOut(bizContext));
        // 执行
        return execute(bizContext);
    }

    /**
     * 执行
     *
     * @param bizContext
     */
    @Override
    public OUT execute(BizContextBO<IN, OUT> bizContext) {
        long startTime = System.currentTimeMillis();
        log.info("DomainService execute start.【RequestBody】:{}", JsonUtils.toJson(bizContext.getIn()));
        try {
            // 参数校验
            paramCheck(bizContext);
            // 执行业务逻辑 前逻辑
            beforeBusiness(bizContext);
            // 执行事物相关业务逻辑
            executeBusiness(bizContext);
        } catch (BusinessException e) {
            bizContext.setErrorMsgBo(ErrorUtils.buildErrorMsgBo(e.getErrCode(), e.getMessage(), e));
        } catch (Exception e) {
            bizContext.setErrorMsgBo(ErrorUtils.buildErrorMsgBo(ErrorCodeEnum.INNER_ERROR.getCode(), "inner error！", e));
        } catch (Throwable e) {
            bizContext.setErrorMsgBo(ErrorUtils.buildErrorMsgBo(ErrorCodeEnum.INNER_ERROR.getCode(), "inner throwable！", e));
        }

        try {
            // 执行业务逻辑 后逻辑：不影响主流程的后置操作，可在此步骤进行
            afterBusiness(bizContext);
        } catch (Throwable e) {
            log.error("后置逻辑处理失败!", e);
        }

        // 构建返回结果
        buildResult(bizContext, startTime);

        return bizContext.getOut();
    }

    /**
     * 初始化返回结果
     *
     * @return
     */
    private Result<OUT> init() {
        Result<OUT> result = ResultUtil.success();
        return result;
    }

    /**
     * 构建业务上下文
     *
     * @param in
     * @return
     */
    private BizContextBO<IN, OUT> buildBizContext(IN in) {
        BizContextBO<IN, OUT> bizContext = new BizContextBO<IN, OUT>();
        bizContext.setIn(in);
        return bizContext;
    }

    /**
     * 实例化OUT
     *
     * @param bizContext
     * @return
     */
    protected OUT initOut(BizContextBO<IN, OUT> bizContext) {
        Class<OUT> outClass = bizContext.getOutClazz();
        if (outClass == null) {
            return null;
        }
        try {
            return outClass.newInstance();
        } catch (Exception e) {
            log.error("instance OUT class fail.", e);
            return null;
        }
    }

    /**
     * 参数校验
     *
     * @param bizContext
     */
    private void paramCheck(BizContextBO<IN, OUT> bizContext) {
        IN input = bizContext.getIn();
        if (input == null) {
            throw new BusinessException(ErrorCodeEnum.PARAMETER_INVALID.getCode(), "input is null.");
        }
        try {
            // 校验参数
            ValidationUtils.validate(input);
            // 自定义参数校验
            paramCheckSelf(bizContext);
        } catch (BusinessException e) {
            throw e;
        } catch (Throwable e) {
            throw new BusinessException(ErrorCodeEnum.PARAMETER_INVALID.getCode(), e.getMessage());
        }
    }

    /**
     * 自定义参数校验
     *
     * @param bizContext
     */
    protected void paramCheckSelf(BizContextBO<IN, OUT> bizContext) {
    }

    /**
     * 执行业务逻辑前逻辑
     *
     * @param bizContext
     */
    protected void beforeBusiness(BizContextBO<IN, OUT> bizContext) {
    }

    /**
     * 执行业务逻辑
     *
     * @param bizContext
     * @throws Exception
     */
    protected abstract void executeBusiness(BizContextBO<IN, OUT> bizContext) throws Exception;

    /**
     * 执行业务逻辑后逻辑
     *
     * @param bizContext
     */
    protected void afterBusiness(BizContextBO<IN, OUT> bizContext) {
    }

    /**
     * 当error情况下，是否返回data
     *
     * @return
     */
    protected boolean isReturnDataWhenError() {
        return false;
    }

    /**
     * 构建响应结果
     *
     * @param bizContext
     * @param startTime
     */
    protected void buildResult(BizContextBO<IN, OUT> bizContext, long startTime) {
        OUT out = bizContext.getOut();
        long useTime = System.currentTimeMillis() - startTime;
        ErrorMsgBO errorMsgBo = bizContext.getErrorMsgBo();
        if (errorMsgBo != null) {
            if (ErrorCodeEnum.INNER_ERROR.getCode().equals(errorMsgBo.getCode())) {
                log.error("DomainService execute error. useTime:{},【ResponseBody】:{}", useTime, JsonUtils.toJson(out), errorMsgBo.getThrowable());
            } else {
                log.warn("DomainService execute error. useTime:{},【ResponseBody】:{}", useTime, JsonUtils.toJson(out), errorMsgBo.getThrowable());
            }
            if (isReturnDataWhenError()) {
                throw new ExchangeException(errorMsgBo.getCode(), errorMsgBo.getMsg(), out);
            } else {
                throw new BusinessException(errorMsgBo.getCode(), errorMsgBo.getMsg());
            }
        } else {
            log.debug("DomainService execute success. useTime:{},【ResponseBody】:{}", useTime, JsonUtils.toJson(out));
        }
    }

    /**
     * 打印日志
     *
     * @param bizContext
     * @param result
     */
    protected void printLog(BizContextBO<IN, OUT> bizContext, Result<OUT> result, long startTime) {
        long useTime = System.currentTimeMillis() - startTime;
        if (result.isSuccess()) {
            log.info("Manager execute success. useTime:{},【ResponseBody】:{}", useTime, JsonUtils.toJson(result));
            return;
        }
        ErrorMsgBO errorMsgBo = bizContext.getErrorMsgBo();
        if (errorMsgBo != null) {
            log.error("Manager execute error. useTime:{},【ResponseBody】:{}", useTime, JsonUtils.toJson(result), errorMsgBo.getThrowable());
        } else {
            log.error("Manager execute error. useTime:{},【ResponseBody】:{}", useTime, JsonUtils.toJson(result));
        }
    }

}
