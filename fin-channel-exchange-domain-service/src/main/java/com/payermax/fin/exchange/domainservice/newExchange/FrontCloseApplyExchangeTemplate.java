package com.payermax.fin.exchange.domainservice.newExchange;

import com.alibaba.fastjson.JSONPath;
import com.payermax.common.lang.util.JsonUtils;
import com.payermax.fin.exchange.common.enums.ApiModeEnum;
import com.payermax.fin.exchange.common.enums.FrontParamEnum;
import com.payermax.fin.exchange.dal.entity.ChannelCommitOrderEntity;
import com.payermax.fin.exchange.dal.entity.ChannelInfoEntity;
import com.payermax.fin.exchange.dal.entity.ChannelMethodEntity;
import com.payermax.fin.exchange.dal.entity.ChannelRequestOrderEntity;
import com.payermax.fin.exchange.domain.trans.ResponseOrderDO;
import com.payermax.fin.exchange.domain.trans.refund.CloseRequestOrderDO;
import com.payermax.fin.exchange.domainservice.bo.BizContextBO;
import com.payermax.fin.exchange.domainservice.bo.ChannelConfigJsonBO;
import com.payermax.fin.exchange.domainservice.bo.TransactionBO;
import com.payermax.fin.exchange.domainservice.repository.IChannelInfoService;
import com.payermax.fin.exchange.domainservice.repository.IChannelMethodService;
import com.payermax.fin.exchange.domainservice.repository.ITargetMerchantService;
import com.payermax.fin.exchange.domainservice.util.AmountUtils;
import com.payermax.fin.exchange.integration.rpc.constants.InternalUrlConstants;
import com.payermax.fin.exchange.integration.rpc.request.CloseFrontRequest;
import com.payermax.fin.exchange.integration.rpc.request.PayFrontRequest;
import com.payermax.fin.exchange.integration.rpc.response.CloseFrontResponse;
import com.payermax.fin.exchange.share.domain.OrderInfoDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 关单功能front处理模版
 *
 * <AUTHOR>
 * @date 2024/3/18 11:14
 */
@Component
public class FrontCloseApplyExchangeTemplate extends AbstractExchangeTemplate<CloseRequestOrderDO, ResponseOrderDO, CloseFrontRequest, CloseFrontResponse> {

    @Autowired
    private IChannelInfoService channelInfoService;

    @Autowired
    private IChannelMethodService channelMethodService;

    @Autowired
    private ITargetMerchantService targetMerchantService;

    @Override
    public String getApiMode() {
        return ApiModeEnum.PAST_FRONT.name();
    }

    @Override
    public String getApiName() {
        return InternalUrlConstants.FRONT_URL_CLOSE_PAY;
    }

    @Override
    public CloseFrontRequest exchangeRequest(BizContextBO<CloseRequestOrderDO, ResponseOrderDO> bizContext) {
        CloseRequestOrderDO request = bizContext.getIn();
        OrderInfoDO orderInfo = request.getOrderInfo();
        TransactionBO transactionBO = getTransactionBO(bizContext);
        String channelPayCommitNo = transactionBO.getChannelPayCommitNo();
        ChannelRequestOrderEntity origRequestOrder = transactionBO.getQuoteRequestOrder();
        ChannelCommitOrderEntity origCommitOrder = transactionBO.getQuoteCommitOrder();
        ChannelInfoEntity channelInfo = transactionBO.getChannelInfoEntity();
        ChannelRequestOrderEntity requestOrder = transactionBO.getChannelRequestOrder();
        ChannelCommitOrderEntity channelCommitOrder = transactionBO.getChannelCommitOrder();

        CloseFrontRequest frontRequest = new CloseFrontRequest();
        frontRequest.setIdcRegion(origCommitOrder.getIdcRegion());
        frontRequest.setChannelCode(transactionBO.getChannelCode());
        frontRequest.setChannelMerchantCode(transactionBO.getChannelMerchantCode());
        frontRequest.setAmount(AmountUtils.toLeastTwoScale(channelCommitOrder.getAmount()));
        frontRequest.setRemark(orderInfo.getRemark());
        frontRequest.setChannelOrderNo(channelPayCommitNo);
        frontRequest.setOriChannelOrderNo(origCommitOrder.getChannelPayCommitNo());
        frontRequest.setOriChannelTransactionId(origCommitOrder.getThirdOrgOrderNo());
        frontRequest.setCurrency(origRequestOrder.getCurrency());
        frontRequest.setMerchantTxnId(origRequestOrder.getBizOrderNo());
        frontRequest.setCountry(origCommitOrder.getCountry());
        frontRequest.setAddField1(origCommitOrder.getAddField1());
        frontRequest.setAddField2(origCommitOrder.getAddField2());
        frontRequest.setAddField3(origCommitOrder.getAddField3());
        frontRequest.setAddField4(origCommitOrder.getAddField4());
        frontRequest.setAddField5(origCommitOrder.getAddField5());
        frontRequest.setAddField6(origCommitOrder.getAddField6());
        String customerType = Objects.toString(JSONPath.eval(origRequestOrder.getRequestBody(), FrontParamEnum.customerType.getCode()), "");
        frontRequest.setCustomerType(customerType);
        // 设置新系统渠道编码等信息
        frontRequest.setNewChannelCode(transactionBO.getChannelCode());
        frontRequest.setChannelMethodCode(transactionBO.getChannelMethodCode());
        if (Objects.nonNull(channelInfo)) {
            ChannelConfigJsonBO channelConfigJson = channelInfoService.getConfigJsonByChannel(channelInfo);
            frontRequest.setTechnicalOrg(channelInfo.getTechnicalOrg());
            frontRequest.setTargetService(channelConfigJson.getTargetService());
        }
        if (Objects.nonNull(requestOrder)) {
            frontRequest.setPaymentMethodType(requestOrder.getPaymentMethodType());
            frontRequest.setTargetOrCardOrg(requestOrder.getTargetOrCardOrg());
            frontRequest.setBankCode(requestOrder.getExtJsonBo().getBankCode());
        }

        // 设置支付方式维度透传到下游的配置
        ChannelMethodEntity channelConfigEntity = transactionBO.getChannelConfigEntity();
        frontRequest.setChannelMethodExtConfig(channelMethodService.getChannelExtConfigByConfigJson(channelConfigEntity.getConfigJson()));

        frontRequest.setSubMerchantNo(origRequestOrder.getExtJsonBo().getSubMerchantNo());
        //设置报备信息上的二级商户信息：从原请求报文中获取
        PayFrontRequest origFrontRequest = JsonUtils.toBean(PayFrontRequest.class, origCommitOrder.getRequestBody());
        String subMerchantId = origFrontRequest == null ? null : origFrontRequest.getTargetSubMerchantId();
        frontRequest.setTargetMerchantDetail(targetMerchantService.getAndCheckTargetMerchant(
                origCommitOrder.getChannelMethodCode(), origCommitOrder.getChannelMerchantCode(), origRequestOrder.getMerchantNo(), subMerchantId));
        frontRequest.setTargetSubMerchantId(subMerchantId);

        frontRequest.setRequestType(origRequestOrder.getRequestType());
        frontRequest.setApi(InternalUrlConstants.CHANNEL_GATEWAY_API_CLOSE_PAY);
        return frontRequest;
    }

    @Override
    public void fillCommitOrderAfterExchange(ChannelCommitOrderEntity commitOrder, BizContextBO<CloseRequestOrderDO, ResponseOrderDO> bizContext) {
        TransactionBO transactionBO = getTransactionBO(bizContext);

        CloseFrontResponse frontResponse = (CloseFrontResponse) transactionBO.getInternalBaseResponse();
        commitOrder.setThirdOrgOrderNo(frontResponse.getChannelTransactionId());
        commitOrder.setFourthOrgOrderNo(frontResponse.getChannelThirdPartyTxnId());
        commitOrder.setAddField1(frontResponse.getAddField1());
        commitOrder.setAddField2(frontResponse.getAddField2());
        commitOrder.setAddField3(frontResponse.getAddField3());
        commitOrder.setAddField4(frontResponse.getAddField4());
        commitOrder.setAddField5(frontResponse.getAddField5());
        commitOrder.setAddField6(frontResponse.getAddField6());
    }
}
