package com.payermax.fin.exchange.domainservice.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.components.tracer.TraceCaptureUtil;
import com.payermax.fin.exchange.common.enums.*;
import com.payermax.fin.exchange.dal.entity.*;
import com.payermax.fin.exchange.domain.trans.RequestOrderDO;
import com.payermax.fin.exchange.domain.trans.ResponseOrderDO;
import com.payermax.fin.exchange.domain.trans.pay.PayRequestOrderDO;
import com.payermax.fin.exchange.domain.trans.pay.PayResponseOrderDO;
import com.payermax.fin.exchange.domain.trans.token.ChannelTokenIdResponseOrderDO;
import com.payermax.fin.exchange.domain.trans.token.ChannelTokenRequestDO;
import com.payermax.fin.exchange.domain.trans.token.ChannelTokenResponseDO;
import com.payermax.fin.exchange.domain.trans.token.TokenPayInfoDO;
import com.payermax.fin.exchange.domainservice.bo.BizContextBO;
import com.payermax.fin.exchange.domainservice.bo.TransactionBO;
import com.payermax.fin.exchange.domainservice.newImpl.CommitOrderDomainService;
import com.payermax.fin.exchange.domainservice.newImpl.UnCommitOrderDomainService;
import com.payermax.fin.exchange.domainservice.repository.IChannelExtConfigService;
import com.payermax.fin.exchange.domainservice.repository.IChannelInfoService;
import com.payermax.fin.exchange.domainservice.repository.IChannelMethodService;
import com.payermax.fin.exchange.domainservice.repository.IChannelStepApiMappingService;
import com.payermax.fin.exchange.domainservice.service.orderNo.IOrderNoService;
import com.payermax.fin.exchange.domainservice.service.route.IRoutingService;
import com.payermax.fin.exchange.share.assembler.CommonAssembler;
import com.payermax.fin.exchange.share.constants.ShareConstants;
import com.payermax.fin.exchange.share.domain.*;
import com.payermax.fin.exchange.share.enums.ChannelTokenEnum;
import com.payermax.fin.exchange.share.enums.FactorKeyEnum;
import com.payermax.fin.exchange.share.enums.PaymentTypeEnum;
import com.payermax.fin.exchange.share.utils.ExtendPropertyUtils;
import com.ushareit.fintech.common.model.dto.Money;
import com.ushareit.fintech.components.dynamic.thread.pool.DynamicThreadPoolExecutor;
import com.ushareit.fintech.components.dynamic.thread.pool.DynamicThreadPoolManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @time 2025-05-16 14:09:29
 * @description TODO
 */
@Slf4j
@Component
public class ChannelTokenUtils {

    @Autowired
    private CommonAssembler commonAssembler;
    @Autowired
    protected IOrderNoService orderNoService;
    @Autowired
    private IChannelStepApiMappingService channelStepApiMappingService;
    @Autowired
    @Qualifier("tokenChannelRoutingService")
    private IRoutingService tokenChannelRoutingService;
    @Autowired
    protected IChannelInfoService channelInfoService;
    @Autowired
    protected IChannelMethodService channelMethodService;
    @Autowired
    @Qualifier("commitOrderDomainService")
    private CommitOrderDomainService<PayRequestOrderDO, ChannelTokenIdResponseOrderDO> commitOrderDomainService;
    @Autowired
    @Qualifier("unCommitOrderDomainService")
    private UnCommitOrderDomainService<ChannelTokenRequestDO, ChannelTokenResponseDO> unCommitOrderDomainService;
    /**
     * token渠道支持的能力
     */
    @NacosValue(value = "#{'${exchange.token.ability.ext-propertys:SUPPORT_CARD_ORG}'.split(',')}", autoRefreshed = true)
    private List<String> tokenAbilityExtPropertys;

    @NacosValue(value = "#{${exchange.token.threeds.auth.update.map}}", autoRefreshed = true)
    private Map<String, String> threeDsAuthUpdateMap;

    @Autowired
    private ChannelTokenGrayUtils channelTokenGrayUtils;

    private static String CHANNEL_TOKEN_POOL = "channelTokenPool";

    private static DynamicThreadPoolManager dynamicThreadPoolManager;

    @Autowired
    private IChannelExtConfigService channelExtConfigService;

    @Autowired
    public void setDynamicThreadPoolManager(DynamicThreadPoolManager dynamicThreadPoolManager) {
        ChannelTokenUtils.dynamicThreadPoolManager = dynamicThreadPoolManager;
    }


    /**
     * 生成渠道token信息
     * @param bizContext
     */
    public void generateChannelTokenInfo(BizContextBO<PayRequestOrderDO, PayResponseOrderDO> bizContext) {
        try {

            PayRequestOrderDO payRequestOrderDO = bizContext.getIn();
            TransactionBO payTransactionBO = (TransactionBO) bizContext.getBaseBo();
            //是否符合token支付
            if (!isTokenPaymentEligible(payRequestOrderDO, payTransactionBO)) {
                return;
            }
            OrderInfoDO orderInfo = payRequestOrderDO.getOrderInfo();
            ChannelRequestOrderEntity channelRequestOrder = payTransactionBO.getChannelRequestOrder();

            // step1. 路由 Token 渠道,一定要copy防止修改原始支付信息
            PaymentMethodDO paymentMethod = commonAssembler.toPaymentMethodDO(payRequestOrderDO.getPaymentMethod());
            TokenRouteOrderInfoDO tokenRouteOrderInfoDO = commonAssembler.toTokenRouteOrderInfoDO(orderInfo);
            ChannelInstanceDO tokenRouteChannelInstance = tokenChannelRoute(paymentMethod, tokenRouteOrderInfoDO);
            if (!isValidChannel(tokenRouteChannelInstance)) {
                return;
            }
            if (payRequestOrderDO.getInnerUseInfo() == null) {
                payRequestOrderDO.setInnerUseInfo(new RequestOrderDO.InnerUseInfo());
            }

            // step2. 构建 token key（渠道编码 + 商户编码）
            String tokenKey = tokenRouteChannelInstance.getChannelCode() + ShareConstants.CONCAT_LINE + tokenRouteChannelInstance.getChannelMerchantCode();
            // 获取并校验 token 信息
            Map<String, PayRequestOrderDO.TokenInfo> tokenInfoMap = toTokenInfoMap(payRequestOrderDO.getTokenInfos());
            PayRequestOrderDO.TokenInfo tokenInfo = tokenInfoMap.get(tokenKey);
            // 如果存在 Token 但状态为挂起，走普通支付
            if (tokenInfo != null && NetWorkTokenStatusEnum.SUSPENDED.getType().equals(tokenInfo.getTokenStatus())) {
                log.warn("Token status is suspended and not token payment, requestNo:{}", payRequestOrderDO.getChannelPayRequestNo());
                return;
            }

            // step3、优先使用已激活的Token；否则尝试通过卡信息获取新Token
            PayRequestOrderDO.TokenInfo finalTokenInfo = null;
            ChannelCommitOrderEntity tokenIdChannelCommitOrder = null;//tokenId那笔提交单
            if (tokenInfo != null && NetWorkTokenStatusEnum.ACTIVATED.getType().equals(tokenInfo.getTokenStatus())) {
                finalTokenInfo = tokenInfo;
            } else {
                //初始化BizContext,这里还需要从里面拿到调用TokenId的CommitOrder
                BizContextBO<PayRequestOrderDO, ChannelTokenIdResponseOrderDO> tokenIdBizContext = buildTokenIdBizContext(payRequestOrderDO);
                finalTokenInfo = queryChannelTokenId(tokenIdBizContext, channelRequestOrder, tokenRouteChannelInstance);
                tokenIdChannelCommitOrder = ((TransactionBO) tokenIdBizContext.getBaseBo()).getChannelCommitOrder();
            }
            if (finalTokenInfo == null) {
                log.warn("Failed to get a valid token for requestNo:{}", payRequestOrderDO.getChannelPayRequestNo());
                return;
            }

            // step4. 获取 Channel Token
            ChannelTokenResponseDO channelTokenResponse = queryChannelToken(finalTokenInfo, payRequestOrderDO, channelRequestOrder, tokenIdChannelCommitOrder, tokenRouteChannelInstance);
            if (channelTokenResponse == null || !OrderStatusEnum.SUCCESS.equals(channelTokenResponse.getStatus())) {
                log.warn("Failed to fetch channel token from provider, requestNo:{}", payRequestOrderDO.getChannelPayRequestNo());
                return;
            }

            // step5. 构造响应并设置 Token 信息
            TokenPayInfoDO tokenPayInfoDO = buildPayChannelTokenResponse(tokenRouteChannelInstance, channelTokenResponse, finalTokenInfo);
            if (tokenPayInfoDO != null) {
                tokenPayInfoDO.setIsFirstApply(tokenIdChannelCommitOrder == null ? ShareConstants.NO_FLAG : ShareConstants.YES_FLAG);
                payRequestOrderDO.getInnerUseInfo().setTokenPayInfo(tokenPayInfoDO);
            }
        } catch (Exception e) {
            log.error("channelToken执行异常,error:", e);
        }
    }


    /**
     * 是否符合token支付
     * @param payRequestOrderDO
     * @param payTransactionBO
     */
    private boolean isTokenPaymentEligible(PayRequestOrderDO payRequestOrderDO, TransactionBO payTransactionBO) {
        if (payRequestOrderDO.getInnerUseInfo() != null) {
            //如果tokenInfo不为空就跳过token路由 或 任意场景回调后,都不在走token路由,只有首次支付的时候，会才token路由
            if (payRequestOrderDO.getInnerUseInfo().getTokenPayInfo() != null || StringUtils.isNotBlank(payRequestOrderDO.getInnerUseInfo().getPreApiStepCode())) {
                return false;
            }
        }
        if (!isSupportNetWorkToken(payTransactionBO)) {
            return false;
        }
        ChannelRequestOrderEntity channelRequestOrder = payTransactionBO.getChannelRequestOrder();
        //Token渠道路由灰度方案
        String countryCode = ExtendPropertyUtils.getExtProperty(payRequestOrderDO.getPaymentMethod().getExtendProperties(), FactorKeyEnum.SUPPORT_CARD_ISSUE_COUNTRY.name());
        if (!channelTokenGrayUtils.networkTokenGrayRule(channelRequestOrder.getMerchantNo(), countryCode)) {
            return false;
        }
        return true;
    }


    /**
     * Token渠道路由
     * @param paymentMethod
     * @param tokenRouteOrderInfoDO
     * @return
     */
    public ChannelInstanceDO tokenChannelRoute(PaymentMethodDO paymentMethod, TokenRouteOrderInfoDO tokenRouteOrderInfoDO) {
        RequestOrderDO request = new RequestOrderDO();
        request.setOrderInfo(commonAssembler.toOrderInfoDO(tokenRouteOrderInfoDO));
        request.setPaymentMethod(paymentMethod);
        //根据配置设置tokenChannel支持的筛选能力,不能把支付上所有的能力都带到tokenchannel筛选
        List<ExtendPropertyDO> extendProperties = paymentMethod.getExtendProperties();
        if (CollectionUtils.isNotEmpty(extendProperties) && CollectionUtils.isNotEmpty(tokenAbilityExtPropertys)) {
            List<ExtendPropertyDO> newExtendProperties = extendProperties.stream().filter(item -> {
                return tokenAbilityExtPropertys.contains(item.getKey());
            }).collect(Collectors.toList());
            paymentMethod.setExtendProperties(newExtendProperties);
        }
        paymentMethod.setIsIgnoreAmountValue(true);
        List<ChannelInstanceDO> channelInstanceList = tokenChannelRoutingService.routing(request);
        if (CollectionUtils.isEmpty(channelInstanceList)) {
            log.warn("netWorkToken 路由失败,data:{}", JSONObject.toJSONString(request));
            return null;
        }
        return channelInstanceList.get(0);// NO_CHECK
    }


    /**
     * 获取tokenId,这里已经存在请求单域，只需生成提交单信息域，所以需要补充提交单的TransactionBO
     * @param tokenIdBizContext
     * @param channelRequestOrderEntity
     * @param channelInstanceDO
     * @return
     */
    public PayRequestOrderDO.TokenInfo queryChannelTokenId(BizContextBO<PayRequestOrderDO, ChannelTokenIdResponseOrderDO> tokenIdBizContext,
                                                             ChannelRequestOrderEntity channelRequestOrderEntity, ChannelInstanceDO channelInstanceDO) {
        //参数校验
        PayRequestOrderDO payRequestOrderDO = tokenIdBizContext.getIn();
        boolean verifyResult = channelTokenIdParamVerify(payRequestOrderDO);
        if (!verifyResult) {
            return null;
        }
        //完善transactionBO信息
        TransactionBO transactionBO = (TransactionBO) tokenIdBizContext.getBaseBo();
        // 其他固定配置
        transactionBO.setRequestType(RequestTypeEnum.APPLY_PAYMENT_TOKEN_ID);
        //交易信息域对象TransactionBO会影响提交单的生成
        setupCommonTransactionFieldsForTokenId(transactionBO, channelInstanceDO);

        //设置订单信息,tokenId不需要补偿单，只需要初始化就可以
        transactionBO.setChannelPayRequestNo(payRequestOrderDO.getChannelPayRequestNo());
        transactionBO.setChannelRequestOrder(Optional.ofNullable(channelRequestOrderEntity).orElse(new ChannelRequestOrderEntity()));
        transactionBO.setOrderCompensate(new ChannelOrderCompensateEntity());
        //Get tokenId设置交易步骤
        initStepApiMapping(transactionBO);

        try {
            ChannelTokenIdResponseOrderDO response = commitOrderDomainService.execute(tokenIdBizContext);
            if (OrderStatusEnum.SUCCESS.equals(response.getStatus()) && NetWorkTokenStatusEnum.ACTIVATED.getType().equals(response.getTokenStatus())) {
                return buildTokenInfoFromResponse(response);
            }
        } catch (Exception e) {
            log.warn("获取tokenId失败", e);
        }
        return null;
    }

    /**
     * 初始化tokenId的BizContext对象
     * @param payRequestOrderDO
     * @return
     */
    public BizContextBO<PayRequestOrderDO, ChannelTokenIdResponseOrderDO> buildTokenIdBizContext(PayRequestOrderDO payRequestOrderDO) {
        BizContextBO<PayRequestOrderDO, ChannelTokenIdResponseOrderDO> tokenIdBizContext = new BizContextBO<>();
        tokenIdBizContext.setIn(payRequestOrderDO);
        tokenIdBizContext.setOut(new ChannelTokenIdResponseOrderDO());
        //补充交易信息域
        TransactionBO transactionBO = new TransactionBO();
        tokenIdBizContext.setBaseBo(transactionBO);
        return tokenIdBizContext;
    }

    /**
     * 通过tokenId换渠道Token和验证令牌信息
     * channelCommitOrder 这个不一定有值因为如果上游已经上送了卡信息，就不会有调用tokenId的channelCommitOrder域
     * @param tokenInfo
     * @param payRequestOrderDO
     * @param channelCommitOrder
     * @return
     */
    public ChannelTokenResponseDO queryChannelToken(PayRequestOrderDO.TokenInfo tokenInfo, PayRequestOrderDO payRequestOrderDO,
                                                    ChannelRequestOrderEntity channelRequestOrder, ChannelCommitOrderEntity channelCommitOrder,
                                                    ChannelInstanceDO channelInstance) {
        if (!channelTokenParamVerify(tokenInfo)) {
            return null;
        }
        try {
            BizContextBO<RequestOrderDO, ResponseOrderDO> tokenIdBizContext = new BizContextBO<>();
            tokenIdBizContext.setOut(new ChannelTokenResponseDO());
            //生成Request
            ChannelTokenRequestDO channelTokenRequestDO = setupCommonFields(payRequestOrderDO);
            channelTokenRequestDO.setOrderInfo(payRequestOrderDO.getOrderInfo());
            channelTokenRequestDO.setParams(payRequestOrderDO.getParams());
            channelTokenRequestDO.setExtParams(payRequestOrderDO.getExtParams());
            tokenIdBizContext.setIn(channelTokenRequestDO);
            //设置tokenId
            channelTokenRequestDO.setChannelTokenId(tokenInfo.getTokenId());

            TransactionBO tokenIdTransaction = null;
            if (channelCommitOrder == null) {
                PaymentMethodDO paymentMethod = channelTokenRequestDO.getPaymentMethod();
                tokenIdTransaction = getChannelTokenTransactionBO(paymentMethod, channelTokenRequestDO, channelRequestOrder, channelInstance);
            } else {
                tokenIdTransaction = getChannelTokenTransactionBO(channelTokenRequestDO.getRequestType(), channelRequestOrder, channelCommitOrder);
            }
            tokenIdBizContext.setBaseBo(tokenIdTransaction);

            return (ChannelTokenResponseDO) unCommitOrderDomainService.execute(tokenIdBizContext);
        } catch (Exception e) {
            log.error("通过渠道token获取失败", e);
        }
        return null;
    }

    /**
     * 异步更新认证方式,如果让认证方式中缺少PASSKEY需要同步
     */
    public void asyncUpdateAuthenticationMethodTypes(PayRequestOrderDO payRequestOrderDO, ChannelRequestOrderEntity channelRequestOrder) {
        DynamicThreadPoolExecutor threadPool = dynamicThreadPoolManager.getThreadPoolExecutor(CHANNEL_TOKEN_POOL);
        // 提交比对任务
        threadPool.execute(TraceCaptureUtil.captureRunnable(() -> {
            updateAuthenticationMethodTypes(payRequestOrderDO, channelRequestOrder);
        }));

    }


    /**
     * 更新认证方式,如果让认证方式中缺少PASSKEY需要同步
     */
    public void updateAuthenticationMethodTypes(PayRequestOrderDO payRequestOrderDO, ChannelRequestOrderEntity channelRequestOrder) {
        try {
            if (channelRequestOrder == null || payRequestOrderDO.getInnerUseInfo() == null || payRequestOrderDO.getInnerUseInfo().getTokenPayInfo() == null) {
                return;
            }
            TokenPayInfoDO tokenPayInfoDO = (TokenPayInfoDO) payRequestOrderDO.getInnerUseInfo().getTokenPayInfo();
            List<String> authenticationMethodTypes = tokenPayInfoDO.getAuthenticationMethodTypes();
            if (CollectionUtils.isEmpty(authenticationMethodTypes) || MapUtil.isEmpty(threeDsAuthUpdateMap)) {
                return;
            }
            String authenticationMethodValue = threeDsAuthUpdateMap.get(tokenPayInfoDO.getTokenChannel());
            if (StringUtils.isBlank(authenticationMethodValue) || authenticationMethodTypes.contains(authenticationMethodValue)) {
                return;
            }

            BizContextBO<RequestOrderDO, ResponseOrderDO> tokenIdBizContext = new BizContextBO<>();
            tokenIdBizContext.setOut(new ChannelTokenResponseDO());
            //生成Request
            ChannelTokenRequestDO channelTokenRequestDO = setupCommonFields(payRequestOrderDO);
            channelTokenRequestDO.setRequestType(RequestTypeEnum.CHANNEL_TOKEN_LOOKUP.getCode());
            channelTokenRequestDO.setInnerUseInfo(payRequestOrderDO.getInnerUseInfo());
            channelTokenRequestDO.setParams(payRequestOrderDO.getParams());
            tokenIdBizContext.setIn(channelTokenRequestDO);

            //设置tokenId
            channelTokenRequestDO.setChannelTokenId(tokenPayInfoDO.getChannelTokenId());
            PaymentMethodDO paymentMethod = channelTokenRequestDO.getPaymentMethod();
            //完善订单域
            TransactionBO channelTokenLookupTransactionBO = getChannelTokenLookupTransactionBO(paymentMethod, channelTokenRequestDO, channelRequestOrder, tokenPayInfoDO);
            tokenIdBizContext.setBaseBo(channelTokenLookupTransactionBO);
            unCommitOrderDomainService.execute(tokenIdBizContext);
        } catch (Exception e) {
            log.warn("updateAuthenticationMethodTypes,fail:", e);
        }
    }

    /**
     * 否是有效实例
     * @param channelInstance
     * @return
     */
    public boolean isValidChannel(ChannelInstanceDO channelInstance) {
        return channelInstance != null
                && StringUtils.isNotBlank(channelInstance.getChannelCode())
                && StringUtils.isNotBlank(channelInstance.getChannelMerchantCode());
    }

    /**
     * 根据Response构建TokenInfo
     * @param response
     * @return
     */
    private PayRequestOrderDO.TokenInfo buildTokenInfoFromResponse(ChannelTokenIdResponseOrderDO response) {
        PayRequestOrderDO.TokenInfo tokenInfo = new PayRequestOrderDO.TokenInfo();
        tokenInfo.setTokenId(response.getTokenId());
        tokenInfo.setTokenChannel(response.getTokenChannel());
        tokenInfo.setTokenChannelMid(response.getTokenChannelMid());
        tokenInfo.setTokenType(response.getTokenType());
        tokenInfo.setTokenStatus(response.getTokenStatus());
        tokenInfo.setAuthenticationMethodTypes(response.getAuthenticationMethodTypes());
        return tokenInfo;
    }

    /**
     * 基于ChannelTokenRequestDO对象填充transactionBO对象
     * @param transactionBO
     * @param channelInstance
     */
    private void setupCommonTransactionFields(TransactionBO transactionBO, ChannelInstanceDO channelInstance) {
        transactionBO.setChannelCode(channelInstance.getChannelCode());
        transactionBO.setChannelMerchantCode(channelInstance.getChannelMerchantCode());
        String channelMethodCode = channelInstance.getChannelMethodCode();
        transactionBO.setChannelMethodCode(channelMethodCode);
        transactionBO.setChannelInfoEntity(channelInfoService.getByChannelCode(channelInstance.getChannelCode()));
        transactionBO.setChannelConfigEntity(StringUtils.isNotBlank(channelMethodCode)
                ? channelMethodService.getChannelMethodByMethodCode(channelMethodCode)
                : new ChannelMethodEntity());
    }

    /**
     * 构建ChannelTokenRequestDO token对象
     * @param payRequestOrderDO
     * @return
     */
    private ChannelTokenRequestDO setupCommonFields(PayRequestOrderDO payRequestOrderDO) {
        ChannelTokenRequestDO channelTokenRequestDO = new ChannelTokenRequestDO();
        channelTokenRequestDO.setPaymentType(PaymentTypeEnum.TOKEN);
        channelTokenRequestDO.setRequestType(RequestTypeEnum.APPLY_PAYMENT_TOKEN.getCode());

        //从支付请求域获取部分信息
        channelTokenRequestDO.setPaymentMethod(payRequestOrderDO.getPaymentMethod());
        channelTokenRequestDO.setChannelPayRequestNo(payRequestOrderDO.getChannelPayRequestNo());
        return channelTokenRequestDO;
    }

    /**
     * 生成channelToken TransactionBo
     * @param paymentMethod
     * @param channelTokenRequestDO
     * @return
     */
    private TransactionBO getChannelTokenTransactionBO(PaymentMethodDO paymentMethod, ChannelTokenRequestDO channelTokenRequestDO,
                                                       ChannelRequestOrderEntity channelRequestOrder, ChannelInstanceDO channelInstance){
        TransactionBO transactionBO = new TransactionBO();
        transactionBO.setRequestType(RequestTypeEnum.getRequestTypeByCode(channelTokenRequestDO.getRequestType()));
        // 设置基础字段
        setupCommonTransactionFields(transactionBO, channelInstance);

        // 设置请求单和提交单信息
        transactionBO.setChannelRequestOrder(channelRequestOrder == null ? new ChannelRequestOrderEntity() : channelRequestOrder);
        transactionBO.setChannelPayRequestNo(channelTokenRequestDO.getChannelPayRequestNo());

        //因为该流程不生成提交单，但是doMain会使用,所以需要初始化，不然会有NPE
        ChannelCommitOrderEntity channelCommitOrderEntity = new ChannelCommitOrderEntity();
        Money amount = paymentMethod.getAmount();
        if (amount != null) {
            channelCommitOrderEntity.setCurrency(amount.getCurrency());
            channelCommitOrderEntity.setAmount(amount.getValue());
        }
        channelCommitOrderEntity.setCountry(paymentMethod.getCountry());
        transactionBO.setChannelCommitOrder(channelCommitOrderEntity);
        String commitOrderNo = orderNoService.genCommitNoByRequestNoAndCode(transactionBO.getChannelPayRequestNo(),
                unCommitOrderDomainService.getOrderNoPrefix(transactionBO.getRequestType().getCode()),
                channelInstance.getChannelMethodCode(), channelInstance.getChannelCode(), false);
        transactionBO.setChannelPayCommitNo(commitOrderNo);

        // 设置交易步骤
        initStepApiMapping(transactionBO);

        return transactionBO;
    }

    /**
     * 生成channelToken TransactionBo
     * @param paymentMethod
     * @param channelTokenRequestDO
     * @return
     */
    private TransactionBO getChannelTokenLookupTransactionBO(PaymentMethodDO paymentMethod, ChannelTokenRequestDO channelTokenRequestDO,
                                                             ChannelRequestOrderEntity channelRequestOrder, TokenPayInfoDO tokenPayInfoDO){
        TransactionBO transactionBO = new TransactionBO();
        transactionBO.setRequestType(RequestTypeEnum.getRequestTypeByCode(channelTokenRequestDO.getRequestType()));
        // 设置基础字段
        setupCommonTransactionFields(transactionBO, tokenPayInfoDO);
        // 设置请求单和提交单信息
        transactionBO.setChannelRequestOrder(channelRequestOrder == null ? new ChannelRequestOrderEntity() : channelRequestOrder);
        transactionBO.setChannelPayRequestNo(channelTokenRequestDO.getChannelPayRequestNo());

        //因为该流程不生成提交单，但是doMain会使用,所以需要初始化，不然会有NPE
        ChannelCommitOrderEntity channelCommitOrderEntity = new ChannelCommitOrderEntity();
        Money amount = paymentMethod.getAmount();
        if (amount != null) {
            channelCommitOrderEntity.setCurrency(amount.getCurrency());
            channelCommitOrderEntity.setAmount(amount.getValue());
        }
        channelCommitOrderEntity.setCountry(paymentMethod.getCountry());
        transactionBO.setChannelCommitOrder(channelCommitOrderEntity);
        String commitOrderNo = orderNoService.genCommitNoByRequestNoAndCode(transactionBO.getChannelPayRequestNo(),
                unCommitOrderDomainService.getOrderNoPrefix(transactionBO.getRequestType().getCode()),
                transactionBO.getChannelMethodCode(), transactionBO.getChannelCode(), false);
        transactionBO.setChannelPayCommitNo(commitOrderNo);
        // 设置交易步骤
        initStepApiMapping(transactionBO);

        return transactionBO;
    }


    /**
     * 构建交易步骤
     * @param transactionBO
     */
    private void initStepApiMapping(TransactionBO transactionBO) {
        List<ChannelStepApiMappingEntity> channelStepApiMappingList = channelStepApiMappingService.getStepApiMappingByCode(
                transactionBO.getChannelCode(),
                transactionBO.getChannelMethodCode(),
                transactionBO.getRequestType().getCode());

        if (CollectionUtils.isNotEmpty(channelStepApiMappingList)) {
            ChannelStepApiMappingEntity channelStepApiMapping = channelStepApiMappingList.get(0);//NO_CHECK
            transactionBO.setCurrentStepApiMapping(channelStepApiMapping);
            ApiModeEnum apiModeEnum = ApiModeEnum.getApiModeEnum(channelStepApiMapping.getApiMode());
            transactionBO.setApiModeEnum(apiModeEnum == null ? ApiModeEnum.PAST_FRONT : apiModeEnum);
        }
    }


    /**
     * 获取channelToken，生成TransactionBo
     * 利用上一步的请求单，和请求单
     * @param requestType
     * @param channelRequestOrder
     * @param commitOrder
     * @return
     */
    private TransactionBO getChannelTokenTransactionBO(String requestType, ChannelRequestOrderEntity channelRequestOrder, ChannelCommitOrderEntity commitOrder) {
        TransactionBO transactionBO = new TransactionBO();
        transactionBO.setRequestType(RequestTypeEnum.getRequestTypeByCode(requestType));

        String channelCode = commitOrder.getChannelCode();
        String channelMethodCode = commitOrder.getChannelMethodCode();
        String channelMerchantCode = commitOrder.getChannelMerchantCode();

        //1、设置渠道信息
        transactionBO.setChannelCode(channelCode);
        transactionBO.setChannelMerchantCode(channelMerchantCode);
        transactionBO.setChannelMethodCode(channelMethodCode);

        //2、设置渠道配置
        transactionBO.setChannelInfoEntity(channelInfoService.getByChannelCode(channelCode));
        transactionBO.setChannelConfigEntity(StringUtils.isNotBlank(channelMethodCode)
                ? channelMethodService.getChannelMethodByMethodCode(channelMethodCode)
                : new ChannelMethodEntity());

        //3、设置单据信息 (要生成新提交单号,请求下游时用的就是这个单号，不是用的提交单对象里面的单号)
        transactionBO.setChannelRequestOrder(channelRequestOrder);
        transactionBO.setChannelPayRequestNo(channelRequestOrder.getChannelPayRequestNo());
        commitOrder.setRequestType(transactionBO.getRequestType().getCode());
        transactionBO.setChannelCommitOrder(commitOrder);
        String commitOrderNo = orderNoService.genCommitNoByRequestNoAndCode(transactionBO.getChannelPayRequestNo(),
                unCommitOrderDomainService.getOrderNoPrefix(transactionBO.getRequestType().getCode()), channelMethodCode, channelCode, false);
        transactionBO.setChannelPayCommitNo(commitOrderNo);

        //4、设置交易步骤
        initStepApiMapping(transactionBO);

        return transactionBO;
    }


    /**
     * 对token进行分组:通过渠道编码+渠道商户
     * TODO 是否要加卡标识?
     * @param tokenInfos
     * @return
     */
    public Map<String, PayRequestOrderDO.TokenInfo> toTokenInfoMap(List<PayRequestOrderDO.TokenInfo> tokenInfos) {
        if (CollectionUtils.isEmpty(tokenInfos)) {
            return new HashMap<>();
        }
        Map<String, PayRequestOrderDO.TokenInfo> tokenInfoMap = tokenInfos.stream().filter(item -> {
            return NetWorkTokenStatusEnum.SUSPENDED.getType().equals(item.getTokenStatus()) || NetWorkTokenStatusEnum.ACTIVATED.getType().equals(item.getTokenStatus());
        }).collect(Collectors.toMap(tokenInfo -> tokenInfo.getTokenChannel() + ShareConstants.CONCAT_LINE + tokenInfo.getTokenChannelMid(), v -> v, (v1, v2) -> v1));
        return tokenInfoMap;
    }

    public TokenPayInfoDO buildPayChannelTokenResponse(ChannelInstanceDO channelInstance, ChannelTokenResponseDO channelTokenResponse,
                                                       PayRequestOrderDO.TokenInfo finalTokenInfo) {
        ChannelTokenResponseDO.TokenInfo tokenInfoResponse = channelTokenResponse.getTokenInfo();
        if (tokenInfoResponse == null || StringUtils.isBlank(tokenInfoResponse.getCryptogram())) {
            return null;
        }
        TokenPayInfoDO tokenPayInfoDO = new TokenPayInfoDO();

        tokenPayInfoDO.setChannelTokenId(channelTokenResponse.getNetworkTokenId());
        tokenPayInfoDO.setChannelToken(tokenInfoResponse.getPaymentToken());
        tokenPayInfoDO.setCryptogram(tokenInfoResponse.getCryptogram());
        tokenPayInfoDO.setTokenExpirationMonth(tokenInfoResponse.getTokenExpirationMonth());
        tokenPayInfoDO.setTokenExpirationYear(tokenInfoResponse.getTokenExpirationYear());
        tokenPayInfoDO.setTokenType(ChannelTokenEnum.NETWORK_TOKEN.getCode());
        tokenPayInfoDO.setTokenChannel(channelInstance.getChannelCode());
        tokenPayInfoDO.setTokenChannelMid(channelInstance.getChannelMerchantCode());
        tokenPayInfoDO.setAuthenticationMethodTypes(finalTokenInfo.getAuthenticationMethodTypes());
        return tokenPayInfoDO;
    }

    /**
     * GET ChannelTokenId
     * 构建transactionBO对象
     * @param transactionBO
     * @param channelInstance
     */
    private void setupCommonTransactionFieldsForTokenId(TransactionBO transactionBO, ChannelInstanceDO channelInstance) {
        // 基础字段设置
        transactionBO.setChannelCode(channelInstance.getChannelCode());
        transactionBO.setChannelMerchantCode(channelInstance.getChannelMerchantCode());
        transactionBO.setChannelMethodCode(channelInstance.getChannelMethodCode());
        // 渠道信息 & 配置
        if (StringUtils.isNotBlank(channelInstance.getChannelCode())) {
            transactionBO.setChannelInfoEntity(channelInfoService.getByChannelCode(channelInstance.getChannelCode()));
        }
        if (StringUtils.isNotBlank(channelInstance.getChannelMethodCode())) {
            transactionBO.setChannelConfigEntity(channelMethodService.getChannelMethodByMethodCode(channelInstance.getChannelMethodCode()));
        } else {
            transactionBO.setChannelConfigEntity(new ChannelMethodEntity());
        }
    }


    /**
     * channelTokenId申请，参数校验
     * @param payRequestOrderDO
     */
    private boolean channelTokenIdParamVerify(PayRequestOrderDO payRequestOrderDO) {
        Map<String, String> params = payRequestOrderDO.getParams();
        return params != null && StringUtils.isNotBlank(params.get(ExtParamEnum.CARD_IDENTIFIER_NO.getCode()));
    }

    private boolean channelTokenParamVerify(PayRequestOrderDO.TokenInfo tokenInfo) {
        return tokenInfo != null && StringUtils.isNotBlank(tokenInfo.getTokenId());
    }


    /**
     * 基于ChannelTokenRequestDO对象填充transactionBO对象
     * @param transactionBO
     * @param tokenPayInfoDO
     */
    private void setupCommonTransactionFields(TransactionBO transactionBO, TokenPayInfoDO tokenPayInfoDO) {
        transactionBO.setChannelCode(tokenPayInfoDO.getTokenChannel());
        transactionBO.setChannelMerchantCode(tokenPayInfoDO.getTokenChannelMid());
        transactionBO.setChannelInfoEntity(channelInfoService.getByChannelCode(transactionBO.getChannelCode()));
        transactionBO.setChannelConfigEntity(new ChannelMethodEntity());
    }


    /**
     * 设置3ds执行的token渠道
     *
     * @param bizContext
     */
    public void handlerTokenChannelThreeDS(BizContextBO<PayRequestOrderDO, PayResponseOrderDO> bizContext) {
        PayRequestOrderDO payRequestOrderDO = bizContext.getIn();
        //排除回调过来的场景，只需要在第一次3ds之前设置
        if (payRequestOrderDO.getInnerUseInfo() == null || payRequestOrderDO.getInnerUseInfo().getTokenPayInfo() == null
                || StringUtils.isNotBlank(payRequestOrderDO.getInnerUseInfo().getPreApiStepCode())) {
            return;
        }
        //获取token信息
        Object tokenPayInfo = payRequestOrderDO.getInnerUseInfo().getTokenPayInfo();
        if (!(tokenPayInfo instanceof TokenPayInfoDO)) {
            return;
        }
        //获取交易信息
        TransactionBO transactionBO = (TransactionBO) bizContext.getBaseBo();
        TokenPayInfoDO tokenPayInfoDO = (TokenPayInfoDO) tokenPayInfo;
        List<String> authenticationMethodTypes = tokenPayInfoDO.getAuthenticationMethodTypes();
        if (CollectionUtils.isEmpty(authenticationMethodTypes) || transactionBO.getChannelRequestOrder() == null) {
            return;
        }
        ChannelRequestOrderEntity channelRequestOrder = transactionBO.getChannelRequestOrder();
        String merchantNo = channelRequestOrder.getMerchantNo();
        String countryCode = ExtendPropertyUtils.getExtProperty(payRequestOrderDO.getPaymentMethod().getExtendProperties(), FactorKeyEnum.SUPPORT_CARD_ISSUE_COUNTRY.name());

        boolean masterCardGrayRule = channelTokenGrayUtils.threeDsGrayRule(merchantNo, countryCode, authenticationMethodTypes);
        payRequestOrderDO.getInnerUseInfo().setThreeDSTokenChannel(masterCardGrayRule ? tokenPayInfoDO.getTokenChannel() : "");

        // 如果命中灰度规则且配置了 methodType，则替换认证方式
        if (masterCardGrayRule && StringUtils.isNotBlank(channelTokenGrayUtils.getThreeDsGrayMethodType())) {
            if (channelTokenGrayUtils.isLogSwitch()) {
                log.warn("使用灰度认证方式, methtype:{},tokenId:{}", channelTokenGrayUtils.getThreeDsGrayMethodType(), tokenPayInfoDO.getChannelTokenId());
            }
            tokenPayInfoDO.setThreeDsGrayMethodType(channelTokenGrayUtils.getThreeDsGrayMethodType());
        }
    }

    /**
     * 渠道是否支持NetWorkToken
     * @param payTransactionBO
     * @return
     */
    private boolean isSupportNetWorkToken(TransactionBO payTransactionBO) {
        ChannelInstanceDO channelInstance = payTransactionBO.getChannelInstance();
        if (channelInstance != null) {
            return channelInstance.isSupportNetWorkToken();
        }
        //查询不同维度的配置
        String channelMerchantCode = payTransactionBO.getChannelMerchantCode();
        String channelMethodCode = payTransactionBO.getChannelMethodCode();
        String supportTokenType = channelExtConfigService.getChannelExtValueByConfigCode(channelMethodCode, channelMerchantCode, FactorKeyEnum.SUPPORT_TOKEN_TYPE.name());
        return Optional.ofNullable(supportTokenType).map(item -> CollUtil.newHashSet(item.split(ShareConstants.COMMA)).contains(ChannelTokenEnum.NETWORK_TOKEN.getCode())).orElse(Boolean.FALSE);
    }
}
