package com.payermax.fin.exchange.domainservice.newExchange;

import com.payermax.fin.exchange.common.enums.ApiModeEnum;
import com.payermax.fin.exchange.dal.entity.ChannelCommitOrderEntity;
import com.payermax.fin.exchange.domainservice.bo.BizContextBO;
import com.payermax.fin.exchange.integration.rpc.request.InternalBaseRequest;
import com.payermax.fin.exchange.integration.rpc.response.InternalBaseResponse;
import com.payermax.fin.exchange.integration.rpc.response.ResultDTO;

/**
 *
 * <AUTHOR>
 * @date 2024/3/17 14:19
 */
public interface IExchangeTemplate<SIN, SOUT, TIN extends InternalBaseRequest, TOUT extends InternalBaseResponse> {

    /**
     * 接口模式，即调用的的哪个系统，
     * @see ApiModeEnum
     *
     * @return
     */
    String getApiMode();

    /**
     * 接口名字或URL
     *
     * @return
     */
    String getApiName();

    /**
     * 请求参数转换，从内部上下文参数转换成接口需要的请求参数
     *
     * @param bizContext
     * @return
     */
    TIN exchangeRequest(BizContextBO<SIN, SOUT> bizContext);

    /**
     * 执行调用
     *
     * @param request
     * @param bizContext
     * @return
     */
    ResultDTO<TOUT> invoke(Object request, BizContextBO<SIN, SOUT> bizContext);

    /**
     * 填充提交单
     *
     * @param commitOrder
     * @param bizContext
     */
    void fillCommitOrderAfterExchange(ChannelCommitOrderEntity commitOrder, BizContextBO<SIN, SOUT> bizContext);

    /**
     * 响应参数转换，从接口响应报文转换成内部需要的报文
     *
     * @param response
     * @param bizContext
     * @return
     */
    SOUT exchangeResponse(TOUT response, BizContextBO<SIN, SOUT> bizContext);

}
