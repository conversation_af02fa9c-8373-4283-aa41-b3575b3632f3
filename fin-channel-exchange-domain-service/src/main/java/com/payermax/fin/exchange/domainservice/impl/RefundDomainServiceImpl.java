package com.payermax.fin.exchange.domainservice.impl;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.util.JsonUtils;
import com.payermax.fin.exchange.common.constants.ChannelCodeConstants;
import com.payermax.fin.exchange.common.constants.CommonConstants;
import com.payermax.fin.exchange.common.enums.*;
import com.payermax.fin.exchange.common.util.ValidationUtils;
import com.payermax.fin.exchange.dal.config.ShardingUtils;
import com.payermax.fin.exchange.dal.entity.*;
import com.payermax.fin.exchange.dal.entity.query.ChannelCommitOrderEntityQuery;
import com.payermax.fin.exchange.domain.trans.refund.RefundRequestOrderDO;
import com.payermax.fin.exchange.domain.trans.refund.RefundResponseOrderDO;
import com.payermax.fin.exchange.domainservice.AbstractTransDomainService;
import com.payermax.fin.exchange.domainservice.bo.*;
import com.payermax.fin.exchange.domainservice.exchange.ExchangeTemplate;
import com.payermax.fin.exchange.domainservice.producer.ExchangeMQProducer;
import com.payermax.fin.exchange.domainservice.util.*;
import com.payermax.fin.exchange.integration.rpc.response.ResultDTO;
import com.payermax.fin.exchange.share.constants.ShareConstants;
import com.payermax.fin.exchange.share.enums.PaymentTypeEnum;
import com.payermax.operating.correction.facade.enums.RefundStrategyProcess;
import com.ushareit.fintech.common.model.dto.Money;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 退款领域服务
 *
 * <AUTHOR>
 * @date 2021/12/16 0:46
 */
@Service
public class RefundDomainServiceImpl extends AbstractTransDomainService<RefundRequestOrderDO, RefundResponseOrderDO> {

    @NacosValue(value = "${refund.partial.switch:true}", autoRefreshed = true)
    private boolean isOpenPartial;

    @NacosValue(value = "${refund.lock.expire-seconds:10}", autoRefreshed = true)
    private long lockExpireSeconds;

    @NacosValue(value = "${refund.time.limit.desc:the refund time is not within the refundable time range!}", autoRefreshed = true)
    private String refundTimeDesc;

    @NacosValue(value = "${refund.transfer.correction.errCode:CHANNEL_NOT_SUPPORT_REFUND,EXCEED_REFUNDABLE_TIME,NOT_SUPPORT_PARTIAL_REFUND,NOT_SUPPORT_MUL_REFUND}", autoRefreshed = true)
    private String[] transferCorrectionErrCode;

    @Autowired
    private ExchangeMQProducer exchangeMQProducer;

    @Autowired
    protected CorrectionOrderUtils correctionOrderUtils;

    @Autowired
    protected ExchangeTemplate<RefundRequestOrderDO, RefundResponseOrderDO> refundFrontExchangeTemplate;

    @Resource
    protected ExchangeTemplate<RefundRequestOrderDO, RefundResponseOrderDO> correctionPushExchangeTemplate;

    @Override
    protected void paramCheckSelf(BizContextBO<RefundRequestOrderDO, RefundResponseOrderDO> bizContext) {
        super.paramCheckSelf(bizContext);

        RefundRequestOrderDO requestOrderDO = bizContext.getIn();
        ValidationUtils.notBlankCheck(requestOrderDO.getAmount().getCurrency(), "[amount.currency] is mandatory");
        ValidationUtils.minNumberCheck(requestOrderDO.getAmount().getValue(), 0, false, "[amount.value] must be greater than zero");
    }

    @Override
    protected void checkAndFillBeforeRouting(BizContextBO<RefundRequestOrderDO, RefundResponseOrderDO> bizContext) {
        super.checkAndFillBeforeRouting(bizContext);
        RefundRequestOrderDO request = bizContext.getIn();
        TransactionBO transactionBO = getTransactionBO(bizContext);

        // 查询原渠道支付请求单
        String mainType = MainRequestTypeEnum.TRANSACTION.getValue();
        String paymentType = PaymentTypeEnum.PAYMENT.getValue();
        String oriPayOrderNo = request.getOriBizOrderNo();
        ChannelRequestOrderEntity quoteRequestOrder = channelRequestOrderService.getByBizOrderNoAndTypeFromMaster(oriPayOrderNo, mainType, paymentType);
        if (quoteRequestOrder == null) {
            throw new BusinessException(ErrorCodeEnum.CHANNEL_ORDER_IS_NOT_EXIST.getCode(), "原始渠道支付请求单不存在");
        }
        if (!OrderStatusEnum.SUCCESS.getStatus().equals(quoteRequestOrder.getStatus())) {
            throw new BusinessException(ErrorCodeEnum.CHANNEL_ORDER_IS_NOT_SUCCESS.getCode(), "原始渠道支付请求单未完结,不能退款");
        }
        transactionBO.setQuoteRequestOrder(quoteRequestOrder);

        // 查询原渠道支付提交单
        String channelPayRequestNo = quoteRequestOrder.getChannelPayRequestNo();
        ChannelCommitOrderEntity quoteCommitOrder = channelCommitOrderService.getByRequestNoAndTypeFromMaster(channelPayRequestNo, mainType, paymentType);
        if (quoteCommitOrder == null) {
            throw new BusinessException(ErrorCodeEnum.CHANNEL_ORDER_IS_NOT_EXIST.getCode(), "原始渠道支付提交单不存在");
        }
        if (!OrderStatusEnum.SUCCESS.getStatus().equals(quoteCommitOrder.getStatus())) {
            throw new BusinessException(ErrorCodeEnum.CHANNEL_ORDER_IS_NOT_SUCCESS.getCode(), "原始渠道支付提交单未完结,不能退款");
        }
        transactionBO.setQuoteCommitOrder(quoteCommitOrder);

        // 使用支付单渠道退款，退款或退款重试
        boolean usePaymentChannel = Objects.isNull(request.getStrategyProcess()) || StringUtils.equals(RefundStrategyProcess.ORIGINAL_CHANNEL_RETRY.name(), request.getStrategyProcess());
        // 使用差错渠道退款
        boolean useCorrectionChannel = StringUtils.equals(RefundStrategyProcess.CORRECTION_CHANNEL_PROCESS.name(), request.getStrategyProcess());

        if (usePaymentChannel) {
            try {
                transactionBO.setChannelCode(quoteCommitOrder.getChannelCode());
                transactionBO.setChannelMerchantCode(quoteCommitOrder.getChannelMerchantCode());
                transactionBO.setChannelMethodCode(quoteCommitOrder.getChannelMethodCode());
                // 是否允许退款校验
                allowRefundCheckBefore(transactionBO, request, quoteRequestOrder);
            } catch (BusinessException businessException) {
                // 渠道不支持退款时，使用差错渠道处理
                if (ArrayUtils.contains(transferCorrectionErrCode, businessException.getErrCode())) {
                    request.setDetailDesc(businessException.getMessage());
                    request.setDetailCode(businessException.getErrCode());
                    useCorrectionChannel = true;
                } else {
                    throw businessException;
                }
            }
        }

        if (useCorrectionChannel) {
            transactionBO.setApiModeEnum(ApiModeEnum.CORREC_CENTER);
            // 差错渠道编码、渠道支付方式
            transactionBO.setChannelCode(ChannelCodeConstants.CORRECTION_CENTER);
            // 是否允许退款校验
            allowRefundCheckBefore(transactionBO, request, quoteRequestOrder);
        }

    }


    /**
     * 是否允许退款校验
     * *
     */
    protected void allowRefundCheckBefore(TransactionBO transactionBO, RefundRequestOrderDO request, ChannelRequestOrderEntity quoteRequestOrder) {
        ChannelMethodEntity channelConfig;
        switch (transactionBO.getChannelCode()) {
            case ChannelCodeConstants.CORRECTION_CENTER:
                // 差错渠道能力配置信息
                channelConfig = new ChannelMethodEntity();
                channelConfig.setChannelCode(ChannelCodeConstants.CORRECTION_CENTER);
                channelConfig.setIsSupportRefund(ShareConstants.NUMBER_ONE);
                channelConfig.setIsPartialRefund(ShareConstants.NUMBER_ONE);
                channelConfig.setIsSupportCancel(ShareConstants.NUMBER_ONE);
                transactionBO.setPaymentMethodConfigJson(null);
                break;
            default:
                // 查询渠道能力配置信息
                channelConfig = channelMethodService.getByMethodCodeIgnoreStatus(transactionBO.getChannelMethodCode());
                if (channelConfig != null && StringUtils.isNotBlank(channelConfig.getConfigJson())) {
                    PaymentMethodConfigJsonBO paymentMethodConfigJson = JsonUtils.toBean(PaymentMethodConfigJsonBO.class, channelConfig.getConfigJson());
                    transactionBO.setPaymentMethodConfigJson(paymentMethodConfigJson);
                }
        }
        transactionBO.setChannelConfigEntity(channelConfig);

        // 查询渠道信息
        ChannelInfoEntity channelInfo = channelInfoService.getByChannelCodeIgnoreStatus(transactionBO.getChannelCode());
        if (channelInfo != null && StringUtils.isNotBlank(channelInfo.getConfigJson())) {
            ChannelConfigJsonBO channelConfigJson = JsonUtils.toBean(ChannelConfigJsonBO.class, channelInfo.getConfigJson());
            transactionBO.setChannelConfigJson(channelConfigJson);
        }
        transactionBO.setChannelInfoEntity(channelInfo);

        // 是否允许退款校验
        allowRefundCheck(transactionBO, channelConfig, quoteRequestOrder, request);
    }


    @Override
    protected ChannelRequestOrderEntity buildChannelRequestOrder(BizContextBO<RefundRequestOrderDO, RefundResponseOrderDO> bizContext) {
        RefundRequestOrderDO request = bizContext.getIn();
        // 构建渠道请求单对象
        ChannelRequestOrderEntity requestOrder = super.buildChannelRequestOrder(bizContext);
        requestOrder.setAmount(request.getAmount().getValue());
        requestOrder.setRemark(request.getRemark());
        return requestOrder;
    }

    @Override
    protected void createChannelRequestOrder(BizContextBO<RefundRequestOrderDO, RefundResponseOrderDO> bizContext) {
        try {
            super.createChannelRequestOrder(bizContext);
        } catch (Exception e) {
            throw e;
        } finally {
            // 请求单保存完成则释放分布式锁
            this.releaseLock(bizContext);
        }
    }

    @Override
    protected void updateChannelRequestOrder(BizContextBO<RefundRequestOrderDO, RefundResponseOrderDO> bizContext) {
        TransactionBO transactionBO = getTransactionBO(bizContext);
        RefundRequestOrderDO request = bizContext.getIn();
        RefundResponseOrderDO response = bizContext.getOut();
        ChannelCommitOrderEntity commitOrder = transactionBO.getChannelCommitOrder();
        ChannelRequestOrderEntity requestOrder = transactionBO.getChannelRequestOrder();
        // 更新请求单提交单前置业务处理
        TransactionRetryBO retryBO = RetryUtils.customBeforeBusiness( transactionBO.getTransactionRetryBO(), requestOrder, commitOrder);
        transactionBO.setTransactionRetryBO(retryBO);
        // 请求单重试则计算重试次数
        if (Objects.equals(request.getRetryType(), RetryTypeEnum.ABNORMAL_APPLY)) {
            String addField2 = requestOrder.getAddField2();
            int retryCount = Integer.parseInt(StringUtils.defaultIfBlank(addField2, "0"));
            if (retryCount >= 0) {
                retryCount++;
            }
            requestOrder.setAddField2(String.valueOf(retryCount));
        }
        // 业务对象状态置为当前请求单状态
        transactionBO.setRequestOrderStatus(requestOrder.getStatus());
        if (Objects.nonNull(response)) {
            // 响应对象状态置为当前请求单状态
            response.setStatus(OrderStatusEnum.getOrderStatusByStatus(requestOrder.getStatus()));
        }
        super.updateChannelRequestOrder(bizContext);
        // 如果不是终态，则返回
        if (!OrderStatusEnum.isFinalStatus(requestOrder.getStatus())) {
            return;
        }
        // 退款请求单号不存在，则说明不是重试，则返回
        if (request.getInnerUseInfo() == null || StringUtils.isBlank(request.getInnerUseInfo().getChannelPayRequestNo())) {
            return;
        }
        // 发送MQ通知
        exchangeMQProducer.sendNotifyMQ(requestOrder, commitOrder);
    }


    @Override
    protected void afterBusiness(BizContextBO<RefundRequestOrderDO, RefundResponseOrderDO> bizContext) {
        TransactionBO transactionBO = getTransactionBO(bizContext);
        RefundResponseOrderDO response = bizContext.getOut();
        ChannelCommitOrderEntity commitOrder = transactionBO.getChannelCommitOrder();
        ChannelRequestOrderEntity requestOrder = transactionBO.getChannelRequestOrder();
        ChannelCommitOrderEntity quoteCommitOrder = transactionBO.getQuoteCommitOrder();
        // 更新请求单提交单前置业务处理
        TransactionRetryBO retryBO = RetryUtils.customBeforeBusiness( transactionBO.getTransactionRetryBO(), requestOrder, commitOrder);
        if (Objects.isNull(commitOrder) || OrderStatusEnum.isFinalStatus(commitOrder.getStatus())) {
            transactionBO.setOrderCompensate(null);
        }
        super.afterBusiness(bizContext);
        // 更新请求单提交单后置业务处理
        RetryUtils.customAfterBusiness(retryBO, requestOrder, commitOrder, quoteCommitOrder.getChannelCode(), quoteCommitOrder.getChannelMethodCode());
        // 获取最新的out
        response = bizContext.getOut();
        // 修复异常后响应状态为空的问题
        if (Objects.isNull(response)) {
            response = this.initOut();
            response.setStatus(OrderStatusEnum.getOrderStatusByStatus(requestOrder.getStatus()));
            response.setChannelPayRequestNo(requestOrder.getChannelPayRequestNo());
            response.setBizOrderNo(requestOrder.getBizOrderNo());
            bizContext.setOut(response);
        }
        // 重试类型填充
        if (Objects.nonNull(retryBO)) {
            response.setNextRetryType(retryBO.getRetryType().name());
        }
    }

    @Override
    protected void fillCustomOut(BizContextBO<RefundRequestOrderDO, RefundResponseOrderDO> bizContext) {
        TransactionBO transactionBO = getTransactionBO(bizContext);
        RefundResponseOrderDO refundResponseOrderDO = bizContext.getOut();
        ChannelRequestOrderEntity channelRequestOrder = transactionBO.getChannelRequestOrder();
        ChannelCommitOrderEntity channelCommitOrder = transactionBO.getChannelCommitOrder();
        if (refundResponseOrderDO != null) {
            refundResponseOrderDO.setStatus(OrderStatusEnum.getOrderStatusByStatus(channelRequestOrder.getStatus()));
            // 差错标识
            if (Objects.nonNull(channelCommitOrder) && Objects.equals(channelCommitOrder.getChannelCode(), ChannelCodeConstants.CORRECTION_CENTER)) {
                refundResponseOrderDO.setCorrectionLogo(CommonConstants.CHANNEL_CORRECTION_HANDLE);
            }
        } else {
            refundResponseOrderDO = ObjectOperateUtils.buildRefundResponseOrderDO(channelRequestOrder, channelCommitOrder);
            // 使用请求单状态
            refundResponseOrderDO.setStatus(OrderStatusEnum.getOrderStatusByStatus(channelRequestOrder.getStatus()));
            bizContext.setOut(refundResponseOrderDO);
        }
        // 设置同步扩展返回数据
        Map syncExtResultParams = ObjectOperateUtils.buildExtResultParams(channelCommitOrder, null, null, true);
        if (null != syncExtResultParams) {
            if (null == refundResponseOrderDO.getExtendResult()) {
                refundResponseOrderDO.setExtendResult(syncExtResultParams);
            } else {
                refundResponseOrderDO.getExtendResult().putAll(syncExtResultParams);
            }
        }
        super.fillCustomOut(bizContext);

    }

    @Override
    protected void channelRouting(BizContextBO<RefundRequestOrderDO, RefundResponseOrderDO> bizContext) {
        // 判断退款接口是否在可用时间
        TransactionBO transactionBO = getTransactionBO(bizContext);
        RefundRequestOrderDO requestOrderDO = bizContext.getIn();
        // 调用撤销跳过
        if (requestOrderDO.getIsRefundToCancel()) {
            return;
        }

        // 退款接口没有时间限制
        String allowRefundTimeLimit = null;
        PaymentMethodConfigJsonBO methodConfigJsonBO = transactionBO.getPaymentMethodConfigJson();
        ChannelConfigJsonBO channelConfigJson = transactionBO.getChannelConfigJson();
        if (Objects.nonNull(methodConfigJsonBO) && StringUtils.isNotBlank(methodConfigJsonBO.getAllowRefundTimeLimit())) {
            allowRefundTimeLimit = methodConfigJsonBO.getAllowRefundTimeLimit();
        } else if (Objects.nonNull(channelConfigJson) && StringUtils.isNotBlank(channelConfigJson.getAllowRefundTimeLimit())) {
            allowRefundTimeLimit = channelConfigJson.getAllowRefundTimeLimit();
        }

        if(StringUtils.isBlank(allowRefundTimeLimit)) {
            return;
        }
        
        Date retryTime = OrderCompensateUtils.calculateApiNextRetryAllowTime(allowRefundTimeLimit);
        if (Objects.nonNull(retryTime)) {
            // 需要重试
            transactionBO.setTransactionRetryBO(new TransactionRetryBO(retryTime, RetryTypeEnum.ABNORMAL_APPLY, ErrorCodeEnum.NOT_REFUND_API_AVAILABLE_TIME.getCode(), refundTimeDesc + "("+ allowRefundTimeLimit +")"));
        }
    }

    @Override
    protected ResultDTO<RefundResponseOrderDO> exchange(BizContextBO<RefundRequestOrderDO, RefundResponseOrderDO> bizContext) {
        TransactionBO transactionBO = getTransactionBO(bizContext);
        TransactionRetryBO transactionRetryBO = transactionBO.getTransactionRetryBO();

        if (Objects.isNull(transactionRetryBO)) {
            return super.exchange(bizContext);
        }
        return ResultDTO.buildSuccess();
    }

    @Override
    protected ExchangeTemplate<RefundRequestOrderDO, RefundResponseOrderDO> getExchangeTemplate(ApiModeEnum apiModeEnum) {
        switch (apiModeEnum) {
            case PAST_FRONT:
            case CHANNEL_GATEWAY:
                return refundFrontExchangeTemplate;
            case CORREC_CENTER:
                return correctionPushExchangeTemplate;
            case PAST_CHANNEL:
            case NEW_CHANNEL:
            default:
                return null;
        }
    }

    @Override
    protected void checkAndFillAfterRouting(BizContextBO<RefundRequestOrderDO, RefundResponseOrderDO> bizContext) {
        TransactionBO transactionBO = getTransactionBO(bizContext);
        String channelMethodCode = transactionBO.getChannelMethodCode();

        switch (transactionBO.getChannelCode()) {
            case ChannelCodeConstants.CORRECTION_CENTER:
                transactionBO.setApiModeEnum(ApiModeEnum.CORREC_CENTER);
                transactionBO.setPaymentMethodConfigJson(null);
                break;
            default:
                transactionBO.setApiModeEnum(ApiModeEnum.PAST_FRONT);
                // 查询渠道能力配置信息
                ChannelMethodEntity channelConfig = channelMethodService.getByMethodCodeIgnoreStatus(channelMethodCode);
                if (channelConfig != null && StringUtils.isNotBlank(channelConfig.getConfigJson())) {
                    PaymentMethodConfigJsonBO paymentMethodConfigJson = JsonUtils.toBean(PaymentMethodConfigJsonBO.class, channelConfig.getConfigJson());
                    transactionBO.setPaymentMethodConfigJson(paymentMethodConfigJson);
                    if (StringUtils.isNotBlank(paymentMethodConfigJson.getApiModeEnum())) {
                        transactionBO.setApiModeEnum(ApiModeEnum.valueOf(paymentMethodConfigJson.getApiModeEnum()));
                    }
                }
                transactionBO.setChannelConfigEntity(channelConfig);
        }

        // 查询渠道信息
        ChannelInfoEntity channelInfo = channelInfoService.getByChannelCodeIgnoreStatus(transactionBO.getChannelCode());
        if (channelInfo != null && StringUtils.isNotBlank(channelInfo.getConfigJson())) {
            ChannelConfigJsonBO channelConfigJson = JsonUtils.toBean(ChannelConfigJsonBO.class, channelInfo.getConfigJson());
            transactionBO.setChannelConfigJson(channelConfigJson);
        }
        transactionBO.setChannelInfoEntity(channelInfo);

        // 如果是调用front模式，则查询老系统映射数据
        if (ApiModeEnum.PAST_FRONT == transactionBO.getApiModeEnum()) {
            ChannelConfigMappingEntity channelConfigMapping = channelConfigMappingService.getByConfigCode(channelMethodCode);
            transactionBO.setChannelConfigMappingEntity(channelConfigMapping);
        }
    }

    @Override
    protected RefundResponseOrderDO initOut() {
        return new RefundResponseOrderDO();
    }

    @Override
    protected RefundResponseOrderDO buildErrorOut(BizContextBO<RefundRequestOrderDO, RefundResponseOrderDO> bizContext) {
        // 异常情况下释放锁
        this.releaseLock(bizContext);
        // 构建异常响应报文
        return super.buildErrorOut(bizContext);
    }

    /**
     * 是否允许退款校验
     *
     * @param channelMethod
     * @param oriRequestOrder
     * @param request
     */
    private void allowRefundCheck(TransactionBO transactionBO, ChannelMethodEntity channelMethod, ChannelRequestOrderEntity oriRequestOrder, RefundRequestOrderDO request) {
        if (channelMethod == null) {
            throw new BusinessException(ErrorCodeEnum.PAYMENT_METHOD_CONFIG_ERROR.getCode(), ErrorCodeEnum.PAYMENT_METHOD_CONFIG_ERROR.getMsg());
        }
        // 渠道不支持退款 且 不支持撤销
        if ((!ShareConstants.NUMBER_ONE.equals(channelMethod.getIsSupportRefund())) && (!ShareConstants.NUMBER_ONE.equals(channelMethod.getIsSupportCancel()))) {
            throw new BusinessException(ErrorCodeEnum.CHANNEL_NOT_SUPPORT_REFUND.getCode(), "refund not support");
        }
        PaymentMethodConfigJsonBO paymentMethodConfigJson = transactionBO.getPaymentMethodConfigJson();
        ChannelConfigJsonBO channelConfigJson = transactionBO.getChannelConfigJson();
        ChannelCommitOrderEntity quoteCommitOrder = transactionBO.getQuoteCommitOrder();
        // 退款金额
        Money refundAmount = request.getAmount();
        BigDecimal refundAmountVal = refundAmount.getValue();
        String refundAmountCurrency = refundAmount.getCurrency();
        // 原支付金额
        BigDecimal oriPayAmount = oriRequestOrder.getAmount();
        String oriAmountCurrency = oriRequestOrder.getCurrency();
        // 币种不一致
        if (!refundAmountCurrency.equals(oriAmountCurrency)) {
            throw new BusinessException(ErrorCodeEnum.CURRENCY_NOT_MATCH.getCode(), ErrorCodeEnum.CURRENCY_NOT_MATCH.getMsg());
        }
        // 金额超限
        if (refundAmountVal.compareTo(oriPayAmount) > 0) {
            throw new BusinessException(ErrorCodeEnum.REFUND_AMOUNT_EXCEED.getCode(), ErrorCodeEnum.REFUND_AMOUNT_EXCEED.getMsg());
        }
        // 退款时间在退款时效内(不支持重试)
        boolean inRefundPeriod = this.inRefundPeriod(channelMethod, paymentMethodConfigJson, quoteCommitOrder);
        if (!inRefundPeriod) {
            throw new BusinessException(ErrorCodeEnum.EXCEED_REFUNDABLE_TIME.getCode(), ErrorCodeEnum.EXCEED_REFUNDABLE_TIME.getMsg());
        }
        // 部分退款 且 渠道支持部分退款(不支持重试)
        if (isOpenPartial && refundAmountVal.compareTo(oriPayAmount) < 0 && !ShareConstants.NUMBER_ONE.equals(channelMethod.getIsPartialRefund())) {
            throw new BusinessException(ErrorCodeEnum.NOT_SUPPORT_PARTIAL_REFUND.getCode(), ErrorCodeEnum.NOT_SUPPORT_PARTIAL_REFUND.getMsg());
        }

        String oriChannelPayRequestNo = oriRequestOrder.getChannelPayRequestNo();
        // 设置分布式锁，防止同时多笔退款并发过来
        String lockKey = this.buildLockKey(oriChannelPayRequestNo);
        boolean lockResult = LockUtils.lock(lockKey, lockExpireSeconds, TimeUnit.SECONDS);
        // 获取锁失败，则抛出异常
        if (!lockResult) {
            throw new BusinessException(ErrorCodeEnum.EXCEED_RATE_LIMIT.getCode(), ErrorCodeEnum.EXCEED_RATE_LIMIT.getMsg());
        }
        // 剩余可退款金额校验
        // 查询此支付单下的退款单
        List<ChannelRequestOrderEntity> refundOrderList = channelRequestOrderService.getByQuoteOrderNoAndTypeFromMaster(
                oriChannelPayRequestNo, MainRequestTypeEnum.TRANSACTION.getValue(), PaymentTypeEnum.REFUND.getValue());
        List<ChannelRequestOrderEntity> refundNotFailedOrder = new ArrayList<>(refundOrderList.size());
        if (CollectionUtils.isNotEmpty(refundOrderList)) {
            // 筛选出非失败 且 去除和请求单号相同的订单（相同单号请求多次，依靠后续幂等）
            refundNotFailedOrder = refundOrderList.stream()
                    .filter(t -> !OrderStatusEnum.FAILED.getStatus().equals(t.getStatus()))
                    .filter(t -> !t.getBizOrderNo().equals(request.getBizOrderNo()))
                    .collect(Collectors.toList());
            // 计算退款总金额
            BigDecimal refundTotalAmount = refundNotFailedOrder.stream()
                    .map(ChannelRequestOrderEntity::getAmount)
                    .reduce(refundAmountVal, BigDecimal::add);
            refundTotalAmount = AmountUtils.toScale(refundTotalAmount);
            // 退款金额校验：退款总金额大于支付金额，则拒绝
            if (refundTotalAmount.compareTo(oriPayAmount) > 0) {
                throw new BusinessException(ErrorCodeEnum.REFUND_AMOUNT_EXCEED.getCode(), ErrorCodeEnum.REFUND_AMOUNT_EXCEED.getMsg());
            }
        }

        // 全额退款 且 支持撤销 且 在撤销时效内
        boolean refund2Cancel = this.isRefund2Cancel(channelMethod, paymentMethodConfigJson, quoteCommitOrder, request);
        request.setIsRefundToCancel(refund2Cancel);
        if (refund2Cancel) {
            return;
        }

        // 获取同一提交单允许退款次数
        Integer allowRefundTimes = Objects.nonNull(paymentMethodConfigJson) && Objects.nonNull(paymentMethodConfigJson.getAllowRefundTimes()) ? Integer.valueOf(paymentMethodConfigJson.getAllowRefundTimes()) : null;
        if (Objects.isNull(allowRefundTimes)) {
            allowRefundTimes = Objects.nonNull(channelConfigJson) && Objects.nonNull(channelConfigJson.getAllowRefundTimes()) ? Integer.valueOf(channelConfigJson.getAllowRefundTimes()) : Integer.MAX_VALUE;
        }

        // 多次退款校验
        if (refundNotFailedOrder.size() >= allowRefundTimes) {
            // 查询原支付渠道退款非失败的提交单数量
            List<String> channelRequestNoList = refundNotFailedOrder.stream().map(ChannelRequestOrderEntity::getChannelPayRequestNo).collect(Collectors.toList());
            ArrayList<Integer> statusList = new ArrayList<>();
            statusList.add(OrderStatusEnum.SUCCESS.getStatus());
            statusList.add(OrderStatusEnum.PENDING.getStatus());
            statusList.add(OrderStatusEnum.INITIATE.getStatus());
            ChannelCommitOrderEntityQuery entityQuery = new ChannelCommitOrderEntityQuery();
            entityQuery.setPair(ShardingUtils.shardingRequestOrderNo(oriChannelPayRequestNo, null));
            entityQuery.setChannelRequestNoList(channelRequestNoList);
            entityQuery.setMainRequestType(MainRequestTypeEnum.TRANSACTION.getValue());
            entityQuery.setPaymentType(PaymentTypeEnum.REFUND.getValue());
            entityQuery.setStatusList(statusList);
            entityQuery.setChannelCode(quoteCommitOrder.getChannelCode());
            List<ChannelCommitOrderEntity> refundCommitOrder = channelCommitOrderService.getByEntityQueryFromMaster(entityQuery);
            // 原渠道退款成功次数为0  || 原渠道退款成功次小于渠道允许退款次数（可以正常调用外部渠道退款）
            if (CollectionUtils.isEmpty(refundCommitOrder) || refundCommitOrder.size() < allowRefundTimes) {
                return;
            }
            throw new BusinessException(ErrorCodeEnum.NOT_SUPPORT_MUL_REFUND.getCode(), "the number of payment order refunds has reached the upper limit of the channel");
        }

    }

    /**
     * 构建分布式锁key
     * *
     * @param oriChannelPayRequestNo
     * @return
     */
    private String buildLockKey(String oriChannelPayRequestNo) {
        return LockUtils.mergeKey(LockUtils.REQUEST, oriChannelPayRequestNo);
    }

    /**
     * 释放分布式锁
     *
     * @param bizContext
     */
    private void releaseLock(BizContextBO<RefundRequestOrderDO, RefundResponseOrderDO> bizContext) {
        try {
            TransactionBO transactionBO = getTransactionBO(bizContext);
            ChannelRequestOrderEntity quoteRequestOrder = transactionBO.getQuoteRequestOrder();
            // 释放分布式锁
            String lockKey = this.buildLockKey(quoteRequestOrder.getChannelPayRequestNo());
            LockUtils.releaseLock(lockKey);
        } catch (Exception e) {
            log.error("Redisson lock release error.", e);
        }
    }


    /**
     * 是否可以撤销
     *
     * @param paymentMethodConfigJson
     * @param origCommitOrder
     * @return
     */
    private boolean isRefund2Cancel(ChannelMethodEntity channelMethod, PaymentMethodConfigJsonBO paymentMethodConfigJson, ChannelCommitOrderEntity origCommitOrder, RefundRequestOrderDO request) {
        try {
            // 退款金额
            Money refundAmount = request.getAmount();
            BigDecimal refundAmountVal = refundAmount.getValue();
            // 原支付金额
            BigDecimal oriPayAmount = origCommitOrder.getAmount();
            if (refundAmountVal.compareTo(oriPayAmount) != 0 || !ShareConstants.NUMBER_ONE.equals(channelMethod.getIsSupportCancel())) {
                return false;
            }
            if (paymentMethodConfigJson == null) {
                return true;
            }
            String cancelTimeZone = paymentMethodConfigJson.getCancelTimeZone();
            String cancelCutTimeStr = paymentMethodConfigJson.getCancelCutTime();
            Integer cancelCancelDays = Integer.valueOf(paymentMethodConfigJson.getCancelCancelDays());

            return inCancelOrRefundPeriod(cancelTimeZone, cancelCutTimeStr, cancelCancelDays, origCommitOrder.getCreateTime());
        } catch (Exception e) {
            log.error("<<<==== [isRefund2Cancel], error:", e);
            return false;
        }
    }

    /**
     * 是否在退款时效内
     *
     * @param paymentMethodConfigJson
     * @param origCommitOrder
     * @return
     */
    private boolean inRefundPeriod(ChannelMethodEntity channelMethod, PaymentMethodConfigJsonBO paymentMethodConfigJson, ChannelCommitOrderEntity origCommitOrder) {
        try {
            if (paymentMethodConfigJson == null) {
                return true;
            }
            String refundTimeZone = paymentMethodConfigJson.getRefundTimeZone();
            String refundCutTimeStr = paymentMethodConfigJson.getRefundCutTime();
            Integer validRefundDays = channelMethod.getValidRefundTime();

            return inCancelOrRefundPeriod(refundTimeZone, refundCutTimeStr, validRefundDays, origCommitOrder.getCreateTime());
        } catch (Exception e) {
            log.error("<<<==== [inRefundPeriod], error:", e);
            return false;
        }
    }

    /**
     * 是否在撤销/退款时效内
     *
     * @param timeZone
     * @param cutTimeStr
     * @param delayDays
     * @return
     */
    private boolean inCancelOrRefundPeriod(String timeZone, String cutTimeStr, Integer delayDays, Date createTime) {
        if (StringUtils.isBlank(timeZone) || Objects.isNull(delayDays)) {
            return true;
        }
        // 根据时区获取原订单创建时间
        LocalDateTime createDateTime = LocalDateTime.ofInstant(createTime.toInstant(), ZoneId.of(timeZone));
        // 计算允许撤销/退款时间
        LocalDateTime tempCutoffDateTime = createDateTime;
        if (StringUtils.isNotBlank(cutTimeStr)) {
            String[] time = cutTimeStr.split(":");
            tempCutoffDateTime = createDateTime.withHour(Integer.valueOf(time[0])).withMinute(Integer.valueOf(time[1])).withSecond(0);
        }
        LocalDateTime cutoffDateTime = tempCutoffDateTime.plusDays(delayDays);
        // 撤销/退款时间和当前时间比较
        return cutoffDateTime.isAfter(LocalDateTime.now(ZoneId.of(timeZone)));
    }
}
