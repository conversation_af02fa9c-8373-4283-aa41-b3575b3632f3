package com.payermax.fin.exchange.domainservice;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.fin.exchange.common.enums.*;
import com.payermax.fin.exchange.dal.entity.*;
import com.payermax.fin.exchange.domain.common.CommonGateRequestDO;
import com.payermax.fin.exchange.domain.common.CommonGateResponseDO;
import com.payermax.fin.exchange.domainservice.bo.BizContextBO;
import com.payermax.fin.exchange.domainservice.bo.ChannelConfigJsonBO;
import com.payermax.fin.exchange.domainservice.bo.TransactionBO;
import com.payermax.fin.exchange.domainservice.expression.ExpressionUtils;
import com.payermax.fin.exchange.domainservice.repository.*;
import com.payermax.fin.exchange.domainservice.rpcproxy.ChannelInternalClientProxy;
import com.payermax.fin.exchange.domainservice.rule.channelgatewaygary.ChannelGatewayGrayExecutor;
import com.payermax.fin.exchange.domainservice.service.orderNo.IOrderNoService;
import com.payermax.fin.exchange.domainservice.state.StateMachineExecutor;
import com.payermax.fin.exchange.domainservice.state.StateRequest;
import com.payermax.fin.exchange.integration.rpc.request.CommonGateFrontRequest;
import com.payermax.fin.exchange.integration.rpc.request.ForexFrontRequest;
import com.payermax.fin.exchange.integration.rpc.response.CommonGateFrontResponse;
import com.payermax.fin.exchange.integration.rpc.response.ResultDTO;
import com.payermax.common.lang.util.JsonUtils;
import com.payermax.fin.exchange.share.domain.ChannelInstanceDO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;

import java.util.*;

import static java.util.Objects.isNull;

/**
 * 通用网关领域服务（服务转发）
 *
 * <AUTHOR>
 * @date 2022/5/26 22:01
 */
public abstract class AbstractGateDomainService<IN extends CommonGateRequestDO, OUT extends CommonGateResponseDO> extends AbstractDomainService<IN, OUT> {

    /**
     * 需要忽略的请求字段
     */
    private static final String[] ignoreReqFields = new String[]{"channelCode", "requestNo", "channelOrderNo", "extendProperties"};
    /**
     * 需要忽略的响应字段
     */
    private static final String[] ignoreRespFields = new String[]{"channelCode", "channelOrderNo", "status", "channelRespCode", "channelRespMsg", "pending", "failed", "success"};

    @Autowired
    protected IChannelInfoService channelInfoService;

    @Autowired
    protected IChannelRequestOrderService channelRequestOrderService;

    @Autowired
    protected IChannelCommitOrderService channelCommitOrderService;

    @Autowired
    protected IChannelResultOrderService channelResultOrderService;

    @Autowired
    protected IOrderNoService orderNoService;

    @Autowired
    protected ChannelInternalClientProxy channelInternalClientProxy;

    @Autowired
    protected StateMachineExecutor stateMachineExecutor;

    @Autowired
    protected IChannelStepApiMappingService channelStepApiMappingService;

    @Autowired
    protected IChannelConfigMappingService channelConfigMappingService;

    @Override
    protected void beforeBusiness(BizContextBO<IN, OUT> bizContext) {
        CommonGateRequestDO commonGateRequestDO = bizContext.getIn();
        // 渠道编码不存在，则返回
        String channelCode = commonGateRequestDO.getChannelCode();
        if (StringUtils.isBlank(channelCode)) {
            throw new BusinessException(ErrorCodeEnum.NO_CHANNEL_SUPPORT.getCode(), "no channel support");
        }
        //判断渠道是否可用
        ChannelInfoEntity channelInfo = channelInfoService.getByChannelCode(channelCode);
        if (isNull(channelInfo)) {
            throw new BusinessException(ErrorCodeEnum.NO_CHANNEL_SUPPORT.getCode(), "no channel support");
        }
    }

    @Override
    protected void executeBusiness(BizContextBO<IN, OUT> bizContext) throws Exception {
        IN requestDO = bizContext.getIn();
        // 设置上下文中间数据对象
        TransactionBO transactionBO = getTransactionBO(bizContext);
        bizContext.setBaseBo(transactionBO);
        String channelCode = requestDO.getChannelCode();
        //1.根据渠道和请求类型查询映射表信息
        List<ChannelStepApiMappingEntity> channelStepApiMappingList = channelStepApiMappingService.getStepApiMappingByCode(channelCode, null, requestDO.getRequestType());
        ChannelStepApiMappingEntity channelStepApiMappingEntity = null;
        if (CollectionUtil.isNotEmpty(channelStepApiMappingList)) {
            channelStepApiMappingEntity = channelStepApiMappingList.get(0);//NO_CHECK
        }
        if (channelStepApiMappingEntity == null) {
            throw new BusinessException(ErrorCodeEnum.PARAMETER_INVALID.getCode(), "The channel not support the requestType");
        }
        //2.落渠道请求单
        ChannelRequestOrderEntity requestOrder = createChannelRequestOrder(bizContext, channelStepApiMappingEntity);
        if (requestOrder == null) {
            return;
        }
        transactionBO.setChannelRequestOrder(requestOrder);

        //2.4 筛选渠道支付实例
        ChannelInstanceDO channelInstance = routeChannelInstance(bizContext);
        //2.5 路由渠道MID
        String channelMerchantCode = routeChannelMid(bizContext, channelInstance);
        //3.生成渠道提交单号
        String commitOrderNo = orderNoService.genCommitNoByRequestNoAndCode(requestOrder.getChannelPayRequestNo(), OrderNoPrefixEnum.GATE_COMMIT, null, channelCode, true);
        //4.构建请求对象
        Map request = buildExchangeRequest(requestDO, channelStepApiMappingEntity, commitOrderNo, channelMerchantCode,
            null == channelInstance ? null : channelInstance.getChannelMethodCode());
        //5.落渠道提交单
        ChannelCommitOrderEntity channelCommitOrder = createChannelCommitOrder(requestOrder, request, commitOrderNo, channelCode, channelMerchantCode);
        transactionBO.setChannelCommitOrder(channelCommitOrder);

        //6.请求下游
        ApiModeEnum apiModeEnum = ApiModeEnum.getApiModeEnum(channelStepApiMappingEntity.getApiMode());
        ResultDTO internalResultDTO = invokeFrontInterface(request, channelCode, channelStepApiMappingEntity.getBackendApiUrl(), apiModeEnum);
        //7.组装通用响应
        Map resultMap = (Map) internalResultDTO.getData();
        CommonGateFrontResponse commonGateFrontResponse = buildCommonResponse(resultMap);
        //8.新增渠道结果单
        createChannelResultOrder(channelCommitOrder, internalResultDTO, commonGateFrontResponse);
        //9.更新请求单与提交单
        updateRequestAndCommitOrder(requestOrder, channelCommitOrder, commonGateFrontResponse, internalResultDTO);
        //10.解析响应报文
        CommonGateResponseDO response = converResponsetDO(commonGateFrontResponse, requestOrder, channelCommitOrder, channelStepApiMappingEntity.getResultMapping());
        bizContext.setOut((OUT) response);
    }

    /**
     * 根据请求单号进行幂等性校验
     *
     * @param requestOrder
     * @param resultMapping
     * @return
     */
    private CommonGateResponseDO checkByRequestNo(ChannelRequestOrderEntity requestOrder, String resultMapping) {
        CommonGateFrontResponse commonGateFrontResponse = new CommonGateFrontResponse();
        //查询请求单信息
        ChannelRequestOrderEntity channelRequestOrderEntity = channelRequestOrderService.getByRequestOrderNoFromMaster(requestOrder.getChannelPayRequestNo());
        //查询提交单信息
        ChannelCommitOrderEntity channelCommitOrderEntity = channelCommitOrderService.getByRequestNoAndTypeFromMaster(requestOrder.getChannelPayRequestNo(), requestOrder.getMainRequestType(), requestOrder.getPaymentType());
        if (channelCommitOrderEntity != null) {
            //查询结果单信息
            ChannelResultOrderEntity channelResultOrderEntity = channelResultOrderService.getByChannelOrderNoFromMaster(channelCommitOrderEntity.getChannelPayCommitNo(), channelCommitOrderEntity.getChannelPayRequestNo());
            if (channelResultOrderEntity != null) {
                ResultDTO internalResultDTO = JsonUtils.toBean(ResultDTO.class, channelResultOrderEntity.getResponseBody());
                Map resultMap = (Map) internalResultDTO.getData();
                commonGateFrontResponse = buildCommonResponse(resultMap);
            }
        }
        //响应转换
        return converResponsetDO(commonGateFrontResponse, channelRequestOrderEntity, channelCommitOrderEntity, resultMapping);
    }

    /**
     * 构建渠道请求单*
     * *
     * @param bizContext
     * @param stepApiMappingEntity
     * @return
     */
    protected ChannelRequestOrderEntity buildChannelRequestOrder(BizContextBO<IN, OUT> bizContext, ChannelStepApiMappingEntity stepApiMappingEntity) {
        CommonGateRequestDO request = bizContext.getIn();

        ChannelRequestOrderEntity channelRequestOrder = new ChannelRequestOrderEntity();
        channelRequestOrder.setChannelPayRequestNo(request.getBizOrderNo());
        channelRequestOrder.setBizOrderNo(request.getBizOrderNo());
        if (StringUtils.isNotBlank(request.getMainRequestType())) {
            channelRequestOrder.setMainRequestType(request.getMainRequestType());
        } else {
            channelRequestOrder.setMainRequestType(stepApiMappingEntity.getStepType());
        }
        channelRequestOrder.setRequestType(request.getRequestType());
        channelRequestOrder.setPaymentType(request.getPaymentType());
        channelRequestOrder.setStatus(OrderStatusEnum.INITIATE.getStatus());
        channelRequestOrder.setRequestBody(JsonUtils.toJson(request));
        channelRequestOrder.setServiceMode(request.getServiceMode());
        channelRequestOrder.setServiceEntity(request.getServiceEntity());
        return channelRequestOrder;
    }

    /**
     * 创建渠道请求单
     *
     * @param bizContext
     * @param stepApiMappingEntity
     * @return
     */
    private ChannelRequestOrderEntity createChannelRequestOrder(BizContextBO<IN, OUT> bizContext, ChannelStepApiMappingEntity stepApiMappingEntity) {
        // 构建渠道请求单
        ChannelRequestOrderEntity channelRequestOrder = this.buildChannelRequestOrder(bizContext, stepApiMappingEntity);
        try {
            // 新增渠道请求单
            channelRequestOrderService.add(channelRequestOrder);
        } catch (DuplicateKeyException e) {
            log.warn("order no already exist，channelPayRequestNo：{}", channelRequestOrder.getChannelPayRequestNo());
            //如果请求单已存在，则进行查询返回信息
            bizContext.setOut((OUT) checkByRequestNo(channelRequestOrder, stepApiMappingEntity.getResultMapping()));
            return null;
        }
        return channelRequestOrder;
    }

    /**
     * 路由渠道MID*
     * *
     * @param bizContext
     * @return
     */
    protected String routeChannelMid(BizContextBO<IN, OUT> bizContext, ChannelInstanceDO channelInstance) {
        return null;
    }

    /**
     * 请求参数转换
     *
     * @param requestDO
     * @param channelStepApiMappingEntity
     * @param commitOrderNo
     * @return
     */
    private Map buildExchangeRequest(IN requestDO, ChannelStepApiMappingEntity channelStepApiMappingEntity, String commitOrderNo,
        String channelMerchantCode, String channelMethodCode) {
        CommonGateFrontRequest commonGateFrontRequest = new CommonGateFrontRequest();
        //新老渠道编码转换
        ChannelConfigMappingEntity channelConfigMappingEntity = channelConfigMappingService.getByChannelCode(requestDO.getChannelCode());
        if (channelConfigMappingEntity != null) {
            commonGateFrontRequest.setChannelCode(channelConfigMappingEntity.getOldChannelCode());
        } else {
            ChannelConfigJsonBO channelConfigJson = channelInfoService.getConfigJsonByChannelCode(requestDO.getChannelCode());
            commonGateFrontRequest.setTargetService(channelConfigJson.getTargetService());
        }
        commonGateFrontRequest.setNewChannelCode(requestDO.getChannelCode());
        commonGateFrontRequest.setChannelOrderNo(commitOrderNo);
        commonGateFrontRequest.setChannelMerchantCode(channelMerchantCode);
        commonGateFrontRequest.setChannelMethodCode(channelMethodCode);

        // 执行定制化请求内容钩子
        customizeCommonGateFrontRequest(requestDO, commonGateFrontRequest);

        //创建请求对象
        Map resultMap = JsonUtils.toBean(Map.class, JSON.toJSONString(commonGateFrontRequest));
        Map<String, Object> data = requestDO.getData();
        if (data != null) {
            //剔除不对外暴露的请求参数
            deleteMapData(data, ignoreReqFields);
            //todo 待改造
            //处理特殊请求参数
            handleSpecialParam(data, requestDO);
            resultMap.putAll(data);
        }
        Map<String, String> tmpParamsMapping = null;
        if (StringUtils.isNotBlank(channelStepApiMappingEntity.getParamsMapping())) {
            tmpParamsMapping = JsonUtils.toBean(Map.class, channelStepApiMappingEntity.getParamsMapping());
        }
        // 根据配置进行参数映射
        if (tmpParamsMapping != null && !tmpParamsMapping.isEmpty()) {
            tmpParamsMapping.forEach((tempField, valKey) -> {
                try {
                    Object value = ExpressionUtils.execute(valKey, data);
                    if (value == null) {
                        return;
                    }
                    resultMap.put(tempField, value);
                } catch (Exception e) {
                    log.warn("采集参数(valKey:{}, Field:{})映射到Front系统入参错误，需排查！", valKey, tempField, e);
                }
            });
        }
        return resultMap;
    }

    /**
     * 创建渠道提交单
     *
     * @param requestOrder
     * @param request
     * @param commitOrderNo
     * @param channelCode
     * @return
     */
    private ChannelCommitOrderEntity createChannelCommitOrder(ChannelRequestOrderEntity requestOrder, Map request, String commitOrderNo, String channelCode, String channelMerchantCode) {
        ChannelCommitOrderEntity commitOrder = new ChannelCommitOrderEntity();
        commitOrder.setChannelCode(channelCode);
        commitOrder.setChannelMerchantCode(channelMerchantCode);
        commitOrder.setChannelPayCommitNo(commitOrderNo);
        commitOrder.setBizOrderNo(requestOrder.getBizOrderNo());
        commitOrder.setChannelPayRequestNo(requestOrder.getChannelPayRequestNo());
        commitOrder.setMainRequestType(requestOrder.getMainRequestType());
        commitOrder.setRequestType(requestOrder.getRequestType());
        commitOrder.setPaymentType(requestOrder.getPaymentType());
        commitOrder.setStatus(OrderStatusEnum.INITIATE.getStatus());
        commitOrder.setRequestBody(JsonUtils.toJson(request));
        commitOrder.setCountry(requestOrder.getCountry());
        commitOrder.setCurrency(requestOrder.getCurrency());
        commitOrder.setServiceMode(requestOrder.getServiceMode());
        commitOrder.setServiceEntity(requestOrder.getServiceEntity());
        channelCommitOrderService.add(commitOrder);
        //toco:czy request 塞入灰度标签
        ChannelGatewayGrayExecutor.setRequestBodyChannelGatewayLabel(commitOrder,request);
        return commitOrder;
    }

    /**
     * 调用下游接口
     *
     * @param request
     * @param channelCode
     * @param internalUrl
     * @return
     */
    private ResultDTO invokeFrontInterface(Map request, String channelCode, String internalUrl, ApiModeEnum apiModeEnum) {
        ResultDTO internalResultDTO;
        try {
            if (apiModeEnum == null) {
                apiModeEnum = ApiModeEnum.PAST_FRONT;
            }
            // 发起调用
            internalResultDTO = channelInternalClientProxy.signAndSend(apiModeEnum, channelCode, internalUrl, request, Map.class);
        } catch (Throwable e) {
            // 响应异常的处理
            internalResultDTO = new ResultDTO();
            if (e instanceof BusinessException) {
                BusinessException baseException = (BusinessException) e;
                internalResultDTO.setBizCode(baseException.getErrCode());
            } else {
                internalResultDTO.setBizCode(ErrorCodeEnum.INNER_ERROR.getCode());
                log.error("通用网关查询请求front出现异常, Exception: ", e);
            }
            internalResultDTO.setMessage(e.getMessage());
        }
        return internalResultDTO;
    }

    /**
     * 构建通用响应
     *
     * @param resultMap
     */
    private CommonGateFrontResponse buildCommonResponse(Map resultMap) {
        CommonGateFrontResponse commonGateFrontResponse = new CommonGateFrontResponse();
        // 对象转换
        if (resultMap != null) {
            commonGateFrontResponse = JsonUtils.toBean(CommonGateFrontResponse.class,
                    JsonUtils.toString(resultMap));
            commonGateFrontResponse.setData(resultMap);
        }
        return commonGateFrontResponse;
    }

    /**
     * 创建渠道结果单
     *
     * @param channelCommitOrder
     * @param internalResultDTO
     * @param commonGateFrontResponse
     */
    private ChannelResultOrderEntity createChannelResultOrder(ChannelCommitOrderEntity channelCommitOrder, ResultDTO internalResultDTO, CommonGateFrontResponse commonGateFrontResponse) {
        ChannelResultOrderEntity resultOrder = new ChannelResultOrderEntity();
        resultOrder.setChannelPayCommitNo(channelCommitOrder.getChannelPayCommitNo());
        resultOrder.setChannelPayRequestNo(channelCommitOrder.getChannelPayRequestNo());
        resultOrder.setResponseCode(commonGateFrontResponse.getChannelRespCode());
        resultOrder.setResponseMsg(commonGateFrontResponse.getChannelRespMsg());
        resultOrder.setResponseBody(JsonUtils.toString(internalResultDTO));
        try {
            //新增渠道结果单
            channelResultOrderService.add(resultOrder);
        } catch (Exception e) {
            log.error("add channel result order error. resultOrder = {}", JsonUtils.toString(resultOrder), e);
        }
        return resultOrder;
    }

    /**
     * 更新请求单与提交单
     *
     * @param requestOrderEntity
     * @param commitOrderEntity
     * @param commonGateFrontResponse
     * @param internalResultDTO
     */
    private void updateRequestAndCommitOrder(ChannelRequestOrderEntity requestOrderEntity, ChannelCommitOrderEntity commitOrderEntity, CommonGateFrontResponse commonGateFrontResponse, ResultDTO internalResultDTO) {
        Integer oriRequestStatus = requestOrderEntity.getStatus();
        Integer oriCommitStatus = commitOrderEntity.getStatus();

        //提交单赋值
        OrderStatusEnum orderStatusEnum = commonGateFrontResponse.getOrderStatus();
        commitOrderEntity.setStatus(orderStatusEnum.getStatus());
        commitOrderEntity.setMappingCode(internalResultDTO.getBizCode());
        commitOrderEntity.setMappingMsg(internalResultDTO.getMessage());
        commitOrderEntity.setResponseCode(commonGateFrontResponse.getChannelRespCode());
        commitOrderEntity.setResponseMsg(commonGateFrontResponse.getChannelRespMsg());
        if (StringUtils.isNotBlank(commonGateFrontResponse.getChannelTransactionId())) {
            commitOrderEntity.setThirdOrgOrderNo(commonGateFrontResponse.getChannelTransactionId());
        }

        //请求单赋值
        //判断是否是终态
        if (OrderStatusEnum.isFinalStatus(commitOrderEntity.getStatus())) {
            commitOrderEntity.setCompleteTime(new Date());
            requestOrderEntity.setStatus(commitOrderEntity.getStatus());
            requestOrderEntity.setCompleteTime(commitOrderEntity.getCompleteTime());
        } else {
            requestOrderEntity.setStatus(OrderStatusEnum.PENDING.getStatus());
        }
        requestOrderEntity.setMappingCode(commitOrderEntity.getMappingCode());
        requestOrderEntity.setMappingMsg(commitOrderEntity.getMappingMsg());
        try {
            StateRequest commitStateRequest = new StateRequest(oriCommitStatus, OrderEventEnum.APPLY, commitOrderEntity);
            StateRequest stateRequest = new StateRequest(oriRequestStatus, OrderEventEnum.APPLY, requestOrderEntity);
            // 更新渠道提交单和请求单
            stateMachineExecutor.changeCommitAndRequestState(commitStateRequest, stateRequest);
        }catch (Exception e) {
            log.error("Update order status to FAILED error.", e);
        }
    }


    /**
     * 响应参数转换
     *
     * @param commonGateFrontResponse
     * @param requestOrder
     * @param commitOrder
     * @param resultMapping
     * @return
     */
    private CommonGateResponseDO converResponsetDO(CommonGateFrontResponse commonGateFrontResponse, ChannelRequestOrderEntity requestOrder, ChannelCommitOrderEntity commitOrder, String resultMapping) {
        CommonGateResponseDO response = new CommonGateResponseDO();
        response.setBizOrderNo(requestOrder.getBizOrderNo());
        response.setMappingCode(requestOrder.getMappingCode());
        response.setMappingMsg(requestOrder.getMappingMsg());
        response.setStatus(OrderStatusEnum.getOrderStatusByStatus(requestOrder.getStatus()).name());
        if (commitOrder != null) {
            response.setChannelCode(commitOrder.getChannelCode());
            response.setChannelPayCommitNo(commitOrder.getChannelPayCommitNo());
        }
        if (null == commonGateFrontResponse) {
            return response;
        }
        Map<String, Object> extraInfoMap = new HashMap();
        Map resultMap = commonGateFrontResponse.getData();
        if (resultMap != null) {
            extraInfoMap.putAll(resultMap);
        }
        //剔除不对外暴露的响应参数
        deleteMapData(extraInfoMap, ignoreRespFields);
        // 如果结果映射字段为空，则返回
        if (StringUtils.isBlank(resultMapping)) {
            response.setData(extraInfoMap);
            return response;
        }
        Map<String, String> resultMappingMap = JsonUtils.toBean(Map.class, resultMapping);
        // 定义数据解析本地缓存Map
        Map<String, Object> resolveMapCache = new HashMap<>();
        resultMappingMap.forEach((key, val) -> {
            // 如果key或value为空，则忽略
            if (StringUtils.isBlank(key) || StringUtils.isBlank(val)) {
                return;
            }
            Object elementVal = null;
            // 如果value中存在逗号，说明值为多个
            elementVal = ExpressionUtils.execute(val, resultMap, resolveMapCache);
            if (elementVal != null) {
                extraInfoMap.put(key, elementVal);
            }
        });
        response.setData(extraInfoMap);
        return response;
    }

    /**
     * 剔除Map中的key
     *
     * @param extraInfoMap
     */
    private void deleteMapData(Map extraInfoMap, String[] ignoreFields) {
        if (extraInfoMap == null || ignoreFields == null) {
            return;
        }
        for (String ignoreRespField : ignoreFields) {
            extraInfoMap.remove(ignoreRespField);
        }
    }

    /**
     * 获取上下文中间数据对象
     *
     * @param bizContext
     * @return
     */
    protected TransactionBO getTransactionBO(BizContextBO<IN, OUT> bizContext) {
        Object baseBo = bizContext.getBaseBo();
        if (baseBo == null) {
            return new TransactionBO();
        }
        if (!(baseBo instanceof TransactionBO)) {
            throw new BusinessException(ErrorCodeEnum.INNER_ERROR.getCode(), "baseBo no instanceof TransactionBO");
        }
        return (TransactionBO) baseBo;
    }

    @Override
    protected void afterBusiness(BizContextBO<IN, OUT> bizContext) {

    }

    /**
     * 处理特殊请求参数
     *
     * @param requestParam
     */
    protected void handleSpecialParam(Map requestParam, IN requestDO) {
        if (requestParam == null) {
            return;
        }
        if (StringUtils.isBlank(requestDO.getRequestType()) || !(RequestTypeEnum.FOREX_TRADE.getCode().equals(requestDO.getRequestType()))) {
            return;
        }
        ForexFrontRequest.TransData transData = JSONObject.parseObject(JSONObject.toJSONString(requestParam), ForexFrontRequest.TransData.class);
        List<ForexFrontRequest.TransData> transDataList = new ArrayList<>();
        transDataList.add(transData);
        requestParam.put("transDatas", transDataList);
    }

    /**
     * <AUTHOR>
     * @Description 渠道支付实例路由
     * @Date 19:43 2024/1/23
     * @Param [bizContext]
     * @return com.payermax.fin.exchange.share.domain.ChannelInstanceDO
     */
    protected ChannelInstanceDO routeChannelInstance(BizContextBO<IN, OUT> bizContext) {
        return null;
    }

   /**
    * <AUTHOR>
    * @Description 定制化CommonGateFrontRequest内容
    * @Date 20:05 2024/1/23
    * @Param [requestDO, request]
    * @return void
    */
    protected void customizeCommonGateFrontRequest(IN requestDO, CommonGateFrontRequest request) {
    }

}
