package com.payermax.fin.exchange.domainservice.newExchange;

import com.alibaba.fastjson.JSONPath;
import com.payermax.fin.exchange.common.enums.ApiModeEnum;
import com.payermax.fin.exchange.common.enums.FrontParamEnum;
import com.payermax.fin.exchange.dal.entity.*;
import com.payermax.fin.exchange.domain.trans.RequestOrderDO;
import com.payermax.fin.exchange.domain.trans.ResponseOrderDO;
import com.payermax.fin.exchange.domainservice.bo.BizContextBO;
import com.payermax.fin.exchange.domainservice.bo.ChannelConfigJsonBO;
import com.payermax.fin.exchange.domainservice.bo.TransactionBO;
import com.payermax.fin.exchange.domainservice.repository.IChannelInfoService;
import com.payermax.fin.exchange.domainservice.repository.IChannelMethodService;
import com.payermax.fin.exchange.domainservice.repository.ITargetMerchantService;
import com.payermax.fin.exchange.domainservice.util.AmountUtils;
import com.payermax.fin.exchange.integration.rpc.constants.InternalUrlConstants;
import com.payermax.fin.exchange.integration.rpc.request.PayFrontRequest;
import com.payermax.fin.exchange.integration.rpc.request.RefundFrontRequest;
import com.payermax.fin.exchange.integration.rpc.response.RefundFrontResponse;
import com.payermax.fin.exchange.share.domain.OrderInfoDO;
import com.payermax.common.lang.util.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

/**
 * 退款功能front处理模版
 *
 * <AUTHOR>
 * @date 2024/3/18 11:14
 */
@Component
public class FrontRefundApplyExchangeTemplate extends AbstractExchangeTemplate<RequestOrderDO, ResponseOrderDO, RefundFrontRequest, RefundFrontResponse> {

    @Autowired
    private IChannelInfoService channelInfoService;

    @Autowired
    private IChannelMethodService channelMethodService;

    @Autowired
    private ITargetMerchantService targetMerchantService;

    @Override
    public String getApiMode() {
        return ApiModeEnum.PAST_FRONT.name();
    }

    @Override
    public String getApiName() {
        return InternalUrlConstants.FRONT_URL_REFUND;
    }

    @Override
    public RefundFrontRequest exchangeRequest(BizContextBO<RequestOrderDO, ResponseOrderDO> bizContext) {
        RequestOrderDO request = bizContext.getIn();
        OrderInfoDO orderInfo = request.getOrderInfo();
        TransactionBO transactionBO = getTransactionBO(bizContext);
        String channelPayCommitNo = transactionBO.getChannelPayCommitNo();
        ChannelRequestOrderEntity origRequestOrder = transactionBO.getQuoteRequestOrder();
        ChannelCommitOrderEntity origCommitOrder = transactionBO.getQuoteCommitOrder();
        ChannelConfigMappingEntity channelConfigMapping = transactionBO.getChannelConfigMappingEntity();
        ChannelInfoEntity channelInfo = transactionBO.getChannelInfoEntity();
        ChannelRequestOrderEntity requestOrder = transactionBO.getChannelRequestOrder();
        ChannelCommitOrderEntity channelCommitOrder = transactionBO.getChannelCommitOrder();

        RefundFrontRequest frontRequest = new RefundFrontRequest();
        frontRequest.setIdcRegion(origCommitOrder.getIdcRegion());
        frontRequest.setChannelMerchantCode(transactionBO.getChannelMerchantCode());
        frontRequest.setAmount(AmountUtils.toLeastTwoScale(channelCommitOrder.getAmount()));
        frontRequest.setRemark(orderInfo.getRemark());
        frontRequest.setChannelOrderNo(channelPayCommitNo);
        frontRequest.setOriChannelOrderNo(origCommitOrder.getChannelPayCommitNo());
        frontRequest.setOriChannelTransactionId(origCommitOrder.getThirdOrgOrderNo());
        frontRequest.setCurrency(origRequestOrder.getCurrency());
        frontRequest.setMerchantTxnId(origRequestOrder.getBizOrderNo());
        frontRequest.setCountry(origCommitOrder.getCountry());
        frontRequest.setAddField1(origCommitOrder.getAddField1());
        frontRequest.setAddField2(origCommitOrder.getAddField2());
        frontRequest.setAddField3(origCommitOrder.getAddField3());
        frontRequest.setAddField6(origCommitOrder.getAddField6());
        String customerType = Objects.toString(JSONPath.eval(origRequestOrder.getRequestBody(), FrontParamEnum.customerType.getCode()), "");
        frontRequest.setCustomerType(customerType);
        if (Objects.nonNull(channelConfigMapping)) {
            frontRequest.setProductCode(channelConfigMapping.getOldProductCode());
            frontRequest.setChannelCode(channelConfigMapping.getOldChannelCode());
            frontRequest.setMethodCode(channelConfigMapping.getOldMethodCode());
            frontRequest.setMethodSubCode(channelConfigMapping.getOldMethodSubCode());
        }
        // 设置新系统渠道编码等信息
        frontRequest.setNewChannelCode(transactionBO.getChannelCode());
        frontRequest.setChannelMethodCode(transactionBO.getChannelMethodCode());
        if (Objects.nonNull(channelInfo)) {
            ChannelConfigJsonBO channelConfigJson = channelInfoService.getConfigJsonByChannel(channelInfo);
            frontRequest.setTechnicalOrg(channelInfo.getTechnicalOrg());
            frontRequest.setTargetService(channelConfigJson.getTargetService());
        }
        if (Objects.nonNull(requestOrder)) {
            frontRequest.setPaymentMethodType(requestOrder.getPaymentMethodType());
            frontRequest.setTargetOrCardOrg(requestOrder.getTargetOrCardOrg());
            frontRequest.setBankCode(requestOrder.getExtJsonBo().getBankCode());
        }

        // 设置支付方式维度透传到下游的配置
        ChannelMethodEntity channelConfigEntity = transactionBO.getChannelConfigEntity();
        frontRequest.setChannelMethodExtConfig(channelMethodService.getChannelExtConfigByConfigJson(channelConfigEntity.getConfigJson()));

        frontRequest.setSubMerchantNo(origRequestOrder.getExtJsonBo().getSubMerchantNo());
        //设置报备信息上的二级商户信息：从原请求报文中获取
        PayFrontRequest origFrontRequest = JsonUtils.toBean(PayFrontRequest.class, origCommitOrder.getRequestBody());
        String subMerchantId = origFrontRequest == null ? null : origFrontRequest.getTargetSubMerchantId();
        frontRequest.setTargetMerchantDetail(targetMerchantService.getAndCheckTargetMerchant(
                origCommitOrder.getChannelMethodCode(), origCommitOrder.getChannelMerchantCode(), origRequestOrder.getMerchantNo(), subMerchantId));
        frontRequest.setTargetSubMerchantId(subMerchantId);

        frontRequest.setRequestType(origRequestOrder.getRequestType());
        frontRequest.setApi(InternalUrlConstants.CHANNEL_GATEWAY_API_REFUND);
        return frontRequest;
    }

    @Override
    public void fillCommitOrderAfterExchange(ChannelCommitOrderEntity commitOrder, BizContextBO<RequestOrderDO, ResponseOrderDO> bizContext) {
        TransactionBO transactionBO = getTransactionBO(bizContext);

        RefundFrontResponse frontResponse = (RefundFrontResponse) transactionBO.getInternalBaseResponse();
        commitOrder.setThirdOrgOrderNo(frontResponse.getChannelTransactionId());
        commitOrder.setFourthOrgOrderNo(frontResponse.getChannelThirdPartyTxnId());
        commitOrder.setAddField1(frontResponse.getAddField1());
        commitOrder.setAddField2(frontResponse.getAddField2());
        commitOrder.setAddField3(frontResponse.getAddField3());
        commitOrder.setAddField4(frontResponse.getAddField4());
        commitOrder.setAddField5(frontResponse.getAddField5());
        commitOrder.setAddField6(frontResponse.getAddField6());
        // 设置ARN和RRN
        Optional.ofNullable(frontResponse.getArn()).ifPresent(arn -> commitOrder.getExtJsonBo().setArn(arn));
        Optional.ofNullable(frontResponse.getRrn()).ifPresent(rrn -> commitOrder.getExtJsonBo().setRrn(rrn));
    }
}
