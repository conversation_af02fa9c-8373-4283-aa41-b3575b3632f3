package com.payermax.fin.exchange.domainservice.rule.filter;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.fin.exchange.domainservice.repository.ITargetMerchantService;
import com.payermax.fin.exchange.service.enums.FilterBizTypeEnum;
import com.payermax.fin.exchange.share.bo.PreFilterConditionBO;
import com.payermax.fin.exchange.share.config.ExchangeConfig;
import com.payermax.fin.exchange.share.constants.ShareConstants;
import com.payermax.fin.exchange.share.domain.ChannelInstanceDO;
import com.payermax.fin.exchange.share.domain.OrderInfoDO;
import com.payermax.fin.exchange.share.domain.TargetMerchantDO;
import com.payermax.fin.exchange.share.enums.PaymentTypeEnum;
import com.payermax.fin.exchange.share.enums.TargetMerchantStatusEnum;
import com.payermax.fin.exchange.share.request.InquiryTargetMerchantRequest;
import com.payermax.fin.exchange.share.rule.RuleConstants;
import com.payermax.fin.exchange.share.rule.filter.AbstractFilterRule;
import com.payermax.fin.exchange.share.utils.CacheUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 商户报备筛选规则器
 *
 * <AUTHOR>
 * @date 2021/7/22 20:10
 */
@Component
@Slf4j
public class TargetMerchantFilterRule extends AbstractFilterRule {

    @NacosValue(value = "${target-merchant.ignore.trans-steps:}", autoRefreshed = true)
    private List<String> ignoreTransSteps;

    @NacosValue(value = "${payout.noTargetMerchant.needReject:false}", autoRefreshed = true)
    private boolean payOutNoTargetMerchantNeedReject;

    @NacosValue(value = "${target-merchant.optional-report:true}", autoRefreshed = true)
    private boolean optionalReport;

    @Autowired
    private CacheUtils cacheUtils;

    @Autowired
    protected ITargetMerchantService targetMerchantService;

    @Override
    public boolean shouldRun(PreFilterConditionBO condition) {
        return true;
    }

    @Override
    public int getOrder() {
        return RuleConstants.FILTER_ORDER_TARGET_MERCHANT;
    }

    @Override
    public void execute(PreFilterConditionBO condition, List<ChannelInstanceDO> dataList) {
        OrderInfoDO orderInfo = condition.getOrderInfo();
        String merchantNo = orderInfo.getMerchantNo();
        String filterBizType = condition.getFilterBizType();
        //资金调拨业务没有商户号 直接跳过此规则
        if (StringUtils.isBlank(merchantNo)) {
            return;
        }
        // 如果交易步骤存在，并且在需要忽略的列表中，则不进行二级商户校验
        String transStep = orderInfo.getTransStep();
        if (StringUtils.isNotBlank(transStep) && ignoreTransSteps.contains(transStep)) {
            return;
        }
        String conditionStr = JSONObject.toJSONString(condition);
        List<ChannelInstanceDO> instance = new ArrayList<>(dataList);
        Map<String, Boolean> reportResultMap = new HashMap<>();
        Map<String, Pair<String, String>> unavailableReasonMap = new HashMap<>();
        // 遍历数据
        dataList.removeIf(data -> {
            // 判断渠道是否需要报备商户信息
            if (!ShareConstants.NUMBER_ONE.equals(data.getIsNeedReportMerchant()) && !ShareConstants.NUMBER_TWO.equals(data.getIsNeedReportMerchant())) {
                return false;
            }
            if (!optionalReport && ShareConstants.NUMBER_TWO.equals(data.getIsNeedReportMerchant())){
                return false;
            }
            // 说明数据有问题，channelId应该配置
            if (StringUtils.isBlank(data.getChannelId())) {
                if (ShareConstants.NUMBER_ONE.equals(data.getIsNeedReportMerchant())) {
                    super.setFilteredRoutedDetail(data.getChannelCode(), data.getChannelMethodCode(), data.getChannelMerchantCode(), merchantNo, "channelId is null");
                    return true;
                } else {
                    // 可报可不报，直接跳过
                    return false;
                }
            }
            String channelKey = String.format("%s:%s", data.getChannelId(), data.getEntity());
            Boolean reportResult = reportResultMap.get(channelKey);
            if (reportResult == null) {
                // 先取渠道维度的二级商户号配置，若不存在，默认取subMerchantNo字段
                String subMerchantNo = targetMerchantService.getSubMerchantIdByRequestOrder(data.getChannelCode(), conditionStr);
                if (StringUtils.isBlank(subMerchantNo)) {
                    subMerchantNo = orderInfo.gainSubMerchantNo();
                }
                InquiryTargetMerchantRequest request = new InquiryTargetMerchantRequest();
                request.setChannelId(data.getChannelId());
                request.setEntity(data.getEntity());
                request.setMerchantNo(merchantNo);
                request.setSubMerchantNo(subMerchantNo);
                Result<TargetMerchantDO> result = ExchangeConfig.getCommonFacade().hasTargetMerchant(request);
                // 查询失败，则放过
                if (!result.isSuccess()) {
                    return false;
                }
                // 有可用的报备信息
                TargetMerchantDO targetMerchantDO = result.getData();
                if (targetMerchantDO != null && TargetMerchantStatusEnum.Y.getCode().equals(targetMerchantDO.getStatus())) {
                    reportResultMap.put(channelKey, true);
                    return false;
                }
                if (ShareConstants.NUMBER_TWO.equals(data.getIsNeedReportMerchant()) && (targetMerchantDO == null ||
                        !(TargetMerchantStatusEnum.N.getCode().equals(targetMerchantDO.getStatus()) || TargetMerchantStatusEnum.F.getCode().equals(targetMerchantDO.getStatus())))) {
                    // 可报可不报的渠道，没有二级商户配置或者二级商户配置状态为待验证或者报备中
                    log.info("TargetMerchant {},channelCode:{},channelMethodCode:{},channelMerchantCode:{} channelId:{} entity:{}", null==targetMerchantDO?"null": JSON.toJSONString(targetMerchantDO),data.getChannelCode(), data.getChannelMethodCode(), data.getChannelMerchantCode(),data.getChannelId(),data.getEntity());
                    reportResultMap.put(channelKey, true);
                    return false;
                }

                // 其他情况则说明无可用报备信息
                reportResultMap.put(channelKey, false);

                String requestParamKey = String.format("%s:%s:%s:%s", data.getChannelId(), data.getEntity(), merchantNo, subMerchantNo);
                String reason = merchantConfigUnavailableReason(targetMerchantDO);
                unavailableReasonMap.put(channelKey, new Pair<>(requestParamKey, reason));
            } else if (Boolean.TRUE.equals(reportResult)) {
                return false;
            }

            // 获取不可用原因
            Pair<String, String> unavailableReasonPair = unavailableReasonMap.get(channelKey);
            super.setFilteredRoutedDetail(data.getChannelCode(), data.getChannelMethodCode(), data.getChannelMerchantCode(), unavailableReasonPair.getKey(), null, unavailableReasonPair.getValue());
            return true;
        });
        // 如果需要强制校验渠道二级商户报备，则返回，不再处理出款无可用渠道的情况
        if (FilterBizTypeEnum.AVAILABILITY.name().equals(filterBizType)) {
            return;
        }
        // 出款无可用渠道时不执行二级商户筛选逻辑
        if (!payOutNoTargetMerchantNeedReject) {
            if (dataList.isEmpty() && PaymentTypeEnum.PAYOUT == condition.getPaymentType()) {
                dataList.addAll(instance);
            }
        }
    }

    /**
     * 商户配置不可用原因
     *
     * @param targetMerchantDO
     * @return
     */
    private String merchantConfigUnavailableReason(TargetMerchantDO targetMerchantDO) {
        try {
            return Optional.ofNullable(targetMerchantDO).map(item -> {
                if (StringUtils.isNotBlank(item.getRemark())) {
                    return item.getRemark();
                }
                TargetMerchantStatusEnum status = TargetMerchantStatusEnum.getByCode(item.getStatus());
                if (status == null) {//新增状态避免处理出错
                    return "二级商户配置状态不可用";
                }
                switch (status) {
                    case P:
                        return "二级商户配置报备处理中";
                    case F:
                    case N:
                        return "二级商户配置报备状态不可用";
                    case I:
                        return "二级商户配置待验证";
                    default:
                        return "二级商户配置状态不可用";
                }
            }).orElseGet(() -> "二级商户未配置");
        } catch (Exception e) {
            log.error("set merchant config unavailable reason error！", e);
            return "二级商户配置状态不可用";
        }
    }

}
