package com.payermax.fin.exchange.domainservice.repository.impl;

import com.google.common.base.Preconditions;
import com.payermax.fin.exchange.dal.config.MigrationShardingAlgorithmUtils;
import com.payermax.fin.exchange.dal.dao.sharding.OrderNoMappingDao;
import com.payermax.fin.exchange.dal.entity.OrderNoMappingEntity;
import com.payermax.fin.exchange.domainservice.repository.IOrderNoMappingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.masterslave.route.engine.impl.MasterVisitedManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 订单号映射Service实现
 *
 * <AUTHOR>
 * @date 2021/9/3 18:45
 */
@Slf4j
@Service
public class OrderNoMappingServiceImpl implements IOrderNoMappingService {

    @Autowired
    private OrderNoMappingDao orderNoMappingDao;

    @Override
    public int add(OrderNoMappingEntity record) {
        return orderNoMappingDao.insert(record);
    }

    @Override
    public List<OrderNoMappingEntity> getByEntity(OrderNoMappingEntity queryEntity) {
        Preconditions.checkArgument(queryEntity != null, "param queryEntity is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(queryEntity.getTargetOrderNo()), "param queryEntity.targetOrderNo is mandatory");
        
        List<OrderNoMappingEntity> orderNoMappingList = null;
        try {
            if(MigrationShardingAlgorithmUtils.getFinMigrateFinish()) {
                orderNoMappingList = orderNoMappingDao.selectByEntity(queryEntity);
                return orderNoMappingList;
            }
            
            MigrationShardingAlgorithmUtils.setDatabasePrefix(MigrationShardingAlgorithmUtils.NEW_DATABASE);
            // 主库查询 迁移时双读 读主库
            MasterVisitedManager.setMasterVisited();
            orderNoMappingList = orderNoMappingDao.selectByEntity(queryEntity);
            if (CollectionUtils.isEmpty(orderNoMappingList)) {
                MigrationShardingAlgorithmUtils.setDatabasePrefix(MigrationShardingAlgorithmUtils.CURRENT_DATABASE);
                // 主库查询 迁移时双读 读主库
                MasterVisitedManager.setMasterVisited();
                orderNoMappingList = orderNoMappingDao.selectByEntity(queryEntity);
            }
        } catch (Exception e) {
            log.error("GetByOrderNoMappingEntity Exception. Please check it.", e);
            throw e;
        } finally {
            MigrationShardingAlgorithmUtils.clear();
        }
        return orderNoMappingList;
    }
}
