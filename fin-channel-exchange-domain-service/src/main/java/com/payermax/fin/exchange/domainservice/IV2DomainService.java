package com.payermax.fin.exchange.domainservice;

import com.payermax.fin.exchange.domainservice.bo.BizContextBO;

/**
 * <AUTHOR>
 * @date 2024/3/16 23:52
 */
public interface IV2DomainService<IN, OUT>  {

    /**
     * 执行
     *
     * @param in
     * @param outClazz
     */
    OUT execute(IN in, Class<OUT> outClazz);

    /**
     * 执行
     *
     * @param bizContext
     */
    OUT execute(BizContextBO<IN, OUT> bizContext);

}
