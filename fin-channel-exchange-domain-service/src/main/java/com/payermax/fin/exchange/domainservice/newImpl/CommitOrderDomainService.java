package com.payermax.fin.exchange.domainservice.newImpl;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.util.JsonUtils;
import com.payermax.fin.exchange.common.enums.*;
import com.payermax.fin.exchange.dal.entity.*;
import com.payermax.fin.exchange.domain.trans.RequestOrderDO;
import com.payermax.fin.exchange.domain.trans.ResponseOrderDO;
import com.payermax.fin.exchange.domainservice.AbstractDomainService;
import com.payermax.fin.exchange.domainservice.bo.BizContextBO;
import com.payermax.fin.exchange.domainservice.bo.OrderVerifyBO;
import com.payermax.fin.exchange.domainservice.bo.TransactionBO;
import com.payermax.fin.exchange.domainservice.limiter.LimitRule;
import com.payermax.fin.exchange.domainservice.limiter.RateLimiterConfig;
import com.payermax.fin.exchange.domainservice.limiter.counter.IMaxCounterRateLimiter;
import com.payermax.fin.exchange.domainservice.newExchange.ExchangeTemplateFactory;
import com.payermax.fin.exchange.domainservice.newExchange.IExchangeTemplate;
import com.payermax.fin.exchange.domainservice.repository.*;
import com.payermax.fin.exchange.domainservice.rpcproxy.ChannelInternalClientProxy;
import com.payermax.fin.exchange.domainservice.rule.channelgatewaygary.ChannelGatewayGrayExecutor;
import com.payermax.fin.exchange.domainservice.service.limit.ILimitService;
import com.payermax.fin.exchange.domainservice.service.orderNo.IOrderNoService;
import com.payermax.fin.exchange.domainservice.state.StateMachineExecutor;
import com.payermax.fin.exchange.domainservice.state.StateRequest;
import com.payermax.fin.exchange.domainservice.util.ObjectOperateUtils;
import com.payermax.fin.exchange.domainservice.verify.IVerifyService;
import com.payermax.fin.exchange.integration.rpc.request.InternalBaseRequest;
import com.payermax.fin.exchange.integration.rpc.response.FrontVerifyResponse;
import com.payermax.fin.exchange.integration.rpc.response.InternalBaseResponse;
import com.payermax.fin.exchange.integration.rpc.response.ResultDTO;
import com.payermax.fin.exchange.share.enums.PaymentTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * 创建渠道提交单领域服务
 *
 * <AUTHOR>
 * @date 2024/3/16 23:02
 */
@Service("commitOrderDomainService")
public class CommitOrderDomainService<IN extends RequestOrderDO, OUT extends ResponseOrderDO> extends AbstractDomainService<IN, OUT> {

    private Map<String, String> requestTypeUrlMap;

    private Map<String, BigDecimal> channelPaymentTokenAmountMap;

    @Autowired
    protected IOrderNoService orderNoService;

    @Autowired
    protected IChannelCommitOrderService channelCommitOrderService;

    @Autowired
    protected IChannelResultOrderService channelResultOrderService;

    @Autowired
    protected ChannelInternalClientProxy channelInternalClientProxy;

    @Autowired
    protected IChannelInfoService channelInfoService;

    @Autowired
    protected IChannelMethodService channelMethodService;

    @Autowired
    protected ITargetMerchantService targetMerchantService;

    @Autowired
    protected StateMachineExecutor stateMachineExecutor;

    @Autowired
    private IMaxCounterRateLimiter redisMaxCounterRateLimiter;

    @Autowired
    private ILimitService limitService;

    @Autowired
    private IVerifyService verifyService;

    @Autowired
    private IChannelMerchantService channelMerchantService;

    @NacosValue(value = "#{${request-type.url.default.map}}", autoRefreshed = true)
    public void setRequestTypeUrlMap(Map<String, String> requestTypeUrlMap) {
        this.requestTypeUrlMap = requestTypeUrlMap;
    }

    @NacosValue(value = "#{${channel.apply.payment.token.default.amount.map}}", autoRefreshed = true)
    public void setChannelPaymentTokenAmountMap(Map<String, BigDecimal> channelPaymentTokenAmountMap) {
        this.channelPaymentTokenAmountMap = channelPaymentTokenAmountMap;
    }

    @Override
    protected void executeBusiness(BizContextBO<IN, OUT> bizContext) throws Exception {
        TransactionBO transactionBO = getTransactionBO(bizContext);

        // 执行限流
        acquireLimit(bizContext);
        // 主流程
        ResultDTO resultDTO;
        try {
            // 下游交换前置逻辑：构建请求报文，落渠道提交单等
            exchangeBefore(bizContext);
            // 调用下游服务
            resultDTO = invokeInternalService(bizContext);
        } catch (Exception e) {
            throw e;
        } finally {
            // 释放限流
            releaseLimit(bizContext);
        }

        IExchangeTemplate exchangeTemplate = this.getExchangeTemplate(bizContext);
        // 根据下游响应结果填充渠道提交单
        exchangeTemplate.fillCommitOrderAfterExchange(transactionBO.getChannelCommitOrder(), bizContext);
        // 更新订单
        updateChannelCommitOrder(resultDTO, bizContext);
        // 服务交换调用最后置逻辑
        exchangeAfter(bizContext);
        // 构建通用响应报文
        buildCommonOut(bizContext);
        // 响应成功，填充成功响应报文
        if (resultDTO.isRespSuccess()) {
            exchangeTemplate.exchangeResponse(transactionBO.getInternalBaseResponse(), bizContext);
        } else {
            throw new BusinessException(resultDTO.getBizCode(), resultDTO.getMessage());
        }
    }

    /**
     * 服务交换调用最前置逻辑
     *
     * @param bizContext
     */
    protected void exchangeBefore(BizContextBO<IN, OUT> bizContext) {
        RequestOrderDO in = bizContext.getIn();
        TransactionBO transactionBO = getTransactionBO(bizContext);
        // 根据渠道请求单生成渠道提交单号（保持分片规则一致）
        ChannelStepApiMappingEntity currentStepApiMapping = transactionBO.getCurrentStepApiMapping();
        String mainRequestType;
        if (currentStepApiMapping != null) {
            mainRequestType = currentStepApiMapping.getStepType();
        } else {
            mainRequestType = transactionBO.getChannelRequestOrder().getMainRequestType();
        }
        boolean isTransaction = MainRequestTypeEnum.isTransactionType(mainRequestType);
        String commitOrderNo = null;
        ChannelCommitOrderEntity retryCommitOrder = null, newChannelCommitOrder = null;
        // 已有提交单号，则查询提交单
        if (Objects.nonNull(in.getInnerUseInfo()) && StringUtils.isNotBlank(in.getInnerUseInfo().getChannelPayCommitNo())) {
            String innerCommitOrderNo = in.getInnerUseInfo().getChannelPayCommitNo();
            ChannelCommitOrderEntity innerCommitOrder = channelCommitOrderService.getByCommitOrderNoFromMaster(innerCommitOrderNo, transactionBO.getChannelRequestOrder().getPaymentType());
            if (OrderStatusEnum.isFinalStatus(innerCommitOrder.getStatus())) {
                throw new BusinessException(ErrorCodeEnum.CHANNEL_ORDER_IS_FINAL.getCode(), ErrorCodeEnum.CHANNEL_ORDER_IS_FINAL.getMsg());
            }
            retryCommitOrder = innerCommitOrder;
            commitOrderNo = innerCommitOrderNo;
            transactionBO.setChannelCommitOrder(retryCommitOrder);
        }
        if (StringUtils.isBlank(commitOrderNo)) {
            newChannelCommitOrder = buildChannelCommitOrder(bizContext);
            transactionBO.setChannelCommitOrder(newChannelCommitOrder);
            // 生成渠道提交单号
            commitOrderNo = orderNoService.genCommitNoByRequestNoAndCode(transactionBO.getChannelPayRequestNo(),
                    this.getOrderNoPrefix(newChannelCommitOrder.getRequestType()), transactionBO.getChannelMethodCode(), transactionBO.getChannelCode(), isTransaction);
            newChannelCommitOrder.setChannelPayCommitNo(commitOrderNo);
        }
        transactionBO.setChannelPayCommitNo(commitOrderNo);

        // 填充请求下游的API名称或URL
        fillRequestApiName(bizContext);
        // 组装请求
        InternalBaseRequest baseRequest = buildExchangeRequest(bizContext);
        // 组装请求后置逻辑
        Object exchangeRequest = buildExchangeRequestAfter(bizContext, baseRequest);
        transactionBO.setExchangeRequest(exchangeRequest);
        // 根据请求获取默认响应
        InternalBaseResponse baseResponse = baseRequest.getResponse();
        transactionBO.setInternalBaseResponse(baseResponse);

        // 落渠道提交单
        if (Objects.nonNull(newChannelCommitOrder)) {
            newChannelCommitOrder.setRequestBody(JsonUtils.toString(exchangeRequest));
            addChannelCommitOrder(newChannelCommitOrder, bizContext);
            //toco:czy request 塞入灰度标签
            ChannelGatewayGrayExecutor.setRequestBodyChannelGatewayLabel(newChannelCommitOrder,exchangeRequest);
        } else {
            // 更新订单发送时间
            retryCommitOrder.getExtJsonBo().setOrderSendTime(new Date());
            channelCommitOrderService.update(retryCommitOrder.getStatus(), retryCommitOrder);
        }
        // 订单创建后逻辑：限额累加等
        afterCreateOrder(bizContext);
    }

    /**
     * 调用下游
     *
     * @param bizContext
     * @return
     */
    protected ResultDTO invokeInternalService(BizContextBO<IN, OUT> bizContext) {
        TransactionBO transactionBO = getTransactionBO(bizContext);
        ChannelCommitOrderEntity channelCommitOrder = transactionBO.getChannelCommitOrder();
        InternalBaseResponse baseResponse = transactionBO.getInternalBaseResponse();

        ResultDTO internalResultDTO = ResultDTO.buildSuccess(baseResponse);
        // 调用下游服务前校验逻辑
        boolean checkResult = invokeInternalBeforeCheck(bizContext);
        if (!checkResult) {
            return internalResultDTO;
        }
        try {
            IExchangeTemplate exchangeTemplate = this.getExchangeTemplate(bizContext);
            // 发起调用
            internalResultDTO = exchangeTemplate.invoke(transactionBO.getExchangeRequest(), bizContext);
            // 正确成功响应下处理
            InternalBaseResponse internalBaseResponse = (InternalBaseResponse) internalResultDTO.getData();
            // 同步报文合法性校验，如果校验不通过，则保持pending状态
            if (!verifyApplyResponse(internalBaseResponse, channelCommitOrder)) {
                transactionBO.setOrderStatusEnum(OrderStatusEnum.PENDING);
            } else {
                transactionBO.setOrderStatusEnum(internalBaseResponse.getOrderStatus());
            }
            // 当非风控时，风控结果保存在上下文
            ApiModeEnum apiModeEnum = transactionBO.getApiModeEnum();
            if (apiModeEnum.getMode() != ApiModeEnum.RISK_QUERY.getMode()) {
                transactionBO.setPreResponse(internalBaseResponse);
                transactionBO.setPreStepChannelCode(channelCommitOrder.getChannelCode());
            }
            // 设置上下文
            transactionBO.setInternalBaseResponse(internalBaseResponse);
        } catch (Throwable e) {
            // 响应异常的处理
            if (e instanceof BusinessException) {
                BusinessException baseException = (BusinessException) e;
                internalResultDTO.setBizCode(baseException.getErrCode());
            } else {
                internalResultDTO.setBizCode(ErrorCodeEnum.INNER_ERROR.getCode());
                log.error("AbstractTransExchangeTemplate.invokeInternalService error, ", e);
            }
            internalResultDTO.setMessage(e.getMessage());
            // 发送出现异常 处理
            internalSendException(bizContext, e);
        }
        return internalResultDTO;
    }

    /**
     * 调用下游服务前校验逻辑
     *
     * @param bizContext
     * @return
     */
    protected boolean invokeInternalBeforeCheck(BizContextBO<IN, OUT> bizContext) {
        TransactionBO transactionBO = getTransactionBO(bizContext);
        ChannelInfoEntity channelInfo = transactionBO.getChannelInfoEntity();
        ChannelMethodEntity channelConfig = transactionBO.getChannelConfigEntity();
        ChannelOrderCompensateEntity orderCompensate = transactionBO.getOrderCompensate();
        Integer interactiveMode = channelInfo.getInteractiveMode();

        // 如果是虚拟渠道，则不调用下游，直接成功
        if (InteractiveMode.VIRTUAL.getType().equals(interactiveMode)) {
            transactionBO.setOrderStatusEnum(OrderStatusEnum.SUCCESS);
            return false;
        }
        // 渠道支付方式paymentFlow=OFFLINE 或者 人工渠道，则保持pengding
        if (PaymentFlowEnum.OFFLINE.getCode().equals(channelConfig.getPaymentFlow())
                || InteractiveMode.MANUAL.getType().equals(interactiveMode)) {
            transactionBO.setOrderStatusEnum(OrderStatusEnum.PENDING);
            return false;
        }
        //渠道需要异步调用
        if (InteractiveMode.ASYNC.getType().equals(interactiveMode)) {
            transactionBO.setOrderStatusEnum(OrderStatusEnum.INITIATE);
            orderCompensate.setRetryType(RetryTypeEnum.APPLY.name());
            return false;
        }
        // 如果API交互方式是文件网关，则不调用其他系统，设置初始态返回，等待文件网关来捞取数据
        if (ApiModeEnum.FILE_EXCHANGE.equals(transactionBO.getApiModeEnum())) {
            transactionBO.setOrderStatusEnum(OrderStatusEnum.INITIATE);
            return false;
        }
        return true;
    }

    /**
     * 校验查询响应报文是否合法
     *
     * @param baseResponse
     * @param commitOrder
     */
    protected boolean verifyApplyResponse(InternalBaseResponse baseResponse, ChannelCommitOrderEntity commitOrder) {
        // 非支付类请求，则不校验报文（如3ds等控制类）
        if (!MainRequestTypeEnum.TRANSACTION.getValue().equals(commitOrder.getMainRequestType())) {
            return true;
        }
        if (!(baseResponse instanceof FrontVerifyResponse)) {
            log.warn("Apply response not instanceof FrontVerifyResponse, pls check it. response = {}", JsonUtils.toString(baseResponse));
            return true;
        }
        FrontVerifyResponse verifyResponse = (FrontVerifyResponse) baseResponse;
        OrderVerifyBO orderVerify = new OrderVerifyBO();
        orderVerify.setPaymentTypeEnum(PaymentTypeEnum.getByValue(commitOrder.getPaymentType()));
        orderVerify.setEventEnum(OrderEventEnum.APPLY);
        orderVerify.setOriCommitOrder(commitOrder);
        orderVerify.setNewStatus(verifyResponse.getOrderStatus().getStatus());
        orderVerify.setAmount(verifyResponse.getMoney());
        orderVerify.setIgnoreAmountVerify(verifyResponse.isIgnoreAmountVerify());
        orderVerify.setThirdOrgOrderNo(verifyResponse.getChannelTransactionId());
        boolean verifyResult = verifyService.verify(orderVerify);
        return verifyResult;
    }

    /**
     * 订单号前缀
     *
     * @return
     */
    public OrderNoPrefixEnum getOrderNoPrefix(String requestType) {
        RequestTypeEnum requestTypeEnum = RequestTypeEnum.getRequestTypeByCode(requestType);
        if (requestTypeEnum == null) {
            requestTypeEnum = RequestTypeEnum.NA;
        }
        return requestTypeEnum.getCommitOrderNoPrefix();
    }

    /**
     * 构建服务调用请求
     *
     * @param bizContext
     * @return
     */
    protected InternalBaseRequest buildExchangeRequest(BizContextBO<IN, OUT> bizContext) {
        IExchangeTemplate exchangeTemplate = this.getExchangeTemplate(bizContext);
        return exchangeTemplate.exchangeRequest(bizContext);
    }

    /**
     * 构建服务调用请求报文后置逻辑*
     * *
     * @param bizContext
     * @param baseRequest
     * @return
     */
    protected Object buildExchangeRequestAfter(BizContextBO<IN, OUT> bizContext, InternalBaseRequest baseRequest) {
        return baseRequest;
    }

    /**
     * 订单创建后逻辑
     *
     * @param bizContext
     */
    protected void afterCreateOrder(BizContextBO<IN, OUT> bizContext) {
    }

    /**
     * 下游调用异常逻辑
     *
     * @param bizContext
     * @param e
     */
    protected void internalSendException(BizContextBO<IN, OUT> bizContext, Throwable e) {
        /*
        TODO 根据此次请求的类型，来判断更新订单状态
        1.在正确响应“0000”的基础上
            1.1.更新为成功
            1.2.更新为失败
        2.其他异常情况
            2.1.更新为失败，跳转类型交易，承载类交易，交换类交易
            2.2.其他更新为pending，联机类交易，直接返回交易结果的
         */
        // 出现异常，默认状态为pending
        getTransactionBO(bizContext).setOrderStatusEnum(OrderStatusEnum.PENDING);
    }

    /**
     * 服务交换调用最后置逻辑
     *
     * @param bizContext
     */
    protected void exchangeAfter(BizContextBO<IN, OUT> bizContext) {
    }

    /**
     * 构建通用响应结果
     *
     * @param bizContext
     * @return
     */
    protected void buildCommonOut(BizContextBO<IN, OUT> bizContext) {
        TransactionBO transactionBO = getTransactionBO(bizContext);
        ChannelRequestOrderEntity channelRequestOrder = transactionBO.getChannelRequestOrder();
        ChannelCommitOrderEntity channelCommitOrder = transactionBO.getChannelCommitOrder();
        ResponseOrderDO responseOrderDO = bizContext.getOut();
        // 填充通用请求单提交单信息
        ObjectOperateUtils.fillResponseOrderDO(responseOrderDO, channelRequestOrder, channelCommitOrder);
    }

    /**
     * 获取上下文中间数据对象
     *
     * @param bizContext 交易上下文
     * @return TransactionBO
     */
    protected TransactionBO getTransactionBO(BizContextBO<IN, OUT> bizContext) {
        Object baseBo = bizContext.getBaseBo();
        if (Objects.isNull(baseBo) || !(baseBo instanceof TransactionBO)) {
            throw new BusinessException(ErrorCodeEnum.INNER_ERROR.getCode(), "baseBo no instanceof TransactionBO");
        }
        return (TransactionBO) baseBo;
    }

    /**
     * 构建提交单
     *
     * @param bizContext
     * @return
     */
    private ChannelCommitOrderEntity buildChannelCommitOrder(BizContextBO<IN, OUT> bizContext) {
        RequestOrderDO in = bizContext.getIn();
        TransactionBO transactionBO = getTransactionBO(bizContext);
        ChannelRequestOrderEntity channelRequestOrder = transactionBO.getChannelRequestOrder();
        ChannelCommitOrderEntity quoteCommitOrder = transactionBO.getQuoteCommitOrder();
        ChannelInfoEntity channelInfo = transactionBO.getChannelInfoEntity();
        ChannelStepApiMappingEntity currentStepApiMapping = transactionBO.getCurrentStepApiMapping();

        // 构建渠道提交单对象
        ChannelCommitOrderEntity commitOrder = new ChannelCommitOrderEntity();
        commitOrder.setChannelCode(transactionBO.getChannelCode());
        commitOrder.setChannelMerchantCode(transactionBO.getChannelMerchantCode());
        commitOrder.setChannelMethodCode(transactionBO.getChannelMethodCode());
        commitOrder.setBizOrderNo(channelRequestOrder.getBizOrderNo());
        commitOrder.setChannelPayRequestNo(channelRequestOrder.getChannelPayRequestNo());
        commitOrder.setServiceMode(channelRequestOrder.getServiceMode());
        commitOrder.setServiceEntity(channelRequestOrder.getServiceEntity());
        if (currentStepApiMapping != null) {
            commitOrder.setMainRequestType(String.valueOf(currentStepApiMapping.getStepType()));
            commitOrder.setRequestType(currentStepApiMapping.getStepCode());
        } else {
            commitOrder.setMainRequestType(channelRequestOrder.getMainRequestType());
            commitOrder.setRequestType(channelRequestOrder.getRequestType());
        }
        commitOrder.setPaymentType(channelRequestOrder.getPaymentType());
        commitOrder.setStatus(OrderStatusEnum.INITIATE.getStatus());
        commitOrder.setAmount(this.buildCommitOrderAmount(channelRequestOrder, transactionBO.getChannelCode()));
        commitOrder.setPaidAmount(commitOrder.getAmount());
        commitOrder.setCurrency(channelRequestOrder.getCurrency());
        commitOrder.setCountry(channelRequestOrder.getCountry());
        if (channelInfo != null) {
            commitOrder.setChannelEntity(channelInfo.getEntity());
        } else if (quoteCommitOrder != null) {
            commitOrder.setChannelEntity(quoteCommitOrder.getChannelEntity());
        }
        ChannelMerchantEntity channelMerchantEntity = channelMerchantService.getByMerchantCodeIgnoreStatus(transactionBO.getChannelMerchantCode());
        //多主体渠道，取mid纬度主体
        if(channelMerchantEntity != null){
            commitOrder.setChannelEntity(channelMerchantEntity.getEntity());
        }
        if (quoteCommitOrder != null) {
            commitOrder.setQuoteChannelPayCommitNo(quoteCommitOrder.getChannelPayCommitNo());
        }
        // 记录支付类渠道提交单关联的控制类渠道请求单号
        // （如OTP重发，渠道不支持，则会有一个控制请求单，一个支付提交单；这个支付提交单会同时关联原支付请求单和此控制请求单）
        if (in.getInnerUseInfo() != null && StringUtils.isNotBlank(in.getInnerUseInfo().getControlChannelPayRequestNo())) {
            commitOrder.setAddField7(in.getInnerUseInfo().getControlChannelPayRequestNo());
        }
        // 如果入参中有渠道完成时间，则设置渠道完成时间（用于充值场景，不需要调用外部渠道，直接完成，上游下发完成时间）
        if (in.getChannelCompleteTime() != null) {
            commitOrder.setCompleteTime(new Date(in.getChannelCompleteTime()));
        }
        commitOrder.setCreateTime(new Date());
        //设置商户号（判断是否需要脱敏）
        commitOrder.setMerchantNo(channelRequestOrder.getMerchantNo());
        // 判断子交易上下文是否存在，存在则报备父渠道信息
        TransactionBO.SubTransactionBO subTransactionBO = transactionBO.getSubTransactionBO();
        if (subTransactionBO != null) {
            ChannelCommitOrderEntity.ExtJsonBO extJsonBo = commitOrder.getExtJsonBo();
            extJsonBo.setChannelCode(transactionBO.getOrigChannelCode());
            extJsonBo.setChannelMethodCode(transactionBO.getOrigChannelMethodCode());
            extJsonBo.setChannelMerchantCode(transactionBO.getOrigChannelMerchantCode());
        }
        // 如果入款上下文有订单过期时间，则存入提交单
        if(null != in.getOrderInfo() && null != in.getOrderInfo().getOrderExpireTimeStamp()){
            ChannelCommitOrderEntity.ExtJsonBO extJsonBo = commitOrder.getExtJsonBo();
            extJsonBo.setOrderExpireTimeStamp(in.getOrderInfo().getOrderExpireTimeStamp());
        }
        return commitOrder;
    }

    /**
     * 构建提交单金额
     *
     * @param requestOrder
     * @param channelCode
     * @return
     */
    private BigDecimal buildCommitOrderAmount(ChannelRequestOrderEntity requestOrder, String channelCode) {
        String requestType = requestOrder.getRequestType();
        if (!RequestTypeEnum.APPLY_PAYMENT_TOKEN.getCode().equals(requestType)) {
            return requestOrder.getAmount();
        }
        BigDecimal requestAmount = requestOrder.getAmount();
        // 如果金额存在并且大于0，则使用原金额
        if (requestAmount != null && BigDecimal.ZERO.compareTo(requestAmount) < 0) {
            return requestAmount;
        }
        return channelPaymentTokenAmountMap.get(channelCode);
    }

    /**
     * 创建请求单
     *
     * @param bizContext
     */
    private void addChannelCommitOrder(ChannelCommitOrderEntity commitOrder, BizContextBO<IN, OUT> bizContext) {
        // 新增渠道提交单
        channelCommitOrderService.add(commitOrder);

        TransactionBO transactionBO = getTransactionBO(bizContext);
        ChannelOrderCompensateEntity orderCompensate = transactionBO.getOrderCompensate();
        // 新增成功，如果是支付单，则填充订单补偿对象信息
        if (MainRequestTypeEnum.isTransactionType(commitOrder.getMainRequestType())) {
            orderCompensate.setChannelPayCommitNo(commitOrder.getChannelPayCommitNo());
            orderCompensate.setChannelCode(commitOrder.getChannelCode());
            orderCompensate.setChannelMethodCode(commitOrder.getChannelMethodCode());
            orderCompensate.setRetryCommitStatus(commitOrder.getStatus());
        }
        orderCompensate.setApiMode(transactionBO.getApiModeEnum().name());
    }

    /**
     * 更新提交单
     *
     * @param bizContext
     */
    protected void updateChannelCommitOrder(ResultDTO internalResult, BizContextBO<IN, OUT> bizContext) {
        TransactionBO transactionBO = getTransactionBO(bizContext);
        InternalBaseResponse baseResponse = transactionBO.getInternalBaseResponse();
        ChannelCommitOrderEntity commitOrder = transactionBO.getChannelCommitOrder();
        ChannelStepEntity nextChannelStep = transactionBO.getNextChannelStep();
        ChannelOrderCompensateEntity orderCompensate = transactionBO.getOrderCompensate();

        // 从上下文中获取订单状态
        OrderStatusEnum orderStatusEnum = transactionBO.getOrderStatusEnum();
        Integer oriCommitStatus = commitOrder.getStatus();
        // 设置提交单属性
        commitOrder.setResponseCode(baseResponse.channelRespCode());
        commitOrder.setResponseMsg(baseResponse.channelRespMsg());
        commitOrder.getExtJsonBo().setOrderSendTime(new Date());
        // 设置映射码
        if (internalResult != null) {
            commitOrder.setMappingCode(internalResult.getBizCode());
            commitOrder.setMappingMsg(internalResult.getMessage());
        }
        commitOrder.setStatus(orderStatusEnum.getStatus());
        // 如果支付单存在下一步，并且本步骤是成功，则支付状态为PENDING
        if (MainRequestTypeEnum.TRANSACTION.getValue().equals(commitOrder.getMainRequestType())) {
            if (nextChannelStep != null && OrderStatusEnum.SUCCESS.getStatus().equals(orderStatusEnum.getStatus())) {
                commitOrder.setStatus(OrderStatusEnum.PENDING.getStatus());
            }
        }
        // 如果是终态，则新增渠道结果单
        if (OrderStatusEnum.isFinalStatus(commitOrder.getStatus())) {
            // 同步成功情况下，如果已经有完成时间，则不再使用当前时间
            commitOrder.setCompleteTime(commitOrder.getCompleteTime() == null ? new Date() : commitOrder.getCompleteTime());
        }
        // 提交单更新前执行逻辑
        updateChannelCommitOrderBefore(bizContext, commitOrder);
        // 不管什么状态，都保存结果单，TODO 保存失败不影响后续流程，可考虑异步化
        addChannelResultOrder(internalResult, commitOrder);
        try {
            // 通过状态机更新提交单
            StateRequest stateRequest = new StateRequest(oriCommitStatus, OrderEventEnum.APPLY, commitOrder);
            stateMachineExecutor.changeCommitState(stateRequest);
        } catch (BusinessException e) {
            // 如果是乐观锁导致的更新失败：说明订单状态有变更，则查询订单
            if (ErrorCodeEnum.UPDATE_ORDER_FAIL.getCode().equals(e.getErrCode())) {
                // 订单状态有变更，则不进行重试
                transactionBO.setIgnoreRetry(true);
                commitOrder = channelCommitOrderService.getByRequestAndCommitOrderNoFromMaster(commitOrder.getChannelPayRequestNo(), commitOrder.getChannelPayCommitNo());
                transactionBO.setChannelCommitOrder(commitOrder);
            } else {
                // 如果订单更新失败，则抛出异常
                throw e;
            }
        }

        // 更新成功，则更新订单补偿对象信息
        if (MainRequestTypeEnum.isTransactionType(commitOrder.getMainRequestType())) {
            orderCompensate.setRetryCommitStatus(commitOrder.getStatus());
        }
    }

    /**
     * 更新渠道提交单前置逻辑
     *
     * @param bizContext
     * @param commitOrder
     */
    protected void updateChannelCommitOrderBefore(BizContextBO<IN, OUT> bizContext, ChannelCommitOrderEntity commitOrder) {
    }

    /**
     * 新增渠道结果单
     *
     * @param internalResult
     * @param commitOrder
     */
    protected void addChannelResultOrder(ResultDTO internalResult, ChannelCommitOrderEntity commitOrder) {
        ChannelResultOrderEntity resultOrder = new ChannelResultOrderEntity();
        try {
            resultOrder.setChannelPayRequestNo(commitOrder.getChannelPayRequestNo());
            resultOrder.setChannelPayCommitNo(commitOrder.getChannelPayCommitNo());
            resultOrder.setEventType(OrderEventEnum.APPLY.name());
            resultOrder.setResponseCode(commitOrder.getResponseCode());
            resultOrder.setResponseMsg(commitOrder.getResponseMsg());
            resultOrder.setResponseBody(JsonUtils.toString(internalResult));
            channelResultOrderService.add(resultOrder);
        } catch (Exception e) {
            log.error("add channel result order error. resultOrder = {}", JsonUtils.toString(resultOrder), e);
        }
    }

    /**
     * 填充请求下游的API名称或URL
     *
     * @param bizContext
     */
    protected void fillRequestApiName(BizContextBO<IN, OUT> bizContext) {
        TransactionBO transactionBO = getTransactionBO(bizContext);
        ChannelCommitOrderEntity commitOrder = transactionBO.getChannelCommitOrder();
        ChannelStepApiMappingEntity currentStepApiMapping = transactionBO.getCurrentStepApiMapping();
        String apiName = null;
        if (currentStepApiMapping != null) {
            apiName = currentStepApiMapping.getBackendApiUrl();
        }
        // 如果步骤未配置或者backendApiUrl无值，则取配置中的默认值
        if (StringUtils.isBlank(apiName)) {
            apiName = requestTypeUrlMap.get(commitOrder.getRequestType());
        }
        transactionBO.setApiName(apiName);
    }

    /**
     * 获取exchangeTemplate
     *
     * @param bizContext
     * @return
     */
    private IExchangeTemplate getExchangeTemplate(BizContextBO<IN, OUT> bizContext) {
        TransactionBO transactionBO = getTransactionBO(bizContext);
        ApiModeEnum apiModeEnum = transactionBO.getApiModeEnum();
        String apiName = transactionBO.getApiName();

        return ExchangeTemplateFactory.getExchangeTemplate(apiModeEnum.name(), apiName);
    }

    /**
     * 获取限流规则
     *
     * @param bizContext
     * @return
     */
    private LimitRule getLimitRule(BizContextBO<IN, OUT> bizContext) {
        TransactionBO transactionBO = (TransactionBO) bizContext.getBaseBo();
        String ruleName = RateLimiterConfig.getTransCounterParamMap().get(transactionBO.getChannelCode());
        if (StringUtils.isBlank(ruleName)) {
            return null;
        }
        LimitRule limitRule = RateLimiterConfig.getTransCounterRateLimitMap().get(ruleName);
        if (Objects.isNull(limitRule)) {
            return null;
        }
        return limitRule;
    }

    /**
     * 获取限流结果
     *
     * @param bizContext
     * @return
     */
    protected void acquireLimit(BizContextBO<IN, OUT> bizContext) {
        TransactionBO transactionBO = (TransactionBO) bizContext.getBaseBo();
        String channelCode = transactionBO.getChannelCode();
        String channelMerchantCode = transactionBO.getChannelMerchantCode();
        limitService.handleLimit(transactionBO.getChannelRequestOrder(), channelCode, channelMerchantCode);
        LimitRule limitRule = getLimitRule(bizContext);
        if (Objects.isNull(limitRule)) {
            return;
        }
        boolean acquire = redisMaxCounterRateLimiter.acquire(limitRule.getRuleKey(), limitRule.getMaxCount());
        // 获取令牌失败
        if (!acquire) {
            // 释放令牌
            redisMaxCounterRateLimiter.release(limitRule.getRuleKey(), limitRule.getMaxCount());
            // 抛出异常
            throw new BusinessException(ErrorCodeEnum.EXCEED_RATE_LIMIT.getCode(), ErrorCodeEnum.EXCEED_RATE_LIMIT.getMsg());
        }
    }

    /**
     * 释放限流
     *
     * @param bizContext
     */
    protected void releaseLimit(BizContextBO<IN, OUT> bizContext) {
        LimitRule limitRule = getLimitRule(bizContext);
        if (Objects.isNull(limitRule)) {
            return;
        }
        redisMaxCounterRateLimiter.release(limitRule.getRuleKey(), limitRule.getMaxCount());
    }

}
