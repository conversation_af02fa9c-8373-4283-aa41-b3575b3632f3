package com.payermax.fin.exchange.domainservice.newExchange;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.payermax.card.security.api.provider.CardChannelTokenFacade;
import com.payermax.card.security.api.request.CardChannelTokenAddRequest;
import com.payermax.card.security.api.response.CardChannelTokenAddResponse;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.fin.exchange.common.enums.ApiModeEnum;
import com.payermax.fin.exchange.common.enums.ExtParamEnum;
import com.payermax.fin.exchange.common.enums.NetWorkTokenStatusEnum;
import com.payermax.fin.exchange.domain.trans.RequestOrderDO;
import com.payermax.fin.exchange.domain.trans.token.ChannelTokenRequestDO;
import com.payermax.fin.exchange.domain.trans.token.ChannelTokenResponseDO;
import com.payermax.fin.exchange.domain.trans.token.TokenPayInfoDO;
import com.payermax.fin.exchange.domainservice.bo.BizContextBO;
import com.payermax.fin.exchange.domainservice.bo.TransactionBO;
import com.payermax.fin.exchange.integration.rpc.constants.InternalUrlConstants;
import com.payermax.fin.exchange.integration.rpc.request.ChannelTokenLookupFrontRequest;
import com.payermax.fin.exchange.integration.rpc.response.ChannelTokenLookupFrontResponse;
import com.payermax.fin.exchange.share.enums.ChannelTokenEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * ChannelToken生成,因为不生成提交单
 * <AUTHOR>
 */
@Slf4j
@Component
public class ChannelTokenLookupExchangeTemplate extends CommonExchangeTemplate<ChannelTokenRequestDO, ChannelTokenResponseDO, ChannelTokenLookupFrontRequest, ChannelTokenLookupFrontResponse> {


    @DubboReference(version = "1.0", timeout = 100)
    private CardChannelTokenFacade cardChannelTokenFacade;

    @Override
    public ChannelTokenLookupFrontRequest initFrontRequest() {
        return new ChannelTokenLookupFrontRequest();
    }

    @Override
    public String getApiMode() {
        return ApiModeEnum.PAST_FRONT.name();
    }

    @Override
    public String getApiName() {
        return InternalUrlConstants.FRONT_URL_CHANNELTOKENLOOKUP;
    }

    @Override
    public ChannelTokenLookupFrontRequest exchangeRequest(BizContextBO<ChannelTokenRequestDO, ChannelTokenResponseDO> bizContext) {
        ChannelTokenLookupFrontRequest frontRequest = super.exchangeRequest(bizContext);
        ChannelTokenRequestDO in = bizContext.getIn();
        frontRequest.setNetworkTokenId(in.getChannelTokenId());
        frontRequest.setApi(InternalUrlConstants.CHANNEL_GATEWAY_API_CHANNELTOKENLOOKUP);
        return frontRequest;
    }


    @Override
    public ChannelTokenResponseDO exchangeResponse(ChannelTokenLookupFrontResponse response, BizContextBO<ChannelTokenRequestDO, ChannelTokenResponseDO> bizContext) {
        ChannelTokenResponseDO channelTokenResponseDO = super.exchangeResponse(response, bizContext);
        ChannelTokenRequestDO requestDO = bizContext.getIn();
        channelTokenResponseDO.setNetworkTokenId(StringUtils.isBlank(response.getNetworkTokenId()) ? requestDO.getChannelTokenId() : response.getNetworkTokenId());
        channelTokenResponseDO.setAuthenticationMethodTypes(response.getAuthenticationMethodTypes());
        //不需要通过commit表取转换。
        channelTokenResponseDO.setStatus(response.getStatus());
        //更新认证方式
        updateAuthenticationMethodToCardServer(bizContext, channelTokenResponseDO);
        return channelTokenResponseDO;
    }


    /**
     * 更新到卡服务
     */
    private void updateAuthenticationMethodToCardServer(BizContextBO<ChannelTokenRequestDO, ChannelTokenResponseDO> bizContext, ChannelTokenResponseDO channelTokenResponseDO) {
        try {
            //1、更新前check是否需要更新
            List<String> newAuthenticationMethodTypes = channelTokenResponseDO.getAuthenticationMethodTypes();
            String networkTokenId = channelTokenResponseDO.getNetworkTokenId();
            if (CollectionUtils.isEmpty(newAuthenticationMethodTypes)) {
                return;
            }
            ChannelTokenRequestDO request = bizContext.getIn();
            TransactionBO transactionBO = (TransactionBO) bizContext.getBaseBo();
            RequestOrderDO.InnerUseInfo innerUseInfo = request.getInnerUseInfo();
            if (innerUseInfo == null || innerUseInfo.getTokenPayInfo() == null) {
                return;
            }
            Object tokenPayInfoObject = innerUseInfo.getTokenPayInfo();
            if (!(tokenPayInfoObject instanceof TokenPayInfoDO)) {
                return;
            }
            TokenPayInfoDO tokenPayInfo = (TokenPayInfoDO) tokenPayInfoObject;
            List<String> authenticationMethodTypes = Optional.ofNullable(tokenPayInfo.getAuthenticationMethodTypes()).orElse(new ArrayList<>());
            boolean equalCollection = CollectionUtils.isEqualCollection(authenticationMethodTypes, newAuthenticationMethodTypes);
            if (equalCollection || !StringUtils.equals(tokenPayInfo.getChannelTokenId(), networkTokenId)) {
                return;
            }

            //2、更新到卡服务
            CardChannelTokenAddRequest updateAuthenticationMethodRequest = new CardChannelTokenAddRequest();
            updateAuthenticationMethodRequest.setTokenChannel(transactionBO.getChannelCode());
            updateAuthenticationMethodRequest.setTokenChannelMid(transactionBO.getChannelMerchantCode());
            updateAuthenticationMethodRequest.setTokenType(ChannelTokenEnum.NETWORK_TOKEN.getCode());

            Map<String, String> params = request.getParams();
            updateAuthenticationMethodRequest.setCardIdentityNo(params.get(ExtParamEnum.CARD_IDENTIFIER_NO.getCode()));
            updateAuthenticationMethodRequest.setTokenValue(tokenPayInfo.getChannelTokenId());
            updateAuthenticationMethodRequest.setStatus(NetWorkTokenStatusEnum.ACTIVATED.getType());
            updateAuthenticationMethodRequest.setAuthenticationMethodTypes(CollUtil.newHashSet(newAuthenticationMethodTypes));
            //基于卡标识+tokenId+渠道编码ID
            Result<CardChannelTokenAddResponse> cardChannelTokenAddResponseResult = cardChannelTokenFacade.addCardChannelToken(updateAuthenticationMethodRequest);
            if (!cardChannelTokenAddResponseResult.isSuccess()) {
                log.warn("ChannelTokenIdExchangeTemplate.updateAuthenticationMethodToCardServer fail,request:{}", JSONObject.toJSONString(request));
            }
        } catch (Exception e) {
            log.warn("ChannelTokenIdExchangeTemplate.updateAuthenticationMethodToCardServer error,request:{}", JSONObject.toJSONString(channelTokenResponseDO));
        }
    }
}
