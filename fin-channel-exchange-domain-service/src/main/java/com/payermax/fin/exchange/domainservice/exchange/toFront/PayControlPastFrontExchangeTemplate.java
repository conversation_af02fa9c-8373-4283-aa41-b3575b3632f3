package com.payermax.fin.exchange.domainservice.exchange.toFront;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.util.JsonUtils;
import com.payermax.fin.exchange.common.enums.*;
import com.payermax.fin.exchange.dal.entity.*;
import com.payermax.fin.exchange.domain.trans.ReturnParamDO;
import com.payermax.fin.exchange.domain.trans.pay.PayControlRequestOrderDO;
import com.payermax.fin.exchange.domain.trans.pay.PayControlResponseOrderDO;
import com.payermax.fin.exchange.domainservice.bo.BizContextBO;
import com.payermax.fin.exchange.domainservice.bo.ChannelConfigJsonBO;
import com.payermax.fin.exchange.domainservice.bo.TransactionBO;
import com.payermax.fin.exchange.domainservice.exchange.AbstractTransExchangeTemplate;
import com.payermax.fin.exchange.domainservice.expression.ExpressionUtils;
import com.payermax.fin.exchange.domainservice.expression.JSONPathExecutor;
import com.payermax.fin.exchange.domainservice.repository.IChannelStepApiMappingService;
import com.payermax.fin.exchange.domainservice.util.AmountUtils;
import com.payermax.fin.exchange.integration.rpc.request.*;
import com.payermax.fin.exchange.integration.rpc.response.*;
import com.payermax.fin.exchange.service.enums.ResultTypeEnum;
import com.payermax.fin.exchange.share.domain.EnvInfoDO;
import com.payermax.fin.exchange.share.domain.PaymentMethodDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * front支付控制交换模板
 *
 * <AUTHOR>
 * @date 2021/8/13 15:54
 */
@Slf4j
@Service
public class PayControlPastFrontExchangeTemplate extends AbstractTransExchangeTemplate<PayControlRequestOrderDO, PayControlResponseOrderDO> {

    @Autowired
    protected IChannelStepApiMappingService channelStepApiMappingService;

    @Override
    protected OrderNoPrefixEnum getOrderNoPrefix() {
        return OrderNoPrefixEnum.CONTROL_COMMIT;
    }

    @NacosValue(value = "#{${queryPayElement.bankCodeMap:{}}}", autoRefreshed = true)
    private Map<String, String> bankCodeMap;

    @Override
    protected InternalBaseRequest buildExchangeRequest(BizContextBO<PayControlRequestOrderDO, PayControlResponseOrderDO> bizContext) {
        PayControlRequestOrderDO request = bizContext.getIn();
        TransactionBO transactionBO = getTransactionBO(bizContext);

        String channelCode = transactionBO.getChannelCode();
        String channelMerchantCode = transactionBO.getChannelMerchantCode();
        String commitOrderNo = transactionBO.getChannelPayCommitNo();
        ChannelRequestOrderEntity requestOrder = transactionBO.getChannelRequestOrder();
        ChannelConfigMappingEntity channelConfigMapping = transactionBO.getChannelConfigMappingEntity();
        ChannelCommitOrderEntity quoteCommitOrder = transactionBO.getQuoteCommitOrder();
        ChannelRequestOrderEntity quoteRequestOrder = transactionBO.getQuoteRequestOrder();
        ChannelInfoEntity channelInfo = transactionBO.getChannelInfoEntity();
        Map<String, String> params = request.getParams();
        // 对应的老系统channelCode
        String origChannelCode = channelCode;
        if (channelConfigMapping != null && StringUtils.isNotBlank(channelConfigMapping.getOldChannelCode())) {
            origChannelCode = channelConfigMapping.getOldChannelCode();
        }

        // 获取请求类型
        RequestTypeEnum requestTypeEnum = transactionBO.getRequestType();
        // 参数映射
        InternalBaseRequest exchangeRequest = null;
        switch (requestTypeEnum) {
            case OTP_VERIFY:
                if (quoteCommitOrder == null) {
                    throw new BusinessException(ErrorCodeEnum.CHANNEL_ORDER_IS_NOT_EXIST.getCode(), "关联支付单不存在");
                }
                PayFrontRequest payFrontRequest = new PayFrontRequest();
                payFrontRequest.setChannelCode(origChannelCode);
                payFrontRequest.setChannelMerchantCode(channelMerchantCode);
                if (channelConfigMapping != null) {
                    payFrontRequest.setMethodCode(channelConfigMapping.getOldMethodCode());
                    payFrontRequest.setMethodSubCode(channelConfigMapping.getOldMethodSubCode());
                    payFrontRequest.setCountry(quoteRequestOrder.getCountry());
                    payFrontRequest.setProductCode(channelConfigMapping.getOldProductCode());
                }
                payFrontRequest.setChannelOrderNo(commitOrderNo);
                payFrontRequest.setAmount(AmountUtils.toLeastTwoScale(quoteRequestOrder.getAmount()));
                payFrontRequest.setCurrency(quoteRequestOrder.getCurrency());
                payFrontRequest.setMerchantId(quoteRequestOrder.getMerchantIdSendToChannel());

                payFrontRequest.setPurchaseInfo(quoteRequestOrder.getPurchaseInfo());
                Map<String, String> passInfoToChannelMap = new HashMap<>();
                passInfoToChannelMap.put(PastParamEnum.otp.name(), params.get(PastParamEnum.otp.getNewParamKey()));
                passInfoToChannelMap.put(PastParamEnum.channelCertificationNo.name(), quoteCommitOrder.getChannelPayCommitNo());
                payFrontRequest.setPassInfoToChannel(JsonUtils.toString(passInfoToChannelMap));
                //用户语言处理
                payFrontRequest.setLanguage(request.getOrderInfo().getUserLanguage());
                if (null == payFrontRequest.getPassInfoToChannelByPayment()) {
                    payFrontRequest.setPassInfoToChannelByPayment(new PayFrontRequest.PassInfoToChannelByPayment());
                }
                payFrontRequest.getPassInfoToChannelByPayment().setLanguage(request.getOrderInfo().getUserLanguage());
                EnvInfoDO envInfo = request.getOrderInfo().getEnvInfo();
                if (envInfo != null) {
                    payFrontRequest.setPayerRemoteIp(envInfo.getClientIp());
                }
                payFrontRequest.setAddField1(quoteCommitOrder.getAddField1());
                payFrontRequest.setAddField2(quoteCommitOrder.getAddField2());
                payFrontRequest.setAddField3(quoteCommitOrder.getAddField3());
                payFrontRequest.setAddField4(quoteCommitOrder.getAddField4());
                payFrontRequest.setAddField5(quoteCommitOrder.getAddField5());
                payFrontRequest.setAddField6(quoteCommitOrder.getAddField6());
                exchangeRequest = payFrontRequest;
                break;
            case OTP_APPLY_RETRY:
                if (quoteCommitOrder == null) {
                    throw new BusinessException(ErrorCodeEnum.CHANNEL_ORDER_IS_NOT_EXIST.getCode(), "关联支付单不存在");
                }
                RepeatCertificationFrontRequest repeatRequest = new RepeatCertificationFrontRequest();
                repeatRequest.setChannelCode(origChannelCode);
                repeatRequest.setChannelMerchantCode(channelMerchantCode);
                repeatRequest.setChannelCertificationNo(quoteCommitOrder.getChannelPayCommitNo());
                repeatRequest.setThirdCertificationNo(quoteCommitOrder.getThirdOrgOrderNo());
                repeatRequest.setMerchantId(quoteRequestOrder.getMerchantIdSendToChannel());
                repeatRequest.setLanguage(request.getOrderInfo().getUserLanguage());
                exchangeRequest = repeatRequest;
                break;
            case QUERY_PAY_ELEMENT:
                PayElementFrontRequest payElementFrontRequest = new PayElementFrontRequest();
                payElementFrontRequest.setChannelCode(origChannelCode);
                payElementFrontRequest.setChannelMerchantCode(channelMerchantCode);
                exchangeRequest = payElementFrontRequest;
                break;
            case QUERY_EXT_PAY_INFO:
                if (quoteCommitOrder == null) {
                    throw new BusinessException(ErrorCodeEnum.CHANNEL_ORDER_IS_NOT_EXIST.getCode(), "关联支付单不存在");
                }
                ExternalPayInfoFrontRequest externalFrontRequest = new ExternalPayInfoFrontRequest();
                externalFrontRequest.setChannelCode(origChannelCode);
                externalFrontRequest.setChannelMerchantCode(channelMerchantCode);
                externalFrontRequest.setChannelOrderNo(quoteCommitOrder.getChannelPayCommitNo());
                if (params != null) {
                    externalFrontRequest.setOtp(params.get(PastParamEnum.otp.getNewParamKey()));
                }
                exchangeRequest = externalFrontRequest;
                break;
            default:
                // 构建参数
                PayControlBaseFrontRequest frontRequest = new PayControlBaseFrontRequest();
                frontRequest.setChannelCode(origChannelCode);
                frontRequest.setNewChannelCode(channelCode);
                frontRequest.setChannelMerchantCode(channelMerchantCode);
                frontRequest.setChannelOrderNo(commitOrderNo);
                frontRequest.setMerchantId(requestOrder.getMerchantIdSendToChannel());
                frontRequest.setCountry(requestOrder.getCountry());
                frontRequest.setCurrency(requestOrder.getCurrency());
                frontRequest.setAmount(requestOrder.getAmount());
                if (quoteCommitOrder != null) {
                    frontRequest.setOrigChannelOrderNo(quoteCommitOrder.getChannelPayCommitNo());
                    frontRequest.setOrigThirdOrgOrderNo(quoteCommitOrder.getThirdOrgOrderNo());
                }
                exchangeRequest = frontRequest;
                break;
        }
        if (exchangeRequest instanceof FrontRequest) {
            FrontRequest frontRequest = (FrontRequest) exchangeRequest;
            // 设置系统渠道编码等信息
            frontRequest.setNewChannelCode(transactionBO.getChannelCode());
            frontRequest.setChannelMethodCode(transactionBO.getChannelMethodCode());
            if (Objects.nonNull(channelInfo)) {
                ChannelConfigJsonBO channelConfigJson = channelInfoService.getConfigJsonByChannel(channelInfo);
                frontRequest.setTechnicalOrg(channelInfo.getTechnicalOrg());
                frontRequest.setTargetService(channelConfigJson.getTargetService());
            }
            if (Objects.nonNull(requestOrder)) {
                frontRequest.setPaymentMethodType(requestOrder.getPaymentMethodType());
                frontRequest.setTargetOrCardOrg(requestOrder.getTargetOrCardOrg());
                frontRequest.setBankCode(requestOrder.getExtJsonBo().getBankCode());
            }
            // 设置api名称
            frontRequest.setApi(getChannelGatewayApi(bizContext, requestTypeEnum.getCode()));
        }
        return exchangeRequest;
    }

    @Override
    protected Object buildExchangeRequestAfter(BizContextBO<PayControlRequestOrderDO, PayControlResponseOrderDO> bizContext, InternalBaseRequest baseRequest) {
        // 不是支付控制基础请求类，则返回
        if (!(baseRequest instanceof PayControlBaseFrontRequest)) {
            return baseRequest;
        }
        PayControlRequestOrderDO request = bizContext.getIn();
        Map<String, String> paramData = request.getParams();

        TransactionBO transactionBO = getTransactionBO(bizContext);
        ChannelStepApiMappingEntity currentStepApiMapping = transactionBO.getCurrentStepApiMapping();
        // 获取参数映射配置
        Map<String, String> tmpParamsMapping = null;
        if (StringUtils.isNotBlank(currentStepApiMapping.getParamsMapping())) {
            tmpParamsMapping = JsonUtils.toBean(Map.class, currentStepApiMapping.getParamsMapping());
        }

        // 把请求入参转成Map
        Map baseRequestMap = JsonUtils.toBean(Map.class, JSON.toJSONString(baseRequest));
        // 根据配置进行参数映射
        if (tmpParamsMapping != null && !tmpParamsMapping.isEmpty()) {
            tmpParamsMapping.forEach((tempField, valKey) -> {
                try {
                    Object value = ExpressionUtils.execute(valKey, paramData);
                    if (value == null) {
                        return;
                    }
                    JSONPathExecutor.set(baseRequestMap, tempField, value);
                } catch (Exception e) {
                    log.warn("采集参数(valKey:{}, Field:{})映射到Front系统入参错误，需排查！", valKey, tempField, e);
                }
            });
        }
        return baseRequestMap;
    }

    @Override
    protected String getInternalUrl(BizContextBO<PayControlRequestOrderDO, PayControlResponseOrderDO> bizContext) {
        TransactionBO transactionBO = getTransactionBO(bizContext);
        ChannelStepApiMappingEntity currentStepApiMapping = transactionBO.getCurrentStepApiMapping();
        return currentStepApiMapping.getBackendApiUrl();
    }

    @Override
    protected ResultDTO doInvokeInternal(BizContextBO<PayControlRequestOrderDO, PayControlResponseOrderDO> bizContext) {
        TransactionBO transactionBO = getTransactionBO(bizContext);
        Object baseRequest = transactionBO.getExchangeRequest();
        InternalBaseResponse baseResponse = transactionBO.getInternalBaseResponse();
        // 不是支付控制基础响应类，则返回
        if (!(baseResponse instanceof PayControlBaseFrontResponse)) {
            return super.doInvokeInternal(bizContext);
        }

        // 调用下游接口
        ResultDTO internalResultDTO = channelInternalClientProxy.signAndSend(transactionBO.getApiModeEnum(),
                transactionBO.getChannelCode(), this.getInternalUrl(bizContext), baseRequest, Map.class);
        // 获取Map响应
        Map resultMap = (Map) internalResultDTO.getData();

        // 把Map响应转换成PayControlBaseFrontResponse对象
        PayControlBaseFrontResponse baseFrontResponse = JsonUtils.toBean(PayControlBaseFrontResponse.class, JsonUtils.toString(resultMap));
        baseFrontResponse.setData(resultMap);

        // 构建新的ResultDTO
        ResultDTO finalResultDTO = ResultDTO.buildSuccess(baseFrontResponse);
        finalResultDTO.setBizCode(internalResultDTO.getBizCode());
        finalResultDTO.setMessage(internalResultDTO.getMessage());
        return finalResultDTO;
    }

    @Override
    protected boolean verifyApplyResponse(InternalBaseResponse baseResponse, ChannelCommitOrderEntity commitOrder) {
        return true;
    }

    @Override
    protected void fillOrderAfterExchange(ChannelCommitOrderEntity channelCommitOrder, BizContextBO<PayControlRequestOrderDO, PayControlResponseOrderDO> bizContext) {
        TransactionBO transactionBO = getTransactionBO(bizContext);
        // 获取请求类型
        RequestTypeEnum requestTypeEnum = transactionBO.getRequestType();

        switch (requestTypeEnum) {
            case OTP_VERIFY:
                PayFrontResponse applyResponse = (PayFrontResponse) transactionBO.getInternalBaseResponse();
                // 设置提交单属性
                channelCommitOrder.setThirdOrgOrderNo(applyResponse.getChannelTransactionId());
                channelCommitOrder.setFourthOrgOrderNo(applyResponse.getChannelThirdPartyTxnId());
                break;
            case OTP_APPLY_RETRY:
                RepeatCertificationFrontResponse repeatResponse = (RepeatCertificationFrontResponse) transactionBO.getInternalBaseResponse();
                // 设置提交单属性
                channelCommitOrder.setThirdOrgOrderNo(repeatResponse.getThirdCertificationNo());
                channelCommitOrder.setFourthOrgOrderNo(repeatResponse.getFourthPartyCertificationNo());
                break;
            case QUERY_PAY_ELEMENT:
            case QUERY_EXT_PAY_INFO:
                break;
            default:
                PayControlBaseFrontResponse payControlResponse  = (PayControlBaseFrontResponse) transactionBO.getInternalBaseResponse();
                // 设置提交单属性
                channelCommitOrder.setThirdOrgOrderNo(payControlResponse.getChannelTransactionId());
                channelCommitOrder.setFourthOrgOrderNo(payControlResponse.getChannelThirdPartyTxnId());
        }
    }

    @Override
    protected PayControlResponseOrderDO buildCommonOut(BizContextBO<PayControlRequestOrderDO, PayControlResponseOrderDO> bizContext) {
        TransactionBO transactionBO = getTransactionBO(bizContext);
        ChannelCommitOrderEntity channelCommitOrder = transactionBO.getChannelCommitOrder();
        ChannelCommitOrderEntity quoteCommitOrder = transactionBO.getQuoteCommitOrder();

        // 构建UnifyControlResponse对象
        PayControlResponseOrderDO controlResponseDTO = new PayControlResponseOrderDO();
        // 如果关联提交单存在，则返回关联提交单的请求单号、提交单号（统一控制接口，永远返回支付单的请求单号、提交单号）
        if (quoteCommitOrder != null) {
            controlResponseDTO.setChannelPayRequestNo(quoteCommitOrder.getChannelPayRequestNo());
            controlResponseDTO.setChannelPayCommitNo(quoteCommitOrder.getChannelPayCommitNo());
        } else {
            controlResponseDTO.setChannelPayRequestNo(transactionBO.getChannelPayRequestNo());
            controlResponseDTO.setChannelPayCommitNo(transactionBO.getChannelPayCommitNo());
        }
        controlResponseDTO.setStatus(OrderStatusEnum.getOrderStatusByStatus(channelCommitOrder.getStatus()));
        return controlResponseDTO;
    }

    @Override
    protected void fillSuccessOut(PayControlResponseOrderDO controlResponseDTO, BizContextBO<PayControlRequestOrderDO, PayControlResponseOrderDO> bizContext) {
        TransactionBO transactionBO = getTransactionBO(bizContext);
        RequestTypeEnum requestTypeEnum = transactionBO.getRequestType();
        PayControlRequestOrderDO payControlRequestOrderDO = bizContext.getIn();
        ChannelConfigMappingEntity channelConfigMapping = transactionBO.getChannelConfigMappingEntity();
        ChannelStepApiMappingEntity currentStepApiMapping = transactionBO.getCurrentStepApiMapping();
        InternalBaseResponse internalBaseResponse = transactionBO.getInternalBaseResponse();

        ReturnParamDO returnParam = new ReturnParamDO();
        controlResponseDTO.setReturnParam(returnParam);
        // 业务数据
        switch (requestTypeEnum) {
            case OTP_VERIFY:
                controlResponseDTO.setResultType(ResultTypeEnum.API.getType());
                break;
            case OTP_APPLY_RETRY:
                controlResponseDTO.setResultType(ResultTypeEnum.COLLECT.getType());
                break;
            case QUERY_PAY_ELEMENT:
                controlResponseDTO.setResultType(ResultTypeEnum.RENDER.getType());
                PayElementFrontResponse elementResponse = (PayElementFrontResponse) internalBaseResponse;
                String jsonInfo = elementResponse.getElementJsonInfo();
                ReturnParamDO.BankTransferInfo bankTransferInfo = null;
                if (StringUtils.isBlank(jsonInfo)) {
                    // 无合法数据，则抛出异常
                    throw new BusinessException(ErrorCodeEnum.CHANNEL_NO_RESPONSE_DATA.getCode(), ErrorCodeEnum.CHANNEL_NO_RESPONSE_DATA.getMsg());
                }
                Map<String, Map> jsonInfoMap = JsonUtils.toBean(Map.class, jsonInfo);
                if (channelConfigMapping == null) {
                    // 无合法数据，则抛出异常
                    throw new BusinessException(ErrorCodeEnum.CHANNEL_NO_RESPONSE_DATA.getCode(), ErrorCodeEnum.CHANNEL_NO_RESPONSE_DATA.getMsg());
                }
                //bankCode -> methodSubCode -> jsonInfo
                Map subMap = Optional.ofNullable(payControlRequestOrderDO)
                        .map(PayControlRequestOrderDO::getPaymentMethod)
                        .map(PaymentMethodDO::getBankCode)
                        .map(bankCodeMap::get)
                        .map(jsonInfoMap::get)
                        .orElse(null);
                //兼容旧数据
                if (Objects.isNull(subMap)) {
                    subMap = jsonInfoMap.get(channelConfigMapping.getOldMethodSubCode());
                }
                bankTransferInfo = new ReturnParamDO.BankTransferInfo();
                if (subMap != null) {
                    bankTransferInfo.setBankId(String.valueOf(subMap.get("bank_id")));
                    bankTransferInfo.setBankName(String.valueOf(subMap.get("bank_adi")));
                    bankTransferInfo.setAccountHolder(String.valueOf(subMap.get("hesap_adi")));
                    bankTransferInfo.setAccountNumber(String.valueOf(subMap.get("hesap_no")));
                    bankTransferInfo.setBranchNo(String.valueOf(subMap.get("sube_kodu")));
                    bankTransferInfo.setIbanNo(String.valueOf(subMap.get("iban")));
                } else {
                    // 无合法数据，则抛出异常
                    throw new BusinessException(ErrorCodeEnum.CHANNEL_NO_RESPONSE_DATA.getCode(), ErrorCodeEnum.CHANNEL_NO_RESPONSE_DATA.getMsg());
                }
                returnParam.setBankTransferInfo(bankTransferInfo);
                break;
            case QUERY_EXT_PAY_INFO:
                controlResponseDTO.setResultType(ResultTypeEnum.RENDER.getType());
                ExternalPayInfoFrontResponse externalResponse = (ExternalPayInfoFrontResponse) internalBaseResponse;
                String paymentInfoStr = externalResponse.getPaymentInfo();
                if (StringUtils.isNotBlank(paymentInfoStr)) {
                    ExternalPayInfoFrontResponse.PaymentInfo paymentInfo = JsonUtils.toBean(ExternalPayInfoFrontResponse.PaymentInfo.class, paymentInfoStr);
                    // barcode
                    List<String> barCodeList = new ArrayList<>();
                    barCodeList.add(paymentInfo.getTransaction1());
                    barCodeList.add(paymentInfo.getTransaction2());
                    barCodeList.add(paymentInfo.getTransaction3());
                    returnParam.setBarCodeList(barCodeList);
                    returnParam.setExpiryTime(paymentInfo.getDateTimeStr());
                }
                break;
        }
        // 配置映射
        if (Objects.nonNull(currentStepApiMapping)) {
            if (StringUtils.isNotBlank(currentStepApiMapping.getResultType())) {
                controlResponseDTO.setResultType(currentStepApiMapping.getResultType());
            }
            // 结果映射
            dealResultParamMapping(internalBaseResponse, returnParam, currentStepApiMapping.getResultMapping());
        }
    }

}
