package com.payermax.fin.exchange.domainservice.state.impl;

import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.util.JsonUtils;
import com.payermax.fin.exchange.common.enums.ErrorCodeEnum;
import com.payermax.fin.exchange.common.enums.MainRequestTypeEnum;
import com.payermax.fin.exchange.common.enums.OrderEventEnum;
import com.payermax.fin.exchange.common.enums.OrderStatusEnum;
import com.payermax.fin.exchange.dal.entity.ChannelRequestOrderEntity;
import com.payermax.fin.exchange.domain.trans.RequestOrderDO;
import com.payermax.fin.exchange.domain.trans.pay.PayRequestOrderDO;
import com.payermax.fin.exchange.domain.trans.token.TokenPayInfoDO;
import com.payermax.fin.exchange.domainservice.repository.IChannelRequestOrderService;
import com.payermax.fin.exchange.domainservice.state.AbstractOrderStateMachineBuilder;
import com.payermax.fin.exchange.domainservice.state.StateConstants;
import com.payermax.fin.exchange.domainservice.util.ChannelTokenUtils;
import com.payermax.fin.exchange.share.enums.PaymentTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.action.Action;
import org.springframework.statemachine.config.StateMachineBuilder;
import org.springframework.statemachine.guard.Guard;
import org.springframework.statemachine.state.State;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 默认请求单状态机构建器
 *
 * <AUTHOR>
 * @date 2021/8/16 14:56
 */
@Slf4j
@Component
public class DefaultRequestOrderStateMachineBuilder extends AbstractOrderStateMachineBuilder {

    private static final String REQUEST_ORDER_MACHINE_ID = "REQUEST_ORDER_MACHINE_ID";

    @Autowired
    private IChannelRequestOrderService channelRequestOrderService;
    @Autowired
    private ChannelTokenUtils channelTokenUtils;

    @Override
    public StateMachine<OrderStatusEnum, OrderEventEnum> build(OrderStatusEnum initState, BeanFactory beanFactory) throws Exception {
        StateMachineBuilder.Builder<OrderStatusEnum, OrderEventEnum> builder = buildStateMachineBuilder(REQUEST_ORDER_MACHINE_ID, initState, beanFactory);

        builder.configureTransitions()
                .withExternal().source(OrderStatusEnum.SUCCESS).target(OrderStatusEnum.RETRY_PENDING)
                .event(OrderEventEnum.CALLBACK).guard(guardWithTarget(PaymentTypeEnum.PAYOUT)).action(this.successAction(), this.errorAction()).and()
                .withExternal().source(OrderStatusEnum.SUCCESS).target(OrderStatusEnum.RETRY_PENDING)
                .event(OrderEventEnum.INQUIRY).guard(guardWithTarget(PaymentTypeEnum.PAYOUT)).action(this.successAction(), this.errorAction()).and()
                .withExternal().source(OrderStatusEnum.RETRY_PENDING).target(OrderStatusEnum.RETRY_PENDING)
                .event(OrderEventEnum.APPLY).guard(guardWithTarget(PaymentTypeEnum.PAYOUT)).action(this.successAction(), this.errorAction()).and()
                .withExternal().source(OrderStatusEnum.RETRY_PENDING).target(OrderStatusEnum.SUCCESS)
                .event(OrderEventEnum.APPLY).guard(guardWithTarget(PaymentTypeEnum.PAYOUT)).action(this.successAction(), this.errorAction()).and()
                .withExternal().source(OrderStatusEnum.RETRY_PENDING).target(OrderStatusEnum.BOUNCEBACK)
                .event(OrderEventEnum.APPLY).guard(guardWithTarget(PaymentTypeEnum.PAYOUT)).action(this.successAction(), this.errorAction()).and()
                .withExternal().source(OrderStatusEnum.RETRY_PENDING).target(OrderStatusEnum.RETRY_PENDING)
                .event(OrderEventEnum.CALLBACK).guard(guardWithTarget(PaymentTypeEnum.PAYOUT)).action(this.successAction(), this.errorAction()).and()
                .withExternal().source(OrderStatusEnum.RETRY_PENDING).target(OrderStatusEnum.SUCCESS)
                .event(OrderEventEnum.CALLBACK).guard(guardWithTarget(PaymentTypeEnum.PAYOUT)).action(this.successAction(), this.errorAction()).and()
                .withExternal().source(OrderStatusEnum.RETRY_PENDING).target(OrderStatusEnum.BOUNCEBACK)
                .event(OrderEventEnum.CALLBACK).guard(guardWithTarget(PaymentTypeEnum.PAYOUT)).action(this.successAction(), this.errorAction()).and()
                .withExternal().source(OrderStatusEnum.RETRY_PENDING).target(OrderStatusEnum.RETRY_PENDING)
                .event(OrderEventEnum.INQUIRY).guard(guardWithTarget(PaymentTypeEnum.PAYOUT)).action(this.successAction(), this.errorAction()).and()
                .withExternal().source(OrderStatusEnum.RETRY_PENDING).target(OrderStatusEnum.SUCCESS)
                .event(OrderEventEnum.INQUIRY).guard(guardWithTarget(PaymentTypeEnum.PAYOUT)).action(this.successAction(), this.errorAction()).and()
                .withExternal().source(OrderStatusEnum.RETRY_PENDING).target(OrderStatusEnum.BOUNCEBACK)
                .event(OrderEventEnum.INQUIRY).guard(guardWithTarget(PaymentTypeEnum.PAYOUT)).action(this.successAction(), this.errorAction());
        return builder.build();
    }

    /**
     * 判断当前状态和目标状态是否一致
     *
     * @return
     */
    protected Guard<OrderStatusEnum, OrderEventEnum> guardWithTarget() {
        return stateContext -> {
            // 获取目标状态
            State<OrderStatusEnum, OrderEventEnum> targetState = stateContext.getTarget();
            if (targetState == null || targetState.getId() == null) {
                return false;
            }
            // 获取订单
            ChannelRequestOrderEntity requestOrderEntity = this.getChannelOrderFromStateContext(stateContext);
            return null != requestOrderEntity && requestOrderEntity.getStatus().equals(targetState.getId().getStatus());
        };
    }

    /**
     * 判断当前状态和目标状态是否一致
     *
     * @return
     */
    protected Guard<OrderStatusEnum, OrderEventEnum> guardWithTarget(PaymentTypeEnum targetPaymentType) {
        return stateContext -> {
            if (targetPaymentType == null) {
                return false;
            }
            // 获取目标状态
            State<OrderStatusEnum, OrderEventEnum> targetState = stateContext.getTarget();
            if (targetState == null || targetState.getId() == null) {
                return false;
            }
            // 获取订单
            ChannelRequestOrderEntity requestOrderEntity = this.getChannelOrderFromStateContext(stateContext);
            if (null == requestOrderEntity) {
                return false;
            }
            // 状态一致并且支付类型一致
            boolean result = requestOrderEntity.getStatus().equals(targetState.getId().getStatus())
                    && requestOrderEntity.getPaymentType().equals(targetPaymentType.getValue());
            return result;
        };
    }

    /**
     * 从状态机上下文获取订单信息
     *
     * @param stateContext
     * @return
     */
    private ChannelRequestOrderEntity getChannelOrderFromStateContext(StateContext<OrderStatusEnum, OrderEventEnum> stateContext) {
        if (null != stateContext) {
            return stateContext.getExtendedState().get(ChannelRequestOrderEntity.class, ChannelRequestOrderEntity.class);
        }
        return null;
    }

    /**
     * 状态机成功Action
     *
     * @return
     */
    protected Action<OrderStatusEnum, OrderEventEnum> successAction() {
        return stateContext -> {
            // 只要走到action中，就说明状态机校验通过，设置执行结果为true
            stateContext.getExtendedState().getVariables().put(StateConstants.VAR_KEY_EXECUTE_RESULT, true);

            Integer origStatus = stateContext.getExtendedState().get(StateConstants.VAR_KEY_ORIG_STATUS, Integer.class);
            ChannelRequestOrderEntity requestOrder = stateContext.getExtendedState().get(ChannelRequestOrderEntity.class, ChannelRequestOrderEntity.class);
            requestOrder.setEventType(stateContext.getEvent().name());
            // 如果订单是终态，则使用当前时间作为请求单完成时间
            if (OrderStatusEnum.isFinalStatus(requestOrder.getStatus())) {
                requestOrder.setCompleteTime(new Date());
            }
            int result = channelRequestOrderService.update(origStatus, requestOrder);
            // 说明乐观锁导致更新失败
            if (result == 0) {
                log.warn("Channel request order update fail. origStatus = {}, requestOrder = {}", origStatus, JsonUtils.toString(requestOrder));
                throw new BusinessException(ErrorCodeEnum.UPDATE_ORDER_FAIL.getCode(), "channel request order update fail");
            }
            //更新认证方式
            threeDSAuthenticationMethodType(requestOrder);
        };
    }

    /**
     * 状态机失败action
     *
     * @return
     */
    protected Action<OrderStatusEnum, OrderEventEnum> errorAction() {
        return stateContext -> {
            stateContext.getExtendedState().getVariables().put(Exception.class, stateContext.getException());
        };
    }

    /**
     * 3ds认证方式更新
     */
    private void threeDSAuthenticationMethodType(ChannelRequestOrderEntity requestOrder) {
        try {
            boolean beforeCheck = MainRequestTypeEnum.TRANSACTION.getValue().equals(requestOrder.getMainRequestType());
            beforeCheck = beforeCheck && PaymentTypeEnum.PAYMENT.getValue().equals(requestOrder.getPaymentType());
            beforeCheck = beforeCheck && OrderStatusEnum.SUCCESS.getStatus().equals(requestOrder.getStatus());
            //入款+并且订单状态成功+交易类
            if (!beforeCheck) {
                return;
            }
            ChannelRequestOrderEntity.ExtJsonBO extJsonBo = requestOrder.getExtJsonBo();
            if (extJsonBo == null || StringUtils.isBlank(extJsonBo.getTokenPayInfo())) {
                return;
            }
            PayRequestOrderDO requestOrderDO = JsonUtils.toBean(PayRequestOrderDO.class, requestOrder.getRequestBody());
            RequestOrderDO.InnerUseInfo innerUseInfo = new RequestOrderDO.InnerUseInfo();
            innerUseInfo.setTokenPayInfo(JsonUtils.toBean(TokenPayInfoDO.class, extJsonBo.getTokenPayInfo()));
            requestOrderDO.setInnerUseInfo(innerUseInfo);
            channelTokenUtils.asyncUpdateAuthenticationMethodTypes(requestOrderDO, requestOrder);
        } catch (Exception e) {
            log.error("statemachine-threeDSAuthenticationMethodType,error:", e);
        }
    }

}
