package com.payermax.fin.exchange.domainservice.rule.route;

import com.payermax.fin.exchange.common.enums.ChannelLimitEnum;
import com.payermax.fin.exchange.dal.entity.ChannelLimitEntity;
import com.payermax.fin.exchange.dal.entity.ChannelRequestOrderEntity;
import com.payermax.fin.exchange.domainservice.repository.IChannelRequestOrderService;
import com.payermax.fin.exchange.domainservice.service.limit.ILimitService;
import com.payermax.fin.exchange.share.bo.RouteConditionBO;
import com.payermax.fin.exchange.share.constants.ShareConstants;
import com.payermax.fin.exchange.share.domain.ChannelInstanceDO;
import com.payermax.fin.exchange.share.rule.RuleConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @date 2022/6/17 14:56
 */
@Slf4j
@Component
public class LimitRouteRule extends AbstractRouteRule {

    @Autowired
    private ILimitService limitService;

    @Autowired
    private IChannelRequestOrderService channelRequestOrderService;

    @Override
    public int getOrder() {
        return RuleConstants.ROUTE_ORDER_LIMIT;
    }

    @Override
    public void execute(RouteConditionBO condition, List<ChannelInstanceDO> dataList) {
        try {
            if (dataList.isEmpty() || StringUtils.isBlank(condition.getPayRequestNo())) {
                return;
            }
            ChannelRequestOrderEntity requestOrder = channelRequestOrderService.getByRequestOrderNoFromMaster(condition.getPayRequestNo());
            if (Objects.isNull(requestOrder)) {
                return;
            }
            List<ChannelInstanceDO> failedDataList = new ArrayList<>();
            List<ChannelInstanceDO> backupDataList = new ArrayList<>();
            // 获取所有无法通过限额限次的渠道
            for (ChannelInstanceDO data : dataList) {
                ChannelLimitEntity entity = limitService.canPassLimit(requestOrder, data.getChannelCode(), data.getChannelMerchantCode());
                if (Objects.nonNull(entity)) {
                    if (ChannelLimitEnum.FAILED == entity.getHitStrategy()) {// 交易失败
                        failedDataList.add(data);
                    } else if (ChannelLimitEnum.INIT == entity.getHitStrategy() || ChannelLimitEnum.ORIG_CHANNEL == entity.getHitStrategy()) { // 换渠道重发（如果有其他渠道）
                        backupDataList.add(data);
                    }
                }
            }

            // 1. 处理命中“交易失败”限流策略的渠道实例
            this.handleLimit(dataList, failedDataList);
            // 2. 处理命中切换备用渠道策略的渠道实例
            this.handleLimit(dataList, backupDataList);
        } catch (Exception e) {
            log.info("LimitRouteRule execute error",e);
        }
    }

    /**
     * 处理命中限流策略的渠道实例
     *
     * @param dataList
     * @param limitDataList
     */
    private void handleLimit(List<ChannelInstanceDO> dataList, List<ChannelInstanceDO> limitDataList) {
        // 全命中限流规则，无法择优，则此规则忽略
        if (limitDataList.size() == 0 || dataList.size() == limitDataList.size()) {
            return;
        }
        // 除了命中限流规则的渠道实例无其他允许切换的渠道实例，则此规则忽略
        long noLimitCount = dataList.stream().filter(data -> ShareConstants.YES_FLAG.equals(data.getIsAllowAutoSwitch())).filter(data -> !limitDataList.contains(data)).count();
        if (noLimitCount == 0) {
            return;
        }
        // 移除命中限流规则的渠道实例
        dataList.removeIf(data -> {
            // 命中限流规则，则过滤
            boolean result = limitDataList.contains(data);
            if (result) {
                super.setFilteredRoutedDetail(data.getChannelCode(), data.getChannelMethodCode(), data.getChannelMerchantCode(), null, "命中限流规则");
                return true;
            }
            // 不允许被自动切换的渠道实例，则过滤
            result = !ShareConstants.YES_FLAG.equals(data.getIsAllowAutoSwitch());
            if (result) {
                super.setFilteredRoutedDetail(data.getChannelCode(), data.getChannelMethodCode(), data.getChannelMerchantCode(), null, "不允许自动切换的渠道实例");
                return true;
            }
            return false;
        });
    }
}
