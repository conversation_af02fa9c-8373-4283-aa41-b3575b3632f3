package com.payermax.fin.exchange.domainservice.newExchange;

import com.payermax.common.lang.util.JsonUtils;
import com.payermax.fin.exchange.common.enums.ApiModeEnum;
import com.payermax.fin.exchange.common.enums.OrderStatusEnum;
import com.payermax.fin.exchange.dal.entity.ChannelCommitOrderEntity;
import com.payermax.fin.exchange.dal.entity.ChannelRequestOrderEntity;
import com.payermax.fin.exchange.domain.inquiry.CompensateInquiryRequestDO;
import com.payermax.fin.exchange.domainservice.bo.BizContextBO;
import com.payermax.fin.exchange.domainservice.bo.CompensateTransactionBO;
import com.payermax.fin.exchange.domainservice.repository.IChannelMethodService;
import com.payermax.fin.exchange.domainservice.rpcproxy.ChannelInternalClientProxy;
import com.payermax.fin.exchange.domainservice.util.OrderCompensateUtils;
import com.payermax.fin.exchange.integration.rpc.request.FrontRequest;
import com.payermax.fin.exchange.integration.rpc.request.InternalBaseRequest;
import com.payermax.fin.exchange.integration.rpc.response.InternalBaseResponse;
import com.payermax.fin.exchange.integration.rpc.response.ResultDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 补偿查询抽象ExchangeTemplate
 *
 * <AUTHOR>
 * @date 2024/12/14 16:41
 */
public abstract class AbstractCompensateExchangeTemplate<SIN extends CompensateInquiryRequestDO, TIN extends InternalBaseRequest, TOUT extends InternalBaseResponse> implements IExchangeTemplate<SIN, OrderStatusEnum, TIN, TOUT> {

    @Autowired
    protected IChannelMethodService channelMethodService;

    @Autowired
    protected ChannelInternalClientProxy channelInternalClientProxy;

    /**
     * front层的请求类
     *
     * @return
     */
    public abstract Class<TIN> frontRequestClass();

    @Override
    public String getApiMode() {
        return ApiModeEnum.PAST_FRONT.name();
    }

    @Override
    public TIN exchangeRequest(BizContextBO<SIN, OrderStatusEnum> bizContext) {
        CompensateInquiryRequestDO requestBO = bizContext.getIn();
        ApiModeEnum apiModeEnum = requestBO.getApiModeEnum();
        String requestJson = requestBO.getRequestJson();
        CompensateTransactionBO transactionBO = getTransactionBO(bizContext);
        ChannelRequestOrderEntity requestOrder = transactionBO.getRequestOrder();
        ChannelCommitOrderEntity commitOrder = transactionBO.getCommitOrder();
        String channelMethodCode = transactionBO.getChannelMethodCode();

        TIN frontRequest;
        // 如果已经有报文，则转成指定查询对象
        if (StringUtils.isNotBlank(requestJson)) {
            frontRequest = JsonUtils.toBean(frontRequestClass(), requestBO.getRequestJson());
        } else {
            // 构建查询对象
            frontRequest = (TIN) OrderCompensateUtils.buildRequestBody(apiModeEnum, requestOrder, commitOrder);
        }
        // 设置需要传递到渠道网关的扩展配置
        if (frontRequest instanceof FrontRequest) {
            ((FrontRequest) frontRequest).setChannelMethodExtConfig(channelMethodService.getChannelExtConfigByMethodCode(channelMethodCode));
        }
        return frontRequest;
    }

    @Override
    public ResultDTO<TOUT> invoke(Object request, BizContextBO<SIN, OrderStatusEnum> bizContext) {
        CompensateTransactionBO transactionBO = getTransactionBO(bizContext);
        InternalBaseResponse baseResponse = transactionBO.getInternalBaseResponse();
        String apiName = transactionBO.getApiName();
        if (request instanceof FrontRequest) {
            FrontRequest frontRequest = (FrontRequest) request;
            // 补偿查询，请求渠道网关时替换真实商户号
            if (StringUtils.isNotBlank(frontRequest.getMerchantIdSendToChannel())) {
                frontRequest.setMerchantId(frontRequest.getMerchantIdSendToChannel());
            }
        }

        // 调用下游接口
        return channelInternalClientProxy.signAndSend(transactionBO.getApiModeEnum(), transactionBO.getChannelCode(),
                apiName, request, baseResponse.getClass());
    }


    @Override
    public OrderStatusEnum exchangeResponse(TOUT response, BizContextBO<SIN, OrderStatusEnum> bizContext) {
        return null;
    }

    /**
     * 获取上下文中间数据对象
     *
     * @param bizContext
     * @return
     */
    protected CompensateTransactionBO getTransactionBO(BizContextBO<SIN, OrderStatusEnum> bizContext) {
        Object baseBo = bizContext.getBaseBo();
        return (CompensateTransactionBO) baseBo;
    }
}
