package com.payermax.fin.exchange.domainservice;

import com.payermax.common.lang.exception.BusinessException;
import com.payermax.fin.exchange.common.enums.*;
import com.payermax.fin.exchange.common.util.ValidationUtils;
import com.payermax.fin.exchange.dal.entity.*;
import com.payermax.fin.exchange.dal.entity.query.ChannelCommitOrderEntityQuery;
import com.payermax.fin.exchange.domain.trans.RequestOrderDO;
import com.payermax.fin.exchange.domain.trans.RetryRequestOrderDO;
import com.payermax.fin.exchange.domain.trans.RetryResponseOrderDO;
import com.payermax.fin.exchange.domainservice.bo.*;
import com.payermax.fin.exchange.domainservice.producer.ExchangeMQProducer;
import com.payermax.fin.exchange.domainservice.repository.*;
import com.payermax.fin.exchange.domainservice.rpcproxy.ChannelInternalClientProxy;
import com.payermax.fin.exchange.domainservice.state.StateMachineExecutor;
import com.payermax.fin.exchange.domainservice.util.LockUtils;
import com.payermax.fin.exchange.domainservice.util.OrderCompensateUtils;
import com.payermax.fin.exchange.domainservice.util.RetryUtils;
import com.payermax.fin.exchange.domainservice.verify.IVerifyService;
import com.payermax.fin.exchange.integration.rpc.response.FrontVerifyResponse;
import com.payermax.fin.exchange.integration.rpc.response.InternalBaseResponse;
import com.payermax.fin.exchange.integration.rpc.response.ResultDTO;
import com.payermax.fin.exchange.share.constants.ShareConstants;
import com.payermax.fin.exchange.share.enums.PaymentTypeEnum;
import com.payermax.common.lang.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 交易领域服务抽象实现类
 *
 * <AUTHOR>
 * @date 2021/12/12 22:01
 */
@Slf4j
public abstract class AbstractRetryTransDomainService<IN extends RetryRequestOrderDO, OUT extends RetryResponseOrderDO> extends AbstractDomainService<IN, OUT> {

    @Autowired
    protected IChannelOrderCompensateService channelOrderCompensateService;
    @Autowired
    protected IChannelRequestOrderService channelRequestOrderService;
    @Autowired
    protected ChannelInternalClientProxy channelInternalClientProxy;
    @Autowired
    protected IChannelCommitOrderService channelCommitOrderService;
    @Autowired
    protected IChannelResultOrderService channelResultOrderService;
    @Autowired
    protected StateMachineExecutor stateMachineExecutor;
    @Autowired
    protected ExchangeMQProducer exchangeMQProducer;
    @Autowired
    protected IVerifyService verifyService;
    @Autowired
    protected RetryUtils retryUtils;
    @Autowired
    protected LockUtils lockUtils;
    
    @Override
    protected void paramCheckSelf(BizContextBO<IN, OUT> bizContext) {
        super.paramCheckSelf(bizContext);
        TransactionBO transactionBO = getTransactionBO(bizContext);
        
        RetryRequestOrderDO contextIn = bizContext.getIn();
        ValidationUtils.notBlankCheck(contextIn.getChannelPayRequestNo(), "[channelPayRequestNo] is mandatory");
        ValidationUtils.notNullCheck(contextIn.getRetryType(), "[retryType] is mandatory");

        String mainType = MainRequestTypeEnum.TRANSACTION.getValue();
        PaymentTypeEnum paymentType = contextIn.getPaymentType();
        String channelPayRequestNo = contextIn.getChannelPayRequestNo();
        transactionBO.setChannelPayRequestNo(channelPayRequestNo);
        
        // 获取请求单分布式锁
        boolean lock = lockUtils.lockRequestOrder(channelPayRequestNo, paymentType);
        if (!lock) {
            throw new BusinessException(ErrorCodeEnum.DISTRIBUTED_LOCK_ACQUISITION_FAILED.getCode(), ErrorCodeEnum.DISTRIBUTED_LOCK_ACQUISITION_FAILED.getMsg());
        }

        // 查询渠道请求单
        ChannelRequestOrderEntity requestOrder = checkAndGetChannelRequestOrder(channelPayRequestNo);
        transactionBO.setChannelRequestOrder(requestOrder);
        
        
        switch (contextIn.getRetryType()) {
            case ABNORMAL_APPLY:
                // 查询渠道请求单的提交单
                ChannelCommitOrderEntityQuery entityQuery = new ChannelCommitOrderEntityQuery();
                entityQuery.setChannelPayRequestNo(channelPayRequestNo);
                entityQuery.setMainRequestType(mainType);
                entityQuery.setPaymentType(paymentType.getValue());
                List<ChannelCommitOrderEntity> commitOrders = channelCommitOrderService.getByEntityQueryFromMaster(entityQuery);
                if (CollectionUtils.isNotEmpty(commitOrders)) {
                    List<ChannelCommitOrderEntity> notFailedCommitOrder = commitOrders.stream().filter(t -> !Objects.equals(t.getStatus(), OrderStatusEnum.FAILED.getStatus())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(notFailedCommitOrder)) {
                        throw new BusinessException(ErrorCodeEnum.UNSUPPORTED_STATE_TRANS.getCode(), "原始渠道请求单下存在非失败提交单,不能进行差错处理或重试!");
                    }
                }
                break;
            case APPLY:
                ValidationUtils.notBlankCheck(contextIn.getChannelPayCommitNo(), "[channelPayCommitNo] is mandatory");
                String channelPayCommitNo = contextIn.getChannelPayCommitNo();
                // 查询提交单
                ChannelCommitOrderEntity commitOrder = channelCommitOrderService.getByRequestAndCommitOrderNoFromMaster(channelPayRequestNo, channelPayCommitNo);
                if (OrderStatusEnum.isFinalStatus(commitOrder.getStatus())) {
                    throw new BusinessException(ErrorCodeEnum.UNSUPPORTED_STATE_TRANS.getCode(), "原始渠道提交单已到达终态,不能进行提交单重试!");
                }
                transactionBO.setChannelCommitOrder(commitOrder);
                break;
            default:
                break;
        }
        bizContext.setBaseBo(transactionBO);
    }

    @Override
    protected void beforeBusiness(BizContextBO<IN, OUT> bizContext) {
        // 设置上下文中间数据对象
        TransactionBO transactionBO = getTransactionBO(bizContext);
        bizContext.setBaseBo(transactionBO);
        // 请求参数数据校验&填充
        checkAndFillBeforeRouting(bizContext);
        
    }

    @Override
    protected void executeBusiness(BizContextBO<IN, OUT> bizContext) throws Exception {
        OUT resultDTO = initOut();
        RetryRequestOrderDO retryTransOrderDO = bizContext.getIn();
        switch (retryTransOrderDO.getRetryType()) {
            // 请求单重试
            case ABNORMAL_APPLY:
                resultDTO = requestOrderRetry(bizContext);
                break;
            // 提交单重试
            case APPLY:
                resultDTO = commitOrderRetry(bizContext);
                break;
            default:
                break;
        }
        bizContext.setOut(resultDTO);
    }

    @Override
    protected void afterBusiness(BizContextBO<IN, OUT> bizContext) {
        // 1. 父类
        super.afterBusiness(bizContext);
    }

    /**
     * 当error情况下，是否返回data
     *
     * @return
     */
    protected boolean isReturnDataWhenError() {
        return true;
    }

    /**
     * 请求单重试
     *
     * @param bizContext 交易信息上下文
     * @return ResultDTO<OUT>
     */
    protected OUT requestOrderRetry(BizContextBO<IN, OUT> bizContext) {
        return executeRequestOrderRetry(bizContext);
    };

    /**
     * 请求单重试调用
     *
     * @param bizContext 交易信息上下文
     * @return ResultDTO<OUT>
     */
    protected abstract OUT executeRequestOrderRetry(BizContextBO<IN, OUT> bizContext);

    /**
     * 提交单重试
     *
     * @param bizContext 交易信息上下文
     * @return ResultDTO<OUT>
     */
    protected OUT commitOrderRetry(BizContextBO<IN, OUT> bizContext) {
        TransactionBO transactionBO = getTransactionBO(bizContext);
        ChannelRequestOrderEntity requestOrder = transactionBO.getChannelRequestOrder();
        ChannelCommitOrderEntity commitOrder = transactionBO.getChannelCommitOrder();
        
        // 调用下游
        ResultDTO internalResultDTO = executeCommitOrderRetry(bizContext);
        // 正确成功响应下处理
        InternalBaseResponse internalBaseResponse = (InternalBaseResponse) internalResultDTO.getData();
        // 同步报文合法性校验，如果校验不通过，则保持pending状态
        if (!verifyApplyResponse(internalBaseResponse, commitOrder)) {
            transactionBO.setOrderStatusEnum(OrderStatusEnum.PENDING);
        } else {
            transactionBO.setOrderStatusEnum(internalBaseResponse.getOrderStatus());
        }

        OrderStatusEnum orderStatusEnum = transactionBO.getOrderStatusEnum();
        // 更新提交单以及请求单
        commitOrder.getExtJsonBo().setOrderSendTime(new Date());
        TransactionRetryBO retryBO = updateRequestAndCommitOrder(requestOrder, commitOrder, requestOrder.getStatus(), commitOrder.getStatus(), orderStatusEnum.getStatus(), internalResultDTO);

        // 终态更新补偿查询单并落结果单
        if (OrderStatusEnum.isFinalStatus(orderStatusEnum.getStatus())) {
            // 关闭补偿查询单信息
            stopOrderCompensate(requestOrder.getChannelPayRequestNo(), commitOrder.getChannelPayCommitNo());
            addChannelResultOrder(commitOrder, internalResultDTO);
        } else {
            // 更新补偿查询单信息
            updateOrderCompensate(commitOrder, RetryTypeEnum.APPLY);
        }
        // 通知上游
        exchangeMQProducer.sendNotifyMQ(requestOrder, commitOrder);
        
        // 构建响应对象
        OUT responseOrderDO = Objects.isNull(bizContext.getOut()) ? initOut() : bizContext.getOut();
        if (Objects.nonNull(retryBO) && Objects.nonNull(retryBO.getRetryType())) {
            responseOrderDO.setNextRetryType(retryBO.getRetryType().name());
        }
        return responseOrderDO;
    }

    /**
     * 提交单重试调用下游
     *
     * @param bizContext 交易信息上下文
     * @return ResultDTO<OUT>
     */
    protected abstract ResultDTO executeCommitOrderRetry(BizContextBO<IN, OUT> bizContext);

    /**
     * 获取上下文中间数据对象
     *
     * @param bizContext
     * @return
     */
    protected TransactionBO getTransactionBO(BizContextBO<IN, OUT> bizContext) {
        Object baseBo = bizContext.getBaseBo();
        if (baseBo == null) {
            return new TransactionBO();
        }
        if (!(baseBo instanceof TransactionBO)) {
            throw new BusinessException(ErrorCodeEnum.INNER_ERROR.getCode(), "baseBo no instanceof TransactionBO");
        }
        return (TransactionBO) baseBo;
    }

    /**
     * 路由前数据校验&填充
     *
     * @param bizContext 交易信息上下文
     * @apiNote 主要针对是否需要进行路由
     */
    protected void checkAndFillBeforeRouting(BizContextBO<IN, OUT> bizContext) {
        TransactionBO transactionBO = getTransactionBO(bizContext);
        RetryRequestOrderDO retryTransOrderDO = bizContext.getIn();
        ChannelRequestOrderEntity channelRequestOrder = transactionBO.getChannelRequestOrder();
        // 业务单号
        retryTransOrderDO.setBizOrderNo(channelRequestOrder.getBizOrderNo());
        retryTransOrderDO.setSourceBizOrderNo(channelRequestOrder.getSourceBizOrderNo());
        
        // 重试请求单号
        RequestOrderDO.InnerUseInfo innerUseInfo = retryTransOrderDO.getInnerUseInfo();
        if (innerUseInfo == null) {
            innerUseInfo = new RequestOrderDO.InnerUseInfo();
            retryTransOrderDO.setInnerUseInfo(innerUseInfo);
        }
        innerUseInfo.setChannelPayRequestNo(channelRequestOrder.getChannelPayRequestNo());
    }
    
    /**
     * 填充自定义out
     *
     * @param bizContext
     */
    protected void fillCustomOut(BizContextBO<IN, OUT> bizContext) {}

    /**
     * 初始化响应
     *
     * @return
     */
    protected abstract OUT initOut();

    /**
     * 校验并获取请求单信息
     *
     * @param channelPayRequestNo
     * @return
     */
    private ChannelRequestOrderEntity checkAndGetChannelRequestOrder(String channelPayRequestNo) {
        if (StringUtils.isBlank(channelPayRequestNo)) {
            return null;
        }
        ChannelRequestOrderEntity orderEntity = channelRequestOrderService.getByRequestOrderNoFromMaster(channelPayRequestNo);
        if (orderEntity == null) {
            throw new BusinessException(ErrorCodeEnum.CHANNEL_ORDER_IS_NOT_EXIST.getCode(), "渠道请求单不存在");
        }
        if (OrderStatusEnum.isFinalStatus(orderEntity.getStatus())) {
            throw new BusinessException(ErrorCodeEnum.CHANNEL_ORDER_IS_FINAL.getCode(), "渠道请求单已为终态，不能操作");
        }
        return orderEntity;
    }


    /**
     * 更新请求单与提交单
     *
     * @param requestOrderEntity
     * @param commitOrderEntity
     * @param oriRequestStatus
     * @param oriCommitStatus
     * @param orderStatus
     * @param internalResult
     */
    protected abstract TransactionRetryBO updateRequestAndCommitOrder(ChannelRequestOrderEntity requestOrderEntity, ChannelCommitOrderEntity commitOrderEntity,
                                                             Integer oriRequestStatus, Integer oriCommitStatus, Integer orderStatus, ResultDTO internalResult);

    /**
     * 落结果单
     *
     * @param commitOrderEntity
     * @param internalResultDTO
     */
    protected void addChannelResultOrder(ChannelCommitOrderEntity commitOrderEntity, ResultDTO internalResultDTO) {
        ChannelResultOrderEntity resultOrder = new ChannelResultOrderEntity();
        try {
            resultOrder.setChannelPayRequestNo(commitOrderEntity.getChannelPayRequestNo());
            resultOrder.setChannelPayCommitNo(commitOrderEntity.getChannelPayCommitNo());
            resultOrder.setEventType(OrderEventEnum.APPLY.name());
            resultOrder.setResponseCode(commitOrderEntity.getResponseCode());
            resultOrder.setResponseMsg(commitOrderEntity.getResponseMsg());
            resultOrder.setResponseBody(com.payermax.common.lang.util.JsonUtils.toString(internalResultDTO));
            channelResultOrderService.add(resultOrder);
        } catch (Exception e) {
            log.error("add channel result order error. resultOrder = {}", JsonUtils.toString(resultOrder), e);
        }
    }

    /**
     * 关闭订单补偿查询表
     *
     * @param channelPayRequestNo
     * @param channelPayCommitNo
     */
    protected void stopOrderCompensate(String channelPayRequestNo, String channelPayCommitNo) {
        ChannelOrderCompensateEntity orderCompensate = new ChannelOrderCompensateEntity();
        orderCompensate.setChannelPayRequestNo(channelPayRequestNo);
        orderCompensate.setChannelPayCommitNo(channelPayCommitNo);
        orderCompensate.setRetryType(RetryTypeEnum.INQUIRY.name());
        orderCompensate.setRetryFlag(ShareConstants.NUMBER_ZERO);
        channelOrderCompensateService.update(orderCompensate);
    }

    /**
     * 更新订单补偿查询表
     *
     * @param commitOrder
     */
    protected void updateOrderCompensate(ChannelCommitOrderEntity commitOrder, RetryTypeEnum retryTypeEnum) {
        switch (retryTypeEnum) {
            case APPLY:
                ChannelOrderCompensateEntity orderCompensate = new ChannelOrderCompensateEntity();
                orderCompensate.setChannelCode(commitOrder.getChannelCode());
                orderCompensate.setPaymentType(commitOrder.getPaymentType());
                orderCompensate.setChannelMethodCode(commitOrder.getChannelMethodCode());
                orderCompensate.setChannelPayRequestNo(commitOrder.getChannelPayRequestNo());
                orderCompensate.setChannelPayCommitNo(commitOrder.getChannelPayCommitNo());
                orderCompensate.setRetryType(RetryTypeEnum.INQUIRY.name());
                orderCompensate.setAddField4(-1);
                orderCompensate.setRetryTime(OrderCompensateUtils.calculateNextRetryTime(orderCompensate));
                channelOrderCompensateService.updateAvailableCompensateOrder(orderCompensate);
                break;
            default:
                break;
        }
       
    }

    /**
     * 校验查询响应报文是否合法
     *
     * @param baseResponse
     * @param commitOrder
     */
    protected boolean verifyApplyResponse(InternalBaseResponse baseResponse, ChannelCommitOrderEntity commitOrder) {
        // 非支付类请求，则不校验报文（如3ds等控制类）
        if (!MainRequestTypeEnum.TRANSACTION.getValue().equals(commitOrder.getMainRequestType())) {
            return true;
        }
        if (!(baseResponse instanceof FrontVerifyResponse)) {
            log.warn("Apply response not instanceof FrontVerifyResponse, pls check it. response = {}", com.payermax.common.lang.util.JsonUtils.toString(baseResponse));
            return true;
        }
        FrontVerifyResponse verifyResponse = (FrontVerifyResponse) baseResponse;
        OrderVerifyBO orderVerify = new OrderVerifyBO();
        orderVerify.setPaymentTypeEnum(PaymentTypeEnum.getByValue(commitOrder.getPaymentType()));
        orderVerify.setEventEnum(OrderEventEnum.APPLY);
        orderVerify.setOriCommitOrder(commitOrder);
        orderVerify.setNewStatus(verifyResponse.getOrderStatus().getStatus());
        orderVerify.setAmount(verifyResponse.getMoney());
        orderVerify.setIgnoreAmountVerify(verifyResponse.isIgnoreAmountVerify());
        orderVerify.setThirdOrgOrderNo(verifyResponse.getChannelTransactionId());
        boolean verifyResult = verifyService.verify(orderVerify);
        return verifyResult;
    }

}
