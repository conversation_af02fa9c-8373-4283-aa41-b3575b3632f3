<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.payermax.channel</groupId>
        <artifactId>channel-exchange</artifactId>
        <version>1.0.0</version>
    </parent>
    <artifactId>channel-exchange-domain-service</artifactId>
    <name>channel-exchange-domain-service</name>
    <version>${channel-exchange.version}</version>

    <dependencies>
        <dependency>
            <groupId>com.payermax.channel</groupId>
            <artifactId>channel-exchange-dal</artifactId>
            <version>${channel-exchange.version}</version>
        </dependency>
        <dependency>
            <groupId>com.payermax.channel</groupId>
            <artifactId>channel-exchange-integration</artifactId>
            <version>${channel-exchange.version}</version>
        </dependency>
        <dependency>
            <groupId>com.payermax.channel</groupId>
            <artifactId>channel-exchange-domain</artifactId>
            <version>${channel-exchange.version}</version>
        </dependency>
        <dependency>
            <groupId>com.payermax.channel</groupId>
            <artifactId>channel-exchange-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.infra</groupId>
            <artifactId>ionia-tool-distributed-id</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ushareit.fintech.parent</groupId>
            <artifactId>fintech-components-distributed</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ushareit.fintech.parent</groupId>
            <artifactId>fintech-components-mq-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.infra</groupId>
            <artifactId>ionia-rocketMQ</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ushareit.fintech.parent</groupId>
            <artifactId>fintech-components-dynamic-thread-pool</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.statemachine</groupId>
            <artifactId>spring-statemachine-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <version>1.3.1</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.mvel</groupId>
            <artifactId>mvel2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <!-- EasyExcel -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.2.0</version>
        </dependency>
        <dependency>
            <groupId>com.payermax.infra</groupId>
            <artifactId>ionia-rocketMQ</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hippo4j</groupId>
            <artifactId>hippo4j-spring-boot-starter</artifactId>
            <version>2.0.0-beta</version>
            <exclusions>
                <exclusion>
                    <artifactId>hutool-all</artifactId>
                    <groupId>cn.hutool</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>javax.mail</groupId>
            <artifactId>mail</artifactId>
            <version>1.4.7</version>
        </dependency>
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.15.3</version>
        </dependency>
        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId>
            <version>0.1.55</version>
        </dependency>
        <dependency>
            <groupId>com.payermax.infra</groupId>
            <artifactId>ionia-fs-all-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>net.javacrumbs.json-unit</groupId>
            <artifactId>json-unit</artifactId>
        </dependency>
    </dependencies>

</project>
