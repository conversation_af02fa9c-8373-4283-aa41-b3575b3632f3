package com.payermax.fin.exchange.integration.rpc.proxy;

import com.alibaba.fastjson.JSON;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.funds.receipt.facade.api.ReceiptOrderFacade;
import com.payermax.funds.receipt.facade.request.OrderDetailQueryRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * 来账服务client
 *
 * <AUTHOR>
 * @date 2022/10/13 22:35
 */
@Component
@Slf4j
public class FundsReceiptClientProxy {

    private final String METHOD_QUERY_ORDER_DETAIL = "queryOrderDetail";

    @DubboReference(version = "1.0", check = false, lazy = true, timeout = 2000)
    private ReceiptOrderFacade receiptOrderFacade;

    /**
     * 执行dubbo方法*
     *
     * @param method
     * @param arg
     * @return
     */
    public String invoke(String method, Object arg) {
        // 参数校验
        if (StringUtils.isBlank(method) || arg == null) {
            log.error("FundsReceiptClientProxy.invoke request parameter is null.");
            return null;
        }
        Result result = null;
        switch (method) {
            case METHOD_QUERY_ORDER_DETAIL:
                OrderDetailQueryRequest request = (OrderDetailQueryRequest) arg;
                result = receiptOrderFacade.queryOrderDetail(request);
                break;
            default:
                log.error("Not support method: {}", method);
                break;
        }
        if (result == null) {
            return null;
        }
        String respJson = JSON.toJSONString(result);
        return respJson;
    }

}
