package com.payermax.fin.exchange.integration.rpc.proxy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.fin.exchange.common.enums.ApiModeEnum;
import com.payermax.fin.exchange.integration.rpc.response.ResultDTO;
import com.ushareit.components.exception.service.StandardErrorCodeClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.google.common.base.Preconditions.checkArgument;

/**
 * <AUTHOR>
 * @apiNote 标准错误转换组件
 */
@Slf4j
@Component
public class StandardErrorCodeClientProxy {

    private Map<String, String> systemIdMap = new HashMap<>(8);

    @NacosValue(value = "${common.mapRespCodeOnSys.systemIdMap:}", autoRefreshed = true)
    public void setSystemIdMap(String systemIdMapStr) {
        if (StringUtils.isNotBlank(systemIdMapStr)) {
            systemIdMap = JSONObject.parseObject(systemIdMapStr, new TypeReference<Map<String, String>>() {
            });
        } else {
            systemIdMap.clear();
        }
        log.info("StandardErrorCodeClientProxy.setSystemIdMapStr: {}", systemIdMapStr);
    }

    @Resource
    private StandardErrorCodeClient standardErrorCodeClient;

    /**
     * 服务调用间的错误码映射（系统）
     *
     * @param systemId 取值不全来自于ApiModeEnum， ex: ApiModeEnum.RISK_QUERY.name()
     * @param resultDTO 响应报文
     */
    public void translateOnSys(String systemId, ResultDTO resultDTO){
        checkArgument(StringUtils.isNotBlank(systemId), "param systemId is mandatory!");

        /*
          调用 in-pay-channel-front/ in-pay-channel，不做映射处理
         */
        if (ApiModeEnum.PAST_FRONT.name().equals(systemId)
                || ApiModeEnum.PAST_CHANNEL.name().equals(systemId)){
            return;
        }

        String destAppName = StringUtils.defaultIfBlank(systemIdMap.get(systemId), systemId) ;

        String bizCode = resultDTO.getBizCode();
        String message = resultDTO.getMessage();
        Map<String, Object> paramMap = buildParamMap(bizCode, message);

        try {
            Result convert = standardErrorCodeClient.convert(destAppName, bizCode, message, paramMap);
            Optional.ofNullable(convert).ifPresent(result -> {
                if (StringUtils.isNotBlank(result.getCode())){
                    resultDTO.setBizCode(result.getCode());
                }
                if (StringUtils.isNotBlank(result.getMsg())){
                    resultDTO.setMessage(result.getMsg());
                }
            });
        } catch (Exception e) {
            log.error("<<== destAppName:{}, resultDTO:{}, error: ", destAppName, JSON.toJSONString(resultDTO));
        }
    }


    private Map<String, Object> buildParamMap(String code, String msg){
        Map<String, Object> map = new HashMap<>();
        map.put("code", code);
        map.put("msg", msg);
        return map;
    }
}
