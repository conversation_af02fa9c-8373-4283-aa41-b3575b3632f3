package com.payermax.fin.exchange.integration.rpc.proxy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONPath;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.channel.gateway.facade.api.ChannelGatewayApi;
import com.payermax.channel.gateway.facade.api.ChannelGatewayApiRequest;
import com.payermax.common.lang.model.dto.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/12/12 20:50
 **/
@Component
@Slf4j
public class ChannelGatewayClientProxy {

    @NacosValue(value = "#{'${channel-gateway.instCode.useTechnicalOrg:O_DANA_P01_WALLET101_DANA_ID_SNAP}'.split(',')}", autoRefreshed = true)
    private List<String> useTechnicalOrgChannelMethodCode;

    @DubboReference(version = "1.0", check = false, retries = 0, timeout = 12000)
    private ChannelGatewayApi channelGatewayApi;

    /**
     * 执行dubbo方法
     */
    public String invoke(String api, Object args, String standardChannelCode) {
        // 参数校验
        if (StringUtils.isBlank(api) || Objects.isNull(args)
                || (StringUtils.isBlank((String) JSONPath.eval(args, "$.technicalOrg")) && StringUtils.isBlank(standardChannelCode))) {
            log.error("ChannelGatewayClientProxy.invoke request parameter or api is null.");
            return null;
        }
        ChannelGatewayApiRequest request = new ChannelGatewayApiRequest();
        request.setVersion("1.0");
        request.setData(JSON.toJSONString(args));
        Optional.ofNullable((String) JSONPath.eval(args, "$.api")).ifPresent(request::setApi);
        // 先从data中获取api，如果为空，则使用api参数（新渠道直接使用配置网关，api即为网关的接口名）
        if (StringUtils.isBlank(request.getApi())) {
            request.setApi(api);
        }

        // [instCode兼容逻辑] 随着渠道接入平台项目将默认使用渠道编码，历史已集成渠道则继续使用技术集成机构(以渠道支付方式维度兼容)
        if (useTechnicalOrgChannelMethodCode.contains(Optional.ofNullable((String) JSONPath.eval(args, "$.channelMethodCode")).orElse(StringUtils.EMPTY))) {
            Optional.ofNullable((String) JSONPath.eval(args, "$.technicalOrg")).ifPresent(request::setInstCode);
        } else {
            request.setInstCode(standardChannelCode);
        }
        Optional.ofNullable((String) JSONPath.eval(args, "$.channelMerchantCode")).ifPresent(request::setInstMerchantCode);
        Result<String> result = channelGatewayApi.invoke(request);
        if (result == null) {
            return null;
        }
        return JSON.toJSONString(new Result<>(JSON.parse(result.getData()), result.getMsg(), result.getCode()));
    }
}
