package com.payermax.fin.exchange;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.payermax.fin.exchange.common.enums.ApiModeEnum;
import com.payermax.fin.exchange.common.enums.RetryTypeEnum;
import com.payermax.fin.exchange.dal.entity.ChannelCommitOrderEntity;
import com.payermax.fin.exchange.dal.entity.ChannelOrderCompensateEntity;
import com.payermax.fin.exchange.dal.entity.CommonCode;
import com.payermax.fin.exchange.dal.entity.TargetMerchantEntity;
import com.payermax.fin.exchange.domainservice.repository.IChannelCommitOrderService;
import com.payermax.fin.exchange.domainservice.repository.ITargetMerchantService;
import com.payermax.fin.exchange.domainservice.repository.impl.ChannelOrderCompensateServiceImpl;
import com.payermax.fin.exchange.domainservice.service.codeMapping.ICodeMappingService;
import com.payermax.fin.exchange.domainservice.util.OrderCompensateUtils;
import com.payermax.fin.exchange.integration.rpc.proxy.StandardErrorCodeClientProxy;
import com.payermax.fin.exchange.integration.rpc.response.ResultDTO;
import com.payermax.fin.exchange.share.constants.ShareConstants;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date: 2022/8/17 16:25
 * @Description
 */
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = StartApplication.class)
public class BaseTest {

//    @Resource
//    private ChannelOrderCompensateServiceImpl channelOrderCompensateService;
//
//    @Resource
//    private ITargetMerchantService targetMerchantService;
//
//    @Resource
//    private StandardErrorCodeClientProxy standardErrorCodeClientProxy;
//
//    @Autowired
//    private ICodeMappingService<ChannelCommitOrderEntity, CommonCode> codeMappingService;
//
//    @Autowired
//    private IChannelCommitOrderService channelCommitOrderService;
//
//    @Test
//    public void testInsert() {
//        // 保持状态补偿任务
//        ChannelOrderCompensateEntity orderCompensate = new ChannelOrderCompensateEntity();
//        orderCompensate.setChannelPayRequestNo("20220115080847PP759000055027-1");
//        orderCompensate.setPaymentType("2");
//        orderCompensate.setRetryRequestStatus(0);
//        orderCompensate.setChannelPayCommitNo("");
//        orderCompensate.setChannelCode("aaa");
//        orderCompensate.setChannelMethodCode("aaa");
//        orderCompensate.setRetryCommitStatus(0);
//        orderCompensate.setAmount(BigDecimal.ZERO);
//        orderCompensate.setRetryType(RetryTypeEnum.INQUIRY.name());
//        orderCompensate.setRetryFlag(ShareConstants.NUMBER_ONE);
//        orderCompensate.setRetryTime(OrderCompensateUtils.calculateNextRetryTime(orderCompensate));
//        orderCompensate.setApiMode(ApiModeEnum.PAST_FRONT.name());
//        orderCompensate.setRetryRequestBody("");
//        int add = channelOrderCompensateService.add(orderCompensate);
//        System.out.println(add);
//    }
//
//    @Test
//    public void testMerchant() {
//        TargetMerchantEntity targetMerchant = targetMerchantService.getTargetMerchant("FAWRY", "2", "SP48625786", null);
//        System.out.println(targetMerchant);
//    }
//
//    @Test
//    public void testStandardCodeMap() {
//
//        String resultDtoStr = "{\"code\":\"APPLY_SUCCESS\",\"data\":{\"riskCode\":\"F_IN_3DS_0002\",\"riskMessage\":\"3DS_AUTH_FAIED\",\"riskResult\":\"1\"},\"msg\":\"\"}";
//
//        ResultDTO resultDTO = JSONObject.parseObject(resultDtoStr, ResultDTO.class);
//
//        standardErrorCodeClientProxy.translateOnSys(ApiModeEnum.RISK_QUERY.name(), resultDTO);
//
//        System.out.println(JSON.toJSONString(resultDTO));
//    }
//
//    @Test
//    public void testStandardCodeMapOnFundsReceipt() {
//
//        String resultDtoStr = "{\"code\":\"APPLY_SUCCESS\",\"data\":{\"amount\":{\"amount\":11200.00,\"currency\":\"PHP\"},\"billingDate\":\"2023-05-27\",\"country\":\"PH\",\"instCode\":\"NETBANKPH01\",\"instOrderNo\":\"3609374\",\"memberId\":\"20221202P01B26000076028893\",\"merchantNo\":\"P01010214185337\",\"merchantVaNickname\":\"DEFAULT\",\"payeeAccount\":\"041-001-00001-5\",\"payeeVa\":\"************\",\"payerAccount\":\"*************\",\"payerName\":\"MEXICA MAY ABRIGO DEL ROSARIO\",\"productCode\":\"1101\",\"receiptOrderNo\":\"20230527031345EO47850060171799003\",\"remark\":\"InstaPay transfer #20230527MBTCPHMMXXXG000000000129895\",\"status\":\"ASSIGN_SUCCESS\",\"tradeTime\":\"2023-05-27 03:13:44\",\"useType\":\"RECEIVE_PAY\"},\"msg\":\"\"}";
//
//        ResultDTO resultDTO = JSONObject.parseObject(resultDtoStr, ResultDTO.class);
//
//        standardErrorCodeClientProxy.translateOnSys(ApiModeEnum.FUNDS_RECEIPT.name(), resultDTO);
//
//        System.out.println(JSON.toJSONString(resultDTO));
//    }
//
//    @Test
//    public void testTranslate() {
//
//        String payCommitNo = "DCC00007016899064283674607019";
//        String paymentType = "10";
//
//        ChannelCommitOrderEntity channelCommitOrderEntity = channelCommitOrderService.getByCommitOrderNoFromSlave(payCommitNo, paymentType);
//
//        Optional<CommonCode> translate = codeMappingService.translate(channelCommitOrderEntity, true);
//
//    }
}
