package com.payermax.fin.exchange.config;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.parser.ParserConfig;
import com.alibaba.fastjson.util.TypeUtils;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.fin.exchange.dal.entity.*;
import com.payermax.fin.exchange.share.domain.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR> at 8/24/23 12:34 AM
 **/
@Slf4j
@Component
public class SafeAutoTypeCheckHandler implements ParserConfig.AutoTypeCheckHandler {

    private final Set<Class> defaultWhitelist = new HashSet<>();

    private Set<String> whitelist = new HashSet<>();

    @NacosValue(value = "${sat.list:}", autoRefreshed = true)
    public void setWhitelist(String whitelist) {
        this.whitelist = new HashSet<>(StrUtil.splitTrim(whitelist, ","));
    }

    public SafeAutoTypeCheckHandler() {
        defaultWhitelist.add(AuthorizationEntity.class);
        defaultWhitelist.add(AutoRouteRecordEntity.class);
        defaultWhitelist.add(BatchDetailEntity.class);
        defaultWhitelist.add(BatchFileEntity.class);
        defaultWhitelist.add(BatchReceiptDetailEntity.class);
        defaultWhitelist.add(BounceBackOrderEntity.class);
        defaultWhitelist.add(ChannelAdditionalTaxEntity.class);
        defaultWhitelist.add(ChannelCommitOrderEntity.class);
        defaultWhitelist.add(ChannelConfigMappingEntity.class);
        defaultWhitelist.add(ChannelEventEntity.class);
        defaultWhitelist.add(ChannelExtConfigEntity.class);
        defaultWhitelist.add(ChannelInfoEntity.class);
        defaultWhitelist.add(ChannelLimitEntity.class);
        defaultWhitelist.add(ChannelMerchantContractEntity.class);
        defaultWhitelist.add(ChannelMerchantEntity.class);
        defaultWhitelist.add(ChannelMethodEntity.class);
        defaultWhitelist.add(ChannelOrderCompensateEntity.class);
        defaultWhitelist.add(ChannelProductInfoEntity.class);
        defaultWhitelist.add(ChannelRequestOrderEntity.class);
        defaultWhitelist.add(ChannelRespCodeEntity.class);
        defaultWhitelist.add(ChannelResultOrderEntity.class);
        defaultWhitelist.add(ChannelRetryConfigEntity.class);
        defaultWhitelist.add(ChannelRouteEntity.class);
        defaultWhitelist.add(ChannelSpecialRouteEntity.class);
        defaultWhitelist.add(ChannelStepApiMappingEntity.class);
        defaultWhitelist.add(ChannelStepEntity.class);
        defaultWhitelist.add(CommonCode.class);
        defaultWhitelist.add(CommonCodeEntity.class);
        defaultWhitelist.add(CommonCodeMapEntity.class);
        defaultWhitelist.add(OrderNoMappingEntity.class);
        defaultWhitelist.add(SpecialMappingConfigEntity.class);
        defaultWhitelist.add(TargetMerchantEntity.class);
        defaultWhitelist.add(ValidateFieldEntity.class);
        defaultWhitelist.add(ValidateRuleEntity.class);
        defaultWhitelist.add(ChannelProductGrayRouteEntity.class);
        defaultWhitelist.add(ChannelProductCustomizationRouteEntity.class);

        defaultWhitelist.add(ChannelEventDO.class);
        defaultWhitelist.add(ChannelInstanceDO.class);
        defaultWhitelist.add(EnvInfoDO.class);
        defaultWhitelist.add(ExtendPropertyDO.class);
        defaultWhitelist.add(OrderInfoDO.class);
        defaultWhitelist.add(PaymentMethodDO.class);
        defaultWhitelist.add(SpecialRouteDO.class);

        defaultWhitelist.add(HashSet.class);
        defaultWhitelist.add(HashMap.class);
        defaultWhitelist.add(LinkedHashMap.class);
        defaultWhitelist.add(ArrayList.class);
        defaultWhitelist.add(LinkedList.class);
    }

    @Override
    public Class<?> handler(String typeName, Class<?> expectClass, int features) {
        Class<?> aClass = TypeUtils.loadClass(typeName);
        if (defaultWhitelist.contains(aClass)) {
            return aClass;
        }
        if (whitelist.contains(typeName)) {
            return aClass;
        }
        log.error("illegal type:{}", typeName);
        return null;
    }
}
