package com.payermax.fin.exchange.controller.omc;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.fin.exchange.domainservice.manage.BaseManage;
import com.payermax.fin.exchange.service.request.*;
import com.payermax.fin.exchange.service.response.OmcOperationResponse;
import com.payermax.fin.exchange.share.constants.ShareConstants;
import com.payermax.fin.exchange.task.request.AsyncPayoutTaskRequest;
import com.payermax.infra.ionia.log.digest.core.annotation.DigestLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Kaichong
 * @date 2021/10/22 9:58
 */
@RestController
@Slf4j
@RequestMapping(value = "/omc/payout")
@Api(description = "OMC出款订单服务", tags = "OMC出款订单服务")
public class OmcPayoutController {

    @NacosValue(value = "${payout.retry.adjustValue:6}", autoRefreshed = true)
    private int adjustValue;

    @Autowired
    private BaseManage<PayoutRetryTransactionRequest, OmcOperationResponse> payoutRetryTransactionManage;
    @Autowired
    private BaseManage<PayoutCompensateRequest, OmcOperationResponse> payoutCompensateManage;

    @Autowired
    private BaseManage<PayoutBatchRetryTransactionRequest, Void> payoutBatchRetryTransactionManage;

    @Autowired
    private BaseManage<PayoutRetryCheckRequest, Boolean> payoutRetryCheckManage;

    @Autowired
    private BaseManage<PayoutCloseOrderRequest, Boolean> payoutCloseOrderManage;

    @Autowired
    @Qualifier("abnormalPayoutApplyManage")
    private BaseManage<AsyncPayoutTaskRequest, Void> abnormalPayoutApplyManage;

    @PostMapping(value = "/retryTransaction")
    @ApiOperation(value = "出款订单重试", notes = "重新发起出款请求  \n" +
            "SYSTEM_BUSY: 通用内部未知错误---需要定位系统异常  \n" +
            "REQUEST_PARAM_INVALID: 参数错误---检查入参  \n" +
            "CHANNEL_ORDER_IS_NOT_EXIST: 渠道订单不存在---检查渠道单号是否正确  \n" +
            "CHANNEL_ORDER_NOT_PENDING: 订单不是pending状态---检查渠道单状态  \n" +
            "EXCEED_RATE_LIMIT: 渠道限流异常---检查渠道限流配置  \n" +
            "DATE_FORMAT_EXCEPTION: 下游响应数据格式异常---检查下游响应数据  \n" +
            "CHANNEL_FRONT_RPC_EXCEPTION: 下游响应数据为空---检查下游响应  \n" +
            "STATE_TRANSITION_NO_SUPPORT: 不支持的订单状态流转---检查订单状态  \n" +
            "ORDER_UPDATE_FAIL: 订单更新失败---订单状态乐观锁，检查订单状态  \n" +
            "F999: 渠道响应码未配置内部映射码时，默认返回码---检查渠道响应  \n" +
            "其它：渠道响应码配置的内部映射码")
    @DigestLog(isRecord= true)
    public Result<OmcOperationResponse> retryTransaction(@RequestBody PayoutRetryTransactionRequest request) {
        request.setIsManually(ShareConstants.YES_FLAG);
        return payoutRetryTransactionManage.execute(request);
    }

    @PostMapping(value = "/compensateQueryAndFinalState")
    @ApiOperation(value = "出款订单补偿查询", notes = "补偿查询出款订单状态  \n" +
            "SYSTEM_BUSY: 通用内部未知错误---需要定位系统异常  \n" +
            "REQUEST_PARAM_INVALID: 参数错误---检查入参  \n" +
            "CHANNEL_ORDER_IS_NOT_EXIST: 渠道订单不存在---检查渠道单号是否正确  \n" +
            "CHANNEL_ORDER_NOT_PENDING: 订单不是pending状态---检查渠道单状态  \n" +
            "DATE_FORMAT_EXCEPTION: 下游响应数据格式异常---检查下游响应数据  \n" +
            "CHANNEL_FRONT_RPC_EXCEPTION: 下游响应数据为空---检查下游响应  \n" +
            "STATE_TRANSITION_NO_SUPPORT: 不支持的订单状态流转---检查订单状态  \n" +
            "ORDER_UPDATE_FAIL: 订单更新失败---订单状态乐观锁，检查订单状态  \n" +
            "F999: 渠道响应码未配置内部映射码时，默认返回码---检查渠道响应  \n" +
            "其它：渠道响应码配置的内部映射码")
    @DigestLog(isRecord= true)
    public Result<OmcOperationResponse> compensateQueryAndFinalState(@RequestBody PayoutCompensateRequest request) {
        return payoutCompensateManage.execute(request);
    }

    @PostMapping(value = "/checkIsSupportRetry")
    @ApiOperation(value = "出款订单重发检查", notes = "出款订单重发检查  \n" +
            "SYSTEM_BUSY: 通用内部未知错误---需要定位系统异常  \n" +
            "其它：渠道响应码配置的内部映射码")
    @DigestLog(isRecord= true)
    public Result<Boolean> checkIsSupportRetry(@RequestBody PayoutRetryCheckRequest request) {
        return payoutRetryCheckManage.execute(request);
    }

    @PostMapping(value = "/batchRetryTransaction")
    @ApiOperation(value = "出款订单重试", notes = "重新发起出款请求  \n" +
            "SYSTEM_BUSY: 通用内部未知错误---需要定位系统异常  \n" +
            "REQUEST_PARAM_INVALID: 参数错误---检查入参  \n" +
            "CHANNEL_ORDER_IS_NOT_EXIST: 渠道订单不存在---检查渠道单号是否正确  \n" +
            "CHANNEL_ORDER_NOT_PENDING: 订单不是pending状态---检查渠道单状态  \n" +
            "EXCEED_RATE_LIMIT: 渠道限流异常---检查渠道限流配置  \n" +
            "DATE_FORMAT_EXCEPTION: 下游响应数据格式异常---检查下游响应数据  \n" +
            "CHANNEL_FRONT_RPC_EXCEPTION: 下游响应数据为空---检查下游响应  \n" +
            "STATE_TRANSITION_NO_SUPPORT: 不支持的订单状态流转---检查订单状态  \n" +
            "ORDER_UPDATE_FAIL: 订单更新失败---订单状态乐观锁，检查订单状态  \n" +
            "F999: 渠道响应码未配置内部映射码时，默认返回码---检查渠道响应  \n" +
            "其它：渠道响应码配置的内部映射码")
    @DigestLog(isRecord= true)
    public Result<Void> batchRetryTransaction(@RequestBody PayoutBatchRetryTransactionRequest request) {
        return payoutBatchRetryTransactionManage.execute(request);
    }

    @PostMapping(value = "/closeExpireOrder")
    @ApiOperation(value = "关闭过期订单", notes = "关闭过期订单")
    @DigestLog(isRecord= true)
    public Result<Boolean> closeExpireOrder(@RequestBody PayoutCloseOrderRequest request) {
        return payoutCloseOrderManage.execute(request);
    }

    @PostMapping(value = "/abnormalRetryTransaction")
    @ApiOperation(value = "出款异常重发", notes = "出款异常重发")
    @DigestLog(isRecord= true)
    public Result<Void> abnormalRetryTransaction(@Validated @RequestBody PayoutAbnormalRetryTransactionRequest request) {
        List<String> payRequestNoList = request.getChannelPayRequestNoList().stream().distinct().collect(Collectors.toList());
        executeAsyncPayoutTask(payRequestNoList);
        return ResultUtil.success();
    }

    /**
     * 执行指定类型的任务
     *
     * @param payRequestNoList
     * @param retryTypeEnum
     */
    private void executeAsyncPayoutTask(List<String> payRequestNoList) {
        payRequestNoList.forEach(payRequestNo -> {
            AsyncPayoutTaskRequest asyncPayoutTaskRequest = new AsyncPayoutTaskRequest();
            asyncPayoutTaskRequest.setRequestOrderNo(payRequestNo);
            abnormalPayoutApplyManage.execute(asyncPayoutTaskRequest);
        });
    }
}
