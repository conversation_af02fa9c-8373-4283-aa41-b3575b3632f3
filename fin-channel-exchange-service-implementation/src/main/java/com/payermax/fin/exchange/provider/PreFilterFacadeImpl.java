package com.payermax.fin.exchange.provider;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.JsonUtils;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.fin.exchange.assembler.FacadeAssembler;
import com.payermax.fin.exchange.domain.filter.PreFilterAccountRequestDO;
import com.payermax.fin.exchange.domain.filter.PreFilterAccountResponseDO;
import com.payermax.fin.exchange.domainservice.IDomainService;
import com.payermax.fin.exchange.domainservice.util.ThreadPoolUtils;
import com.payermax.fin.exchange.service.enums.FilterBizTypeEnum;
import com.payermax.fin.exchange.service.facade.PreFilterFacade;
import com.payermax.fin.exchange.service.request.*;
import com.payermax.fin.exchange.service.response.*;
import com.payermax.fin.exchange.share.bo.PreFilterDisableMethodBo;
import com.payermax.fin.exchange.share.bo.PreFilterDisableReasonDetailBo;
import com.payermax.fin.exchange.share.client.PreFilterClient;
import com.payermax.fin.exchange.share.constants.ShareConstants;
import com.payermax.fin.exchange.share.enums.FactorKeyEnum;
import com.payermax.fin.exchange.share.enums.FilterErrorCodeEnum;
import com.payermax.fin.exchange.share.utils.ThreadLocalUtils;
import com.payermax.infra.ionia.log.digest.core.annotation.DigestLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 预筛选服务
 *
 * <AUTHOR>
 * @date 2021/12/7 14:57
 */
@Slf4j
@DubboService(version = "${exchange.dubbo.api.version}", group = "${exchange.dubbo.api.group-id:}")
@Service
public class PreFilterFacadeImpl implements PreFilterFacade {

    @Autowired
    private PreFilterClient preFilterClient;

    @Autowired
    private FacadeAssembler facadeAssembler;

    @Autowired
    private IDomainService<PreFilterAccountRequestDO, List<PreFilterAccountResponseDO>> preFilterAccountDomainService;

    @NacosValue(value = "${pre-filter.print-log.flag:Y}", autoRefreshed = true)
    private String isPrintLog;

    @NacosValue(value = "${merchant-report-filter.thread-pool.time-out:13}", autoRefreshed = true)
    private long merchantReportFilterExeTimeOut;

    @NacosValue(value = "${available-filter.thread-pool.time-out:10}", autoRefreshed = true)
    private long availableFilterExeTimeOut;

    @Override
    @DigestLog(isRecord = true)
    public Result<com.payermax.fin.exchange.service.response.PreFilterResponse> filter(PreFilterRequest request) {
        Result<PreFilterResponse> result = ThreadPoolUtils.executeFilter(() -> {

            long startTime = System.currentTimeMillis();
            try {
                this.printLog("Pre filter start");
                // 初始化本地缓存
                ThreadLocalUtils.setLocalCache(ThreadLocalUtils.DISABLE_PAYMENT_METHOD, new HashMap<>());
                // 查询渠道实例
                com.payermax.fin.exchange.share.request.PreFilterRequest filterRequest = JsonUtils.
                        toBean(com.payermax.fin.exchange.share.request.PreFilterRequest.class, JsonUtils.toString(request));
                Result<com.payermax.fin.exchange.share.response.PreFilterResponse> filterResult = preFilterClient.filter(filterRequest);
                // 调用失败
                if (!filterResult.isSuccess()) {
                    return ResultUtil.fail(filterResult.getCode(), filterResult.getMsg());
                }
                PreFilterResponse response = JsonUtils.toBean(PreFilterResponse.class, JsonUtils.toString(filterResult.getData()));
                // 设置不可用支付方式对象
                try {
                    List<PreFilterResponse.DisablePaymentMethod> disablePaymentMethods = this.getPreFilterDisableMethod().entrySet().stream()
                            .map(t -> facadeAssembler.toPreFilterDisablePaymentMethod(t.getKey(), t.getValue())).collect(Collectors.toList());
                    response.setDisablePaymentMethods(disablePaymentMethods);
                } catch (Exception e) {
                    log.error("Build disable payment method error.", e);
                }

                return ResultUtil.success(response);
            } catch (Exception e) {
                throw e;
            } finally {
                ThreadLocalUtils.removeLocalCache(ThreadLocalUtils.DISABLE_PAYMENT_METHOD);
                this.printLog(String.format("Pre filter end. useTime = %s", System.currentTimeMillis() - startTime));
            }
        });
        return result;
    }

    @Override
    @DigestLog(isRecord = true)
    public Result<PreFilterAccountResponse> filterAccount(PreFilterAccountRequest request) {
        Result<PreFilterAccountResponse> result = ThreadPoolUtils.executeFilter(() -> {
            // 请求转换
            PreFilterAccountRequestDO requestDO = facadeAssembler.toPreFilterAccountRequest(request);
            // 调用领域服务
            List<PreFilterAccountResponseDO> responseDOList = preFilterAccountDomainService.execute(requestDO);
            // 响应转换
            PreFilterAccountResponse response = new PreFilterAccountResponse();
            response.setPaymentMethods(facadeAssembler.toPreFilterAccountResponse(responseDOList));
            return ResultUtil.success(response);
        });
        return result;
    }

    @Override
    public Result<ProductPreFilterResponse> cashierProductFilter(ProductPreFilterRequest request) {
        Result<ProductPreFilterResponse> result = ThreadPoolUtils.executeFilter(() -> {
            try {
                // 初始化本地缓存
                ThreadLocalUtils.setLocalCache(ThreadLocalUtils.DISABLE_PAYMENT_METHOD, new HashMap<>());
                // 查询渠道实例
                com.payermax.fin.exchange.share.request.PreFilterRequest filterRequest = facadeAssembler.toPreFilterRequest(request);
                com.payermax.fin.exchange.share.response.PreFilterResponse preFilterResponse = preFilterClient.productFilter(filterRequest);
                // 响应转换
                ProductPreFilterResponse response = facadeAssembler.toProductPreFilterResponse(preFilterResponse);
                // 设置不可用支付方式对象
                try {
                    List<ProductPreFilterResponse.DisablePaymentMethod> disablePaymentMethods = this.getPreFilterDisableMethod().entrySet().stream()
                            .map(t -> facadeAssembler.toProductPreFilterDisablePaymentMethod(t.getKey(), t.getValue())).collect(Collectors.toList());
                    response.setDisablePaymentMethods(disablePaymentMethods);
                } catch (Exception e) {
                    log.error("Build disable payment method error.", e);
                }

                return ResultUtil.success(response);
            } catch (Exception e) {
                throw e;
            } finally {
                ThreadLocalUtils.removeLocalCache(ThreadLocalUtils.DISABLE_PAYMENT_METHOD);
            }
        });
        return result;
    }

    @Override
    public Result<AvailabilityFilterResponse> availabilityFilter(AvailabilityFilterRequest request) {
        Result<AvailabilityFilterResponse> result = ThreadPoolUtils.executeFilter(() -> {
            try {
                // 初始化本地缓存
                ThreadLocalUtils.setLocalCache(ThreadLocalUtils.DISABLE_PAYMENT_METHOD, new HashMap<>());
                // 查询渠道实例
                com.payermax.fin.exchange.share.request.PreFilterRequest filterRequest = facadeAssembler.availabilityFilterToPreFilterRequest(request);
                com.payermax.fin.exchange.share.response.PreFilterResponse preFilterResponse = preFilterClient.productFilter(filterRequest);
                // 响应转换
                AvailabilityFilterResponse response = facadeAssembler.toAvailabilityFilterResponse(preFilterResponse);
                // 设置不可用支付方式对象
                try {
                    List<AvailabilityFilterResponse.DisablePaymentMethod> disablePaymentMethods = this.getPreFilterDisableMethod().entrySet().stream()
                            .map(t -> facadeAssembler.toAvailabilityFilterDisablePaymentMethod(t.getKey(), t.getValue())).collect(Collectors.toList());
                    response.setDisablePaymentMethods(disablePaymentMethods);
                } catch (Exception e) {
                    log.error("Build disable payment method error.", e);
                }

                return ResultUtil.success(response);
            } catch (Exception e) {
                throw e;
            } finally {
                ThreadLocalUtils.removeLocalCache(ThreadLocalUtils.DISABLE_PAYMENT_METHOD);
            }
        });
        return result;
    }

    @NacosValue(value = "${available-filter.is-new:true}", autoRefreshed = true)
    private boolean isNew;

    @Override
    public Result<NewAvailabilityFilterResponse> newAvailabilityFilter(AvailabilityFilterRequest request) {
        return ThreadPoolUtils.executeFilter(availableFilterExeTimeOut, () -> {
            try {
                // 初始化本地缓存
                ThreadLocalUtils.setLocalCache(ThreadLocalUtils.DISABLE_PAYMENT_METHOD, new HashMap<>());
                // 查询渠道实例
                com.payermax.fin.exchange.share.request.PreFilterRequest filterRequest = facadeAssembler.availabilityFilterToPreFilterRequest(request);
                NewAvailabilityFilterResponse response;
                if (isNew) {
                    List<NewAvailabilityFilterResponse.Ability> abilities = preFilterClient.productFilter(filterRequest, this::toNewAvailabilityAbility);
                    response = new NewAvailabilityFilterResponse();
                    response.setAbilityList(abilities);
                } else {
                    com.payermax.fin.exchange.share.response.PreFilterResponse preFilterResponse = preFilterClient.productFilter(filterRequest);
                    // 响应转换
                    response = toNewAvailabilityFilterResponse(request, preFilterResponse, this.getPreFilterDisableMethod());
                }
                return ResultUtil.success(response);
            } catch (Exception e) {
                log.error("newAvailabilityFilter filter error : {}", e.getMessage(), e);
                throw e;
            } finally {
                ThreadLocalUtils.removeLocalCache(ThreadLocalUtils.DISABLE_PAYMENT_METHOD);
            }
        });
    }

    @Override
    public Result<MerchantReportFilterResponse> merchantReportFilter(MerchantReportFilterRequest request) {
        return ThreadPoolUtils.executeFilter(merchantReportFilterExeTimeOut, () -> {
            try {
                // 初始化本地缓存
                ThreadLocalUtils.setLocalCache(ThreadLocalUtils.DISABLE_PAYMENT_METHOD, new HashMap<>());
                // 查询渠道实例
                com.payermax.fin.exchange.share.request.PreFilterRequest filterRequest = facadeAssembler.merchantReportFilterToPreFilterRequest(request);
                com.payermax.fin.exchange.share.response.PreFilterResponse preFilterResponse = preFilterClient.productFilter(filterRequest);
                // 响应转换
                Map<PreFilterDisableMethodBo, List<PreFilterDisableReasonDetailBo>> preFilterDisableMethodMap = new HashMap<>();
                if (FilterBizTypeEnum.MERCHANT_REPORT.name().equals(filterRequest.getFilterBizType())) {
                    preFilterDisableMethodMap = this.getPreFilterDisableMethod();
                }
                MerchantReportFilterResponse response = facadeAssembler.toMerchantReportFilterResponse(preFilterResponse, preFilterDisableMethodMap);
                return ResultUtil.success(response);
            } finally {
                ThreadLocalUtils.removeLocalCache(ThreadLocalUtils.DISABLE_PAYMENT_METHOD);
            }
        });
    }

    @Override
    public Result<FilterChannelResponse> filterChannel(FilterChannelRequest request) {
        return filterChannel(request, facadeAssembler::filterChannelToPreFilterRequest);
    }

    @Override
    public Result<FilterChannelResponse> filterChannelTest(PreFilterTestRequest request) {
        return filterChannel(request, facadeAssembler::preFilterTestToPreFilterRequest);
    }

    /**
     * 可用性查询
     *
     * @param request
     * @param requestConverter
     * @param <T>
     * @return
     */
    public <T> Result<FilterChannelResponse> filterChannel(T request, Function<T, com.payermax.fin.exchange.share.request.PreFilterRequest> requestConverter) {
        Result<FilterChannelResponse> result = ThreadPoolUtils.executeFilter(() -> {
            try {
                // 初始化本地缓存
                ThreadLocalUtils.setLocalCache(ThreadLocalUtils.DISABLE_PAYMENT_METHOD, new HashMap<>());
                // 查询渠道实例
                com.payermax.fin.exchange.share.request.PreFilterRequest filterRequest = requestConverter.apply(request);
                com.payermax.fin.exchange.share.response.PreFilterResponse preFilterResponse = preFilterClient.productFilter(filterRequest);
                // 响应转换
                FilterChannelResponse response = facadeAssembler.toFilterChannelResponse(preFilterResponse);

                return ResultUtil.success(response);
            } catch (Exception e) {
                throw e;
            } finally {
                ThreadLocalUtils.removeLocalCache(ThreadLocalUtils.DISABLE_PAYMENT_METHOD);
            }
        });
        return result;
    }

    /**
     * 获取支付方式不可用原因
     *
     * @return
     */
    private Map<PreFilterDisableMethodBo, List<PreFilterDisableReasonDetailBo>> getPreFilterDisableMethod() {
        Map<String, List<PreFilterDisableReasonDetailBo>> disablePaymentCache = ThreadLocalUtils.getLocalCache(ThreadLocalUtils.DISABLE_PAYMENT_METHOD, Map.class);
        if (disablePaymentCache == null) {
            return new HashMap<>();
        }
        Map<PreFilterDisableMethodBo, List<PreFilterDisableReasonDetailBo>> disableMethodMap = disablePaymentCache.entrySet().stream()
                .collect(Collectors.toMap(t -> JSON.parseObject(t.getKey(), PreFilterDisableMethodBo.class), Map.Entry::getValue));
        return disableMethodMap;
    }

    /**
     * 打印日志*
     * *
     *
     * @param msg
     */
    private void printLog(String msg) {
        if (StringUtils.isBlank(msg) || !ShareConstants.YES_FLAG.equals(isPrintLog)) {
            return;
        }
        log.info(msg);
    }

    private NewAvailabilityFilterResponse.Ability toNewAvailabilityAbility(com.payermax.fin.exchange.share.request.PreFilterRequest.PaymentMethod reqPaymentMethod, List<com.payermax.fin.exchange.share.response.PreFilterResponse.PaymentMethod> innerPaymentMethods) {
        NewAvailabilityFilterResponse.Ability ability = new NewAvailabilityFilterResponse.Ability();
        ability.setPaymentMethodId(reqPaymentMethod.getPaymentMethodNo());
        ability.setCashierProductNo(reqPaymentMethod.getCashierProductNo());
        ability.setCurrency(reqPaymentMethod.getAmount() == null ? "" : reqPaymentMethod.getAmount().getCurrency());
        ability.setBrandCode(getBrandCode(reqPaymentMethod.getTargetOrg(), reqPaymentMethod.getCardOrg()));
        ability.setChannelAbilities(new ArrayList<>());
        ability.setDisableChannelDetails(new ArrayList<>());

        if (CollectionUtils.isNotEmpty(innerPaymentMethods)) {
            // 渠道支付方式的能力, KEY 是 渠道支付方式编码， Value 是能力
            // 因为 渠道支付方式编码+渠道MID 是渠道实例的一部分，所以尽量少创建对象，减少内存占用
            Map<String, NewAvailabilityFilterResponse.Ability.ChannelAbility> channelAbilityMap = new HashMap<>();
            innerPaymentMethods.forEach(paymentMethod -> {
                NewAvailabilityFilterResponse.Ability.ChannelAbility finalChannelAbility = channelAbilityMap.compute(paymentMethod.getChannelMethodCode() + paymentMethod.getChannelMerchantCode(), (_k, channelAbility) -> {
                    if (null == channelAbility) {
                        channelAbility = new NewAvailabilityFilterResponse.Ability.ChannelAbility();
                        channelAbility.setChannelMethodCode(paymentMethod.getChannelMethodCode());
                        channelAbility.setChannelMerchantCode(paymentMethod.getChannelMerchantCode());
                        channelAbility.setSingleAmountLimit(new HashSet<>());
                    }
                    if (CollectionUtils.isNotEmpty(paymentMethod.getExtendProperties())) {
                        List<com.payermax.fin.exchange.share.response.PreFilterResponse.PaymentMethod.ExtendProperty> singleTransLimitList = paymentMethod.getExtendProperties().stream()
                                .filter(e -> Objects.equals(FactorKeyEnum.SINGLE_TRANS_LIMIT.name(), e.getKey())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(singleTransLimitList)) {
                            channelAbility.getSingleAmountLimit().addAll(facadeAssembler.toAbilityExtendPropertySet(singleTransLimitList));
                        }
                    }
                    return channelAbility;
                });
                ability.getChannelAbilities().add(finalChannelAbility);
            });
        }
        Map<PreFilterDisableMethodBo, List<PreFilterDisableReasonDetailBo>> disableMethodListMap = this.getPreFilterDisableMethod();
        if (MapUtils.isNotEmpty(disableMethodListMap)) {
            disableMethodListMap.forEach((disableMethodBo, disableReasonDetailList) -> {
                disableReasonDetailList.forEach(disableReasonDetail -> {
                    NewAvailabilityFilterResponse.Ability.DisableChannelDetail disableChannelDetail = new NewAvailabilityFilterResponse.Ability.DisableChannelDetail();
                    disableChannelDetail.setChannelMethodCode(disableReasonDetail.getChannelMethodCode());
                    disableChannelDetail.setChannelMerchantCode(disableReasonDetail.getChannelMerchantCode());
                    if (FilterErrorCodeEnum.ONBOARD_ERROR.name().equals(disableReasonDetail.getErrorCode())) {
                        disableChannelDetail.setChannelId(disableReasonDetail.getChannelId());
                        disableChannelDetail.setEntity(disableReasonDetail.getEntity());
                    }
                    disableChannelDetail.setErrorCode(disableReasonDetail.getErrorCode());
                    disableChannelDetail.setErrorMsg(disableReasonDetail.getErrorMsg());
                    ability.getDisableChannelDetails().add(disableChannelDetail);
                });
            });
        }
        return ability;
    }

    private NewAvailabilityFilterResponse toNewAvailabilityFilterResponse(AvailabilityFilterRequest request, com.payermax.fin.exchange.share.response.PreFilterResponse response, Map<PreFilterDisableMethodBo, List<PreFilterDisableReasonDetailBo>> disableMethodListMap) {
        NewAvailabilityFilterResponse targetResponse = new NewAvailabilityFilterResponse();

        Map<String, NewAvailabilityFilterResponse.Ability> brandAbilityMap = new HashMap<>();
        request.getCashierProducts().forEach(cashierProduct -> {
            NewAvailabilityFilterResponse.Ability ability = new NewAvailabilityFilterResponse.Ability();
            ability.setPaymentMethodId(cashierProduct.getPaymentMethodId());
            ability.setCashierProductNo(cashierProduct.getCashierProductNo());

            String currency = cashierProduct.getAmount() == null ? "" : cashierProduct.getAmount().getCurrency();
            String brandCode = getBrandCode(cashierProduct.getTargetOrg(), cashierProduct.getCardOrg());
            ability.setCurrency(currency);
            ability.setBrandCode(brandCode);
            ability.setChannelAbilities(new ArrayList<>());
            ability.setDisableChannelDetails(new ArrayList<>());

            String mapKey = String.format("%s:%s:%s", cashierProduct.getCashierProductNo(), currency, brandCode);
            brandAbilityMap.put(mapKey, ability);
        });

        List<com.payermax.fin.exchange.share.response.PreFilterResponse.PaymentMethod> paymentMethods = Optional.ofNullable(response).map(com.payermax.fin.exchange.share.response.PreFilterResponse::getPaymentMethods).orElse(new ArrayList<>());
        if (CollectionUtils.isNotEmpty(paymentMethods)) {
            // 渠道支付方式的能力, KEY 是 渠道支付方式编码， Value 是能力
            // 因为 渠道支付方式编码+渠道MID 是渠道实例的一部分，所以尽量少创建对象，减少内存占用
            Map<String, NewAvailabilityFilterResponse.Ability.ChannelAbility> channelAbilityMap = new HashMap<>();
            paymentMethods.forEach(paymentMethod -> {
                String currency = paymentMethod.getAmount() == null ? "" : paymentMethod.getAmount().getCurrency();
                String brandCode = getBrandCode(paymentMethod);
                String mapKey = String.format("%s:%s:%s", paymentMethod.getCashierProductNo(), currency, brandCode);

                brandAbilityMap.compute(mapKey, (k, ability) -> {
                    if (null == ability) {
                        ability = new NewAvailabilityFilterResponse.Ability();
                        ability.setPaymentMethodId(paymentMethod.getPaymentMethodNo());
                        ability.setCashierProductNo(paymentMethod.getCashierProductNo());
                        ability.setCurrency(currency);
                        ability.setBrandCode(brandCode);
                        ability.setChannelAbilities(new ArrayList<>());
                        ability.setDisableChannelDetails(new ArrayList<>());
                        log.warn("金融交换返回的支付方式信息中 ，有其他可用的品牌 : {}:{}:{}", paymentMethod.getCashierProductNo(), currency, brandCode);
                    }

                    NewAvailabilityFilterResponse.Ability.ChannelAbility finalChannelAbility = channelAbilityMap.compute(paymentMethod.getChannelMethodCode() + paymentMethod.getChannelMerchantCode(), (_k, channelAbility) -> {
                        if (null == channelAbility) {
                            channelAbility = new NewAvailabilityFilterResponse.Ability.ChannelAbility();
                            channelAbility.setChannelMethodCode(paymentMethod.getChannelMethodCode());
                            channelAbility.setChannelMerchantCode(paymentMethod.getChannelMerchantCode());
                            channelAbility.setSingleAmountLimit(new HashSet<>());
                        }
                        if (CollectionUtils.isNotEmpty(paymentMethod.getExtendProperties())) {
                            List<com.payermax.fin.exchange.share.response.PreFilterResponse.PaymentMethod.ExtendProperty> singleTransLimitList = paymentMethod.getExtendProperties().stream()
                                    .filter(e -> Objects.equals(FactorKeyEnum.SINGLE_TRANS_LIMIT.name(), e.getKey())).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(singleTransLimitList)) {
                                channelAbility.getSingleAmountLimit().addAll(facadeAssembler.toAbilityExtendPropertySet(singleTransLimitList));
                            }
                        }
                        return channelAbility;
                    });
                    ability.getChannelAbilities().add(finalChannelAbility);
                    return ability;
                });
            });
        }
        if (MapUtils.isNotEmpty(disableMethodListMap)) {
            disableMethodListMap.forEach((disableMethodBo, disableReasonDetailList) -> {
                String brandCode = getBrandCode(disableMethodBo.getTargetOrg(), disableMethodBo.getCardOrg());
                String mapKey = String.format("%s:%s:%s", disableMethodBo.getCashierProductNo(), disableMethodBo.getCurrency(), brandCode);
                // 设置目标支付方式
                NewAvailabilityFilterResponse.Ability availableBo = brandAbilityMap.compute(mapKey, (k, ability) -> {
                    if (null == ability) {
                        log.warn("金融交换返回的支付方式信息中 ，有其他不可用的品牌 : {}:{}:{} 渠道 : {}", disableMethodBo.getCashierProductNo(), disableMethodBo.getCurrency(), brandCode, JSON.toJSONString(disableReasonDetailList));
                        return null;
                    }
                    return ability;
                });
                if (null == availableBo) {
                    return;
                }
                disableReasonDetailList.forEach(disableReasonDetail -> {
                    NewAvailabilityFilterResponse.Ability.DisableChannelDetail disableChannelDetail = new NewAvailabilityFilterResponse.Ability.DisableChannelDetail();
                    disableChannelDetail.setChannelMethodCode(disableReasonDetail.getChannelMethodCode());
                    disableChannelDetail.setChannelMerchantCode(disableReasonDetail.getChannelMerchantCode());
                    if (FilterErrorCodeEnum.ONBOARD_ERROR.name().equals(disableReasonDetail.getErrorCode())) {
                        disableChannelDetail.setChannelId(disableReasonDetail.getChannelId());
                        disableChannelDetail.setEntity(disableReasonDetail.getEntity());
                    }
                    disableChannelDetail.setErrorCode(disableReasonDetail.getErrorCode());
                    disableChannelDetail.setErrorMsg(disableReasonDetail.getErrorMsg());
                    availableBo.getDisableChannelDetails().add(disableChannelDetail);
                });
            });
        }

        targetResponse.setAbilityList(new ArrayList<>(brandAbilityMap.values()));
        return targetResponse;
    }


    private String getBrandCode(com.payermax.fin.exchange.share.response.PreFilterResponse.PaymentMethod paymentMethod) {
        if (StringUtils.isBlank(paymentMethod.getTargetOrg()) || ShareConstants.STAR.equals(paymentMethod.getTargetOrg())) {
            if (CollectionUtils.isEmpty(paymentMethod.getExtendProperties())) {
                return null;
            }
            for (com.payermax.fin.exchange.share.response.PreFilterResponse.PaymentMethod.ExtendProperty extendProperty : paymentMethod.getExtendProperties()) {
                if (FactorKeyEnum.SUPPORT_CARD_ORG.name().equals(extendProperty.getKey())) {
                    return extendProperty.getValue();
                }
            }
            return null;
        } else {
            return paymentMethod.getTargetOrg();
        }
    }

    private String getBrandCode(String targetOrg, String cardOrg) {
        if (StringUtils.isBlank(targetOrg) || ShareConstants.STAR.equals(targetOrg)) {
            return cardOrg;
        } else {
            return targetOrg;
        }
    }

}
