package com.payermax.fin.exchange.controller;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.fin.exchange.service.facade.UpdateOrderFacade;
import com.payermax.fin.exchange.service.request.CloseOrderRequest;
import com.payermax.fin.exchange.service.request.UpdateCommitOrderRequest;
import com.payermax.fin.exchange.service.request.UpdateRequestOrderRequest;
import com.payermax.infra.ionia.log.digest.core.annotation.DigestLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/19 14:42
 */
@RestController
@Slf4j
@RequestMapping(value = "/updateOrder")
@Api(description = "更新订单服务", tags = "更新订单服务")
public class UpdateOrderController {

    @Autowired
    private UpdateOrderFacade updateOrderFacade;

    @PostMapping(value = "/updateCommitOrder")
    @ApiOperation(value = "更新提交单", notes = "更新提交单数据  \n" +
            "SYSTEM_BUSY: 通用内部未知错误---需要定位系统异常  \n" +
            "REQUEST_PARAM_INVALID: 参数错误---检查入参  \n" +
            "CHANNEL_ORDER_IS_NOT_EXIST: 渠道订单不存在---检查渠道单号是否正确")
    @DigestLog(isRecord = true)
    public Result<Long> updateCommitOrder(@RequestBody List<UpdateCommitOrderRequest> request) {
        log.info("UpdateOrderController#updateCommitOrder request:{}",request);
        return updateOrderFacade.updateCommitOrder(request);
    }

    @PostMapping(value = "/updateRequestOrder")
    @ApiOperation(value = "更新请求单", notes = "更新请求单数据  \n" +
            "SYSTEM_BUSY: 通用内部未知错误---需要定位系统异常  \n" +
            "REQUEST_PARAM_INVALID: 参数错误---检查入参  \n" +
            "CHANNEL_ORDER_IS_NOT_EXIST: 渠道订单不存在---检查渠道单号是否正确")
    @DigestLog(isRecord = true)
    public Result<Long> updateRequestOrder(@RequestBody List<UpdateRequestOrderRequest> request) {
        log.info("UpdateOrderController#updateRequestOrder request:{}",request);
        return updateOrderFacade.updateRequestOrder(request);
    }

    @PostMapping(value = "/close/notify")
    @ApiOperation(value = "关单通知", notes = "关单通知")
    @DigestLog(isRecord = true)
    public Result<Void> closeOrderNotify(@RequestBody CloseOrderRequest request) {
        return updateOrderFacade.closeOrderNotify(request);
    }

}
