package com.payermax.fin.exchange.sentinel;

import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.rpc.Filter;
import org.apache.dubbo.rpc.Invocation;

/**
 * Sentinel dubbo基础过滤器
 *
 * <AUTHOR>
 * @date 2025/1/10 10:17
 */
@Slf4j
public abstract class SentinelDubboBaseFilter implements Filter {

    protected String genericInvokeMethodName = "$invoke";

    /**
     * 构建资源名称
     *
     * @param invocation
     * @return
     */
    protected String buildResourceName(Invocation invocation) {
        try {
            // 通过invoke获取真实否服务名称，兼容泛化调用
            String serviceName;
            if (invocation.getInvoker() != null) {
                serviceName = invocation.getInvoker().getUrl().getServiceInterface();
            } else {
                serviceName = invocation.getServiceName();
            }
            String methodName = invocation.getMethodName();
            Object[] arguments = invocation.getArguments();
            // 如果是泛化调用，则获取真实的方法名称
            if (genericInvokeMethodName.equals(methodName) && arguments != null && arguments.length > 1) {
                // GenericService.$invoke接口的第一个参数为真实方法名
                methodName = arguments[0].toString();
            }
            String resourceName = String.format("%s.%s", serviceName, methodName);
            return resourceName;
        } catch (Exception e) {
            log.warn("Sentinel bulild resource name failed.", e);
        }
        return null;
    }

}
