package com.payermax.fin.exchange.aspect;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.fin.exchange.assembler.FacadeAssembler;
import com.payermax.fin.exchange.common.enums.ErrorCodeEnum;
import com.payermax.fin.exchange.common.exception.ExchangeException;
import com.payermax.fin.exchange.common.util.ValidationUtils;
import com.payermax.fin.exchange.domain.trans.auth.AuthResponseOrderDO;
import com.payermax.fin.exchange.domain.trans.auth.RevokeAuthResponseOrderDO;
import com.payermax.fin.exchange.domain.trans.pay.PayControlResponseOrderDO;
import com.payermax.fin.exchange.domain.trans.pay.PayResponseOrderDO;
import com.payermax.fin.exchange.domain.trans.payout.PayoutResponseOrderDO;
import com.payermax.fin.exchange.domain.trans.refund.RefundResponseOrderDO;
import com.payermax.common.lang.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.UndeclaredThrowableException;
import java.util.Optional;

/**
 * 全局异常拦截
 *
 * <AUTHOR>
 * @date 2021/12/14 13:37
 */
@Aspect
@Component
@Slf4j
public class GlobalExceptionAspect {

    @Autowired
    private FacadeAssembler facadeAssembler;

    @Pointcut(value = "execution(* com.payermax.fin.exchange.provider..*.*(..))")
    public void anyDubboProvider() {
    }

    @Pointcut(value = "execution(* com.payermax.fin.exchange.controller..*.*(..)) && @annotation(com.payermax.fin.exchange.aspect.AroundLog)")
    public void anyControllerAroundLog() {
    }

    @Pointcut(value = "(anyDubboProvider() || anyControllerAroundLog())")
    public void pointCut() {
    }

    @Around(value = "pointCut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        Object request = Optional.ofNullable(args).orElse(new Object[]{})[0];

        Object result;
        try {
            log.info("Facade execute start.【RequestBody】:{}", JsonUtils.toJson(request));
            ValidationUtils.notNullCheck(request, "input is null");
            ValidationUtils.validate(request);

            result = joinPoint.proceed();
        } catch (Exception e) {
            result = this.exceptionHandler(joinPoint, e);
        }

        log.info("Facade execute end.【ResponseBody】:{}", JsonUtils.toJson(result));
        return result;
    }

    /**
     * 全局异常捕捉处理
     *
     * @param e 异常对象
     * @return Result
     */
    private Result exceptionHandler(ProceedingJoinPoint joinPoint, Exception e) {
        if (e instanceof ExchangeException) {
            ExchangeException t = (ExchangeException) e;
            // facade响应转换
            Object facadeResp = toFacadeResp(t.getData());
            return ResultUtil.fail(t.getErrCode(), t.getMessage(), facadeResp);
        }
        if (e instanceof BusinessException) {
            BusinessException t = (BusinessException) e;
            return ResultUtil.fail(t.getErrCode(), t.getMessage());
        }
        // SENTINEL阻塞异常
        if (e instanceof BlockException) {
            return ResultUtil.fail(ErrorCodeEnum.SENTINEL_BLOCK_EXCEPTION.getCode(), ErrorCodeEnum.SENTINEL_BLOCK_EXCEPTION.getMsg());
        }
        if (e instanceof UndeclaredThrowableException) {
            Throwable realExp = e.getCause();
            // SENTINEL阻塞异常
            if (realExp instanceof BlockException) {
                return ResultUtil.fail(ErrorCodeEnum.SENTINEL_BLOCK_EXCEPTION.getCode(), ErrorCodeEnum.SENTINEL_BLOCK_EXCEPTION.getMsg());
            }
        }
        log.error("{} invoke catch exception! ", this.currentAbsoluteName(joinPoint), e);
        if (e instanceof IllegalArgumentException) {
            return ResultUtil.fail(ErrorCodeEnum.PARAMETER_INVALID.getCode(), e.getMessage());
        } else {
            return ResultUtil.fail(ErrorCodeEnum.INNER_ERROR.getCode(), ErrorCodeEnum.INNER_ERROR.getMsg());
        }
    }

    /**
     * facade响应转换
     *
     * @param response
     * @return
     */
    private Object toFacadeResp(Object response) {
        if (response == null) {
            return null;
        }
        // 入款响应转换
        if (response instanceof PayResponseOrderDO) {
            return facadeAssembler.toPayResp((PayResponseOrderDO) response);
        }
        // 入款控制响应转换
        if (response instanceof PayControlResponseOrderDO) {
            return facadeAssembler.toPayControlResp((PayControlResponseOrderDO) response);
        }
        // 出款响应转换
        if (response instanceof PayoutResponseOrderDO) {
            return facadeAssembler.toPayoutResp((PayoutResponseOrderDO) response);
        }
        // 退款响应转换
        if (response instanceof RefundResponseOrderDO) {
            return facadeAssembler.toRefundResp((RefundResponseOrderDO) response);
        }
        // 授权响应转换
        if (response instanceof AuthResponseOrderDO) {
            return facadeAssembler.toAuthResp((AuthResponseOrderDO) response);
        }
        // 授权解约响应转换
        if (response instanceof RevokeAuthResponseOrderDO) {
            return facadeAssembler.toRevokeAuthResp((RevokeAuthResponseOrderDO) response);
        }
        return null;
    }

    /**
     * 组装当前执行类全限定路径
     *
     * @param joinPoint
     * @return String
     */
    private String currentAbsoluteName(ProceedingJoinPoint joinPoint) {
        return joinPoint.getSignature().getDeclaringTypeName().concat(".")
                .concat(((MethodInvocationProceedingJoinPoint) joinPoint).getSignature().getName());
    }

}
