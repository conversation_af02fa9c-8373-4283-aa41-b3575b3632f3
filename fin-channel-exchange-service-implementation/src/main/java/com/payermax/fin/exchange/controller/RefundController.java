package com.payermax.fin.exchange.controller;

import com.payermax.fin.exchange.condition.OnHttpCondition;
import com.payermax.fin.exchange.service.facade.RefundFacade;
import com.payermax.fin.exchange.service.request.InquiryVoidRequest;
import com.payermax.fin.exchange.service.request.VoidRequest;
import com.payermax.fin.exchange.service.response.InquiryVoidResponse;
import com.payermax.fin.exchange.service.response.VoidResponse;
import com.payermax.fin.exchange.service.response.InquiryRefundResponse;
import com.payermax.fin.exchange.service.response.RefundResponse;
import com.payermax.fin.exchange.service.request.InquiryRefundRequest;
import com.payermax.fin.exchange.service.request.RefundRequest;
import com.payermax.infra.ionia.log.digest.core.annotation.DigestLog;
import com.payermax.common.lang.model.dto.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.web.bind.annotation.*;

/**
 * 退款Controller
 *
 * <AUTHOR>
 * @date 2021/8/23 14:32
 */
@RestController
@Slf4j
@RequestMapping(value = "/refunds")
@Api(value = "退款服务", tags = "退款服务")
@Conditional(OnHttpCondition.class)
public class RefundController {

    @Autowired
    private RefundFacade refundFacade;

    @RequestMapping(value = "/refundApply", method = RequestMethod.POST)
    @ApiOperation(value = "退款申请", notes = "退款申请接口  \n" +
            "SYSTEM_BUSY: 通用内部未知错误---需要定位系统异常  \n" +
            "REQUEST_PARAM_INVALID: 参数错误---检查入参  \n" +
            "CHANNEL_ORDER_EXIST: 订单已存在---幂等校验，检查支付单号是否相同  \n" +
            "CHANNEL_ORDER_IS_NOT_EXIST: 原渠道支付订单不存在---检查原渠道支付单号是否正确  \n" +
            "CHANNEL_ORDER_NO_PAID: 原渠道支付单未支付成功---检查原渠道支付单状态" +
            "AMOUNT_INVALID: 退款金额异常---检查退款金额是否小于等于原支付金额  \n" +
            "PAY_METHOD_CONFIG_ERROR: 渠道支付方式配置错误---检查渠道配置数据  \n" +
            "CHANNEL_NOT_SUPPORT_REFUND: 渠道不支持退款---检查渠道是否支持退款  \n" +
            "DATE_FORMAT_EXCEPTION: 下游响应数据格式异常---检查下游响应数据  \n" +
            "CHANNEL_FRONT_RPC_EXCEPTION: 下游响应数据为空---检查下游响应  \n" +
            "STATE_TRANSITION_NO_SUPPORT: 不支持的订单状态流转---检查订单状态  \n" +
            "ORDER_UPDATE_FAIL: 订单更新失败---订单状态乐观锁，检查订单状态  \n" +
            "F999: 渠道响应码未配置内部映射码时，默认返回码---检查渠道响应  \n" +
            "其它：渠道响应码配置的内部映射码")
    @DigestLog(isRecord= true)
    public Result<RefundResponse> refundApply(@RequestBody RefundRequest request) {
        return refundFacade.refundApply(request);
    }

    @RequestMapping(value = "/voidApply", method = RequestMethod.POST)
    @DigestLog(isRecord= true)
    public Result<VoidResponse> voidApply(@RequestBody VoidRequest request) {
        return refundFacade.voidApply(request);
    }

    @PostMapping(value = "/inquiryRefund")
    @ApiOperation(value = "退款查询", notes = "退款单信息查询接口  \n" +
            "SYSTEM_BUSY: 通用内部未知错误---需要定位系统异常  \n" +
            "REQUEST_PARAM_INVALID: 参数错误---检查入参  \n" +
            "CHANNEL_ORDER_IS_NOT_EXIST: 渠道订单不存在---检查渠道单号是否正确")
    public Result<InquiryRefundResponse> inquiryRefund(@RequestBody InquiryRefundRequest request) {
        Result<InquiryRefundResponse> inquiryResult = refundFacade.inquiryRefund(request);
        return inquiryResult;
    }

    @RequestMapping(value = "/inquiryVoid", method = RequestMethod.POST)
    @DigestLog(isRecord= true)
    public Result<InquiryVoidResponse> inquiryVoid(@RequestBody InquiryVoidRequest request) {
        return refundFacade.inquiryVoid(request);
    }

}
