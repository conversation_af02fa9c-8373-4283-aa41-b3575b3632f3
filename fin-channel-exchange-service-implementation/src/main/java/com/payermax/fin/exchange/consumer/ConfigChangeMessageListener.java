package com.payermax.fin.exchange.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.fin.exchange.common.enums.ErrorCodeEnum;
import com.payermax.fin.exchange.domain.common.RefreshCacheRequestDO;
import com.payermax.fin.exchange.domainservice.cache.CacheManager;
import com.payermax.fin.exchange.domainservice.util.LockUtils;
import com.payermax.fin.exchange.share.enums.RegionEnum;
import com.payermax.fin.exchange.share.utils.LocalCacheUtils;
import com.payermax.infra.ionia.rocketmq.bean.ObjectMqMessage;
import com.payermax.infra.ionia.rocketmq.enums.TimeDelayLevel;
import com.payermax.infra.ionia.rocketmq.handler.BaseMqMessageListener;
import com.payermax.infra.ionia.rocketmq.handler.IoniaRocketMqTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.apache.rocketmq.spring.core.RocketMQPushConsumerLifecycleListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/1/24 20:36
 */
@Slf4j
@Component
@RocketMQMessageListener(
        topic = "${rocketMq.topic.channel.exchange.config.change.topic}",
        consumerGroup = "${rocketmq.consumer.channel.config.change.group_id}",
        messageModel = MessageModel.BROADCASTING,
        consumeThreadNumber = 1,
        consumeThreadMax = 1
)
public class ConfigChangeMessageListener extends BaseMqMessageListener<ObjectMqMessage>
        implements RocketMQListener<ObjectMqMessage>, RocketMQPushConsumerLifecycleListener {

    @NacosValue(value = "${rocketMq.topic.channel.exchange.config.change.topic}", autoRefreshed = true)
    private String configChangeTopic;

    @Autowired
    private CacheManager cacheManager;

    @Autowired
    private IoniaRocketMqTemplate ioniaRocketMqTemplate;

    @Override
    protected void handleMessage(ObjectMqMessage message) throws Exception {
        if (message.getMsgData() == null) {
            return;
        }
        // 参数转换
        RefreshCacheRequestDO requestDO = JSON.parseObject(JSON.toJSONString(message.getMsgData()), RefreshCacheRequestDO.class);
        // 参数校验
        if (!check(requestDO)) {
            log.error("Invalid parameters: {}", message);
            return;
        }

        try {
            // 清理老版本地缓存（按需加载类的）
            if (requestDO.getCacheGroup() == RefreshCacheRequestDO.CacheGroup.OLD) {
                this.clearOldCache(requestDO.getRegions(), requestDO.getIds());
            } else {
                cacheManager.refresh(requestDO);
            }
        } catch (BusinessException e) {
            log.warn("Config change mq consume failed. msg = {}", message, e);
            int delayLevel = TimeDelayLevel.TEN_SECOND.getLevel();
            // 缓存未初始化，则延迟1分钟重试
            if (ErrorCodeEnum.CACHE_NOT_INIT.getCode().equals(e.getErrCode())) {
                delayLevel = TimeDelayLevel.ONE_MINUTE.getLevel();
            }
            // 延迟重发MQ
            this.retrySendMsg(message, delayLevel);
        } catch (Exception e) {
            log.error("Config change mq consume failed. msg = {}", message, e);
            // 重发MQ
            this.retrySendMsg(message, TimeDelayLevel.TEN_SECOND.getLevel());
        }
    }

    @Override
    protected void overMaxRetryTimesMessage(ObjectMqMessage message) {
        log.error("The channel config change mq message processing fail over max retry times, message = {}", JSON.toJSONString(message));
    }

    @Override
    public void onMessage(ObjectMqMessage message) {
        super.dispatchMessage(message);
    }

    /**
     * 参数校验
     *
     * @param requestDO
     * @return
     */
    private boolean check(RefreshCacheRequestDO requestDO) {
        if (requestDO == null) {
            return false;
        }
        // 老版缓存
        if (requestDO.getCacheGroup() == RefreshCacheRequestDO.CacheGroup.OLD) {
            if (CollectionUtils.isEmpty(requestDO.getRegions())) {
                return false;
            }
            return true;
        } else {
            // 新版缓存
            if (StringUtils.isBlank(requestDO.getCacheType())) {
                return false;
            }
        }
        return true;
    }

    /**
     * 清理老缓存
     *
     * @param regions
     * @param ids
     */
    private void clearOldCache(List<String> regions, List<String> ids) {
        String[] keys;
        if (CollectionUtils.isNotEmpty(ids)) {
            keys = ids.toArray(new String[]{});
        } else {
            keys = null;
        }

        // 遍历region，进行缓存清理
        regions.stream().map(RegionEnum::getValueByKey)
                .filter(Objects::nonNull)
                .filter(RegionEnum::isClear)
                .forEach(t -> {
            // 清理region下指定key的缓存
            if (keys != null) {
                LocalCacheUtils.clear(t, keys);
            } else {
                // 清理region下所有的缓存
                LocalCacheUtils.clear(t);
            }
            log.info("Mq local cache clear region {} : {}", t, ids);
        });
    }

    /**
     * 重发MQ
     *
     * @param message
     */
    private void retrySendMsg(ObjectMqMessage message, int delayLevel) {
        String traceId = message.getTraceId();
        // 重发MQ之前增加分布式锁，防止不同机器重复发送MQ
        String lockName = LockUtils.mergeKey("configChange", traceId);
        boolean lockResult = LockUtils.lock(lockName, 10, TimeUnit.SECONDS);
        if (!lockResult) {
            return;
        }

        message.setTraceId(UUID.randomUUID().toString());
        message.setRetryTimes(message.getRetryTimes() + 1);
        // 如果消息处理失败，则再次重新发送（因为RocketMQ广播模式不支持重试，所以业务自己重试）
        // 延后10s
        ioniaRocketMqTemplate.sendMsgSyncDelay(configChangeTopic, message, delayLevel);
    }

    @Override
    public void prepareStart(DefaultMQPushConsumer consumer) {
        // 当新服务启动时（本地消费位点不存在的情况下），使用时间戳消费历史消息，具体见RebalancePushImpl.computePullFromWhereWithException
        consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_TIMESTAMP);
        // 设置历史消息时间点，使用默认值：当前时间减去30分钟。consumeTimestamp = UtilAll.timeMillisToHumanString3(System.currentTimeMillis() - (1000 * 60 * 30))
        // consumer.setConsumeTimestamp();
    }
}
