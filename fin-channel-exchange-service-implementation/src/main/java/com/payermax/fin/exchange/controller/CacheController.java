package com.payermax.fin.exchange.controller;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.fin.exchange.common.constants.CommonConstants;
import com.payermax.fin.exchange.domainservice.cache.CacheManager;
import com.payermax.fin.exchange.service.request.ClearCacheRequest;
import com.payermax.fin.exchange.share.enums.RedisRegionEnum;
import com.payermax.fin.exchange.share.utils.RedisUtils;
import com.payermax.fin.exchange.share.enums.RegionEnum;
import com.payermax.fin.exchange.share.utils.CacheUtils;
import com.payermax.infra.ionia.rocketmq.handler.IoniaRocketMqTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 缓存Controller
 *
 * <AUTHOR>
 * @date 2021/7/28 19:30
 */
@RestController
@Slf4j
@RequestMapping(value = "/cache")
public class CacheController {

    @Value("${spring.application.name}")
    private String appName;

    @NacosValue(value = "${rocketMq.topic.channel.exchange.config.change.topic}", autoRefreshed = true)
    private String configChangeTopic;

    @Autowired
    private IoniaRocketMqTemplate ioniaRocketMqTemplate;

    @Autowired
    private CacheUtils cacheUtils;

    @Autowired
    private CacheManager cacheManager;

    @GetMapping(value = "/clearAll")
    public Result<String> clearAll() {
        RegionEnum[] regionEnums = RegionEnum.values();
        List<String> regions = new ArrayList<>();
        for (RegionEnum item : regionEnums) {
            if (item.isClear()) {
                log.info("cache clear region:{}", item.getRegion());
                regions.add(item.getRegion());
                cacheUtils.clear(item);
            }
        }

        // 发送清理本地缓存MQ
        this.sendClearLocalCacheMq(regions, null);
        return new Result<>("SUCCESS");
    }

    @GetMapping(value = "/clearByRegion")
    public Result<String> clearByRegion(String region) {
        if (StringUtils.isEmpty(region)) {
            return new Result<>("FAIL");
        }
        String[] regions = region.split(",");
        for (String tmpRegion : regions) {
            RegionEnum regionEnum = RegionEnum.getValueByKey(tmpRegion);
            if (regionEnum == null || !regionEnum.isClear()) {
                continue;
            }
            log.info("cache clear region:{}", regionEnum.getRegion());
            cacheUtils.clear(regionEnum);
        }

        // 发送清理本地缓存MQ
        this.sendClearLocalCacheMq(Arrays.asList(regions), null);
        return new Result<>("SUCCESS");
    }

    @GetMapping(value = "/clearByCacheKey")
    public Result<String> clearByCacheKey(@RequestParam("region") String region,@RequestParam("cacheKeys") String cacheKeys){
        RegionEnum regionEnum = RegionEnum.getValueByKey(region);
        if(regionEnum == null){
            return new Result<>("FAIL");
        }
        String[] keys = cacheKeys.split(",");
        // 如果是二级商户缓存，则增加清理范围
        if (RegionEnum.TABLE_TARGET_MERCHANT == regionEnum) {
            List<String> shareKeys = Arrays.stream(keys).map(k -> cacheUtils.mergeKey(k, CommonConstants.CACHE_KEY_SHARE_TARGET_MERCHANT)).collect(Collectors.toList());
            shareKeys.addAll(Arrays.asList(keys));
            keys = shareKeys.toArray(new String[]{});
        }
        cacheUtils.del(regionEnum, keys);
        log.info("cache clear region:{},cacheKey:{}", regionEnum.getRegion(),keys);

        // 发送清理本地缓存MQ
        this.sendClearLocalCacheMq(Collections.singletonList(region), Arrays.asList(keys));
        return new Result<>("SUCCESS");
    }

    @GetMapping(value = "/delByKey")
    public Result<String> delByKey(String cacheKey) {
        if (StringUtils.isEmpty(cacheKey)) {
            return new Result<>("FAIL");
        }

        RedisUtils.getNewRedisTemplate().delete(cacheKey);
        log.info("cache clear key:{}", cacheKey);

        return new Result<>("SUCCESS");
    }

    /**
     * 设置账号余额标识 0：余额不足*
     * @param accountId
     * @param flag
     * @param timeout 超时时间 单位：秒
     * @return
     */
    @GetMapping(value = "/setAccountBalanceFlag")
    public Result<String> setAccountBalanceFlag(String accountId, String flag, @RequestParam(required = false) Long timeout) {
        if (StringUtils.isEmpty(accountId) || StringUtils.isEmpty(flag)) {
            return new Result<>("FAIL");
        }
        String cacheKey = cacheUtils.mergeKey(RedisRegionEnum.LIMIT.getRegion(), "accountBalanceFlag", accountId);
        // 如果没有超时时间，则默认30天
        if (timeout == null || timeout <= 0) {
            timeout = 30 * 24 * 3600L;
        }
        RedisUtils.getNewRedisTemplate().opsForValue().set(cacheKey, flag, timeout, TimeUnit.SECONDS);

        log.info("set cache key:{}, val:{}", cacheKey, flag);

        return new Result<>("SUCCESS");
    }


    /**
     * 根据表空间和业务类型清空缓存
     * @param clearCacheRequest
     * @return
     */
    @PostMapping(value = "/clearByBusinessKey")
    public Result<String> clearByBusinessKey(@RequestBody ClearCacheRequest clearCacheRequest) {
        List<ClearCacheRequest.CacheRegionRequest> regions = clearCacheRequest.getRegions();
        if (CollectionUtils.isEmpty(regions)) {
            return new Result<>("FAIL");
        }
        for (ClearCacheRequest.CacheRegionRequest regionItem : regions) {
            RegionEnum regionEnum = RegionEnum.getValueByKey(regionItem.getRegion());
            if (regionEnum == null) {
                continue;
            }
            List<ClearCacheRequest.CacheBusinessRequest> businessTypes = regionItem.getBusinessTypes();
            if (CollectionUtils.isEmpty(businessTypes)) {
                continue;
            }
            for (ClearCacheRequest.CacheBusinessRequest businessTypeItem : businessTypes) {
                String businessType = businessTypeItem.getBusinessType();
                if (StringUtils.isBlank(businessType)) {
                    continue;
                }
                List<String> businessKeys = businessTypeItem.getBusinessKeys();
                if (CollectionUtils.isEmpty(businessKeys)) {
                    continue;
                }
                String[] keys = businessKeys.stream().map(businessKey -> cacheUtils.mergeKey(businessType, businessKey)).toArray(String[]::new);
                cacheUtils.del(regionEnum, keys);
                log.info("cache clear region:{},cacheKey:{}", regionEnum.getRegion(), keys);

                // 发送清理本地缓存MQ
                this.sendClearLocalCacheMq(Collections.singletonList(regionEnum.getRegion()), Arrays.asList(keys));
            }
        }
        return new Result<>("SUCCESS");
    }

    /**
     * 发送清理本地缓存的MQ
     *
     * @param regions
     * @param cacheKeys
     */
    private void sendClearLocalCacheMq(List<String> regions, List<String> cacheKeys) {
        if (CollectionUtils.isEmpty(regions)) {
            return;
        }
        cacheManager.refreshDynamicCacheMq(regions, cacheKeys);
    }
}
