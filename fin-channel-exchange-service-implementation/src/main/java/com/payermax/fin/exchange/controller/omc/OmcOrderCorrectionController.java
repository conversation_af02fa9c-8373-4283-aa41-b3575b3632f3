package com.payermax.fin.exchange.controller.omc;

import com.payermax.fin.exchange.domainservice.manage.BaseManage;
import com.payermax.fin.exchange.service.request.PaymentDataCorrectionRequest;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.fin.exchange.service.request.PendingTranDataCorrectionRequest;
import com.payermax.infra.ionia.log.digest.core.annotation.DigestLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2021/10/21 14:42
 */
@RestController
@Slf4j
@RequestMapping(value = "/omc/orderCorrection")
@Api(description = "OMC入款订单服务", tags = "OMC入款订单服务")
public class OmcOrderCorrectionController {

    @Autowired
    private BaseManage<PaymentDataCorrectionRequest, Boolean> paymentDataCorrectionManage;
    
    @Autowired
    private BaseManage<PendingTranDataCorrectionRequest, Void> pendingTransChangeOrderRetryManage;

    @PostMapping(value = "/paymentDataCorrection")
    @ApiOperation(value = "订单订正", notes = "入款数据订正，只支持入款订单状态为失败的订正  \n" +
            "SYSTEM_BUSY: 通用内部未知错误---需要定位系统异常  \n" +
            "REQUEST_PARAM_INVALID: 参数错误---检查入参  \n" +
            "CHANNEL_ORDER_IS_NOT_EXIST: 渠道订单不存在---检查渠道单号是否正确  \n" +
            "STATE_TRANSITION_NO_SUPPORT: 不支持的订单状态流转---检查订单状态  \n" +
            "ORDER_UPDATE_FAIL: 订单更新失败---订单状态乐观锁，检查订单状态")
    @DigestLog(isRecord= true)
    public Result<Boolean> paymentDataCorrection(@RequestBody PaymentDataCorrectionRequest request) {
        return paymentDataCorrectionManage.execute(request);
    }

    @PostMapping(value = "/pendingTransChangeOrderRetry")
    @ApiOperation(value = "支付中交易换单重试", notes = "出款、退款支付中提交单置为失败，生成新的订单重试  \n" +
            "SYSTEM_BUSY: 通用内部未知错误---需要定位系统异常  \n" +
            "REQUEST_PARAM_INVALID: 参数错误---检查入参  \n" +
            "CHANNEL_ORDER_IS_NOT_EXIST: 渠道订单不存在---检查渠道单号是否正确  \n" +
            "STATE_TRANSITION_NO_SUPPORT: 不支持的订单状态流转---检查订单状态  \n" +
            "ORDER_UPDATE_FAIL: 订单更新失败---订单状态乐观锁，检查订单状态")
    @DigestLog(isRecord= true)
    public Result<Void> pendingTransChangeOrderRetry(@RequestBody PendingTranDataCorrectionRequest request) {
        return pendingTransChangeOrderRetryManage.execute(request);
    }

}
