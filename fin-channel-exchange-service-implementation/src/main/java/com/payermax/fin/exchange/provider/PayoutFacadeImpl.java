package com.payermax.fin.exchange.provider;

import com.alibaba.csp.sentinel.EntryType;
import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.fin.exchange.share.utils.ThreadLocalUtils;
import com.payermax.infra.ionia.log.digest.core.annotation.DigestLog;
import com.payermax.fin.exchange.assembler.FacadeAssembler;
import com.payermax.fin.exchange.common.util.ValidationUtils;
import com.payermax.fin.exchange.domain.inquiry.InquiryOrderRequestDO;
import com.payermax.fin.exchange.domain.inquiry.InquiryOrderResponseDO;
import com.payermax.fin.exchange.domain.trans.payout.PayoutRequestOrderDO;
import com.payermax.fin.exchange.domain.trans.payout.PayoutResponseOrderDO;
import com.payermax.fin.exchange.domainservice.IDomainService;
import com.payermax.fin.exchange.integration.rpc.proxy.ContextClientProxy;
import com.payermax.fin.exchange.service.context.PayoutRequestContext;
import com.payermax.fin.exchange.service.facade.PayoutFacade;
import com.payermax.fin.exchange.service.request.InquiryPayoutRequest;
import com.payermax.fin.exchange.service.request.PayoutRequest;
import com.payermax.fin.exchange.service.response.InquiryPayoutResponse;
import com.payermax.fin.exchange.service.response.PayoutResponse;
import com.payermax.fin.exchange.util.PayoutAsyncUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.config.annotation.Method;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 出款服务
 *
 * <AUTHOR>
 * @date 2021/12/7 17:53
 */
@Slf4j
@DubboService(version = "${exchange.dubbo.api.version}", group = "${exchange.dubbo.api.group-id:}", methods = {
        @Method(name = "withdraw", timeout = 13000)
})
@Service
public class PayoutFacadeImpl implements PayoutFacade {

    @Autowired
    private FacadeAssembler facadeAssembler;

    @Autowired
    private ContextClientProxy contextClientProxy;

    @Autowired
    private IDomainService<PayoutRequestOrderDO, PayoutResponseOrderDO> payoutDomainService;

    @Autowired
    private IDomainService<InquiryOrderRequestDO, InquiryOrderResponseDO> inquiryDomainService;

    @Autowired
    private IDomainService<PayoutRequestOrderDO, PayoutResponseOrderDO> asyncPayoutDomainService;

    @Override
    @DigestLog(isRecord = true)
    public Result<PayoutResponse> withdraw(PayoutRequest request) {
        // 从上下文查询信息
        String requestNo = request.getPayRequestNo();
        PayoutRequestContext requestContext = contextClientProxy.queryPayoutRequest(requestNo);
        // 上下文不为空，则取上下文
        if (requestContext != null) {
            request.setRequestContext(requestContext);
            // 校验上下文参数
            ValidationUtils.validate(requestContext);
        }
        // 入参转换
        PayoutRequestOrderDO requestOrder = facadeAssembler.toPayoutReqOrder(request);
        // 设置同步申请标识
        ThreadLocalUtils.setLocalCache(ThreadLocalUtils.IS_SYNC_APPLY, true);
        try {
            // 调用领域服务
            PayoutResponseOrderDO responseOrder = PayoutAsyncUtil.supportAsync(requestOrder) ? asyncPayoutDomainService.execute(requestOrder) : payoutDomainService.execute(requestOrder);
            // 响应转换
            PayoutResponse response = facadeAssembler.toPayoutResp(responseOrder);
            // 响应报文保存上下文
            contextClientProxy.savePayoutResponse(requestNo, response);
            return ResultUtil.success(response);
        } catch (Exception e) {
            throw e;
        } finally {
            ThreadLocalUtils.removeLocalCache(ThreadLocalUtils.IS_SYNC_APPLY);
        }
    }

    @Override
    @SentinelResource(value = "dubbo:provider:PayoutFacade.inquiryPayout", entryType = EntryType.IN)
    public Result<InquiryPayoutResponse> inquiry(InquiryPayoutRequest request) {
        // 请求转换
        InquiryOrderRequestDO requestOrder = facadeAssembler.toPayoutInquiryReqOrder(request);
        // 调用领域服务
        InquiryOrderResponseDO responseOrder = inquiryDomainService.execute(requestOrder);
        // 响应转换
        return ResultUtil.success(facadeAssembler.toPayoutInquiryResp(responseOrder));
    }
}
