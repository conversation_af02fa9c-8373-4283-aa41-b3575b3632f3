package com.payermax.fin.exchange.serializer;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

public class MyStringSerializer extends StringRedisSerializer {

    @Value("${spring.application.name:fintech}")
    private String applicationName;

    private String prefix;

    private final Charset charset = StandardCharsets.UTF_8;

    public MyStringSerializer(String prefix) {
        this.prefix = prefix;
    }

    @Override
    public String deserialize(byte[] bytes) {
        String keyPrefix = prefix;
        if (StringUtils.isBlank(keyPrefix)) {
            keyPrefix = applicationName;
        }
        String saveKey = new String(bytes, charset);
        int indexOf = saveKey.indexOf(keyPrefix);
        if (indexOf > 0) {
            // logger.info ( "key缺少前缀" );
        } else {
            saveKey = saveKey.substring(indexOf);
        }
        return (saveKey.getBytes() == null ? null : saveKey);
    }

    @Override
    public byte[] serialize(String string) {
        String keyPrefix = prefix;
        if (StringUtils.isBlank(keyPrefix)) {
            keyPrefix = applicationName;
        }
        String key = keyPrefix + string;
        return (key == null ? null : key.getBytes(charset));
    }
}
