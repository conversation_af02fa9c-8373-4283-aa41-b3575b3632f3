package com.payermax.fin.exchange.config;

import com.alibaba.fastjson.parser.ParserConfig;
import com.alibaba.fastjson.util.TypeUtils;
import com.payermax.common.lang.fastjson.MoneyDeserializer;
import com.payermax.common.lang.util.money.Money;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/6/6 11:54
 */
@Component
public class FastjsonConfig {

    @Autowired
    private SafeAutoTypeCheckHandler autoTypeCheckHandler;

    @PostConstruct
    public void init(){
        /**
         * 最新版本摘要日志依赖fastjson 1.2.76以及以上版本,而高版本fastjson在开启safeMode情况下默认不支持 autoType,并且无法通过配置支持
         * 项目中使用的j2cache序列化强依赖fastjson的autoType
         * fastjson提供了一个入口支持autoType: 注入AutoTypeCheckHandler
         *
         * net.oschina.j2cache.util.FastjsonSerializer#deserialize(byte[])
         * com.alibaba.fastjson.parser.ParserConfig#checkAutoType(java.lang.String, java.lang.Class<?>, int)
         */
        ParserConfig.getGlobalInstance().addAutoTypeCheckHandler(autoTypeCheckHandler);
        ParserConfig.getGlobalInstance().setSafeMode(true);
        ParserConfig.getGlobalInstance().setAutoTypeSupport(false);

        /**
         * 解决Money反序列化字段顺序问题
         */
        ParserConfig.getGlobalInstance().putDeserializer(Money.class, new MoneyDeserializer());
    }

}
