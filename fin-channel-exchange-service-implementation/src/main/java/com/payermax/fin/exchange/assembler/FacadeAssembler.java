package com.payermax.fin.exchange.assembler;

import com.alibaba.fastjson.JSONObject;
import com.payermax.basic.contexcenter.service.client.model.risk.RiskContextInfo;
import com.payermax.common.lang.util.JsonUtils;
import com.payermax.fin.exchange.common.config.PropertiesCenter;
import com.payermax.fin.exchange.common.enums.MainRequestTypeEnum;
import com.payermax.fin.exchange.common.enums.OrderStatusEnum;
import com.payermax.fin.exchange.common.enums.RequestTypeEnum;
import com.payermax.fin.exchange.common.util.ValidationUtils;
import com.payermax.fin.exchange.domain.callback.CallbackRequestDO;
import com.payermax.fin.exchange.domain.common.*;
import com.payermax.fin.exchange.domain.filter.PreFilterAccountRequestDO;
import com.payermax.fin.exchange.domain.filter.PreFilterAccountResponseDO;
import com.payermax.fin.exchange.domain.inquiry.*;
import com.payermax.fin.exchange.domain.trans.ForexTradeRequestDO;
import com.payermax.fin.exchange.domain.trans.RequestOrderDO;
import com.payermax.fin.exchange.domain.trans.ResponseOrderDO;
import com.payermax.fin.exchange.domain.trans.applyToken.ApplyPaymentTokenRequestDO;
import com.payermax.fin.exchange.domain.trans.applyToken.TokenUnbindingRequestOrderDO;
import com.payermax.fin.exchange.domain.trans.auth.AuthRequestOrderDO;
import com.payermax.fin.exchange.domain.trans.auth.AuthResponseOrderDO;
import com.payermax.fin.exchange.domain.trans.auth.RevokeAuthRequestOrderDO;
import com.payermax.fin.exchange.domain.trans.auth.RevokeAuthResponseOrderDO;
import com.payermax.fin.exchange.domain.trans.pay.*;
import com.payermax.fin.exchange.domain.trans.payout.PayoutRequestOrderDO;
import com.payermax.fin.exchange.domain.trans.payout.PayoutResponseOrderDO;
import com.payermax.fin.exchange.domain.trans.refund.CloseRequestOrderDO;
import com.payermax.fin.exchange.domain.trans.refund.RefundRequestOrderDO;
import com.payermax.fin.exchange.domain.trans.refund.RefundResponseOrderDO;
import com.payermax.fin.exchange.domain.trans.refund.VoidRequestOrderDO;
import com.payermax.fin.exchange.service.context.AuthorizationRequestContext;
import com.payermax.fin.exchange.service.context.PayRequestContext;
import com.payermax.fin.exchange.service.context.PayoutRequestContext;
import com.payermax.fin.exchange.service.enums.FilterBizTypeEnum;
import com.payermax.fin.exchange.service.enums.OrderStatus4OutEnum;
import com.payermax.fin.exchange.service.request.*;
import com.payermax.fin.exchange.service.response.*;
import com.payermax.fin.exchange.share.bo.PreFilterDisableMethodBo;
import com.payermax.fin.exchange.share.bo.PreFilterDisableReasonDetailBo;
import com.payermax.fin.exchange.share.constants.ShareConstants;
import com.payermax.fin.exchange.share.domain.ExtendPropertyDO;
import com.payermax.fin.exchange.share.domain.OrderInfoDO;
import com.payermax.fin.exchange.share.domain.PaymentMethodDO;
import com.payermax.fin.exchange.share.enums.ActionTypeEnum;
import com.payermax.fin.exchange.share.enums.FactorKeyEnum;
import com.payermax.fin.exchange.share.enums.LogicKeyEnum;
import com.payermax.fin.exchange.share.enums.PaymentTypeEnum;
import com.payermax.fin.exchange.share.request.InquiryChannelEventRequest;
import com.payermax.fin.exchange.share.request.InquiryChannelInstRequest;
import com.payermax.fin.exchange.share.request.InquiryTargetMerchantRequest;
import com.payermax.fin.exchange.share.request.PreFilterRequest;
import com.payermax.fin.exchange.share.response.PreFilterResponse;
import com.payermax.fin.exchange.share.utils.ExtendPropertyUtils;
import com.ushareit.fintech.common.model.dto.Money;
import com.ushareit.fintech.components.enums.cashier.CustomerTypeEnum;
import com.ushareit.fintech.components.enums.cashier.PaymentMethodTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;

import java.math.BigDecimal;
import java.util.*;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2021/12/13 21:07
 */
@Mapper(componentModel = "spring", imports = {OrderStatus4OutEnum.class, OrderStatusEnum.class, PaymentTypeEnum.class, RequestTypeEnum.class, ActionTypeEnum.class, MainRequestTypeEnum.class,
        JsonUtils.class, JSONObject.class, PaymentMethodTypeEnum.class, Money.class, PropertiesCenter.class, FilterBizTypeEnum.class, StringUtils.class})
public interface FacadeAssembler {

    /**
     * 查询渠道实例请求参数转换
     *
     * @param request
     * @return
     */
    InquiryChannelInstRequestDO toInquiryChannelInstReqOrder(InquiryChannelInstRequest request);

    /**
     * 查询二级商户请求参数转换
     *
     * @param request
     * @return
     */
    InquiryTargetMerchantRequestDO toInquiryTargetMerchantReqOrder(InquiryTargetMerchantRequest request);

    /**
     * 查询特殊路由规则请求参数转换
     *
     * @param request
     * @return
     */
    InquiryChannelEventRequestDO toInquiryChannelEventReqOrder(InquiryChannelEventRequest request);

    /**
     * 入款请求参数转换
     *
     * @param payRequest
     * @return
     */
    default PayRequestOrderDO toPayReqOrder(PayRequest payRequest) {
        PayRequestContext payRequestContext = payRequest.getRequestContext();
        ValidationUtils.notNullCheck(payRequest.getRequestContext(), "[requestContext] is mandatory");

        // 先从上下文中转换对象
        PayRequestOrderDO payRequestOrder = toPayReqOrder(payRequestContext);
        payRequestOrder.setSourceBizOrderNo(payRequest.getPayRequestNo());
        payRequestOrder.setTradeOrderNo(payRequest.getTradeOrderNo());
        payRequestOrder.setBizOrderNo(payRequest.getPayOrderNo());
        payRequestOrder.setChannelPayRequestNo(payRequest.getPayOrderNo());
        payRequestOrder.setPaymentType(PaymentTypeEnum.PAYMENT);
        payRequestOrder.setActionType(ActionTypeEnum.APPLY);
        if (StringUtils.isBlank(payRequestOrder.getRequestType())) {
            payRequestOrder.setRequestType(RequestTypeEnum.PAY.getCode());
        }
        // 支付方式
        PaymentMethodDO paymentMethod = payRequestOrder.getPaymentMethod();
        paymentMethod.setBizIdentify(payRequest.getBizIdentify());
        paymentMethod.setProductCode(payRequest.getProductCode());
        paymentMethod.setPaymentMethodType(payRequest.getPaymentMethodType());
        paymentMethod.setTargetOrg(payRequest.getTargetOrg());
        paymentMethod.setAmount(payRequest.getAmount());
        // 订单信息
        OrderInfoDO orderInfo = payRequestOrder.getOrderInfo();
        orderInfo.setServiceMode(payRequest.getServiceMode());
        orderInfo.setServiceEntity(payRequest.getServiceEntity());

        // 扩展参数
        Map extParams = payRequestOrder.getExtParams();
        if (extParams == null) {
            extParams = new HashMap<>();
            payRequestOrder.setExtParams(extParams);
        }
        PayRequestContext.ShippingInfo shippingInfo = payRequestContext.getShippingInfo();
        if (shippingInfo != null) {
            extParams.putAll(JsonUtils.toBean(Map.class, JsonUtils.toString(shippingInfo)));
        }
        PayRequestContext.BillingInfo billingInfo = payRequestContext.getBillingInfo();
        if (billingInfo != null) {
            extParams.putAll(JsonUtils.toBean(Map.class, JsonUtils.toString(billingInfo)));
        }
        PayRequestContext.MerchantInfo merchantInfo = payRequestContext.getMerchantInfo();
        if (merchantInfo != null) {
            extParams.putAll(JsonUtils.toBean(Map.class, JsonUtils.toString(merchantInfo)));
        }
        PayRequestContext.OutUserInfo userInfo = payRequestContext.getOutUserInfo();
        if (userInfo != null) {
            extParams.putAll(JsonUtils.toBean(Map.class, JsonUtils.toString(userInfo)));
        }
        PayRequestContext.RiskInfo riskInfo = payRequestContext.getRiskInfo();
        if (riskInfo != null) {
            extParams.putAll(JsonUtils.toBean(Map.class, JsonUtils.toString(riskInfo)));
        }
        // 外部3DS场景
        if (Objects.nonNull(payRequestOrder.getThreeDSecureInfo())) {
            if (BooleanUtils.isTrue(payRequestOrder.getThreeDSecureInfo().getUseExt3DS())) {
                ExtendPropertyUtils.addExtendProperties(paymentMethod, FactorKeyEnum.IS_SUPPORT_EXT3DS.name(), ShareConstants.YES_FLAG, LogicKeyEnum.STRONG_INCLUDE.getCode());
            } else {
                // 非法场景，3DS信息置为空，不向下游传递
                payRequestOrder.setThreeDSecureInfo(null);
            }
        }
        // 兼容逻辑 考虑废弃additionalPaymentTag字段
        if (StringUtils.isNotBlank(payRequestOrder.getAdditionalPaymentTag())) {
            if (null == payRequestOrder.getScenarioInfo()) {
                payRequestOrder.setScenarioInfo(new PayRequestOrderDO.ScenarioInfo());
            }
            if ("NETWORK_TOKEN".equals(payRequestOrder.getAdditionalPaymentTag())) {
                payRequestOrder.getScenarioInfo().setExtraNetworkToken(Boolean.TRUE);
            } else if ("CARD_ON_FILE".equals(payRequestOrder.getAdditionalPaymentTag())) {
                payRequestOrder.getScenarioInfo().setCardholderInitiated(Boolean.TRUE);
            }
        }
        //场景信息逻辑
        if (Objects.nonNull(payRequestOrder.getScenarioInfo())) {
            PayRequestOrderDO.ScenarioInfo scenarioInfo = payRequestOrder.getScenarioInfo();
            if (StringUtils.isNotBlank(scenarioInfo.getRecurringType())) { //代扣场景
                Boolean merchantInitiated = scenarioInfo.getMerchantInitiated();
                if (BooleanUtils.isTrue(merchantInitiated)) { //MIT
                    ExtendPropertyUtils.addExtendProperties(paymentMethod, FactorKeyEnum.INITIATED_MODE.name(), ShareConstants.MIT, LogicKeyEnum.STRONG_INCLUDE.getCode());
                } else { //首次绑卡
                    ExtendPropertyUtils.addExtendProperties(paymentMethod, FactorKeyEnum.INITIATED_MODE.name(), ShareConstants.CIT_FIRST_STORED, LogicKeyEnum.STRONG_INCLUDE.getCode());

                }
            }
            if (BooleanUtils.isTrue(scenarioInfo.getExtraNetworkToken())) { // 外部network token
                ExtendPropertyUtils.addExtendProperties(paymentMethod, FactorKeyEnum.IS_SUPPORT_NETWORKTOKEN.name(), ShareConstants.YES_FLAG, LogicKeyEnum.STRONG_INCLUDE.getCode());
            }
        }
        //0元auth
        if (paymentMethod.getAmount() != null && BigDecimal.ZERO.compareTo(paymentMethod.getAmount().getValue()) == 0 && RequestTypeEnum.AUTH.getCode().equals(payRequestOrder.getRequestType())) {
            paymentMethod.setIsIgnoreAmountValue(true);
        }
        return payRequestOrder;
    }

    /**
     * 入款请求参数转换
     *
     * @param payRequestContext
     * @return
     */
    PayRequestOrderDO toPayReqOrder(PayRequestContext payRequestContext);

    /**
     * 风控上下文参数转换*
     * *
     *
     * @param riskContextInfo
     * @return
     */
    PayRequestContext.RiskInfo fillRiskInfo(@MappingTarget PayRequestContext.RiskInfo riskInfo, RiskContextInfo riskContextInfo);

    /**
     * 入款响应参数转换
     *
     * @param responseOrder
     * @return
     */
    @Mapping(target = "payOrderNo", source = "bizOrderNo")
    @Mapping(target = "status", expression = "java(OrderStatus4OutEnum.mappingStatus4Out(responseOrder.getStatus().name()))")
    PayResponse toPayResp(PayResponseOrderDO responseOrder);

    /**
     * capture请求参数转换
     *
     * @param request
     * @return
     */
    @Mapping(target = "bizOrderNo", source = "payOrderNo")
    @Mapping(target = "oriBizOrderNo", source = "oriPayOrderNo")
    @Mapping(target = "sourceBizOrderNo", source = "payRequestNo")
    @Mapping(target = "paymentMethod.amount", source = "amount")
    @Mapping(target = "orderInfo.remark", source = "remark")
    @Mapping(target = "paymentType", expression = "java(PaymentTypeEnum.PAYMENT)")
    @Mapping(target = "requestType", expression = "java(RequestTypeEnum.CAPTURE.getCode())")
    CaptureRequestOrderDO toCaptureRequest(CaptureRequest request);

    /**
     * capture请求参数转换
     *
     * @param responseOrder
     * @return
     */
    @Mapping(target = "payOrderNo", source = "bizOrderNo")
    @Mapping(target = "status", expression = "java(OrderStatus4OutEnum.mappingStatus4Out(responseOrder.getStatus().name()))")
    CaptureResponse toCaptureResp(ResponseOrderDO responseOrder);

    /**
     * 支付控制请求参数转换
     *
     * @param request
     * @return
     */
    @Mapping(target = "paymentType", expression = "java(PaymentTypeEnum.PAYMENT)")
    @Mapping(target = "actionType", expression = "java(ActionTypeEnum.CONTROL_APPLY)")
    PayControlRequestOrderDO toPayControlReqOrder(UnifyControlRequest request);

    /**
     * 后置处理
     *
     * @param request
     * @param requestOrderDO
     */
    @AfterMapping
    default void afterToPayControlReqOrder(UnifyControlRequest request, @MappingTarget PayControlRequestOrderDO requestOrderDO) {
        if (request == null || request.getOrderInfo() == null) {
            return;
        }
        PaymentMethodDO paymentMethodDO = requestOrderDO.getPaymentMethod();
        if (paymentMethodDO == null) {
            requestOrderDO.setPaymentMethod(new PaymentMethodDO());
        }
        requestOrderDO.getPaymentMethod().setAmount(request.getOrderInfo().getAmount());
    }

    /**
     * 支付控制响应参数转换
     *
     * @param responseOrder
     * @return
     */
    @Mapping(target = "status", expression = "java(OrderStatus4OutEnum.mappingStatus4Out(responseOrder.getStatus().name()))")
    @Mapping(target = "payStatus", expression = "java(OrderStatus4OutEnum.mappingStatus4Out(responseOrder.getPayStatus()!=null?responseOrder.getPayStatus().name():null))")
    UnifyControlResponse toPayControlResp(PayControlResponseOrderDO responseOrder);

    /**
     * 撤销请求参数转换
     *
     * @param request
     * @return
     */
    @Mapping(target = "bizOrderNo", source = "payOrderNo")
    @Mapping(target = "oriBizOrderNo", source = "oriPayOrderNo")
    @Mapping(target = "sourceBizOrderNo", source = "payRequestNo")
    @Mapping(target = "orderInfo.remark", source = "remark")
    @Mapping(target = "paymentMethod.amount", source = "amount")
    @Mapping(target = "paymentType", expression = "java(PaymentTypeEnum.REFUND)")
    @Mapping(target = "requestType", expression = "java(RequestTypeEnum.VOID.getCode())")
    VoidRequestOrderDO toVoidReqOrder(VoidRequest request);

    /**
     * 撤销请求参数转换
     *
     * @param request
     * @return
     */
    @Mapping(target = "bizOrderNo", source = "payOrderNo")
    @Mapping(target = "oriBizOrderNo", source = "oriPayOrderNo")
    @Mapping(target = "sourceBizOrderNo", source = "payRequestNo")
    @Mapping(target = "orderInfo.remark", source = "remark")
    @Mapping(target = "paymentMethod.amount", source = "amount")
    @Mapping(target = "paymentType", expression = "java(PaymentTypeEnum.PAYMENT)")
    @Mapping(target = "requestType", expression = "java(RequestTypeEnum.CLOSE.getCode())")
    CloseRequestOrderDO toCloseReqOrder(CloseRequest request);

    /**
     * 退款响应参数转换
     *
     * @param responseOrder
     * @return
     */
    @Mapping(target = "payOrderNo", source = "bizOrderNo")
    @Mapping(target = "status", expression = "java(OrderStatus4OutEnum.mappingStatus4Out(responseOrder.getStatus().name()))")
    VoidResponse toVoidResp(ResponseOrderDO responseOrder);

    @Mapping(target = "status", expression = "java(OrderStatus4OutEnum.mappingStatus4Out(responseOrder.getStatus().name()))")
    CloseResponse toCloseResp(ResponseOrderDO responseOrder);

    /**
     * 退款请求参数转换
     *
     * @param request
     * @return
     */
    @Mapping(target = "bizOrderNo", source = "payOrderNo")
    @Mapping(target = "oriBizOrderNo", source = "oriPayOrderNo")
    @Mapping(target = "sourceBizOrderNo", source = "payRequestNo")
    @Mapping(target = "paymentType", expression = "java(PaymentTypeEnum.REFUND)")
    @Mapping(target = "requestType", expression = "java(RequestTypeEnum.REFUND.getCode())")
    RefundRequestOrderDO toRefundReqOrder(RefundRequest request);

    /**
     * 退款响应参数转换
     *
     * @param responseOrder
     * @return
     */
    @Mapping(target = "payOrderNo", source = "bizOrderNo")
    @Mapping(target = "status", expression = "java(OrderStatus4OutEnum.mappingStatus4Out(responseOrder.getStatus().name()))")
    RefundResponse toRefundResp(RefundResponseOrderDO responseOrder);

    /**
     * 出款请求参数转换
     *
     * @param request
     * @return
     */
    default PayoutRequestOrderDO toPayoutReqOrder(PayoutRequest request) {
        PayoutRequestContext requestContext = request.getRequestContext();
        ValidationUtils.notNullCheck(requestContext, "[requestContext] is mandatory");

        // 先从上下文中转换对象
        PayoutRequestOrderDO requestOrder = toPayoutReqOrder(requestContext);
        requestOrder.setSourceBizOrderNo(request.getPayRequestNo());
        requestOrder.setBizOrderNo(request.getPayOrderNo());
        requestOrder.setChannelPayRequestNo(request.getPayOrderNo());
        requestOrder.setPaymentType(PaymentTypeEnum.PAYOUT);
        requestOrder.setActionType(ActionTypeEnum.APPLY);
        requestOrder.setRequestType(RequestTypeEnum.PAYOUT.getCode());
        // 支付方式
        PaymentMethodDO paymentMethod = requestOrder.getPaymentMethod();
        paymentMethod.setBizIdentify(request.getBizIdentify());
        paymentMethod.setProductCode(request.getProductCode());
        paymentMethod.setPaymentMethodType(request.getPaymentMethodType());
        paymentMethod.setTargetOrg(request.getTargetOrg());
        paymentMethod.setAmount(request.getAmount());
        // 设置paymentType
        if (StringUtils.isNotBlank(paymentMethod.getPaymentType())) {
            PaymentTypeEnum paymentTypeEnum = PaymentTypeEnum.getByValue(paymentMethod.getPaymentType());
            if (paymentTypeEnum != null) {
                requestOrder.setPaymentType(paymentTypeEnum);
            }
        }
        // 订单信息
        OrderInfoDO orderInfo = requestOrder.getOrderInfo();
        orderInfo.setServiceMode(request.getServiceMode());
        orderInfo.setServiceEntity(request.getServiceEntity());
        return requestOrder;
    }

    /**
     * 出款请求参数转换
     *
     * @param requestContext
     * @return
     */
    PayoutRequestOrderDO toPayoutReqOrder(PayoutRequestContext requestContext);

    /**
     * 出款响应参数转换
     *
     * @param responseOrder
     * @return
     */
    @Mapping(target = "payOrderNo", source = "bizOrderNo")
    @Mapping(target = "status", expression = "java(OrderStatus4OutEnum.mappingStatus4Out(responseOrder.getStatus().name()))")
    PayoutResponse toPayoutResp(PayoutResponseOrderDO responseOrder);

    /**
     * 支付查询请求转换
     *
     * @param request
     * @return
     */
    @Mapping(target = "paymentType", expression = "java(PaymentTypeEnum.PAYMENT)")
    @Mapping(target = "bizOrderNo", source = "payOrderNo")
    InquiryOrderRequestDO toPayInquiryReqOrder(InquiryPaymentRequest request);

    /**
     * capture查询请求转换
     *
     * @param request
     * @return
     */
    @Mapping(target = "paymentType", expression = "java(PaymentTypeEnum.PAYMENT)")
    @Mapping(target = "bizOrderNo", source = "payOrderNo")
    InquiryOrderRequestDO toCaptureInquiryReqOrder(InquiryCaptureRequest request);

    /**
     * 退款查询请求转换
     *
     * @param request
     * @return
     */
    @Mapping(target = "paymentType", expression = "java(PaymentTypeEnum.PAYOUT)")
    @Mapping(target = "bizOrderNo", source = "payOrderNo")
    InquiryOrderRequestDO toPayoutInquiryReqOrder(InquiryPayoutRequest request);

    /**
     * 撤销查询请求转换
     *
     * @param request
     * @return
     */
    @Mapping(target = "paymentType", expression = "java(PaymentTypeEnum.REFUND)")
    @Mapping(target = "bizOrderNo", source = "payOrderNo")
    InquiryOrderRequestDO toVoidInquiryReqOrder(InquiryVoidRequest request);

    /**
     * 退款查询请求转换
     *
     * @param request
     * @return
     */
    @Mapping(target = "paymentType", expression = "java(PaymentTypeEnum.REFUND)")
    @Mapping(target = "bizOrderNo", source = "payOrderNo")
    InquiryOrderRequestDO toRefundInquiryReqOrder(InquiryRefundRequest request);

    /**
     * 支付查询响应转换
     *
     * @param responseOrder
     * @return
     */
    @Mapping(target = "status", expression = "java(OrderStatus4OutEnum.mappingStatus4Out(responseOrder.getStatus().name()))")
    @Mapping(target = "payOrderNo", source = "bizOrderNo")
    InquiryPaymentResponse toPayInquiryResp(InquiryOrderResponseDO responseOrder);

    /**
     * capture查询响应转换
     *
     * @param responseOrder
     * @return
     */
    @Mapping(target = "status", expression = "java(OrderStatus4OutEnum.mappingStatus4Out(responseOrder.getStatus().name()))")
    @Mapping(target = "payOrderNo", source = "bizOrderNo")
    InquiryCaptureResponse toCaptureInquiryResp(InquiryOrderResponseDO responseOrder);

    /**
     * 出款查询响应转换
     *
     * @param responseOrder
     * @return
     */
    @Mapping(target = "status", expression = "java(OrderStatus4OutEnum.mappingStatus4Out(responseOrder.getStatus().name()))")
    @Mapping(target = "payOrderNo", source = "bizOrderNo")
    InquiryPayoutResponse toPayoutInquiryResp(InquiryOrderResponseDO responseOrder);

    /**
     * 退款查询响应转换
     *
     * @param responseOrder
     * @return
     */
    @Mapping(target = "status", expression = "java(OrderStatus4OutEnum.mappingStatus4Out(responseOrder.getStatus().name()))")
    @Mapping(target = "payOrderNo", source = "bizOrderNo")
    InquiryVoidResponse toVoidInquiryResp(InquiryOrderResponseDO responseOrder);

    /**
     * 退款查询响应转换
     *
     * @param responseOrder
     * @return
     */
    @Mapping(target = "status", expression = "java(OrderStatus4OutEnum.mappingStatus4Out(responseOrder.getStatus().name()))")
    @Mapping(target = "payOrderNo", source = "bizOrderNo")
    InquiryRefundResponse toRefundInquiryResp(InquiryOrderResponseDO responseOrder);

    /**
     * callback回调请求转换
     *
     * @param request
     * @return
     */
    @Mapping(target = "channelPayCommitNo", source = "referenceNo")
    @Mapping(target = "thirdOrgOrderNo", source = "payThirdChannelNo")
    @Mapping(target = "fourthOrgOrderNo", source = "payThirdBankTxnId")
    CallbackRequestDO toCallbackReqOrder(CallbackNotifyRequest request);

    /**
     * 授权请求转换
     *
     * @param request
     * @return
     */
    @Mapping(target = "channelPayRequestNo", source = "requestNo")
    @Mapping(target = "bizOrderNo", source = "requestNo")
    @Mapping(target = "paymentType", expression = "java(PaymentTypeEnum.PAYMENT)")
    @Mapping(target = "actionType", expression = "java(ActionTypeEnum.AUTHORIZATION_APPLY)")
    @Mapping(target = "requestType", expression = "java(RequestTypeEnum.AUTHORIZATION_APPLY.getCode())")
    @Mapping(target = "orderInfo", source = "authorizationInfo")
    @Mapping(target = "authorizeCode", source = "authorizationInfo.authorizeCode")
    AuthRequestOrderDO toAuthReqOrder(ApplyAuthorizationRequest request);

    default AuthRequestOrderDO toAuthReqOrder(ApplyAuthorizationRequest request, AuthorizationRequestContext authRequestContext) {
        AuthRequestOrderDO authRequestOrder = toAuthReqOrder(request);
        if (authRequestOrder == null) {
            return null;
        }
        ApplyAuthorizationRequest.AuthorizationInfo authorizationInfo = request.getAuthorizationInfo();
        // 授权申请，忽略金额值的校验
        if (authRequestOrder.getPaymentMethod() != null) {
            authRequestOrder.getPaymentMethod().setAmount(authorizationInfo.getAmount());
            authRequestOrder.getPaymentMethod().setIsIgnoreAmountValue(true);
        }
        // 上下文不存在，则返回
        if (Objects.isNull(authRequestContext) || Objects.isNull(authRequestContext.getOrderInfo())) {
            return authRequestOrder;
        }
        OrderInfoDO orderInfoDO = authRequestOrder.getOrderInfo();
        if (Objects.isNull(orderInfoDO)) {
            orderInfoDO = new OrderInfoDO();
            authRequestOrder.setOrderInfo(orderInfoDO);
        }
        // 当上下文中存在数据的情况下，使用上下文中的数据覆盖原数据（上游有多个调用方，目前仅收银台使用上下文，后续会舍弃上下文）
        AuthorizationRequestContext.OrderInfo contextOrderInfo = authRequestContext.getOrderInfo();
        orderInfoDO.setCustomId(StringUtils.defaultIfBlank(contextOrderInfo.getCustomId(), orderInfoDO.getCustomId()));
        orderInfoDO.setShopId(StringUtils.defaultIfBlank(contextOrderInfo.getShopId(), orderInfoDO.getShopId()));
        orderInfoDO.setMcc(StringUtils.defaultIfBlank(contextOrderInfo.getMcc(), orderInfoDO.getMcc()));
        orderInfoDO.setSubMcc(StringUtils.defaultIfBlank(contextOrderInfo.getSubMcc(), orderInfoDO.getSubMcc()));
        orderInfoDO.setUserMemberId(StringUtils.defaultIfBlank(contextOrderInfo.getUserMemberId(), orderInfoDO.getUserMemberId()));
        orderInfoDO.setPurchaseInfo(StringUtils.defaultIfBlank(contextOrderInfo.getPurchaseInfo(), orderInfoDO.getPurchaseInfo()));
        orderInfoDO.setUserLanguage(StringUtils.defaultIfBlank(contextOrderInfo.getUserLanguage(), orderInfoDO.getUserLanguage()));

        return authRequestOrder;
    }

    /**
     * 授权响应转换
     *
     * @param responseOrder
     * @return
     */
    @Mapping(target = "requestNo", source = "bizOrderNo")
    @Mapping(target = "status", source = "authStatus")
    ApplyAuthorizationResponse toAuthResp(AuthResponseOrderDO responseOrder);

    /**
     * 撤销授权请求转换
     *
     * @param request
     * @return
     */
    @Mapping(target = "bizOrderNo", source = "requestNo")
    @Mapping(target = "paymentType", expression = "java(PaymentTypeEnum.PAYMENT)")
    @Mapping(target = "requestType", expression = "java(RequestTypeEnum.AUTHORIZATION_REVOKE.getCode())")
    RevokeAuthRequestOrderDO toRevokeAuthReqOrder(RevokeAuthorizationRequest request);

    /**
     * 撤销授权响应转换
     *
     * @param responseOrder
     * @return
     */
    @Mapping(target = "requestNo", source = "bizOrderNo")
    RevokeAuthorizationResponse toRevokeAuthResp(RevokeAuthResponseOrderDO responseOrder);

    /**
     * 授权查询请求转换
     *
     * @param request
     * @return
     */
    InquiryAuthRequestDO toAuthInquiryReqOrder(InquiryAuthorizationRequest request);

    /**
     * 授权查询响应转换
     *
     * @param responseOrder
     * @return
     */
    @Mapping(target = "requestNo", source = "bizOrderNo")
    @Mapping(target = "status", source = "authStatus")
    InquiryAuthorizationResponse toAuthInquiryResp(InquiryAuthResponseDO responseOrder);

    /**
     * 补偿查询请求转换
     *
     * @param request
     * @return
     */
    @Mapping(target = "paymentTypeEnum", expression = "java(PaymentTypeEnum.getByValue(request.getPaymentType()))")
    CompensateInquiryRequestDO toCompensateInquiryReq(CompensateInquiryRequest request);

    /**
     * 补偿查询请求转换
     *
     * @param request
     * @return
     */
    List<CompensateNotifyRequestDO> toCompensateNotifyReq(List<CompensateNotifyRequest> request);

    /**
     * 请求转换
     * @param request
     * @return
     */
    List<CompensateThreeDSNotifyRequestDO> toCompensateThreeDSNotifyRequest(List<CompensateThreeDSNotifyRequest> request);


    /**
     * 转账请求转换
     *
     * @param request
     * @return
     */
    default PayoutRequestOrderDO toTransferReq(TransferRequest request) {
        PayoutRequestOrderDO requestDO = new PayoutRequestOrderDO();
        requestDO.setBizSource(request.getBizSource());
        requestDO.setPaymentType(PaymentTypeEnum.TRANSFER);
        requestDO.setActionType(ActionTypeEnum.APPLY);
        requestDO.setBizOrderNo(request.getRequestNo());
        requestDO.setSourceBizOrderNo(request.getRequestNo());
        requestDO.setRequestType(RequestTypeEnum.PAYOUT.getCode());

        requestDO.setExtParams(JsonUtils.toBean(Map.class, JsonUtils.toString(request)));
        // 订单信息
        OrderInfoDO orderInfo = new OrderInfoDO();
        orderInfo.setMerchantNo(request.getMerchantNo());
        orderInfo.setRemark(request.getRemark());
        orderInfo.setPurchaseInfo(request.getRemark());
        requestDO.setOrderInfo(orderInfo);
        // 支付方式
        PaymentMethodDO paymentMethod = new PaymentMethodDO();
        requestDO.setPaymentMethod(paymentMethod);
        paymentMethod.setPaymentType(PaymentTypeEnum.TRANSFER.getValue());
        paymentMethod.setPaymentMethodType(PaymentMethodTypeEnum.VIRTUAL_ACCOUNT.getCode());
        paymentMethod.setCountry(request.getCountry());
        paymentMethod.setCustomerType(String.valueOf(CustomerTypeEnum.TO_BUSINESS.getCode()));
        paymentMethod.setAmount(new Money(request.getAmount(), request.getCurrency()));
        // 扩展配置
        ExtendPropertyDO extendProperty = new ExtendPropertyDO();
        extendProperty.setKey(FactorKeyEnum.PAY_METHOD_ID.name());
        extendProperty.setLogicKey(LogicKeyEnum.STRONG_INCLUDE.name());
        extendProperty.setValue(request.getPayMethodId());
        List<ExtendPropertyDO> extendProperties = new ArrayList<>();
        extendProperties.add(extendProperty);
        paymentMethod.setExtendProperties(extendProperties);
        return requestDO;
    }

    /**
     * 转账响应转换
     *
     * @param responseOrder
     * @return
     */
    @Mapping(target = "requestNo", source = "bizOrderNo")
    @Mapping(target = "channelOrderNo", source = "channelPayCommitNo")
    @Mapping(target = "status", expression = "java(OrderStatus4OutEnum.mappingStatus4Out(responseOrder.getStatus().name()))")
    TransferResponse toTransferResp(PayoutResponseOrderDO responseOrder);

    /**
     * 查询转账请求转换
     *
     * @param request
     * @return
     */
    @Mapping(target = "paymentType", expression = "java(PaymentTypeEnum.TRANSFER)")
    @Mapping(target = "bizOrderNo", source = "requestNo")
    InquiryOrderRequestDO toInquiryTransferReq(InquiryTransferRequest request);

    /**
     * 查询转账响应转换
     *
     * @param responseOrder
     * @return
     */
    @Mapping(target = "requestNo", source = "bizOrderNo")
    @Mapping(target = "channelOrderNo", source = "channelPayCommitNo")
    @Mapping(target = "status", expression = "java(OrderStatus4OutEnum.mappingStatus4Out(responseOrder.getStatus().name()))")
    @Mapping(target = "respCode", source = "mappingCode")
    @Mapping(target = "respMsg", source = "mappingMsg")
    InquiryTransferResponse toInquiryTransferResp(InquiryOrderResponseDO responseOrder);

    /**
     * 查询渠道提交单请求转换
     *
     * @param request
     * @return
     */
    InquiryCommitOrderRequestDO toInquiryCommitOrderReq(InquiryCommitOrderRequest request);

    /**
     * 查询渠道提交单响应转换
     *
     * @param responseOrder
     * @return
     */
    @Mapping(target = "status", expression = "java(OrderStatus4OutEnum.mappingStatus4Out(responseOrder.getOrderStatus().name()))")
    InquiryCommitOrderResponse toInquiryCommitOrderResp(InquiryCommitOrderResponseDO responseOrder);

    /**
     * 即期报价查询请求转换
     *
     * @param request
     * @return
     */
    @Mapping(target = "bizOrderNo", source = "requestNo")
    @Mapping(target = "mainRequestType", expression = "java(MainRequestTypeEnum.TRANSACTION.getValue())")
    @Mapping(target = "requestType", expression = "java(RequestTypeEnum.QUERY_SPOT_RATE.getCode())")
    @Mapping(target = "paymentType", expression = "java(PaymentTypeEnum.RATE)")
    InquirySpotRateRequestDO toInquirySpotRateReq(InquirySpotRateRequest request);

    /**
     * 即期报价查询响应转换
     *
     * @param responseDO
     * @return
     */
    @Mapping(target = "requestNo", source = "bizOrderNo")
    InquirySpotRateResponse toInquirySpotRateResp(InquirySpotRateResponseDO responseDO);

    /**
     * 更新订单请求转换
     *
     * @param request
     * @return
     */
    List<UpdateCommitOrderRequestDO> toUpdateCommitOrderReq(List<UpdateCommitOrderRequest> request);

    /**
     * 更新订单请求转换
     *
     * @param request
     * @return
     */
    List<UpdateRequestOrderRequestDO> toUpdateRequestOrderReq(List<UpdateRequestOrderRequest> request);

    /**
     * 渠道路由请求转换
     *
     * @param request
     * @return
     */
    @Mapping(target = "paymentType", expression = "java(PaymentTypeEnum.getByValue(request.getPaymentMethod().getPaymentType()))")
    @Mapping(target = "actionType", expression = "java(ActionTypeEnum.PRE_FILTER)")
    RequestOrderDO toChannelRoutingReq(ChannelRoutingRequest request);

    /**
     * 远期报价查询请求转换
     *
     * @param request
     * @return
     */
    @Mapping(target = "bizOrderNo", source = "requestNo")
    @Mapping(target = "mainRequestType", expression = "java(MainRequestTypeEnum.TRANSACTION.getValue())")
    @Mapping(target = "requestType", expression = "java(RequestTypeEnum.QUERY_FORWARD_RATE.getCode())")
    @Mapping(target = "paymentType", expression = "java(PaymentTypeEnum.RATE.getValue())")
    @Mapping(target = "data", expression = "java(JsonUtils.toBean(Map.class, JsonUtils.toString(request)))")
    CommonGateRequestDO toInquiryForwardRateReq(InquiryForwardRateRequest request);

    /**
     * 远期报价查询响应转换
     *
     * @param responseDO
     * @return
     */
    default InquiryForwardRateResponse toInquiryForwardRateResp(CommonGateResponseDO responseDO) {
        if (responseDO == null) {
            return null;
        }
        InquiryForwardRateResponse response = new InquiryForwardRateResponse();
        // 如果业务数据存在，则先转换业务数据
        if (responseDO.getData() != null) {
            response = JsonUtils.toBean(InquiryForwardRateResponse.class, JsonUtils.toString(responseDO.getData()));
        }
        response.setRequestNo(responseDO.getBizOrderNo());
        response.setChannelCode(responseDO.getChannelCode());
        response.setChannelPayCommitNo(responseDO.getChannelPayCommitNo());
        response.setMappingCode(responseDO.getMappingCode());
        response.setMappingMsg(responseDO.getMappingMsg());
        response.setStatus(responseDO.getStatus());
        return response;
    }

    /**
     * 通用网关转发请求转换
     *
     * @param request
     * @return
     */
    @Mapping(target = "bizOrderNo", source = "requestNo")
    CommonGateRequestDO toCommonGateReq(CommonGateRequest request);

    /**
     * 通用网关转发响应转换
     *
     * @param responseDO
     * @return
     */
    @Mapping(target = "requestNo", source = "bizOrderNo")
    CommonGateResponse toCommonGateResp(CommonGateResponseDO responseDO);

    /**
     * HA交易请求转换
     *
     * @param request
     * @return
     */
    @Mapping(target = "bizOrderNo", source = "transactionOrderNo")
    @Mapping(target = "requestType", expression = "java(RequestTypeEnum.HEDGE_ADVICE.getCode())")
    @Mapping(target = "mainRequestType", expression = "java(MainRequestTypeEnum.TRANSACTION.getValue())")
    @Mapping(target = "paymentType", expression = "java(PaymentTypeEnum.RATE.getValue())")
    @Mapping(target = "data", expression = "java(JsonUtils.toBean(Map.class, JsonUtils.toString(request)))")
    CommonGateRequestDO toHedgeAdviceReq(HedgeAdviceRequest request);

    /**
     * HA交易响应转换
     *
     * @param responseDO
     * @return
     */
    default HedgeAdviceResponse toHedgeAdviceResp(CommonGateResponseDO responseDO) {
        if (responseDO == null) {
            return null;
        }
        HedgeAdviceResponse response = new HedgeAdviceResponse();
        // 如果业务数据存在，则先转换业务数据
        if (responseDO.getData() != null) {
            response = JsonUtils.toBean(HedgeAdviceResponse.class, JsonUtils.toString(responseDO.getData()));
        }
        response.setChannelCode(responseDO.getChannelCode());
        response.setTransactionOrderNo(responseDO.getBizOrderNo());
        response.setChannelPayCommitNo(responseDO.getChannelPayCommitNo());
        response.setMappingCode(responseDO.getMappingCode());
        response.setMappingMsg(responseDO.getMappingMsg());
        response.setStatus(responseDO.getStatus());
        return response;
    }

    /**
     * TA交易请求转换
     *
     * @param request
     * @return
     */
    @Mapping(target = "bizOrderNo", source = "transactionOrderNo")
    @Mapping(target = "requestType", expression = "java(RequestTypeEnum.TRADE_ADVICE.getCode())")
    @Mapping(target = "mainRequestType", expression = "java(MainRequestTypeEnum.TRANSACTION.getValue())")
    @Mapping(target = "paymentType", expression = "java(PaymentTypeEnum.RATE.getValue())")
    @Mapping(target = "data", expression = "java(JsonUtils.toBean(Map.class, JsonUtils.toString(request)))")
    CommonGateRequestDO toTradeAdviceReq(TradeAdviceRequest request);

    /**
     * TA交易响应转换
     *
     * @param responseDO
     * @return
     */
    default TradeAdviceResponse toTradeAdviceResp(CommonGateResponseDO responseDO) {
        if (responseDO == null) {
            return null;
        }
        TradeAdviceResponse response = new TradeAdviceResponse();
        // 如果业务数据存在，则先转换业务数据
        if (responseDO.getData() != null) {
            response = JsonUtils.toBean(TradeAdviceResponse.class, JsonUtils.toString(responseDO.getData()));
        }
        response.setTransactionOrderNo(responseDO.getBizOrderNo());
        response.setChannelPayCommitNo(responseDO.getChannelPayCommitNo());
        response.setChannelCode(responseDO.getChannelCode());
        response.setMappingCode(responseDO.getMappingCode());
        response.setMappingMsg(responseDO.getMappingMsg());
        response.setStatus(responseDO.getStatus());
        return response;
    }

    /**
     * 交易补偿请求转换
     *
     * @param request
     * @return
     */
    @Mapping(target = "bizOrderNo", source = "requestNo")
    OrderCompensateRequestDO toOrderCompensateReq(com.payermax.fin.exchange.service.request.OrderCompensateRequest request);

    /**
     * 关单通知请求转换
     *
     * @param request
     * @return
     */
    CloseOrderRequestDO toCloseOrderReq(CloseOrderRequest request);

    /**
     * 外汇交易请求转换
     *
     * @param request
     * @return
     */
    @Mapping(target = "bizOrderNo", source = "transactionOrderNo")
    @Mapping(target = "requestType", expression = "java(RequestTypeEnum.FOREX_TRADE.getCode())")
    @Mapping(target = "mainRequestType", expression = "java(MainRequestTypeEnum.TRANSACTION.getValue())")
    @Mapping(target = "paymentType", expression = "java(PaymentTypeEnum.RATE.getValue())")
    @Mapping(target = "data", expression = "java(JsonUtils.toBean(Map.class, JsonUtils.toString(request)))")
    ForexTradeRequestDO toForexTradeReq(ForexTradeRequest request);

    /**
     * 外汇交易响应转换
     *
     * @param responseDO
     * @return
     */
    default ForexTradeResponse toForexTradeResp(CommonGateResponseDO responseDO) {
        if (responseDO == null) {
            return null;
        }
        ForexTradeResponse response = new ForexTradeResponse();
        // 如果业务数据存在，则先转换业务数据
        if (responseDO.getData() != null) {
            response = JSONObject.parseObject(JSONObject.toJSONString(responseDO.getData()), ForexTradeResponse.class);
        }
        response.setChannelCode(responseDO.getChannelCode());
        response.setTransactionOrderNo(responseDO.getBizOrderNo());
        response.setChannelPayCommitNo(responseDO.getChannelPayCommitNo());
        response.setMappingCode(responseDO.getMappingCode());
        response.setMappingMsg(responseDO.getMappingMsg());
        response.setStatus(responseDO.getStatus());
        return response;
    }

    /**
     * 外汇交易查询请求转换
     *
     * @param request
     * @return
     */
    @Mapping(target = "bizOrderNo", source = "transactionOrderNo")
    @Mapping(target = "requestType", expression = "java(RequestTypeEnum.QUERY_FOREX_TRADE.getCode())")
    @Mapping(target = "mainRequestType", expression = "java(MainRequestTypeEnum.TRANSACTION.getValue())")
    @Mapping(target = "paymentType", expression = "java(PaymentTypeEnum.RATE.getValue())")
    InquiryForexTradeRequestDO toInquiryForexTradeReq(InquiryForexTradeRequest request);

    /**
     * 外汇交易查询响应转换
     *
     * @param responseDO
     * @return
     */
    default ForexTradeResponse toInquiryForexTradeResp(InquiryForexTradeResponseDO responseDO) {
        if (responseDO == null) {
            return null;
        }
        ForexTradeResponse response = new ForexTradeResponse();
        // 如果业务数据存在，则先转换业务数据
        if (responseDO.getData() != null) {
            response = JSONObject.parseObject(JSONObject.toJSONString(responseDO.getData()), ForexTradeResponse.class);
        }
        response.setChannelCode(responseDO.getChannelCode());
        response.setTransactionOrderNo(responseDO.getBizOrderNo());
        response.setChannelPayCommitNo(responseDO.getChannelPayCommitNo());
        response.setMappingCode(responseDO.getMappingCode());
        response.setMappingMsg(responseDO.getMappingMsg());
        response.setStatus(responseDO.getStatus());
        return response;
    }

    /**
     * 入款差错请求参数转换
     *
     * @param request
     * @return
     */
    default PayCorrectionOrderRequestDO toPayCorrectionReqOrder(PayCorrectionOrderRequest request) {
        PayCorrectionOrderRequestDO payCorrectionOrderRequest = new PayCorrectionOrderRequestDO();
        payCorrectionOrderRequest.setBizOrderNo(request.getPayOrderNo());
        payCorrectionOrderRequest.setSourceBizOrderNo(request.getPayRequestNo());
        payCorrectionOrderRequest.setChannelPayRequestNo(request.getPayOrderNo());
        payCorrectionOrderRequest.setPayOrderNoOriginal(request.getPayOrderNoOriginal());
        payCorrectionOrderRequest.setPayRequestNoOriginal(request.getPayRequestNoOriginal());
        payCorrectionOrderRequest.setPaymentType(PaymentTypeEnum.PAYMENT);
        payCorrectionOrderRequest.setCorrectionType(request.getCorrectionType().getCode());
        return payCorrectionOrderRequest;
    }

    /**
     * 入款差错响应参数转换
     *
     * @param responseOrder
     * @return
     */
    @Mapping(target = "payOrderNo", source = "bizOrderNo")
    @Mapping(target = "status", expression = "java(OrderStatus4OutEnum.mappingStatus4Out(responseOrder.getStatus().name()))")
    PayResponse toPayCorrectionResp(PayCorrectionOrderResponseDO responseOrder);

    /**
     * 预筛选资金账户请求转换*
     * *
     *
     * @param request
     * @return
     */
    PreFilterAccountRequestDO toPreFilterAccountRequest(PreFilterAccountRequest request);

    /**
     * 预筛选资金账户响应转换*
     * *
     *
     * @param responseDOList
     * @return
     */
    List<PreFilterAccountResponse.PaymentMethod> toPreFilterAccountResponse(List<PreFilterAccountResponseDO> responseDOList);

    /**
     * 转换申请DDC请求*
     * *
     *
     * @param request
     * @return
     */
    @Mapping(target = "bizOrderNo", source = "requestNo")
    @Mapping(target = "requestType", expression = "java(RequestTypeEnum.DDC_APPLY.getCode())")
    @Mapping(target = "mainRequestType", expression = "java(MainRequestTypeEnum.CONTROL.getValue())")
    @Mapping(target = "paymentType", expression = "java(PaymentTypeEnum.PAYMENT.getValue())")
    @Mapping(target = "paymentMethodType", source = "paymentMethodType", defaultExpression = "java(PaymentMethodTypeEnum.CARD_PAY.getCode())")
    @Mapping(target = "data", expression = "java(JsonUtils.toBean(Map.class, JsonUtils.toString(request)))")
    MidRouteCommonGateRequestDO toApplyDDCRequest(ApplyDDCRequest request);

    /**
     * 转换申请DDC响应*
     * *
     *
     * @param responseDO
     * @return
     */
    default ApplyDDCResponse toApplyDDCResponse(CommonGateResponseDO responseDO) {
        if (responseDO == null) {
            return null;
        }
        ApplyDDCResponse applyDDCResponse = new ApplyDDCResponse();
        // 如果业务数据存在，则先转换业务数据
        if (responseDO.getData() != null) {
            applyDDCResponse = JSONObject.parseObject(JSONObject.toJSONString(responseDO.getData()), ApplyDDCResponse.class);
        }
        applyDDCResponse.setChannelCode(responseDO.getChannelCode());

        return applyDDCResponse;
    }

    /**
     * 转换更新限流请求
     *
     * @param request
     * @return
     */
    UpdateChannelLimitRequestDO toUpdateChannelLimitRequest(UpdateChannelLimitRequest request);

    /**
     * 转换更新限流请求
     *
     * @param request
     * @return
     */
    AccelerateCompensationRequestDO toAccelerateCompensationRequest(AccelerateCompensationRequest request);

    /**
     * 查询二级商户信息请求转换
     *
     * @param request
     * @return
     */
    InquiryNewTargetMerchantRequestDO toInquiryTargetMerchantReq(InquiryNewTargetMerchantRequest request);

    /**
     * 查询二级商户信息响应转换
     *
     * @param responseDO
     * @return
     */
    InquiryNewTargetMerchantResponse toInquiryTargetMerchantResp(InquiryNewTargetMerchantResponseDO responseDO);

    /**
     * 查询渠道信息响应转换
     *
     * @param response
     * @return
     */
    FundingChannelInfoResponse toFundingChannelInfoResponse(FundingChannelInfoResponseDO response);

    /**
     * 转换补偿订单状态请求
     *
     * @param request
     * @return
     */
    @Mapping(target = "currentStatus", expression = "java(OrderStatusEnum.valueOf(request.getCurrentStatus()))")
    @Mapping(target = "targetStatus", expression = "java(OrderStatusEnum.valueOf(request.getTargetStatus()))")
    @Mapping(target = "paymentTypeEnum", expression = "java(PaymentTypeEnum.valueOf(request.getPaymentType()))")
    CompensateStatusRequestDO toCompensationStatusRequestDO(CompensateStatusRequest request);

    /**
     * 转换收银产品筛选请求
     *
     * @param request
     * @return
     */
    @Mapping(target = "paymentMethods", source = "cashierProducts")
    PreFilterRequest toPreFilterRequest(ProductPreFilterRequest request);

    /**
     * 卡组转换
     *
     * @param cashierProduct
     * @param paymentMethod
     */
    @AfterMapping
    default void cashierProductCardOrgToPaymentMethodExtProperty(ProductPreFilterRequest.CashierProduct cashierProduct, @MappingTarget PreFilterRequest.PaymentMethod paymentMethod) {
        if (cashierProduct == null || StringUtils.isBlank(cashierProduct.getCardOrg())) {
            return;
        }
        List<PreFilterRequest.PaymentMethod.ExtendProperty> extendPropertyList = paymentMethod.getExtendProperties();
        if (CollectionUtils.isEmpty(extendPropertyList)) {
            extendPropertyList = new ArrayList<>();
            paymentMethod.setExtendProperties(extendPropertyList);
        }
        PreFilterRequest.PaymentMethod.ExtendProperty extendProperty = new PreFilterRequest.PaymentMethod.ExtendProperty();
        extendProperty.setKey(FactorKeyEnum.SUPPORT_CARD_ORG.name());
        extendProperty.setValue(cashierProduct.getCardOrg());
        extendProperty.setLogicKey(LogicKeyEnum.INCLUDE.getCode());
        extendPropertyList.add(extendProperty);
    }

    /**
     * 转换收银产品筛选响应
     *
     * @param response
     * @return
     */
    ProductPreFilterResponse toProductPreFilterResponse(PreFilterResponse response);

    /**
     * 卡组转换
     *
     * @param source
     * @param paymentMethod
     */
    @AfterMapping
    default void filterResponseToProductFilterResponse(PreFilterResponse.PaymentMethod source, @MappingTarget ProductPreFilterResponse.PaymentMethod paymentMethod) {
        if (source == null || paymentMethod == null) {
            return;
        }
        paymentMethod.setPaymentMethodType(source.getNewPaymentMethodType());
        // 重新设置Amount对象，解决json $ref问题
        if (source.getAmount() != null) {
            paymentMethod.setAmount(new Money(source.getAmount().getValue(), source.getAmount().getCurrency()));
        }
        List<PreFilterResponse.PaymentMethod.ExtendProperty> extendProperties = source.getExtendProperties();
        if (extendProperties == null || extendProperties.isEmpty()) {
            return;
        }
        PreFilterResponse.PaymentMethod.ExtendProperty cardOrgExtProperty = extendProperties.stream()
                .filter(t -> FactorKeyEnum.SUPPORT_CARD_ORG.name().equals(t.getKey()))
                .findFirst().orElse(null);//NO_CHECK
        if (cardOrgExtProperty == null) {
            return;
        }
        paymentMethod.setCardOrg(cardOrgExtProperty.getValue());
    }

    /**
     * 转换收银产品可用性查询请求
     *
     * @param request
     * @return
     */
    @Mapping(target = "paymentMethods", source = "cashierProducts")
    PreFilterRequest availabilityFilterToPreFilterRequest(AvailabilityFilterRequest request);

    /**
     * 转换报备查询请求
     *
     * @param request
     * @return
     */
    @Mappings({
            @Mapping(target = "paymentMethods", source = "cashierProducts"),
            @Mapping(target = "filterBizType", expression = "java(StringUtils.isBlank(request.getFilterBizType()) ? FilterBizTypeEnum.MERCHANT_REPORT.name() : request.getFilterBizType())")
    })
    PreFilterRequest merchantReportFilterToPreFilterRequest(MerchantReportFilterRequest request);

    /**
     * 卡组转换
     *
     * @param cashierProduct
     * @param paymentMethod
     */
    @AfterMapping
    default void availabilityFilterCardOrgToPaymentMethodExtProperty(AvailabilityFilterRequest.CashierProduct cashierProduct, @MappingTarget PreFilterRequest.PaymentMethod paymentMethod) {
        if (cashierProduct == null) {
            return;
        }
        paymentMethod.setPaymentMethodNo(cashierProduct.getPaymentMethodId());
        if (StringUtils.isBlank(cashierProduct.getCardOrg())) {
            return;
        }
        List<PreFilterRequest.PaymentMethod.ExtendProperty> extendPropertyList = paymentMethod.getExtendProperties();
        if (CollectionUtils.isEmpty(extendPropertyList)) {
            extendPropertyList = new ArrayList<>();
            paymentMethod.setExtendProperties(extendPropertyList);
        }
        PreFilterRequest.PaymentMethod.ExtendProperty extendProperty = new PreFilterRequest.PaymentMethod.ExtendProperty();
        extendProperty.setKey(FactorKeyEnum.SUPPORT_CARD_ORG.name());
        extendProperty.setValue(cashierProduct.getCardOrg());
        extendProperty.setLogicKey(LogicKeyEnum.INCLUDE.getCode());
        extendPropertyList.add(extendProperty);
    }

    @AfterMapping
    default void merchantReportFilterCardOrgToPaymentMethodExtProperty(MerchantReportFilterRequest.CashierProduct cashierProduct, @MappingTarget PreFilterRequest.PaymentMethod paymentMethod) {
        if (cashierProduct == null || StringUtils.isBlank(cashierProduct.getCardOrg())) {
            return;
        }
        List<PreFilterRequest.PaymentMethod.ExtendProperty> extendPropertyList = paymentMethod.getExtendProperties();
        if (CollectionUtils.isEmpty(extendPropertyList)) {
            extendPropertyList = new ArrayList<>();
            paymentMethod.setExtendProperties(extendPropertyList);
        }
        PreFilterRequest.PaymentMethod.ExtendProperty extendProperty = new PreFilterRequest.PaymentMethod.ExtendProperty();
        extendProperty.setKey(FactorKeyEnum.SUPPORT_CARD_ORG.name());
        extendProperty.setValue(cashierProduct.getCardOrg());
        extendProperty.setLogicKey(LogicKeyEnum.INCLUDE.getCode());
        extendPropertyList.add(extendProperty);
    }

    /**
     * 转换收银产品可用性查询请求
     *
     * @param request
     * @return
     */
    @Mapping(target = "orderInfo.customId", source = "customId")
    @Mapping(target = "orderInfo.merchantNo", source = "merchantNo")
    @Mapping(target = "orderInfo.merchantType", source = "merchantType")
    @Mapping(target = "orderInfo.mcc", source = "mcc")
    @Mapping(target = "orderInfo.subMcc", source = "subMcc")
    @Mapping(target = "filterBizType", expression = "java(FilterBizTypeEnum.AVAILABILITY.name())")
    PreFilterRequest filterChannelToPreFilterRequest(FilterChannelRequest request);

    /**
     * 测试对象转可用性查询请求
     *
     * @param request
     * @return
     */
    PreFilterRequest preFilterTestToPreFilterRequest(PreFilterTestRequest request);

    /**
     * 卡组转换
     *
     * @param sourcePaymentMethod
     * @param targetPaymentMethod
     */
    @AfterMapping
    default void filterChannelCardOrgToPaymentMethodExtProperty(FilterChannelRequest.PaymentMethod sourcePaymentMethod, @MappingTarget PreFilterRequest.PaymentMethod targetPaymentMethod) {
        if (sourcePaymentMethod == null) {
            return;
        }
        if (StringUtils.isNotBlank(sourcePaymentMethod.getCurrency())) {
            targetPaymentMethod.setAmount(new Money(BigDecimal.ZERO, sourcePaymentMethod.getCurrency()));
            targetPaymentMethod.setIsIgnoreAmountValue(true);
        }

        String targetOrg = sourcePaymentMethod.getTargetOrg();
        boolean isCardOrg = PropertiesCenter.getCardOrgList().contains(targetOrg);
        String cardOrg = sourcePaymentMethod.getCardOrg();
        // 如果入参卡组为空，并且目标机构又属于卡组，则使用目标机构作为卡组
        if (StringUtils.isBlank(cardOrg) && isCardOrg) {
            cardOrg = targetOrg;
        }
        if (StringUtils.isBlank(cardOrg)) {
            return;
        }
        List<PreFilterRequest.PaymentMethod.ExtendProperty> extendPropertyList = targetPaymentMethod.getExtendProperties();
        if (CollectionUtils.isEmpty(extendPropertyList)) {
            extendPropertyList = new ArrayList<>();
            targetPaymentMethod.setExtendProperties(extendPropertyList);
        }
        PreFilterRequest.PaymentMethod.ExtendProperty extendProperty = new PreFilterRequest.PaymentMethod.ExtendProperty();
        extendProperty.setKey(FactorKeyEnum.SUPPORT_CARD_ORG.name());
        extendProperty.setValue(cardOrg);
        extendProperty.setLogicKey(LogicKeyEnum.INCLUDE.getCode());
        extendPropertyList.add(extendProperty);
    }

    /**
     * 申请支付token请求参数转换
     *
     * @param request
     * @return
     */
    @Mapping(target = "paymentType", expression = "java(PaymentTypeEnum.PAYMENT)")
    @Mapping(target = "actionType", expression = "java(ActionTypeEnum.APPLY)")
    @Mapping(target = "requestType", expression = "java(RequestTypeEnum.APPLY_PAYMENT_TOKEN.getCode())")
    @Mapping(target = "mainRequestType", expression = "java(MainRequestTypeEnum.CONTROL.getValue())")
    @Mapping(target = "sourceBizOrderNo", source = "bizOrderNo")
    ApplyPaymentTokenRequestDO applyPaymentTokenToRequestOrder(ApplyPaymentTokenRequest request);

    /**
     * 后置转换
     *
     * @param request
     * @param requestOrderDO
     */
    @AfterMapping
    default void afterApplyPaymentTokenToApplyPaymentTokenRequestDO(ApplyPaymentTokenRequest request, @MappingTarget ApplyPaymentTokenRequestDO requestOrderDO) {
        if (request == null) {
            return;
        }
        if (StringUtils.isNotBlank(request.getRecurringType())) {
            ScenarioInfoDO scenarioInfo = new ScenarioInfoDO();
            scenarioInfo.setRecurringType(request.getRecurringType());
            scenarioInfo.setMerchantInitiated(false);
            requestOrderDO.setScenarioInfo(scenarioInfo);
        }
        ApplyPaymentTokenRequest.PaymentMethod sourcePayment = request.getPaymentMethod();
        PaymentMethodDO paymentMethod = requestOrderDO.getPaymentMethod();
        // 由于申请支付token时没金额，故设置默认金额
        paymentMethod.setIsIgnoreAmountValue(true);
        paymentMethod.setAmount(new Money(BigDecimal.ZERO, sourcePayment.getCurrency()));

        // 设置扩展参数
        Map<String, Object> extParams = requestOrderDO.getExtParams();
        if (extParams == null) {
            requestOrderDO.setExtParams(new HashMap<>());
        }
        requestOrderDO.getExtParams().put("frontRedirectUrl", request.getFrontRedirectUrl());

        if (StringUtils.isBlank(sourcePayment.getCardOrg())) {
            return;
        }
        List<ExtendPropertyDO> extendPropertyList = paymentMethod.getExtendProperties();
        if (CollectionUtils.isEmpty(extendPropertyList)) {
            extendPropertyList = new ArrayList<>();
            paymentMethod.setExtendProperties(extendPropertyList);
        }
        ExtendPropertyDO extendProperty = new ExtendPropertyDO();
        extendProperty.setKey(FactorKeyEnum.SUPPORT_CARD_ORG.name());
        extendProperty.setValue(sourcePayment.getCardOrg());
        extendProperty.setLogicKey(LogicKeyEnum.INCLUDE.getCode());
        extendPropertyList.add(extendProperty);
        // 指定筛选 INITIATED_MODE = CIT_FIRST_STORED
        if (StringUtils.isNotBlank(request.getRecurringType())) {
            ExtendPropertyUtils.addExtendProperties(paymentMethod, FactorKeyEnum.INITIATED_MODE.name(), ShareConstants.CIT_FIRST_STORED, LogicKeyEnum.STRONG_INCLUDE.getCode());
        }
    }

    /**
     * 申请支付token响应参数转换
     *
     * @param responseOrder
     * @return
     */
    @Mapping(target = "status", expression = "java(OrderStatus4OutEnum.mappingStatus4Out(responseOrder.getStatus().name()))")
    default ApplyPaymentTokenResponse toApplyPaymentTokenResponse(ResponseOrderDO responseOrder) {
        if (responseOrder == null) {
            return null;
        }
        ApplyPaymentTokenResponse response = new ApplyPaymentTokenResponse();
        // 如果业务数据存在，则先转换业务数据
        if (responseOrder.getMappingData() != null) {
            response = JSONObject.parseObject(JSONObject.toJSONString(responseOrder.getMappingData()), ApplyPaymentTokenResponse.class);
        }
        response.setBizOrderNo(responseOrder.getBizOrderNo());
        response.setMappingCode(responseOrder.getMappingCode());
        response.setMappingMsg(responseOrder.getMappingMsg());
        response.setChannelRespCode(responseOrder.getChannelRespCode());
        response.setChannelRespMsg(responseOrder.getChannelRespMsg());
        response.setStatus(OrderStatus4OutEnum.mappingStatus4Out(responseOrder.getStatus().name()));
        if (Objects.nonNull(responseOrder.getMappingData())) {
            response.setExtendResult(responseOrder.getMappingData());
        }
        return response;
    }

    /**
     * 转换收银产品筛选响应
     *
     * @param response
     * @return
     */
    default AvailabilityFilterResponse toAvailabilityFilterResponse(PreFilterResponse response) {
        if (response == null) {
            return new AvailabilityFilterResponse();
        }
        AvailabilityFilterResponse targetResponse = new AvailabilityFilterResponse();
        List<PreFilterResponse.PaymentMethod> paymentMethods = response.getPaymentMethods();
        if (CollectionUtils.isEmpty(paymentMethods)) {
            targetResponse.setPaymentMethods(new ArrayList<>());
            return targetResponse;
        }
        Map<String, AvailabilityFilterResponse.PaymentMethod> targetPaymentMethodMap = new HashMap<>();
        paymentMethods.forEach(paymentMethod -> {
            String currency = paymentMethod.getAmount() == null ? "" : paymentMethod.getAmount().getCurrency();
            String mapKey = String.format("%s:%s", paymentMethod.getCashierProductNo(), currency);
            // 设置目标支付方式
            targetPaymentMethodMap.computeIfAbsent(mapKey, t -> {
                AvailabilityFilterResponse.PaymentMethod targetPaymentMethod = new AvailabilityFilterResponse.PaymentMethod();
                targetPaymentMethod.setCashierProductNo(paymentMethod.getCashierProductNo());
                targetPaymentMethod.setCurrency(currency);
                return targetPaymentMethod;
            });
        });
        targetResponse.setPaymentMethods(new ArrayList<>(targetPaymentMethodMap.values()));
        return targetResponse;
    }

    Set<NewAvailabilityFilterResponse.Ability.ExtendProperty> toAbilityExtendPropertySet(List<PreFilterResponse.PaymentMethod.ExtendProperty> extendProperties);

    /**
     * 转换报备筛选响应
     *
     * @param response
     * @return
     */
    default MerchantReportFilterResponse toMerchantReportFilterResponse(PreFilterResponse response, Map<PreFilterDisableMethodBo, List<PreFilterDisableReasonDetailBo>> disableMethodListMap) {
        MerchantReportFilterResponse targetResponse = new MerchantReportFilterResponse();
        targetResponse.setPaymentMethods(new ArrayList<>());
        targetResponse.setDisablePaymentMethods(new ArrayList<>());
        List<PreFilterResponse.PaymentMethod> paymentMethods = Optional.ofNullable(response).map(PreFilterResponse::getPaymentMethods).orElse(new ArrayList<>());
        if (CollectionUtils.isNotEmpty(paymentMethods)) {
            Map<String, MerchantReportFilterResponse.PaymentMethod> targetPaymentMethodMap = new HashMap<>();
            paymentMethods.forEach(paymentMethod -> {
                String currency = paymentMethod.getAmount() == null ? "" : paymentMethod.getAmount().getCurrency();
                String mapKey = String.format("%s:%s", paymentMethod.getCashierProductNo(), currency);
                // 设置目标支付方式
                targetPaymentMethodMap.compute(mapKey, (k, v) -> {
                    if (null == v) {
                        MerchantReportFilterResponse.PaymentMethod targetPaymentMethod = new MerchantReportFilterResponse.PaymentMethod();
                        targetPaymentMethod.setCashierProductNo(paymentMethod.getCashierProductNo());
                        targetPaymentMethod.setCurrency(currency);
                        targetPaymentMethod.setChannelDetails(new HashSet<>());
                        v = targetPaymentMethod;
                    }
                    MerchantReportFilterResponse.ChannelDetail channelDetail = new MerchantReportFilterResponse.ChannelDetail();
                    if (paymentMethod.getIsNeedReportMerchant() == null || ShareConstants.NUMBER_ZERO.equals(paymentMethod.getIsNeedReportMerchant())
                            || StringUtils.isBlank(paymentMethod.getChannelId()) || StringUtils.isBlank(paymentMethod.getEntity())) {
                        // 不报备渠道，返回空 channelId 和 entity
                        channelDetail.setChannelId(StringUtils.EMPTY);
                        channelDetail.setEntity(StringUtils.EMPTY);
                    } else {
                        channelDetail.setChannelId(paymentMethod.getChannelId());
                        channelDetail.setEntity(paymentMethod.getEntity());
                    }
                    v.getChannelDetails().add(channelDetail);
                    return v;
                });
            });
            targetResponse.setPaymentMethods(new ArrayList<>(targetPaymentMethodMap.values()));
        }
        if (MapUtils.isEmpty(disableMethodListMap)) {
            return targetResponse;
        }
        Map<String, MerchantReportFilterResponse.DisablePaymentMethod> disablePaymentMethodMap = new HashMap<>();
        disableMethodListMap.forEach((disableMethodBo, disableReasonDetailList) -> {
            String mapKey = String.format("%s:%s", disableMethodBo.getCashierProductNo(), disableMethodBo.getCurrency());
            // 设置目标支付方式
            disablePaymentMethodMap.compute(mapKey, (k, v) -> {
                if (null == v) {
                    MerchantReportFilterResponse.DisablePaymentMethod targetPaymentMethod = new MerchantReportFilterResponse.DisablePaymentMethod();
                    targetPaymentMethod.setCashierProductNo(disableMethodBo.getCashierProductNo());
                    targetPaymentMethod.setCurrency(disableMethodBo.getCurrency());
                    targetPaymentMethod.setChannelDetails(new HashSet<>());
                    v = targetPaymentMethod;
                }
                MerchantReportFilterResponse.DisablePaymentMethod finalV = v;

                Map<String, MerchantReportFilterResponse.DisableChannelDetail> disableChannelDetailMap = new HashMap<>();
                disableReasonDetailList.forEach(disableReasonDetail -> {
                    if (disableReasonDetail.getIsNeedReportMerchant() == null || ShareConstants.NUMBER_ZERO.equals(disableReasonDetail.getIsNeedReportMerchant())
                            || StringUtils.isBlank(disableReasonDetail.getChannelId()) || StringUtils.isBlank(disableReasonDetail.getEntity())) {
                        // 不报备渠道剔除
                        return;
                    }

                    String detailKey = String.format("%s:%s:%s:%s", disableMethodBo.getCashierProductNo(), disableMethodBo.getCurrency(), disableReasonDetail.getChannelId(), disableReasonDetail.getEntity());
                    MerchantReportFilterResponse.DisableChannelDetail ans = disableChannelDetailMap.compute(detailKey, (ignored, detail) -> {
                        if (null == detail) {
                            MerchantReportFilterResponse.DisableChannelDetail channelDetail = new MerchantReportFilterResponse.DisableChannelDetail();
                            channelDetail.setChannelId(disableReasonDetail.getChannelId());
                            channelDetail.setEntity(disableReasonDetail.getEntity());
                            channelDetail.setErrorCodes(new HashSet<>());
                            detail = channelDetail;
                        }
                        detail.getErrorCodes().add(disableReasonDetail.getErrorCode());
                        return detail;
                    });
                    finalV.getChannelDetails().add(ans);
                });
                return finalV;
            });
        });
        targetResponse.setDisablePaymentMethods(new ArrayList<>(disablePaymentMethodMap.values()));
        return targetResponse;
    }


    /**
     * 转换收银产品筛选响应
     *
     * @param response
     * @return
     */
    default FilterChannelResponse toFilterChannelResponse(PreFilterResponse response) {
        if (response == null) {
            return new FilterChannelResponse();
        }
        FilterChannelResponse targetResponse = new FilterChannelResponse();
        List<PreFilterResponse.PaymentMethod> paymentMethods = response.getPaymentMethods();
        if (CollectionUtils.isEmpty(paymentMethods)) {
            targetResponse.setPaymentMethods(new ArrayList<>());
            return targetResponse;
        }
        Map<String, FilterChannelResponse.PaymentMethod> targetPaymentMethodMap = new HashMap<>();
        paymentMethods.forEach(paymentMethod -> {
            String mapKey = paymentMethod.getPaymentMethodNo();
            // 设置目标支付方式
            FilterChannelResponse.PaymentMethod targetPaymentMethod = targetPaymentMethodMap.computeIfAbsent(mapKey, t -> {
                FilterChannelResponse.PaymentMethod tmpTargetPaymentMethod = new FilterChannelResponse.PaymentMethod();
                tmpTargetPaymentMethod.setPaymentMethodNo(paymentMethod.getPaymentMethodNo());
                tmpTargetPaymentMethod.setAvailableChannelCodes(new HashSet<>());
                return tmpTargetPaymentMethod;
            });
            // 出款渠道支付方式实例 并且 不允许自动切换，则不返回
            if (PaymentTypeEnum.PAYOUT.getValue().equals(paymentMethod.getPaymentType()) && !ShareConstants.YES_FLAG.equals(paymentMethod.getIsAllowAutoSwitch())) {
                return;
            }
            targetPaymentMethod.getAvailableChannelCodes().add(paymentMethod.getChannelCode());
        });

        targetResponse.setPaymentMethods(new ArrayList<>(targetPaymentMethodMap.values()));
        return targetResponse;
    }

    /**
     * 不可用支付方式
     *
     * @param disableMethodBo
     * @return
     */
    @Mapping(target = "reasonDetails", source = "disableReasonDetailBos")
    AvailabilityFilterResponse.DisablePaymentMethod toAvailabilityFilterDisablePaymentMethod(PreFilterDisableMethodBo disableMethodBo, List<PreFilterDisableReasonDetailBo> disableReasonDetailBos);

    /**
     * 不可用支付方式
     *
     * @param disableMethodBo
     * @return
     */
    @Mapping(target = "reasonDetails", source = "disableReasonDetailBos")
    @Mapping(target = "reasonMap", expression = "java(new HashMap<>())")
    com.payermax.fin.exchange.service.response.PreFilterResponse.DisablePaymentMethod toPreFilterDisablePaymentMethod(PreFilterDisableMethodBo disableMethodBo, List<PreFilterDisableReasonDetailBo> disableReasonDetailBos);

    /**
     * 不可用支付方式
     *
     * @param disableMethodBo
     * @return
     */
    @Mapping(target = "paymentMethodType", source = "disableMethodBo.newPaymentMethodType")
    @Mapping(target = "reasonDetails", source = "disableReasonDetailBos")
    @Mapping(target = "reasonMap", expression = "java(new HashMap<>())")
    ProductPreFilterResponse.DisablePaymentMethod toProductPreFilterDisablePaymentMethod(PreFilterDisableMethodBo disableMethodBo, List<PreFilterDisableReasonDetailBo> disableReasonDetailBos);

    /**
     * 对账请求参数转换
     *
     * @param request
     * @return
     */
    @Mapping(source = "bizNoInfo.arn", target = "arn")
    @Mapping(source = "bizNoInfo.rrn", target = "rrn")
    UpdateCommitOrderRequest toUpdateCommitOrderRequest(UpdateCommitOrderDTO request);

    /**
     * 授权用户信息请求转换
     *
     * @param request
     * @return
     */
    InquiryAuthUserInfoRequestDO toAuthUserInfoInquiryReqOrder(InquiryAuthorizationUserInfoRequest request);

    /**
     * 授权用户信息响应转换
     *
     * @param responseOrder
     * @return
     */
    InquiryAuthorizationUserInfoResponse toAuthUserInfoInquiryResp(InquiryAuthUserInfoResponseDO responseOrder);

    /**
     * 锁定订单请求转换
     *
     * @param request
     * @return
     */
    LockOrderRequestDO toLockOrderReq(LockOrderRequest request);

    /**
     * 锁定订单响应转换
     *
     * @param response
     * @return
     */
    LockOrderResponse toLockOrderResp(LockOrderResponseDO response);

    /**
     * 将ApplyDDCRequest转换
     *
     * @param request
     * @return
     */
    default MidRouteCommonGateRequestDO toMidRouteCommonGateRequestDO(ApplyDDCRequest request) {
        MidRouteCommonGateRequestDO midRouteCommonGateRequestDO = toApplyDDCRequest(request);
        ApplyDDCRequest.ScenarioInfo scenarioInfo = request.getScenarioInfo();
        if (scenarioInfo == null) {
            return midRouteCommonGateRequestDO;
        }
        List<ExtendPropertyDO> extendPropertyDOList = midRouteCommonGateRequestDO.getExtendProperties();
        if (extendPropertyDOList == null) {
            extendPropertyDOList = new ArrayList<>();
            midRouteCommonGateRequestDO.setExtendProperties(extendPropertyDOList);
        }
        if (StringUtils.isNotBlank(scenarioInfo.getRecurringType())) { //代扣场景
            Boolean merchantInitiated = scenarioInfo.getMerchantInitiated();
            if (!BooleanUtils.isTrue(merchantInitiated)) { //CIT(DDC申请不存在MIT)
                ExtendPropertyDO extendPropertyDO = ExtendPropertyUtils.buildExtendProperty(FactorKeyEnum.INITIATED_MODE.name(), ShareConstants.CIT_FIRST_STORED, LogicKeyEnum.STRONG_INCLUDE.getCode());
                extendPropertyDOList.add(extendPropertyDO);
            }
        }
        return midRouteCommonGateRequestDO;
    }

    @Mapping(target = "paymentType", expression = "java(PaymentTypeEnum.TOKEN)")
    @Mapping(target = "requestType", expression = "java(RequestTypeEnum.REVOKE_PAYMENT_TOKEN.getCode())")
    TokenUnbindingRequestOrderDO toTokenUnbindingRequestOrderDO(TokenUnbindingRequest request);

    @Mapping(target = "status", expression = "java(OrderStatus4OutEnum.mappingStatus4Out(responseOrder.getStatus().name()))")
    TokenUnbindingResponse toTokenUnbindingResponse(ResponseOrderDO responseOrder);

    RequestOrderDO.TokenInfo toTokenInfo(PayRequestContext.TokenInfo request);

    List<RequestOrderDO.TokenInfo> toTokenInfos(List<PayRequestContext.TokenInfo> requests);
}
