package com.payermax.fin.exchange;

import cn.hippo4j.starter.enable.EnableDynamicThreadPool;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.payermax.common.lang.util.JsonUtils;
import com.payermax.components.ribbon.gray.config.GrayRouteAutoConfiguration;
import com.payermax.fin.exchange.config.NestedEncryptPropertyResolver;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.jasypt.encryption.StringEncryptor;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.ribbon.RibbonClients;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@EnableDynamicThreadPool
@SpringBootApplication(scanBasePackages = {"com.payermax.fin.exchange", "com.payermax.basic.common.sharding","com.ushareit.components", "com.ushareit.fintech.components", "com.ushareit.fintech.common"},exclude = {GrayRouteAutoConfiguration.class})
@EnableFeignClients(basePackages = {"com.payermax.fintech.contextcenter.api.facade", "com.payermax.fin.exchange.integration"})
@EnableDubbo(scanBasePackages = "com.payermax.fin.exchange.provider")
@MapperScan(basePackages = {"com.payermax.fin.exchange.dal"})
@RibbonClients(defaultConfiguration = GrayRouteAutoConfiguration.class)
public class StartApplication {
    private static final Logger log = LoggerFactory.getLogger(StartApplication.class);

    public static void main(String[] args) {
        log.info("Service started...");
        initJacksonConfig();
        System.setProperty("sentry.release", "release_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        SpringApplication springApplication = new SpringApplication(StartApplication.class);
        springApplication.run(args);
        log.info("Service started successful !");
    }

    /**
     * 初始化Jackson配置
     */
    private static void initJacksonConfig() {
        ObjectMapper objectMapper = JsonUtils.getMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
    }

    @Bean("encryptablePropertyResolver")
    public NestedEncryptPropertyResolver myResolver(@Qualifier("lazyJasyptStringEncryptor") StringEncryptor encryptor) {
        return new NestedEncryptPropertyResolver(encryptor);
    }

}
