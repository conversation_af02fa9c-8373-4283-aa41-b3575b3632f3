package com.payermax.fin.exchange.controller.omc;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.fin.exchange.service.facade.OmcExchangeFacade;
import com.payermax.fin.exchange.service.request.*;
import com.payermax.fin.exchange.service.response.LockOrderResponse;
import com.payermax.infra.ionia.log.digest.core.annotation.DigestLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * OMC-订单
 *
 * <AUTHOR>
 * @date 2022/1/22 2:28
 */
@RestController
@Slf4j
@Validated
@RequestMapping(value = "/omc")
public class OmcOrderController {

    @Autowired
    private OmcExchangeFacade omcExchangeFacade;

    @PostMapping(value = "/order/compensate")
    @DigestLog(isRecord= true)
    public Result<String> compensateInquiry(@RequestBody CompensateInquiryRequest request) {
        return omcExchangeFacade.compensateInquiry(request);
    }

    @PostMapping(value = "/channel/routing")
    @DigestLog(isRecord= true)
    public Result<Map> channelRouting(@RequestBody ChannelRoutingRequest request) {
        return omcExchangeFacade.channelRouting(request);
    }

    @PostMapping(value = "/order/notifyCompensate")
    @DigestLog(isRecord= true)
    public Result<Void> notifyCompensate(@RequestBody @Valid List<CompensateNotifyRequest> request) {
        return omcExchangeFacade.compensateNotify(request);
    }


    @PostMapping(value = "/order/lock")
    @DigestLog(isRecord= true)
    public Result<LockOrderResponse> lockOrder(@RequestBody @Valid LockOrderRequest request) {
        return omcExchangeFacade.lockOrder(request);
    }

    @PostMapping(value = "/order/threeDSNotifyCompensate")
    @DigestLog(isRecord = true)
    public Result<Void> threeDSNotifyCompensate(@RequestBody @Valid List<CompensateThreeDSNotifyRequest> request) {
        return omcExchangeFacade.compensateThreeDSNotify(request);
    }

}
