package com.payermax.fin.exchange.provider;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.fin.exchange.assembler.FacadeAssembler;
import com.payermax.fin.exchange.domain.inquiry.*;
import com.payermax.fin.exchange.domainservice.IDomainService;
import com.payermax.fin.exchange.share.domain.*;
import com.payermax.fin.exchange.share.facade.CommonFacade;
import com.payermax.fin.exchange.share.request.*;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 通用服务
 *
 * <AUTHOR>
 * @date 2021/12/28 14:20
 */
@DubboService(version = "${exchange.dubbo.api.version}", group = "${exchange.dubbo.api.group-id:}")
@Service
public class CommonFacadeImpl implements CommonFacade {

    @Autowired
    private FacadeAssembler facadeAssembler;

    @Autowired
    private IDomainService<InquiryChannelInstRequestDO, List<ChannelInstanceDO>> inquiryChannelInstDomainService;

    @Autowired
    private IDomainService<InquiryTargetMerchantRequestDO, TargetMerchantDO> inquiryTargetMerchantDomainService;

    @Autowired
    private IDomainService<InquiryChannelEventRequestDO, List<ChannelEventDO>> inquiryChannelEventDomainService;

    @Override
    public Result<List<ChannelInstanceDO>> inquiryChannelInst(InquiryChannelInstRequest request) {
        // 入参转换
        InquiryChannelInstRequestDO requestDO = facadeAssembler.toInquiryChannelInstReqOrder(request);
        // 调用下游领域服务
        List<ChannelInstanceDO> channelInstanceDOS = inquiryChannelInstDomainService.execute(requestDO);
        // 响应
        return ResultUtil.success(channelInstanceDOS);
    }

    @Override
    public Result<TargetMerchantDO> hasTargetMerchant(InquiryTargetMerchantRequest request) {
        // 入参转换
        InquiryTargetMerchantRequestDO requestDO = facadeAssembler.toInquiryTargetMerchantReqOrder(request);
        // 调用下游领域服务
        TargetMerchantDO execute = inquiryTargetMerchantDomainService.execute(requestDO);
        // 响应
        return ResultUtil.success(execute);
    }

    @Override
    public Result<List<ChannelEventDO>> inquiryChannelEvent(InquiryChannelEventRequest request) {
        // 入参转换
        InquiryChannelEventRequestDO requestDO = facadeAssembler.toInquiryChannelEventReqOrder(request);
        // 调用下游领域服务
        List<ChannelEventDO> channelEventDOS = inquiryChannelEventDomainService.execute(requestDO);
        // 响应
        return ResultUtil.success(channelEventDOS);
    }

}
