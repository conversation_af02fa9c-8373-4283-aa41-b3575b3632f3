package com.payermax.fin.exchange.config;

import com.payermax.basic.common.sharding.ShardingConstants;
import com.ushareit.components.shardingphysical.core.CachedPhysicalTableStore;
import com.ushareit.components.shardingphysical.core.DefaultThreadPoolExecutor;
import com.ushareit.components.shardingphysical.core.HintPhysicalAspect;
import com.ushareit.components.shardingphysical.core.PhysicalSharingExecutor;
import org.apache.shardingsphere.shardingjdbc.jdbc.core.datasource.ShardingDataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/2/15 22:18
 */
@Configuration
public class PhysicalSharingAutoConfig {

    @Bean
    public HintPhysicalAspect hintPhysicalAspect() {
        // 手动注册切面Bean，否则切面不生效
        return new HintPhysicalAspect();
    }

    @Bean
    public PhysicalSharingExecutor physicalSharingExecutor(@Qualifier(ShardingConstants.DATASOURCE_MASTER_SLAVE_SHARDING) ShardingDataSource shardingMasterSlaveDataSource) {
        return new PhysicalSharingExecutor(new CachedPhysicalTableStore(shardingMasterSlaveDataSource), new DefaultThreadPoolExecutor());
    }

}
