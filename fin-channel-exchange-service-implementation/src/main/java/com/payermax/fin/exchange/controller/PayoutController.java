package com.payermax.fin.exchange.controller;

import com.payermax.fin.exchange.condition.OnHttpCondition;
import com.payermax.fin.exchange.service.facade.PayoutFacade;
import com.payermax.fin.exchange.service.request.InquiryPayoutRequest;
import com.payermax.fin.exchange.service.request.PayoutRequest;
import com.payermax.fin.exchange.service.response.InquiryPayoutResponse;
import com.payermax.fin.exchange.service.response.PayoutResponse;
import com.payermax.infra.ionia.log.digest.core.annotation.DigestLog;
import com.payermax.common.lang.model.dto.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2021/9/28 21:30
 */
@RestController
@Slf4j
@RequestMapping(value = "/payout")
@Api(value = "出款服务", tags = "出款服务")
@Conditional(OnHttpCondition.class)
public class PayoutController {

    @Autowired
    private PayoutFacade payoutFacade;

    @PostMapping(value = "/withdraw")
    @ApiOperation(value = "出款申请", notes = "出款申请接口  \n" +
            "SYSTEM_BUSY: 通用内部未知错误---需要定位系统异常  \n" +
            "REQUEST_PARAM_INVALID: 参数错误---检查入参  \n" +
            "CHANNEL_ORDER_EXIST: 订单已存在---幂等校验，检查支付单号是否相同  \n" +
            "PAY_METHOD_CONFIG_ERROR: 渠道支付方式配置错误---检查渠道配置数据  \n" +
            "TARGET_MERCHANT_NOT_EXIST: 商户报备信息不存在---检查商户报备信息  \n" +
            "EXCEED_RATE_LIMIT: 渠道限流异常---检查渠道限流配置  \n" +
            "CHANNEL_NO_SUPPORT: 无可用渠道---检查入参条件，是否有匹配渠道可用" +
            "DATE_FORMAT_EXCEPTION: 下游响应数据格式异常---检查下游响应数据  \n" +
            "CHANNEL_FRONT_RPC_EXCEPTION: 下游响应数据为空---检查下游响应  \n" +
            "STATE_TRANSITION_NO_SUPPORT: 不支持的订单状态流转---检查订单状态  \n" +
            "ORDER_UPDATE_FAIL: 订单更新失败---订单状态乐观锁，检查订单状态  \n" +
            "F999: 渠道响应码未配置内部映射码时，默认返回码---检查渠道响应  \n" +
            "其它：渠道响应码配置的内部映射码")
    @DigestLog(isRecord = true)
    public Result<PayoutResponse> withdraw(@RequestBody PayoutRequest request) {
        return payoutFacade.withdraw(request);
    }

    @PostMapping(value = "/inquiry")
    @ApiOperation(value = "出款查询", notes = "查询出款结果接口  \n" +
            "SYSTEM_BUSY: 通用内部未知错误---需要定位系统异常  \n" +
            "REQUEST_PARAM_INVALID: 参数错误---检查入参  \n" +
            "CHANNEL_ORDER_IS_NOT_EXIST: 渠道订单不存在---检查渠道单号是否正确")
    public Result<InquiryPayoutResponse> inquiry(@RequestBody InquiryPayoutRequest request) {
        return payoutFacade.inquiry(request);
    }
}
