package com.payermax.fin.exchange.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.payermax.fin.exchange.domainservice.bo.CodeMapBO;
import com.payermax.fin.exchange.domainservice.entity.RocketMqMessage;
import com.payermax.fin.exchange.domainservice.service.codeMapping.ICodeMappingService;
import com.payermax.infra.ionia.rocketmq.handler.BaseMqMessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 订单callback消息监听
 *
 * <AUTHOR>
 * @date 2023/2/21 17:29
 */
@Slf4j
@Component
@RocketMQMessageListener(
        topic = "${rocketMq.topic.channel.exchange.resp.code.save.topic}",
        consumerGroup = "${rocketmq.consumer.channel.resp.code.group_id}"
)
public class ChannelRespCodeMessageListener extends BaseMqMessageListener<RocketMqMessage>
        implements RocketMQListener<RocketMqMessage> {

    @Autowired
    private ICodeMappingService responseCodeMappingServiceImpl;

    @Override
    protected void handleMessage(RocketMqMessage message) throws Exception {
        if (message.getData() == null) {
            return;
        }

        try {
            CodeMapBO.ChannelRespCode channelRespCode = JSONObject.parseObject(JSON.toJSONString(message.getData()), CodeMapBO.ChannelRespCode.class);
            responseCodeMappingServiceImpl.saveChannelRespCode(channelRespCode);
        } catch (Exception e) {
            log.warn("<== topic[resp.code.save.topic] saveChannelRespCode, exception: {}", e.getMessage());
        }
    }

    @Override
    protected void overMaxRetryTimesMessage(RocketMqMessage message) {
        log.error("The order callback mq message processing fail over max retry times, message = {}", JSON.toJSONString(message));
    }

    @Override
    public void onMessage(RocketMqMessage message) {
        super.dispatchMessage(message);
    }


}
