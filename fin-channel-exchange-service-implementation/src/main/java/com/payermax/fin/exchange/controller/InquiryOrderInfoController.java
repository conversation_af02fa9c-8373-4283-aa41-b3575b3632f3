package com.payermax.fin.exchange.controller;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.fin.exchange.aspect.AroundLog;
import com.payermax.fin.exchange.service.facade.InquiryOrderInfoFacade;
import com.payermax.fin.exchange.service.request.InquiryCommitOrderRequest;
import com.payermax.fin.exchange.service.request.InquiryNewTargetMerchantRequest;
import com.payermax.fin.exchange.service.request.InquiryOrderInfoRequest;
import com.payermax.fin.exchange.service.response.InquiryCommitOrderResponse;
import com.payermax.fin.exchange.service.response.InquiryNewTargetMerchantResponse;
import com.payermax.fin.exchange.service.response.InquiryOrderInfoResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date Create on 2021/8/30 11:05
 */
@RestController
@Slf4j
@RequestMapping(value = "/order/info")
@Api(description = "提供给 payment-channel 服务的接口，为兼容新老服务查询订单信息")
public class InquiryOrderInfoController {

    @Autowired
    private InquiryOrderInfoFacade inquiryOrderInfoFacade;

    @PostMapping(value = "/inquiry")
    @ApiOperation(value = "Inquiry Payment API", notes = "Use this interface to query the payment order info.")
    public Result<InquiryOrderInfoResponse> inquiryOrderInfo(@RequestBody InquiryOrderInfoRequest request) {
        return inquiryOrderInfoFacade.inquiryOrderInfo(request);
    }

    @AroundLog
    @PostMapping(value = "/inquiryCommitOrder")
    public Result<List<InquiryCommitOrderResponse>> inquiryCommitOrder(@RequestBody InquiryCommitOrderRequest request) {
        return inquiryOrderInfoFacade.inquiryCommitOrder(request);
    }

    @PostMapping(value = "/inquiryTargetMerchant")
    @ApiOperation(value = "Inquiry TargetMerchant API", notes = "query target merchant info by pay commit no")
    public Result<InquiryNewTargetMerchantResponse> inquiryTargetMerchant(@RequestBody InquiryNewTargetMerchantRequest request) {
        return inquiryOrderInfoFacade.inquiryTargetMerchant(request);
    }

}
