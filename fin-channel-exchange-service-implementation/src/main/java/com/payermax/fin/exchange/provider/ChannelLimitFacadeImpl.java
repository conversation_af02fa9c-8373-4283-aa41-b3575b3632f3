package com.payermax.fin.exchange.provider;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.fin.exchange.assembler.FacadeAssembler;
import com.payermax.fin.exchange.domain.common.UpdateChannelLimitRequestDO;
import com.payermax.fin.exchange.domainservice.IDomainService;
import com.payermax.fin.exchange.service.facade.ChannelLimitFacade;
import com.payermax.fin.exchange.service.request.UpdateChannelLimitRequest;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/4/12 14:18
 */
@DubboService(version = "${exchange.dubbo.api.version}", group = "${exchange.dubbo.api.group-id:}")
@Service
public class ChannelLimitFacadeImpl implements ChannelLimitFacade {

    @Autowired
    private FacadeAssembler facadeAssembler;

    @Autowired
    private IDomainService<UpdateChannelLimitRequestDO, Integer> updateChannelLimitService;

    @Override
    public Result<Integer> updateChannelLimit(UpdateChannelLimitRequest request) {
        // 入参转换
        UpdateChannelLimitRequestDO requestDO = facadeAssembler.toUpdateChannelLimitRequest(request);
        // 调用领域服务
        Integer response = updateChannelLimitService.execute(requestDO);
        // 响应转换
        return ResultUtil.success(response);
    }

}