package com.payermax.fin.exchange.provider;

import com.alibaba.fastjson.JSON;
import com.payermax.channel.gateway.facade.spi.ChannelGatewaySpi;
import com.payermax.channel.gateway.facade.spi.ChannelGatewaySpiRequest;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.fin.exchange.domain.callback.CallbackRequestDO;
import com.payermax.fin.exchange.domainservice.IDomainService;
import com.payermax.fin.exchange.domainservice.rule.channelgatewaygary.ChannelGatewayGrayExecutor;
import com.payermax.infra.ionia.log.digest.core.annotation.DigestLog;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.config.annotation.Method;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/12/16 15:12
 **/
@DubboService(version = "${exchange.dubbo.api.version}", group = "${spring.application.name}", methods = {
        @Method(name = "invoke", timeout = 10000),
})
@Service
public class ChannelGatewaySpiImpl implements ChannelGatewaySpi {

    @Autowired
    private IDomainService<CallbackRequestDO, String> callbackDomainService;

    @Override
    @DigestLog(isRecord = true)
    public Result<String> invoke(ChannelGatewaySpiRequest request) {
        // 入参转换
        CallbackRequestDO requestOrder = JSON.parseObject(request.getData(), CallbackRequestDO.class);
        //渠道迁移 增加配置化网关标记
        ChannelGatewayGrayExecutor.setCallBackSystem(requestOrder);
        // 调用领域服务
        String response = callbackDomainService.execute(requestOrder);
        // 响应转换
        return ResultUtil.success(response);
    }
}
