package com.payermax.fin.exchange.controller;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.fin.exchange.service.facade.ChannelLimitFacade;
import com.payermax.fin.exchange.service.request.UpdateChannelLimitRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023/4/12 10:32
 */

@RestController
@Slf4j
@RequestMapping(value = "/limit")
@Api(value = "限流配置", tags = "限流配置")
public class ChannelLimitController {

    @Autowired
    private ChannelLimitFacade channelLimitFacade;

    @PostMapping(value = "/update")
    @ApiOperation(value = "限流配置更新", notes = "用于系统更新有效限流配置")
    public Result<Integer> updateChannelLimit(@RequestBody UpdateChannelLimitRequest request) {
        return channelLimitFacade.updateChannelLimit(request);
    }

}
