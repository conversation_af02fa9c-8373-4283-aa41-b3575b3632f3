package com.payermax.fin.exchange.provider;

import com.alibaba.csp.sentinel.EntryType;
import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.basic.contexcenter.service.client.model.risk.RiskContextInfo;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.fin.exchange.assembler.FacadeAssembler;
import com.payermax.fin.exchange.common.exception.ExchangeException;
import com.payermax.fin.exchange.common.util.ValidationUtils;
import com.payermax.fin.exchange.domain.common.CommonGateResponseDO;
import com.payermax.fin.exchange.domain.common.MidRouteCommonGateRequestDO;
import com.payermax.fin.exchange.domain.inquiry.InquiryOrderRequestDO;
import com.payermax.fin.exchange.domain.inquiry.InquiryOrderResponseDO;
import com.payermax.fin.exchange.domain.trans.RequestOrderDO;
import com.payermax.fin.exchange.domain.trans.ResponseOrderDO;
import com.payermax.fin.exchange.domain.trans.applyToken.ApplyPaymentTokenRequestDO;
import com.payermax.fin.exchange.domain.trans.applyToken.TokenUnbindingRequestOrderDO;
import com.payermax.fin.exchange.domain.trans.applyToken.TokenUnbindingResponseOrderDO;
import com.payermax.fin.exchange.domain.trans.pay.*;
import com.payermax.fin.exchange.domain.trans.refund.CloseRequestOrderDO;
import com.payermax.fin.exchange.domainservice.IDomainService;
import com.payermax.fin.exchange.domainservice.IV2DomainService;
import com.payermax.fin.exchange.domainservice.assembler.DoAssembler;
import com.payermax.fin.exchange.domainservice.newImpl.RequestOrderDomainService;
import com.payermax.fin.exchange.domainservice.service.route.IRoutingService;
import com.payermax.fin.exchange.integration.rpc.proxy.ContextClientProxy;
import com.payermax.fin.exchange.service.context.PayRequestContext;
import com.payermax.fin.exchange.service.facade.PaymentFacade;
import com.payermax.fin.exchange.service.request.*;
import com.payermax.fin.exchange.service.response.*;
import com.payermax.fin.exchange.share.domain.ChannelInstanceDO;
import com.payermax.fin.exchange.share.domain.PaymentMethodDO;
import com.payermax.fin.exchange.share.enums.ActionTypeEnum;
import com.payermax.fin.exchange.share.enums.FactorKeyEnum;
import com.payermax.fin.exchange.share.enums.PaymentTypeEnum;
import com.payermax.fin.exchange.share.utils.ExtendPropertyUtils;
import com.payermax.infra.ionia.log.digest.core.annotation.DigestLog;
import com.ushareit.fintech.common.model.dto.Money;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.config.annotation.Method;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 支付服务
 *
 * <AUTHOR>
 * @date 2021/12/6 21:30
 */
@DubboService(version = "${exchange.dubbo.api.version}", group = "${exchange.dubbo.api.group-id:}", methods = {
        @Method(name = "pay", timeout = 13000),
        @Method(name = "capture", timeout = 13000),
        @Method(name = "unifyControl", timeout = 13000),
        @Method(name = "applyPaymentToken", timeout = 13000),
        @Method(name = "closeApply", timeout = 13000),
        @Method(name = "handlePayCorrectionOrder", timeout = 5000)
})
@Service
public class PaymentFacadeImpl implements PaymentFacade {

    private static final String RISK_CONTEXT_PREFIX = "RISK:";

    @NacosValue(value = "#{${ddc.channel.map}}", autoRefreshed = true)
    private Map<String, String> ddcChannelMap;

    @Autowired
    private FacadeAssembler facadeAssembler;

    @Autowired
    private DoAssembler doAssembler;

    @Autowired
    private ContextClientProxy contextClientProxy;

    @Autowired
    @Qualifier("defaultRoutingService")
    protected IRoutingService<RequestOrderDO> routingService;

    @Autowired
    @Qualifier("asyncPayDomainService")
    private IDomainService<PayRequestOrderDO, PayResponseOrderDO> payDomainService;

    @Autowired
    private IDomainService<InquiryOrderRequestDO, InquiryOrderResponseDO> inquiryDomainService;

    @Autowired
    private IDomainService<PayControlRequestOrderDO, PayControlResponseOrderDO> payControlDomainService;

    @Autowired
    private IDomainService<PayCorrectionOrderRequestDO, PayCorrectionOrderResponseDO> payCorrectionService;

    @Autowired
    private IDomainService<MidRouteCommonGateRequestDO, CommonGateResponseDO> midRouteGateDomainService;

    @Autowired
    @Qualifier("requestOrderDomainService")
    private IV2DomainService<ApplyPaymentTokenRequestDO, ResponseOrderDO> requestOrderDomainService;

    @Resource
    private IV2DomainService<CaptureRequestOrderDO, ResponseOrderDO> captureDomainService;

    @Autowired
    @Qualifier("tokenUnbindingDomainService")
    private IV2DomainService<TokenUnbindingRequestOrderDO, TokenUnbindingResponseOrderDO> tokenUnbindingDomainService;

    @Autowired
    @Qualifier("closeDomainService")
    private RequestOrderDomainService<CloseRequestOrderDO, ResponseOrderDO> closeDomainService;

    @Override
    @DigestLog(isRecord = true)
    public Result<PayResponse> pay(PayRequest request) {
        // 从上下文查询信息
        PayRequestContext requestContext = contextClientProxy.queryPaymentRequest(request.getPayRequestNo());
        // 上下文不为空，则取上下文
        if (requestContext != null) {
            request.setRequestContext(requestContext);
            // 校验上下文参数
            ValidationUtils.validate(requestContext);
            // 构建3ds相关信息
            this.build3dsInfo(requestContext);
        }
        // 入参转换
        PayRequestOrderDO payRequestOrder = facadeAssembler.toPayReqOrder(request);
        // 调用下游服务，如果出现ExchangeException异常，则获取data
        PayResponseOrderDO payResponseOrder;
        try {
            // 调用领域服务
            payResponseOrder = payDomainService.execute(payRequestOrder);
        } catch (ExchangeException e) {
            payResponseOrder = (PayResponseOrderDO) e.getData();
        }
        // 响应转换
        PayResponse payResponse = facadeAssembler.toPayResp(payResponseOrder);
        // 响应报文保存上下文
        contextClientProxy.savePaymentResponse(request.getPayRequestNo(), payResponse);

        return ResultUtil.success(payResponse);
    }

    @Override
    @DigestLog(isRecord = true)
    public Result<CaptureResponse> capture(CaptureRequest request) {
        // 请求转换
        CaptureRequestOrderDO requestOrder = facadeAssembler.toCaptureRequest(request);
        // 调用下游服务，如果出现ExchangeException异常，则获取data
        ResponseOrderDO responseOrder;
        try {
            // 调用领域服务
            responseOrder = captureDomainService.execute(requestOrder, ResponseOrderDO.class);
        } catch (ExchangeException e) {
            responseOrder = (ResponseOrderDO) e.getData();
        }

        // 响应转换
        return ResultUtil.success(facadeAssembler.toCaptureResp(responseOrder));
    }

    @Override
    @DigestLog(isRecord = true)
    public Result<UnifyControlResponse> unifyControl(UnifyControlRequest request) {
        // 请求转换
        PayControlRequestOrderDO requestOrder = facadeAssembler.toPayControlReqOrder(request);
        // 调用领域服务
        PayControlResponseOrderDO responseOrder = payControlDomainService.execute(requestOrder);
        // 响应转换
        return ResultUtil.success(facadeAssembler.toPayControlResp(responseOrder));
    }

    @Override
    @SentinelResource(value = "dubbo:provider:PaymentFacade.inquiryPayment", entryType = EntryType.IN)
    public Result<InquiryPaymentResponse> inquiryPayment(InquiryPaymentRequest request) {
        // 请求转换
        InquiryOrderRequestDO requestOrder = facadeAssembler.toPayInquiryReqOrder(request);
        // 调用领域服务
        InquiryOrderResponseDO responseOrder = inquiryDomainService.execute(requestOrder);
        // 响应转换
        return ResultUtil.success(facadeAssembler.toPayInquiryResp(responseOrder));
    }

    @Override
    @SentinelResource(value = "dubbo:provider:PaymentFacade.inquiryCapture", entryType = EntryType.IN)
    public Result<InquiryCaptureResponse> inquiryCapture(InquiryCaptureRequest request) {
        // 请求转换
        InquiryOrderRequestDO requestOrder = facadeAssembler.toCaptureInquiryReqOrder(request);
        // 调用领域服务
        InquiryOrderResponseDO responseOrder = inquiryDomainService.execute(requestOrder);
        // 响应转换
        return ResultUtil.success(facadeAssembler.toCaptureInquiryResp(responseOrder));
    }

    @Override
    public Result<PayResponse> handlePayCorrectionOrder(PayCorrectionOrderRequest request) {
        // 从上下文查询信息
        PayRequestContext requestContext = contextClientProxy.queryPaymentRequest(request.getPayRequestNo());
        // 上下文不为空，则取上下文
        if (requestContext != null) {
            request.setRequestContext(requestContext);
            // 校验上下文参数
            ValidationUtils.validate(requestContext);
        }
        // 入参转换
        PayCorrectionOrderRequestDO payCorrectionOrderRequestDO = facadeAssembler.toPayCorrectionReqOrder(request);
        // 处理差错逻辑
        PayCorrectionOrderResponseDO payCorrectionOrderResponseDO;
        try {
            // 调用领域服务
            payCorrectionOrderResponseDO = payCorrectionService.execute(payCorrectionOrderRequestDO);
        } catch (ExchangeException e) {
            payCorrectionOrderResponseDO = (PayCorrectionOrderResponseDO) e.getData();
        }
        // 响应转换
        PayResponse payResponse = facadeAssembler.toPayCorrectionResp(payCorrectionOrderResponseDO);
        // 响应报文保存上下文
        contextClientProxy.savePaymentResponse(request.getPayRequestNo(), payResponse);

        return ResultUtil.success(payResponse);
    }

    @Override
    public Result<ApplyDDCResponse> applyDDC(ApplyDDCRequest request) {
        // 请求转换
        MidRouteCommonGateRequestDO gateRequestDO = facadeAssembler.toMidRouteCommonGateRequestDO(request);
        String realDdcOrg = request.getDdcOrg();
        // 如果渠道编码不存在，并且DDC机构存在，则通过DDC机构获取渠道编码
        if (StringUtils.isBlank(gateRequestDO.getChannelCode())) {
            // 如果入参中ddcOrg不存在，则根据入参条件路由ddcOrg
            if (StringUtils.isBlank(realDdcOrg)) {
                realDdcOrg = this.routeDdcOrg(gateRequestDO);
            }
            gateRequestDO.setChannelCode(ddcChannelMap.get(realDdcOrg));
        }

        // 调用领域服务
        CommonGateResponseDO gateResponseDO = midRouteGateDomainService.execute(gateRequestDO);
        // 响应转换
        ApplyDDCResponse response = facadeAssembler.toApplyDDCResponse(gateResponseDO);
        // 把路由出的ddcOrg返回给上游
        response.setDdcOrg(realDdcOrg);

        return ResultUtil.success(response);
    }

    @Override
    public Result<ApplyPaymentTokenResponse> applyPaymentToken(ApplyPaymentTokenRequest request) {
        // 请求转换
        ApplyPaymentTokenRequestDO applyPaymentTokenRequestDO = facadeAssembler.applyPaymentTokenToRequestOrder(request);
        ResponseOrderDO responseOrder;
        try {
            // 调用领域服务
            responseOrder = requestOrderDomainService.execute(applyPaymentTokenRequestDO, ResponseOrderDO.class);
        } catch (ExchangeException e) {
            responseOrder = (ResponseOrderDO) e.getData();
        }
        // 响应转换
        return ResultUtil.success(facadeAssembler.toApplyPaymentTokenResponse(responseOrder));
    }

    /**
     * 构建3ds相关信息
     *
     * @param requestContext
     */
    private void build3dsInfo(PayRequestContext requestContext) {
        // 根据3dsToken获取ddcSessionId等信息
        String threeDomainSecureToken = requestContext.getThreeDomainSecureToken();
        if (StringUtils.isBlank(threeDomainSecureToken)) {
            return;
        }
        threeDomainSecureToken = threeDomainSecureToken.replace(RISK_CONTEXT_PREFIX, "");
        RiskContextInfo contextInfo = contextClientProxy.queryRisk(threeDomainSecureToken);
        if (contextInfo == null) {
            return;
        }
        // 如果风控信息为空，则进行初始化
        if (requestContext.getRiskInfo() == null) {
            requestContext.setRiskInfo(new PayRequestContext.RiskInfo());
        }
        // 使用风控上下文填充风控信息
        facadeAssembler.fillRiskInfo(requestContext.getRiskInfo(), contextInfo);
    }

    /**
     * 路由ddc机构
     *
     * @param gateRequestDO
     * @return
     */
    private String routeDdcOrg(MidRouteCommonGateRequestDO gateRequestDO) {
        // 构建筛选&路由请求报文
        RequestOrderDO requestOrderDO = new RequestOrderDO();
        requestOrderDO.setActionType(ActionTypeEnum.PRE_FILTER);
        requestOrderDO.setRequestType(gateRequestDO.getRequestType());
        requestOrderDO.setPaymentType(PaymentTypeEnum.getByValue(gateRequestDO.getPaymentType()));
        // 订单信息
        requestOrderDO.setOrderInfo(doAssembler.midRouteToOrderInfoDO(gateRequestDO));
        // 支付方式信息
        PaymentMethodDO paymentMethodDO = doAssembler.midRouteToPaymentMethodDO(gateRequestDO);
        if (gateRequestDO.getAmount() == null) {
            paymentMethodDO.setAmount(new Money(BigDecimal.ZERO, gateRequestDO.getCurrency()));
            paymentMethodDO.setIsIgnoreAmountValue(true);
        } else if (BigDecimal.ZERO.compareTo(gateRequestDO.getAmount().getValue()) == 0 && paymentMethodDO.getIsIgnoreAmountValue() == null) {
            paymentMethodDO.setIsIgnoreAmountValue(true);
        }
        requestOrderDO.setPaymentMethod(paymentMethodDO);
        // 进行渠道筛选&路由
        List<ChannelInstanceDO> channelInstanceDOList = routingService.routing(requestOrderDO);
        if (CollectionUtils.isEmpty(channelInstanceDOList)) {
            return null;
        }
        ChannelInstanceDO channelInstanceDO = channelInstanceDOList.get(0);//NO_CHECK
        String ddcOrg = ExtendPropertyUtils.getExtProperty(channelInstanceDO.getExtendPropertyMap(), FactorKeyEnum.DDC_ORG.name());
        if (StringUtils.isBlank(ddcOrg)) {
            return null;
        }
        String[] ddcOrgs = StringUtils.split(ddcOrg, ",");
        return ddcOrgs[0];//NO_CHECK，取优先级最高的DDC机构
    }

    @Override
    public Result<TokenUnbindingResponse> tokenUnbinding(TokenUnbindingRequest request) {
        TokenUnbindingRequestOrderDO tokenUnbindingRequestOrderDO = facadeAssembler.toTokenUnbindingRequestOrderDO(request);
        ResponseOrderDO responseOrder;
        try {
            // 调用领域服务
            responseOrder = tokenUnbindingDomainService.execute(tokenUnbindingRequestOrderDO, TokenUnbindingResponseOrderDO.class);
        } catch (ExchangeException e) {
            responseOrder = (ResponseOrderDO) e.getData();
        }
        return ResultUtil.success(facadeAssembler.toTokenUnbindingResponse(responseOrder));
    }

    @Override
    public Result<CloseResponse> closeApply(CloseRequest request) {
        // 请求转换
        CloseRequestOrderDO requestOrder = facadeAssembler.toCloseReqOrder(request);
        // 调用下游服务，如果出现ExchangeException异常，则获取data
        ResponseOrderDO responseOrder;
        try {
            // 调用领域服务
            responseOrder = closeDomainService.execute(requestOrder, ResponseOrderDO.class);
        } catch (ExchangeException e) {
            responseOrder = (ResponseOrderDO) e.getData();
        }

        // 响应转换
        return ResultUtil.success(facadeAssembler.toCloseResp(responseOrder));
    }
}
