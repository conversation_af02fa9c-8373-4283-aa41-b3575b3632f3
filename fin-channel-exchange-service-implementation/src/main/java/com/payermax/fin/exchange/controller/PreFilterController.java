package com.payermax.fin.exchange.controller;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.fin.exchange.domainservice.manage.BaseManage;
import com.payermax.fin.exchange.service.facade.PreFilterFacade;
import com.payermax.fin.exchange.service.request.*;
import com.payermax.fin.exchange.service.response.*;
import com.payermax.fin.exchange.task.request.BatchCallbackTaskRequest;
import com.payermax.infra.ionia.log.digest.core.annotation.DigestLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2021/7/19 21:05
 */
@RestController
@Slf4j
@RequestMapping(value = "/preFilter")
@Api(value = "预筛选服务", tags = "预筛选服务")
public class PreFilterController {

    @Autowired
    private PreFilterFacade preFilterFacade;

    @Autowired
    private BaseManage<BatchCallbackTaskRequest, Void> callback4OfflineManager;

    @PostMapping(value = "/filter")
    @ApiOperation(value = "实例筛选", notes = "渠道实例筛选接口  \n" +
            "SYSTEM_BUSY: 通用内部未知错误---需要定位系统异常  \n" +
            "REQUEST_PARAM_INVALID: 参数错误---检查入参")
    @DigestLog(isRecord = true)
    public Result<PreFilterResponse> filter(@RequestBody PreFilterRequest request) {
        return preFilterFacade.filter(request);
    }

    @PostMapping(value = "/filterAccount")
    @ApiOperation(value = "筛选渠道账户")
    @DigestLog(isRecord = true)
    public Result<PreFilterAccountResponse> filterAccount(@RequestBody PreFilterAccountRequest request) {
        return preFilterFacade.filterAccount(request);
    }

    @PostMapping(value = "/cashierProductFilter")
    @ApiOperation(value = "收银产品-渠道筛选")
    @DigestLog(isRecord = true)
    public Result<ProductPreFilterResponse> cashierProductFilter(@RequestBody ProductPreFilterRequest request) {
        return preFilterFacade.cashierProductFilter(request);
    }

    @PostMapping(value = "/availabilityFilter")
    @ApiOperation(value = "收银产品渠道可用性筛选")
    @DigestLog(isRecord = true)
    public Result<AvailabilityFilterResponse> availabilityFilter(@RequestBody AvailabilityFilterRequest request) {
        return preFilterFacade.availabilityFilter(request);
    }

    @PostMapping(value = "/newAvailabilityFilter")
    @ApiOperation(value = "新版可用性筛选")
    @DigestLog(isRecord = true)
    public Result<NewAvailabilityFilterResponse> newAvailabilityFilter(@RequestBody AvailabilityFilterRequest request) {
        return preFilterFacade.newAvailabilityFilter(request);
    }

    @PostMapping(value = "/merchantReportFilter")
    @ApiOperation(value = "报备可用性筛选")
    @DigestLog(isRecord = true)
    public Result<MerchantReportFilterResponse> merchantReportFilter(@RequestBody MerchantReportFilterRequest request) {
        return preFilterFacade.merchantReportFilter(request);
    }

    @PostMapping(value = "/filterChannel")
    @ApiOperation(value = "筛选支付方式下可用渠道")
    @DigestLog(isRecord = true)
    public Result<FilterChannelResponse> filterChannel(@RequestBody FilterChannelRequest request) {
        return preFilterFacade.filterChannel(request);
    }

    @PostMapping(value = "/filterChannelTest")
    @ApiOperation(value = "实例筛选测试,收银产品可传可不传")
    @DigestLog(isRecord = true)
    public Result<FilterChannelResponse> filterChannelTest(@RequestBody PreFilterTestRequest request) {
        return preFilterFacade.filterChannelTest(request);
    }
}
