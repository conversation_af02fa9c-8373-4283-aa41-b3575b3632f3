package com.payermax.fin.exchange.consumer;

import com.alibaba.fastjson.JSON;
import com.payermax.fin.exchange.assembler.FacadeAssembler;
import com.payermax.fin.exchange.domainservice.entity.RocketMqMessage;
import com.payermax.fin.exchange.service.facade.UpdateOrderFacade;
import com.payermax.fin.exchange.service.request.UpdateCommitOrderDTO;
import com.payermax.fin.exchange.service.request.UpdateCommitOrderRequest;
import com.payermax.infra.ionia.rocketmq.handler.BaseMqMessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;

/**
 * 接收ARN RRN消息
 */
@Slf4j
@Component
@RocketMQMessageListener(
        topic = "${rocketMq.topic.channel.exchange.arn.save.topic}",
        consumerGroup = "${rocketmq.consumer.arn.save.group_id}",
        consumeThreadNumber = 1,
        consumeThreadMax = 1)
public class ChannelArnMessageListener extends BaseMqMessageListener<RocketMqMessage>
        implements RocketMQListener<RocketMqMessage> {

    @Autowired
    private UpdateOrderFacade updateOrderFacade;

    @Autowired
    private FacadeAssembler facadeAssembler;

    @Override
    public void handleMessage(RocketMqMessage message) throws Exception {
        if (message.getData() == null) {
            return;
        }

        UpdateCommitOrderDTO requestDTO = JSON.parseObject(JSON.toJSONString(message.getData()), UpdateCommitOrderDTO.class);
        if (requestDTO == null) {
            return;
        }

        UpdateCommitOrderRequest requestDO = facadeAssembler.toUpdateCommitOrderRequest(requestDTO);
        if (requestDO == null) {
            return;
        }

        try {
            updateOrderFacade.updateCommitOrder(Collections.singletonList(requestDO));
        } catch (Exception e) {
            log.warn("<== topic[arn.save.topic] update exception: {}", e.getMessage());
        }
    }

    @Override
    protected void overMaxRetryTimesMessage(RocketMqMessage message) {
        log.error("<== topic[arn.save.topic] processing fail over max retry times, message = {}", JSON.toJSONString(message));
    }

    @Override
    public void onMessage(RocketMqMessage message) {
        super.dispatchMessage(message);
    }
}
