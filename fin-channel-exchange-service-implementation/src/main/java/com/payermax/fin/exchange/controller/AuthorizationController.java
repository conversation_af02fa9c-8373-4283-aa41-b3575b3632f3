package com.payermax.fin.exchange.controller;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.fin.exchange.condition.OnHttpCondition;
import com.payermax.fin.exchange.service.facade.AuthorizationFacade;
import com.payermax.fin.exchange.service.request.ApplyAuthorizationRequest;
import com.payermax.fin.exchange.service.request.InquiryAuthorizationRequest;
import com.payermax.fin.exchange.service.request.InquiryAuthorizationUserInfoRequest;
import com.payermax.fin.exchange.service.request.RevokeAuthorizationRequest;
import com.payermax.fin.exchange.service.response.ApplyAuthorizationResponse;
import com.payermax.fin.exchange.service.response.InquiryAuthorizationResponse;
import com.payermax.fin.exchange.service.response.InquiryAuthorizationUserInfoResponse;
import com.payermax.fin.exchange.service.response.RevokeAuthorizationResponse;
import com.payermax.infra.ionia.log.digest.core.annotation.DigestLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date Create on 2021/8/24 10:01
 */
@RestController
@Slf4j
@RequestMapping(value = "/authorizations")
@Api(value = "授权服务", tags = "授权服务")
@Conditional(OnHttpCondition.class)
public class AuthorizationController {

    @Autowired
    private AuthorizationFacade authorizationFacade;

    @PostMapping(value = "/apply")
    @ApiOperation(value = "授权申请", notes = "授权申请接口  \n" +
            "SYSTEM_BUSY: 通用内部未知错误---需要定位系统异常  \n" +
            "REQUEST_PARAM_INVALID: 参数错误---检查入参  \n" +
            "CHANNEL_ORDER_EXIST: 请求单已存在---幂等校验，检查请求单号是否相同  \n" +
            "PAY_METHOD_CONFIG_ERROR: 渠道支付方式配置错误---检查渠道配置数据  \n" +
            "TARGET_MERCHANT_NOT_EXIST: 商户报备信息不存在---检查商户报备信息  \n" +
            "CHANNEL_NO_SUPPORT: 无可用渠道---检查入参条件，是否有匹配渠道可用" +
            "DATE_FORMAT_EXCEPTION: 下游响应数据格式异常---检查下游响应数据  \n" +
            "CHANNEL_FRONT_RPC_EXCEPTION: 下游响应数据为空---检查下游响应  \n" +
            "STATE_TRANSITION_NO_SUPPORT: 不支持的订单状态流转---检查订单状态  \n" +
            "ORDER_UPDATE_FAIL: 订单更新失败---订单状态乐观锁，检查订单状态  \n" +
            "F999: 渠道响应码未配置内部映射码时，默认返回码---检查渠道响应  \n" +
            "其它：渠道响应码配置的内部映射码")
    @DigestLog(isRecord= true)
    public Result<ApplyAuthorizationResponse> apply(@RequestBody ApplyAuthorizationRequest request) {
        return authorizationFacade.apply(request);
    }

    @PostMapping(value = "/inquiry")
    @ApiOperation(value = "授权查询", notes = "授权信息查询接口  \n" +
            "SYSTEM_BUSY: 通用内部未知错误---需要定位系统异常  \n" +
            "REQUEST_PARAM_INVALID: 参数错误---检查入参  \n")
    public Result<InquiryAuthorizationResponse> inquiry(@RequestBody InquiryAuthorizationRequest request) {
        return authorizationFacade.inquiry(request);
    }

    @PostMapping(value = "/revoke")
    @ApiOperation(value = "授权解约", notes = "授权解约接口  \n" +
            "SYSTEM_BUSY: 通用内部未知错误---需要定位系统异常  \n" +
            "REQUEST_PARAM_INVALID: 参数错误---检查入参  \n" +
            "CHANNEL_ORDER_IS_NOT_EXIST: 授权单不存在---检查授权单号是否正确  \n" +
            "CHANNEL_ORDER_EXIST: 请求单已存在---幂等校验，检查请求单号是否相同  \n" +
            "PAY_METHOD_CONFIG_ERROR: 渠道支付方式配置错误---检查渠道配置数据  \n" +
            "DATE_FORMAT_EXCEPTION: 下游响应数据格式异常---检查下游响应数据  \n" +
            "CHANNEL_FRONT_RPC_EXCEPTION: 下游响应数据为空---检查下游响应  \n" +
            "STATE_TRANSITION_NO_SUPPORT: 不支持的订单状态流转---检查订单状态  \n" +
            "ORDER_UPDATE_FAIL: 订单更新失败---订单状态乐观锁，检查订单状态  \n" +
            "F999: 渠道响应码未配置内部映射码时，默认返回码---检查渠道响应  \n" +
            "其它：渠道响应码配置的内部映射码")
    @DigestLog(isRecord= true)
    public Result<RevokeAuthorizationResponse> revoke(@RequestBody RevokeAuthorizationRequest request) {
        return authorizationFacade.revoke(request);
    }

    @PostMapping(value = "/inquiryUserInfo")
    @ApiOperation(value = "用户信息查询", notes = "授权用户信息查询接口  \n" +
            "SYSTEM_BUSY: 通用内部未知错误---需要定位系统异常  \n" +
            "REQUEST_PARAM_INVALID: 参数错误---检查入参  \n")
    public Result<InquiryAuthorizationUserInfoResponse> inquiryUserInfo(@RequestBody InquiryAuthorizationUserInfoRequest request) {
        return authorizationFacade.inquiryUserInfo(request);
    }
}
