package com.payermax.fin.exchange.consumer;

import com.alibaba.fastjson.JSON;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.fin.exchange.domainservice.entity.RocketMqMessage;
import com.payermax.fin.exchange.service.facade.UpdateOrderFacade;
import com.payermax.fin.exchange.service.request.UpdateCommitOrderRequest;
import com.payermax.infra.ionia.rocketmq.handler.BaseMqMessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;

/**
 * 订单更新消息监听
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = "${rocketMq.topic.channel.exchange.update.delay.topic}", consumerGroup = "${rocketmq.consumer.update.group_id}")
public class UpdateMessageListener extends BaseMqMessageListener<RocketMqMessage> implements RocketMQListener<RocketMqMessage> {

    @Autowired
    private UpdateOrderFacade updateOrderFacade;

    @Override
    protected void handleMessage(RocketMqMessage message) throws Exception {
        if (message.getData() == null) {
            return;
        }
        // 参数转换
        UpdateCommitOrderRequest requestDO = JSON.parseObject(JSON.toJSONString(message.getData()), UpdateCommitOrderRequest.class);
        if (requestDO == null) {
            return;
        }
        Result<Long> longResult = updateOrderFacade.updateCommitOrder(Collections.singletonList(requestDO));
        log.info("update result = {}", JSON.toJSONString(longResult));
    }

    @Override
    protected void overMaxRetryTimesMessage(RocketMqMessage message) {
        log.error("The order update mq message processing fail over max retry times, message = {}", JSON.toJSONString(message));
    }

    @Override
    public void onMessage(RocketMqMessage message) {
        super.dispatchMessage(message);
    }
}
