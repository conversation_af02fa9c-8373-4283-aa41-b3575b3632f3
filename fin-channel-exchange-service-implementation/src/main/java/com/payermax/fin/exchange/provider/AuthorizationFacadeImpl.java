package com.payermax.fin.exchange.provider;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.fin.exchange.assembler.FacadeAssembler;
import com.payermax.fin.exchange.domain.inquiry.InquiryAuthRequestDO;
import com.payermax.fin.exchange.domain.inquiry.InquiryAuthResponseDO;
import com.payermax.fin.exchange.domain.inquiry.InquiryAuthUserInfoRequestDO;
import com.payermax.fin.exchange.domain.inquiry.InquiryAuthUserInfoResponseDO;
import com.payermax.fin.exchange.domain.trans.auth.AuthRequestOrderDO;
import com.payermax.fin.exchange.domain.trans.auth.AuthResponseOrderDO;
import com.payermax.fin.exchange.domain.trans.auth.RevokeAuthRequestOrderDO;
import com.payermax.fin.exchange.domain.trans.auth.RevokeAuthResponseOrderDO;
import com.payermax.fin.exchange.domainservice.IDomainService;
import com.payermax.fin.exchange.integration.rpc.proxy.ContextClientProxy;
import com.payermax.fin.exchange.service.context.AuthorizationRequestContext;
import com.payermax.fin.exchange.service.facade.AuthorizationFacade;
import com.payermax.fin.exchange.service.request.ApplyAuthorizationRequest;
import com.payermax.fin.exchange.service.request.InquiryAuthorizationRequest;
import com.payermax.fin.exchange.service.request.InquiryAuthorizationUserInfoRequest;
import com.payermax.fin.exchange.service.request.RevokeAuthorizationRequest;
import com.payermax.fin.exchange.service.response.ApplyAuthorizationResponse;
import com.payermax.fin.exchange.service.response.InquiryAuthorizationResponse;
import com.payermax.fin.exchange.service.response.InquiryAuthorizationUserInfoResponse;
import com.payermax.fin.exchange.service.response.RevokeAuthorizationResponse;
import com.payermax.infra.ionia.log.digest.core.annotation.DigestLog;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.config.annotation.Method;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 授权服务
 *
 * <AUTHOR>
 * @date 2021/12/7 17:57
 */
@DubboService(version = "${exchange.dubbo.api.version}", group = "${exchange.dubbo.api.group-id:}", methods = {
        @Method(name = "apply", timeout = 13000),
        @Method(name = "revoke", timeout = 13000)
})
@Service
public class AuthorizationFacadeImpl implements AuthorizationFacade {

    @Autowired
    private FacadeAssembler facadeAssembler;

    @Autowired
    private ContextClientProxy contextClientProxy;

    @Autowired
    private IDomainService<AuthRequestOrderDO, AuthResponseOrderDO> authDomainService;

    @Autowired
    private IDomainService<RevokeAuthRequestOrderDO, RevokeAuthResponseOrderDO> revokeAuthDomainService;

    @Autowired
    private IDomainService<InquiryAuthRequestDO, InquiryAuthResponseDO> inquiryAuthDomainService;

    @Autowired
    private IDomainService<InquiryAuthUserInfoRequestDO, InquiryAuthUserInfoResponseDO> inquiryAuthUserInfoDomainService;

    @Override
    @DigestLog(isRecord = true)
    public Result<ApplyAuthorizationResponse> apply(ApplyAuthorizationRequest request) {
        // 从上下文查询信息
        AuthorizationRequestContext authRequestContext = contextClientProxy.queryAuthorizationRequest(request.getRequestNo());
        // 请求转换
        AuthRequestOrderDO requestOrder = facadeAssembler.toAuthReqOrder(request, authRequestContext);
        // 调用领域服务
        AuthResponseOrderDO responseOrder = authDomainService.execute(requestOrder);
        // 响应转换
        ApplyAuthorizationResponse response = facadeAssembler.toAuthResp(responseOrder);
        return ResultUtil.success(response);
    }

    @Override
    @DigestLog(isRecord = true)
    public Result<RevokeAuthorizationResponse> revoke(RevokeAuthorizationRequest request) {
        // 请求转换
        RevokeAuthRequestOrderDO requestOrder = facadeAssembler.toRevokeAuthReqOrder(request);
        // 调用领域服务
        RevokeAuthResponseOrderDO responseOrder = revokeAuthDomainService.execute(requestOrder);
        // 响应转换
        RevokeAuthorizationResponse response = facadeAssembler.toRevokeAuthResp(responseOrder);
        return ResultUtil.success(response);
    }

    @Override
    public Result<InquiryAuthorizationResponse> inquiry(InquiryAuthorizationRequest request) {
        // 请求转换
        InquiryAuthRequestDO requestOrder = facadeAssembler.toAuthInquiryReqOrder(request);
        // 调用领域服务
        InquiryAuthResponseDO responseOrder = inquiryAuthDomainService.execute(requestOrder);
        // 响应转换
        InquiryAuthorizationResponse response = facadeAssembler.toAuthInquiryResp(responseOrder);
        return ResultUtil.success(response);
    }

    @Override
    public Result<InquiryAuthorizationUserInfoResponse> inquiryUserInfo(InquiryAuthorizationUserInfoRequest request) {
        // 请求转换
        InquiryAuthUserInfoRequestDO requestOrder = facadeAssembler.toAuthUserInfoInquiryReqOrder(request);
        // 调用领域服务
        InquiryAuthUserInfoResponseDO responseOrder = inquiryAuthUserInfoDomainService.execute(requestOrder);
        // 响应转换
        InquiryAuthorizationUserInfoResponse response = facadeAssembler.toAuthUserInfoInquiryResp(responseOrder);
        return ResultUtil.success(response);
    }
}
