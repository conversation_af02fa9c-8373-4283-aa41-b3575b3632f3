package com.payermax.fin.exchange.config;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/15 10:49
 */
@Component
@Data
public class PayoutAsyncConfig {

    /**
     * 是否支持异步
     */
    @NacosValue(value = "${payout.async.supportAsync:false}", autoRefreshed = true)
    private boolean supportAsync;

    /**
     * k：支付方式类型
     * v：目标机构
     */
    private List<PaymentMethod> supportMethodList;

    /**
     * 不支持的支付方式
     * k：支付方式类型
     * v：目标机构
     */
    private List<PaymentMethod> unSupportMethodList;

    /**
     * k:商户号
     * v:百分比
     */
    private Map<String, Integer> supportMerchantMap;

    @NacosValue(value = "${payout.async.supportMethodStr:}", autoRefreshed = true)
    public void setSupportMethodStr(String supportMethodStr) {
        if (StringUtils.isBlank(supportMethodStr)) {
            return;
        }
        supportMethodList = JSONArray.parseArray(supportMethodStr, PaymentMethod.class);
    }

    @NacosValue(value = "${payout.async.unSupportMethodStr:}", autoRefreshed = true)
    public void setUnSupportMethodStr(String unSupportMethodStr) {
        if (StringUtils.isBlank(unSupportMethodStr)) {
            return;
        }
        unSupportMethodList = JSONArray.parseArray(unSupportMethodStr, PaymentMethod.class);
    }

    @NacosValue(value = "${payout.async.supportMerchantStr:}", autoRefreshed = true)
    public void setSupportMerchantStr(String supportMerchantStr) {
        if (StringUtils.isBlank(supportMerchantStr)) {
            return;
        }
        supportMerchantMap = JSONObject.parseObject(supportMerchantStr, Map.class);
    }

    @Data
    public static class PaymentMethod {

        /**
         * 支付方式类型
         */
        private String paymentMethodType;

        /**
         * 目标机构
         */
        private String targetOrg;


        @Override
        public boolean equals(Object obj) {
            if (this == obj) {
                return true;
            }
            if (obj == null) {
                return false;
            }
            if (getClass() != obj.getClass()) {
                return false;
            }
            PaymentMethod method = (PaymentMethod) obj;
            return StringUtils.equals(method.getPaymentMethodType(), paymentMethodType) && StringUtils.equals(method.getTargetOrg(), targetOrg);
        }
    }
}
