package com.payermax.fin.exchange.provider;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.fin.exchange.share.constants.ShareConstants;
import com.payermax.infra.ionia.log.digest.core.annotation.DigestLog;
import com.payermax.fin.exchange.assembler.FacadeAssembler;
import com.payermax.fin.exchange.common.enums.ExtParamEnum;
import com.payermax.fin.exchange.domain.inquiry.InquiryOrderRequestDO;
import com.payermax.fin.exchange.domain.inquiry.InquiryOrderResponseDO;
import com.payermax.fin.exchange.domain.trans.payout.PayoutRequestOrderDO;
import com.payermax.fin.exchange.domain.trans.payout.PayoutResponseOrderDO;
import com.payermax.fin.exchange.domainservice.IDomainService;
import com.payermax.fin.exchange.integration.rpc.proxy.CardClientProxy;
import com.payermax.fin.exchange.service.facade.TransferFacade;
import com.payermax.fin.exchange.service.request.InquiryTransferRequest;
import com.payermax.fin.exchange.service.request.TransferRequest;
import com.payermax.fin.exchange.service.response.InquiryTransferResponse;
import com.payermax.fin.exchange.service.response.TransferResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 转账服务
 *
 * <AUTHOR>
 * @date 2022/2/16 15:26
 */
@DubboService(version = "${exchange.dubbo.api.version}", group = "${exchange.dubbo.api.group-id:}")
@Service
public class TransferFacadeImpl implements TransferFacade {

    @NacosValue(value = "${transfer.account.encrypt.flag:Y}",autoRefreshed = true)
    private String accountEncryptFlag;

    @Autowired
    private FacadeAssembler facadeAssembler;

    @Autowired
    private CardClientProxy cardClientProxy;

    @Autowired
    private IDomainService<PayoutRequestOrderDO, PayoutResponseOrderDO> payoutDomainService;

    @Autowired
    private IDomainService<InquiryOrderRequestDO, InquiryOrderResponseDO> inquiryDomainService;

    @Override
    @DigestLog(isRecord = true)
    public Result<TransferResponse> transfer(TransferRequest request) {
        paramCheck(request);

        PayoutRequestOrderDO requestOrder;
        // 判断是否需要进行账号加密
        boolean isNeedAccountEncrypt = ShareConstants.YES_FLAG.equals(accountEncryptFlag);
        if (isNeedAccountEncrypt) {
            String payeeIBanCardIdentifierNo = cardClientProxy.addCardAssetInfo(request.getPayeeIBanAccount(), request.getPayerAccountName());
            String payerCardIdentifierNo = cardClientProxy.addCardAssetInfo(request.getPayerAccount(), request.getPayerAccountName());
            String payeeCardIdentifierNo = cardClientProxy.addCardAssetInfo(request.getPayeeAccount(), request.getPayeeAccountName());
            // 请求转换
            requestOrder = facadeAssembler.toTransferReq(request);
            requestOrder.getExtParams().put(ExtParamEnum.PAYEE_IBAN_CARD_IDENTIFIER_NO.getCode(), payeeIBanCardIdentifierNo);
            requestOrder.getExtParams().put(ExtParamEnum.PAYER_CARD_IDENTIFIER_NO.getCode(), payerCardIdentifierNo);
            requestOrder.getExtParams().put(ExtParamEnum.PAYEE_CARD_IDENTIFIER_NO.getCode(), payeeCardIdentifierNo);
        } else {
            // 请求转换
            requestOrder = facadeAssembler.toTransferReq(request);
        }
        // 调用领域服务
        PayoutResponseOrderDO responseOrder = payoutDomainService.execute(requestOrder);
        // 响应转换
        return ResultUtil.success(facadeAssembler.toTransferResp(responseOrder));
    }

    @Override
    public Result<InquiryTransferResponse> inquiry(InquiryTransferRequest request) {
        // 请求转换
        InquiryOrderRequestDO requestOrder = facadeAssembler.toInquiryTransferReq(request);
        // 调用领域服务
        InquiryOrderResponseDO responseOrder = inquiryDomainService.execute(requestOrder);
        // 响应转换
        return ResultUtil.success(facadeAssembler.toInquiryTransferResp(responseOrder));
    }

    /**
     * 校验必填账号二选一
     *
     * @param request
     */
    private void paramCheck(TransferRequest request) {
        if (StringUtils.isBlank(request.getPayeeIBanAccount()) && StringUtils.isBlank(request.getPayeeAccount())) {
            throw new IllegalStateException("payeeIBanAccount and payeeAccount is null");
        }
    }


}