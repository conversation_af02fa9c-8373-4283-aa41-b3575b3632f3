package com.payermax.fin.exchange.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.ValueFilter;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.fin.exchange.common.enums.CacheTypeEnum;
import com.payermax.fin.exchange.dal.entity.ChannelMerchantEntity;
import com.payermax.fin.exchange.domain.common.RefreshCacheRequestDO;
import com.payermax.fin.exchange.domainservice.cache.CacheManager;
import com.payermax.fin.exchange.domainservice.cache.CacheQueryer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/1/24 21:14
 */
@RestController
@Slf4j
@RequestMapping(value = "/localCache")
public class LocalCacheController {

    private Map<String, CacheQueryer> cacheQueryerMap = new HashMap<>();

    @Autowired
    private CacheManager cacheManager;

    @Autowired
    public void setCacheQueryerMap(List<CacheQueryer> cacheQueryers) {
        for (CacheQueryer<?> cacheQueryer : cacheQueryers) {
            this.cacheQueryerMap.put(cacheQueryer.supportedCacheType().name(), cacheQueryer);
        }
    }

    /**
     * 刷新本机器所有本地缓存
     *
     * @return
     */
    @GetMapping(value = "/refreshAll")
    public Result<String> refreshAll() {
        cacheManager.refreshAll();
        return ResultUtil.success();
    }

    /**
     * 刷新本机器指定类型缓存
     *
     * @param cacheType
     * @param ids
     * @return
     */
    @GetMapping(value = "/refresh")
    public Result<String> refresh(@RequestParam("cacheType") String cacheType,
                                  @RequestParam(value = "ids", required = false) String ids,
                                  @RequestParam(value = "queryNewest", required = false) Boolean queryNewest) {
        if (StringUtils.isBlank(cacheType)) {
            return ResultUtil.fail("FAIL");
        }

        RefreshCacheRequestDO request = new RefreshCacheRequestDO();
        request.setCacheType(cacheType);
        request.setIds(StrUtil.split(ids, ','));
        request.setQueryNewest(queryNewest);
        // 刷新缓存
        cacheManager.refresh(request);
        return ResultUtil.success();
    }

    /**
     * 发送刷新指定类型缓存MQ（刷新所有机器）
     *
     * @param cacheType
     * @param ids
     * @return
     */
    @GetMapping(value = "/refresh/mq")
    public Result<String> refreshMq(@RequestParam("cacheType") String cacheType, @RequestParam(value = "ids", required = false) String ids) {
        if (StringUtils.isBlank(cacheType)) {
            return ResultUtil.fail("FAIL");
        }

        // 通过MQ刷新缓存
        cacheManager.refreshMq(cacheType, StrUtil.split(ids, ','));
        return ResultUtil.success();
    }

    @GetMapping(value = "/getCache")
    public Result getCache(@RequestParam("cacheType") String cacheType, String businessType, @RequestParam("id") String id) {
        if (StringUtils.isBlank(cacheType) || StringUtils.isBlank(id)) {
            return ResultUtil.fail("FAIL");
        }
        CacheQueryer cacheQueryer = cacheQueryerMap.get(cacheType);
        if (cacheQueryer == null) {
            return ResultUtil.fail("FAIL");
        }
        Object cacheVal = cacheQueryer.getCacheByKey(businessType, id);
        // 不是渠道缓存，则直接返回
        if (!CacheTypeEnum.CHANNEL.name().equals(cacheType)) {
            return ResultUtil.success(cacheVal);
        }
        // 如果是渠道缓存，则进行特殊处理，对敏感字段进行脱敏
        String val = JSON.toJSONString(cacheVal, (ValueFilter) (object, name, value) -> {
            if (object instanceof ChannelMerchantEntity) {
                if(name.equals("accountJson")) {
                    return "存在敏感信息，脱敏返回";
                }
            }
            return value;
        });
        return ResultUtil.success(val);
    }

}
