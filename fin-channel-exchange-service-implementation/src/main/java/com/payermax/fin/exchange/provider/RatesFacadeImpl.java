package com.payermax.fin.exchange.provider;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.fin.exchange.assembler.FacadeAssembler;
import com.payermax.fin.exchange.domain.common.CommonGateRequestDO;
import com.payermax.fin.exchange.domain.common.CommonGateResponseDO;
import com.payermax.fin.exchange.domain.common.OrderCompensateRequestDO;
import com.payermax.fin.exchange.domain.inquiry.InquiryForexTradeRequestDO;
import com.payermax.fin.exchange.domain.inquiry.InquiryForexTradeResponseDO;
import com.payermax.fin.exchange.domain.inquiry.InquirySpotRateRequestDO;
import com.payermax.fin.exchange.domain.inquiry.InquirySpotRateResponseDO;
import com.payermax.fin.exchange.domain.trans.ForexTradeRequestDO;
import com.payermax.fin.exchange.domainservice.IDomainService;
import com.payermax.fin.exchange.service.facade.RatesFacade;
import com.payermax.fin.exchange.service.request.*;
import com.payermax.fin.exchange.service.response.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 报价服务
 *
 * <AUTHOR>
 * @date 2022/4/8 11:30
 */
@DubboService(version = "${exchange.dubbo.api.version}", group = "${exchange.dubbo.api.group-id:}")
@Service
public class RatesFacadeImpl implements RatesFacade {

    @Autowired
    private FacadeAssembler facadeAssembler;

    @Autowired
    private IDomainService<InquirySpotRateRequestDO, InquirySpotRateResponseDO> inquirySpotRateDomainService;

    @Autowired
    private IDomainService<CommonGateRequestDO, CommonGateResponseDO> commonGateDomainService;

    @Autowired
    private IDomainService<OrderCompensateRequestDO, CommonGateResponseDO> orderCompensateDomainService;


    @Autowired
    private IDomainService<InquiryForexTradeRequestDO, InquiryForexTradeResponseDO> inquiryForexTradeDomainService;

    @Autowired
    private IDomainService<ForexTradeRequestDO, CommonGateResponseDO> forexTradeDomainService;

    @Override
    public Result<InquirySpotRateResponse> inquirySpotRate(InquirySpotRateRequest request) {
        // 请求转换
        InquirySpotRateRequestDO requestInfo = facadeAssembler.toInquirySpotRateReq(request);
        // 调用领域服务
        InquirySpotRateResponseDO responseOrder = inquirySpotRateDomainService.execute(requestInfo);
        // 响应转换
        return ResultUtil.success(facadeAssembler.toInquirySpotRateResp(responseOrder));
    }

    @Override
    public Result<InquiryForwardRateResponse> inquiryForwardRate(InquiryForwardRateRequest request) {
        //校验客户号和币种对二选一
        paramCheck(request);
        // 请求转换
        CommonGateRequestDO requestInfo = facadeAssembler.toInquiryForwardRateReq(request);
        // 调用领域服务
        CommonGateResponseDO responseOrder = commonGateDomainService.execute(requestInfo);
        // 响应转换
        return ResultUtil.success(facadeAssembler.toInquiryForwardRateResp(responseOrder));
    }

    @Override
    public Result<HedgeAdviceResponse> hedgeAdvice(HedgeAdviceRequest request) {
        // 请求转换
        CommonGateRequestDO requestInfo = facadeAssembler.toHedgeAdviceReq(request);
        // 调用领域服务
        CommonGateResponseDO responseOrder = commonGateDomainService.execute(requestInfo);
        // 响应转换
        return ResultUtil.success(facadeAssembler.toHedgeAdviceResp(responseOrder));
    }

    @Override
    public Result<TradeAdviceResponse> tradeAdvice(TradeAdviceRequest request) {
        // 请求转换
        CommonGateRequestDO requestInfo = facadeAssembler.toTradeAdviceReq(request);
        // 调用领域服务
        CommonGateResponseDO responseOrder = commonGateDomainService.execute(requestInfo);
        // 响应转换
        return ResultUtil.success(facadeAssembler.toTradeAdviceResp(responseOrder));
    }

    @Override
    public Result<HedgeAdviceResponse> hedgeCompensate(OrderCompensateRequest request) {
        // 请求转换
        OrderCompensateRequestDO requestInfo = facadeAssembler.toOrderCompensateReq(request);
        // 调用领域服务
        CommonGateResponseDO response = orderCompensateDomainService.execute(requestInfo);
        // 响应转换
        return ResultUtil.success(facadeAssembler.toHedgeAdviceResp(response));
    }

    @Override
    public Result<TradeAdviceResponse> tradeCompensate(OrderCompensateRequest request) {
        // 请求转换
        OrderCompensateRequestDO requestInfo = facadeAssembler.toOrderCompensateReq(request);
        // 调用领域服务
        CommonGateResponseDO response = orderCompensateDomainService.execute(requestInfo);
        // 响应转换
        return ResultUtil.success(facadeAssembler.toTradeAdviceResp(response));
    }

    @Override
    public Result<ForexTradeResponse> forexTrade(ForexTradeRequest request) {
        // 请求转换
        ForexTradeRequestDO requestInfo = facadeAssembler.toForexTradeReq(request);
        // 调用领域服务
        CommonGateResponseDO responseOrder = forexTradeDomainService.execute(requestInfo);
        // 响应转换
        return ResultUtil.success(facadeAssembler.toForexTradeResp(responseOrder));
    }

    @Override
    public Result<ForexTradeResponse> inquiryForexTrade(InquiryForexTradeRequest request) {
        // 请求转换
        InquiryForexTradeRequestDO requestInfo = facadeAssembler.toInquiryForexTradeReq(request);
        // 调用领域服务
        InquiryForexTradeResponseDO responseOrder = inquiryForexTradeDomainService.execute(requestInfo);
        // 响应转换
        return ResultUtil.success(facadeAssembler.toInquiryForexTradeResp(responseOrder));
    }

    @Override
    public Result<ForexTradeResponse> forexTradeCompensate(OrderCompensateRequest request) {
        // 请求转换
        OrderCompensateRequestDO requestInfo = facadeAssembler.toOrderCompensateReq(request);
        // 调用领域服务
        CommonGateResponseDO response = orderCompensateDomainService.execute(requestInfo);
        // 响应转换
        return ResultUtil.success(facadeAssembler.toForexTradeResp(response));
    }

    /**
     * 校验客户号和币种对二选一
     *
     * @param request
     */
    private void paramCheck(InquiryForwardRateRequest request) {
        if (CollectionUtils.isEmpty(request.getCurrencyPairs()) && StringUtils.isBlank(request.getCustomerNo())) {
            throw new IllegalStateException("currencyPairs and customerNo is null");
        }
    }

}
