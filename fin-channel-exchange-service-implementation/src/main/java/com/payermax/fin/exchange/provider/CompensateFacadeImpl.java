package com.payermax.fin.exchange.provider;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.fin.exchange.assembler.FacadeAssembler;
import com.payermax.fin.exchange.domain.common.AccelerateCompensationRequestDO;
import com.payermax.fin.exchange.domain.common.CompensateStatusRequestDO;
import com.payermax.fin.exchange.domainservice.IDomainService;
import com.payermax.fin.exchange.service.facade.CompensateFacade;
import com.payermax.fin.exchange.service.request.AccelerateCompensationRequest;
import com.payermax.fin.exchange.service.request.CompensateStatusRequest;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/4/28 16:51
 */
@DubboService(version = "${exchange.dubbo.api.version}", group = "${exchange.dubbo.api.group-id:}")
@Service
public class CompensateFacadeImpl implements CompensateFacade {

    @Autowired
    private FacadeAssembler facadeAssembler;

    @Autowired
    private IDomainService<AccelerateCompensationRequestDO, Boolean> accelerateCompensationService;

    @Autowired
    private IDomainService<CompensateStatusRequestDO, String> compensateStatusDomainService;

    @Override
    public Result<Boolean> accelerateCompensation(AccelerateCompensationRequest request) {
        // 入参转换
        AccelerateCompensationRequestDO requestDO = facadeAssembler.toAccelerateCompensationRequest(request);
        // 调用领域服务
        Boolean response = accelerateCompensationService.execute(requestDO);
        // 响应转换
        return ResultUtil.success(response);
    }

    @Override
    public Result<String> compensateOrderStatus(CompensateStatusRequest request) {
        // 入参转换
        CompensateStatusRequestDO requestDO = facadeAssembler.toCompensationStatusRequestDO(request);
        // 调用领域服务
        String response = compensateStatusDomainService.execute(requestDO);
        // 响应转换
        return ResultUtil.success(response);
    }

}
