package com.payermax.fin.exchange.provider;

import com.payermax.fin.exchange.assembler.FacadeAssembler;
import com.payermax.fin.exchange.domain.callback.CallbackRequestDO;
import com.payermax.fin.exchange.domainservice.IDomainService;
import com.payermax.fin.exchange.domainservice.rule.channelgatewaygary.ChannelGatewayGrayExecutor;
import com.payermax.fin.exchange.service.facade.CallbackFacade;
import com.payermax.fin.exchange.service.request.CallbackNotifyRequest;
import com.payermax.infra.ionia.log.digest.core.annotation.DigestLog;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.ResultUtil;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.config.annotation.Method;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 回调服务
 *
 * <AUTHOR>
 * @date 2021/12/22 14:56
 */
@DubboService(version = "${exchange.dubbo.api.version}", group = "${exchange.dubbo.api.group-id:}", methods = {
        @Method(name = "callbackNotify", timeout = 10000)
})
@Service
public class CallbackFacadeImpl implements CallbackFacade {

    @Autowired
    private FacadeAssembler facadeAssembler;

    @Autowired
    private IDomainService<CallbackRequestDO, String> callbackDomainService;

    @Override
    @DigestLog(isRecord = true)
    public Result<String> callbackNotify(CallbackNotifyRequest request) {
        // 入参转换
        CallbackRequestDO requestOrder = facadeAssembler.toCallbackReqOrder(request);
        // 调用领域服务
        String response = callbackDomainService.execute(requestOrder);
        // 响应转换
        return ResultUtil.success(response);
    }
}
