package com.payermax.fin.exchange.controller;

import com.payermax.fin.exchange.domainservice.manage.BaseManage;
import com.payermax.fin.exchange.service.request.UpdatePayoutRequest;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.infra.ionia.log.digest.core.annotation.DigestLog;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2021/10/18 19:31
 */
@RestController
@Slf4j
@RequestMapping(value = "/compatibleWithdraw")
public class CompatibleWithdrawController {

    @Autowired
    private BaseManage<UpdatePayoutRequest, Integer> updatePayoutManage;

    @PostMapping(value = "/update")
    @ApiOperation(value = "update API", notes = "Use this interface to update the withdraw transaction info.")
    @DigestLog(isRecord = true)
    public Result<Integer> update(@RequestBody UpdatePayoutRequest request) {
        return updatePayoutManage.execute(request);
    }
}
