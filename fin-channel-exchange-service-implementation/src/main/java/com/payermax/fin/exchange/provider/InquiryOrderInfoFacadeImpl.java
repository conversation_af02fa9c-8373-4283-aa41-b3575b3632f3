package com.payermax.fin.exchange.provider;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.fin.exchange.assembler.FacadeAssembler;
import com.payermax.fin.exchange.domain.inquiry.InquiryCommitOrderRequestDO;
import com.payermax.fin.exchange.domain.inquiry.InquiryCommitOrderResponseDO;
import com.payermax.fin.exchange.domain.inquiry.InquiryNewTargetMerchantRequestDO;
import com.payermax.fin.exchange.domain.inquiry.InquiryNewTargetMerchantResponseDO;
import com.payermax.fin.exchange.domainservice.IDomainService;
import com.payermax.fin.exchange.domainservice.manage.AbstractBaseManage;
import com.payermax.fin.exchange.service.facade.InquiryOrderInfoFacade;
import com.payermax.fin.exchange.service.request.InquiryCommitOrderRequest;
import com.payermax.fin.exchange.service.request.InquiryNewTargetMerchantRequest;
import com.payermax.fin.exchange.service.request.InquiryOrderInfoRequest;
import com.payermax.fin.exchange.service.response.InquiryCommitOrderResponse;
import com.payermax.fin.exchange.service.response.InquiryNewTargetMerchantResponse;
import com.payermax.fin.exchange.service.response.InquiryOrderInfoResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@DubboService(version = "${exchange.dubbo.api.version}", group = "${exchange.dubbo.api.group-id:}")
@Service
public class InquiryOrderInfoFacadeImpl implements InquiryOrderInfoFacade {

    @Autowired
    private FacadeAssembler facadeAssembler;

    @Autowired
    private AbstractBaseManage<InquiryOrderInfoRequest, InquiryOrderInfoResponse> inquiryOrderInfoManageImpl;

    @Autowired
    private IDomainService<InquiryCommitOrderRequestDO, List<InquiryCommitOrderResponseDO>> inquiryCommitOrderDomainService;

    @Autowired
    private IDomainService<InquiryNewTargetMerchantRequestDO, InquiryNewTargetMerchantResponseDO> inquiryNewTargetMerchantDomainService;

    @Override
    public Result<InquiryOrderInfoResponse> inquiryOrderInfo(InquiryOrderInfoRequest inquiryOrderInfoRequest) {
        return inquiryOrderInfoManageImpl.execute(inquiryOrderInfoRequest);
    }

    @Override
    public Result<List<InquiryCommitOrderResponse>> inquiryCommitOrder(InquiryCommitOrderRequest inquiryCommitOrderRequest) {
        InquiryCommitOrderRequestDO requestDO = facadeAssembler.toInquiryCommitOrderReq(inquiryCommitOrderRequest);
        List<InquiryCommitOrderResponseDO> responseDOList = inquiryCommitOrderDomainService.execute(requestDO);
        List<InquiryCommitOrderResponse> responseList = new ArrayList<>();
        responseDOList.forEach(responseOrder -> {
            responseList.add(facadeAssembler.toInquiryCommitOrderResp(responseOrder));
        });
        return ResultUtil.success(responseList);
    }

    @Override
    public Result<InquiryNewTargetMerchantResponse> inquiryTargetMerchant(InquiryNewTargetMerchantRequest inquiryNewTargetMerchantRequest) {
        InquiryNewTargetMerchantRequestDO requestDO = facadeAssembler.toInquiryTargetMerchantReq(inquiryNewTargetMerchantRequest);
        InquiryNewTargetMerchantResponseDO responseDO = inquiryNewTargetMerchantDomainService.execute(requestDO);
        return ResultUtil.success(facadeAssembler.toInquiryTargetMerchantResp(responseDO));
    }

}
