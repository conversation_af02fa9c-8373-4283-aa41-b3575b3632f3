package com.payermax.fin.exchange.condition;

import cn.hutool.core.util.ArrayUtil;
import com.payermax.fin.exchange.common.enums.EnvEnum;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

/**
 * http调用condition
 *
 * <AUTHOR>
 * @date 2024/8/19 15:55
 */
public class OnHttpCondition implements Condition {

    /**
     * 仅local、dev、test、uat环境支持开启http请求；staging环境用于此功能的验证，也不开启http
     */
    private static final String[] availableProfiles = new String[] {EnvEnum.LOCAL.getEnv(), EnvEnum.DEV.getEnv(), EnvEnum.DEV_NEW.getEnv(), EnvEnum.TEST.getEnv(), EnvEnum.UAT.getEnv()};

    @Override
    public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
        String[] activeProfiles = context.getEnvironment().getActiveProfiles();
        if (activeProfiles == null) {
            return false;
        }
        // 允许http调用的profile
        return ArrayUtil.containsAny(activeProfiles, availableProfiles);
    }
}
