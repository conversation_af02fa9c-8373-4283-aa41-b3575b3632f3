package com.payermax.fin.exchange.controller;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.fin.exchange.assembler.FacadeAssembler;
import com.payermax.fin.exchange.domain.inquiry.FundingChannelInfoResponseDO;
import com.payermax.fin.exchange.domainservice.IDomainService;
import com.payermax.fin.exchange.service.response.FundingChannelInfoResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2023/7/4 10:52
 **/
@RestController
@Slf4j
@RequestMapping(value = "/channel")
@Api(value = "提供给 payment-channel 服务的接口 ，查询资金渠道信息", tags = "资金渠道")
public class FundingChannelController {
    
    @Autowired
    private FacadeAssembler facadeAssembler;
    @Autowired
    private IDomainService<String, FundingChannelInfoResponseDO> fundingChannelInfoDomainService;
    
    @GetMapping(value = "/info")
    @ApiOperation(value = "资金渠道信息", notes = "资金渠道信息")
    public Result<FundingChannelInfoResponse> inquiryFundingChannelInfo(String channelCode) {
        FundingChannelInfoResponseDO fundingChannelInfoResponseDO = fundingChannelInfoDomainService.execute(channelCode);
        return ResultUtil.success(facadeAssembler.toFundingChannelInfoResponse(fundingChannelInfoResponseDO));
    }
}
