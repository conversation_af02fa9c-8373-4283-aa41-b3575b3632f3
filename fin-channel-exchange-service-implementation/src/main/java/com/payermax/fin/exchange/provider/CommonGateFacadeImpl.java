package com.payermax.fin.exchange.provider;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.fin.exchange.assembler.FacadeAssembler;
import com.payermax.fin.exchange.domain.common.CommonGateRequestDO;
import com.payermax.fin.exchange.domain.common.CommonGateResponseDO;
import com.payermax.fin.exchange.domainservice.IDomainService;
import com.payermax.fin.exchange.service.facade.CommonGateFacade;
import com.payermax.fin.exchange.service.request.CommonGateRequest;
import com.payermax.fin.exchange.service.response.CommonGateResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 通用网关转发服务
 *
 * <AUTHOR>
 * @date 2022/5/26 11:30
 */
@DubboService(version = "${exchange.dubbo.api.version}", group = "${exchange.dubbo.api.group-id:}")
@Service
public class CommonGateFacadeImpl implements CommonGateFacade {

    @Autowired
    private FacadeAssembler facadeAssembler;

    @Autowired
    private IDomainService<CommonGateRequestDO, CommonGateResponseDO> commonGateDomainService;

    @Override
    public Result<CommonGateResponse> commonGateForward(CommonGateRequest request) {
        // 请求转换
        CommonGateRequestDO requestInfo = facadeAssembler.toCommonGateReq(request);
        // 调用通用网关领域服务
        CommonGateResponseDO response = commonGateDomainService.execute(requestInfo);
        // 响应转换
        return ResultUtil.success(facadeAssembler.toCommonGateResp(response));
    }

}
