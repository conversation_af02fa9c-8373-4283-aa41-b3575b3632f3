package com.payermax.fin.exchange.util;

import com.payermax.fin.exchange.config.PayoutAsyncConfig;
import com.payermax.fin.exchange.domain.trans.payout.PayoutRequestOrderDO;
import com.payermax.fin.exchange.share.constants.ShareConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.math.RandomUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/7/15 11:34
 */
@Component
@Slf4j
public class PayoutAsyncUtil {

    private static PayoutAsyncConfig payoutAsyncConfig;

    private static final String SUPPORT_ALL = "ALL";


    public static boolean supportAsync(PayoutRequestOrderDO requestOrder) {
        try {
            if (Objects.isNull(payoutAsyncConfig) || !payoutAsyncConfig.isSupportAsync()
                    || Objects.isNull(payoutAsyncConfig.getSupportMethodList()) || payoutAsyncConfig.getSupportMethodList().isEmpty()
                    || Objects.isNull(payoutAsyncConfig.getSupportMerchantMap()) || payoutAsyncConfig.getSupportMerchantMap().isEmpty()) {
                return false;
            }

            boolean supportMethod = false;
            String paymentMethodType = requestOrder.getPaymentMethod().getPaymentMethodType();
            String targetOrg = requestOrder.getPaymentMethod().getTargetOrg();
            PayoutAsyncConfig.PaymentMethod method = new PayoutAsyncConfig.PaymentMethod();
            method.setPaymentMethodType(paymentMethodType);
            method.setTargetOrg(targetOrg);
            PayoutAsyncConfig.PaymentMethod all = new PayoutAsyncConfig.PaymentMethod();
            all.setPaymentMethodType(ShareConstants.ALL);
            all.setTargetOrg(ShareConstants.ALL);
            List<PayoutAsyncConfig.PaymentMethod> paymentMethods = Arrays.asList(method, all);
            if (CollectionUtils.isNotEmpty(payoutAsyncConfig.getUnSupportMethodList()) &&
                    CollectionUtils.containsAny(payoutAsyncConfig.getUnSupportMethodList(), paymentMethods)) {
                return false;
            }
            supportMethod = CollectionUtils.containsAny(payoutAsyncConfig.getSupportMethodList(), paymentMethods);
            if (!supportMethod) {
                return false;
            }
            String merchantNo = requestOrder.getOrderInfo().getMerchantNo();
            boolean supportMerchant = payoutAsyncConfig.getSupportMerchantMap().containsKey(merchantNo);
            boolean supportAllMerchant = payoutAsyncConfig.getSupportMerchantMap().containsKey(SUPPORT_ALL);
            if (!supportMerchant && !supportAllMerchant) {
                return false;
            }
            int percentage = supportAllMerchant ? MapUtils.getInteger(payoutAsyncConfig.getSupportMerchantMap(), SUPPORT_ALL) :
                    MapUtils.getInteger(payoutAsyncConfig.getSupportMerchantMap(), merchantNo);
            return percentage >= RandomUtils.nextInt(100);
        } catch (Exception e) {
            log.error("异步出款配置异常", e);
            return false;
        }
    }

    @Autowired
    public void setPayoutAsyncConfig(PayoutAsyncConfig payoutAsyncConfig) {
        PayoutAsyncUtil.payoutAsyncConfig = payoutAsyncConfig;
    }
}

