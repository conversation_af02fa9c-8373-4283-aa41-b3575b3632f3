package com.payermax.fin.exchange.sentinel;

import com.alibaba.csp.sentinel.*;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.common.lang.util.BusinessExceptionUtil;
import com.payermax.fin.exchange.common.enums.ErrorCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.rpc.*;
import org.springframework.stereotype.Component;

import java.util.List;

import static org.apache.dubbo.common.constants.CommonConstants.CONSUMER;

/**
 * sentinel dubbo consumer处理器
 *
 * <AUTHOR>
 * @date 2025/1/6 19:02
 */
@Slf4j
@Component
@Activate(group = CONSUMER)
public class SentinelDubboConsumerFilter extends SentinelDubboBaseFilter {

    private String prefix = "dubbo:consumer:";

    private static List<String> allowedServiceList;

    @NacosValue(value = "${sentinel.dubbo.consumer.allowed.service:}", autoRefreshed = true)
    public void setAllowedServiceList(List<String> allowedServiceList) {
        this.allowedServiceList = allowedServiceList;
    }

    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        Entry entry = null;
        // 构建资源名称
        String resourceName = buildResourceName(invocation);

        try {
            // 如果此服务需要sentinel监控，则
            if (CollectionUtils.isNotEmpty(allowedServiceList) && allowedServiceList.contains(resourceName)) {
                resourceName = String.format("%s%s", prefix, resourceName);
                entry = SphU.entry(resourceName, ResourceTypeConstants.COMMON_RPC, EntryType.OUT);
            }

            // 执行调用
            Result result = invoker.invoke(invocation);

            if (result.hasException()) {
                Tracer.traceEntry(result.getException(), entry);
            }
            return result;
        } catch (BlockException e) {
            log.error("{} was sentinel blocked. blockedRule = {}", resourceName, JSON.toJSONString(e.getRule()));
            return AsyncRpcResult.newDefaultAsyncResult(BusinessExceptionUtil.error(ErrorCodeEnum.SENTINEL_BLOCK_EXCEPTION.getCode(), ErrorCodeEnum.SENTINEL_BLOCK_EXCEPTION.getMsg()), invocation);
        } catch (RpcException e) {
            Tracer.traceEntry(e, entry);
            throw e;
        } finally {
            if (entry != null) {
                entry.exit();
            }
        }
    }
}
