package com.payermax.fin.exchange.controller;

import com.payermax.fin.exchange.service.facade.CallbackFacade;
import com.payermax.fin.exchange.service.request.CallbackNotifyRequest;
import com.payermax.infra.ionia.log.digest.core.annotation.DigestLog;
import com.payermax.common.lang.model.dto.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * callback controller
 *
 * <AUTHOR>
 * @date 2021/8/27 15:58
 */
@RestController
@Slf4j
@RequestMapping(value = "/callback")
@Api(value = "回调服务", tags = "回调服务")
public class CallbackController {

    @Autowired
    private CallbackFacade callbackFacade;

    @RequestMapping(value = "/notify", method = RequestMethod.POST)
    @ApiOperation(value = "回调通知", notes = "外部渠道订单状态变更回调通知  \n" +
            "SYSTEM_BUSY: 通用内部未知错误---需要定位系统异常  \n" +
            "REQUEST_PARAM_INVALID: 参数错误---检查入参  \n" +
            "STATE_TRANSITION_NO_SUPPORT: 不支持的订单状态流转---检查订单状态  \n" +
            "ORDER_UPDATE_FAIL: 订单更新失败---订单状态乐观锁，检查订单状态")
    @DigestLog(isRecord = true)
    public Result<String> callbackNotify(@RequestBody CallbackNotifyRequest request) {
        return callbackFacade.callbackNotify(request);
    }
}
