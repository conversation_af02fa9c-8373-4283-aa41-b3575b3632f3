package com.payermax.fin.exchange.controller;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 健康检查
 *
 * <AUTHOR>
 * @date 2021/7/29 15:59
 */
@RestController
public class HealthController {

    @RequestMapping(value = "/", method = RequestMethod.GET)
    @ResponseBody
    public String heartBeat() {
        return "working";
    }

}
