package com.payermax.fin.exchange.controller;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.fin.exchange.condition.OnHttpCondition;
import com.payermax.fin.exchange.service.facade.TransferFacade;
import com.payermax.fin.exchange.service.request.InquiryTransferRequest;
import com.payermax.fin.exchange.service.request.TransferRequest;
import com.payermax.fin.exchange.service.response.InquiryTransferResponse;
import com.payermax.fin.exchange.service.response.TransferResponse;
import com.payermax.infra.ionia.log.digest.core.annotation.DigestLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/2/16 16:59
 */
@RestController
@Slf4j
@RequestMapping(value = "/transfer")
@Conditional(OnHttpCondition.class)
public class TransferController {

    @Autowired
    private TransferFacade transferFacade;

    /**
     * 转账申请
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/transfer", method = RequestMethod.POST)
    @DigestLog(isRecord = true)
    public Result<TransferResponse> transfer(@RequestBody TransferRequest request) {
        return transferFacade.transfer(request);
    }

    /**
     * 查询出款单信息
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/inquiry", method = RequestMethod.POST)
    Result<InquiryTransferResponse> inquiry(@RequestBody InquiryTransferRequest request) {
        return transferFacade.inquiry(request);
    }

}
