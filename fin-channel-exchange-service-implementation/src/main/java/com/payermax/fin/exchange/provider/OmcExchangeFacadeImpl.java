package com.payermax.fin.exchange.provider;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.fin.exchange.assembler.FacadeAssembler;
import com.payermax.fin.exchange.common.enums.OrderStatusEnum;
import com.payermax.fin.exchange.domain.common.LockOrderRequestDO;
import com.payermax.fin.exchange.domain.common.LockOrderResponseDO;
import com.payermax.fin.exchange.domain.inquiry.CompensateInquiryRequestDO;
import com.payermax.fin.exchange.domain.inquiry.CompensateNotifyRequestDO;
import com.payermax.fin.exchange.domain.inquiry.CompensateThreeDSNotifyRequestDO;
import com.payermax.fin.exchange.domain.trans.RequestOrderDO;
import com.payermax.fin.exchange.domainservice.IDomainService;
import com.payermax.fin.exchange.service.facade.OmcExchangeFacade;
import com.payermax.fin.exchange.service.request.*;
import com.payermax.fin.exchange.service.response.LockOrderResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * OMC-金融交换订单服务
 *
 * <AUTHOR>
 * @date 2022/1/22 1:50
 */
@DubboService(version = "${exchange.dubbo.api.version}", group = "${exchange.dubbo.api.group-id:}")
@Service
public class OmcExchangeFacadeImpl implements OmcExchangeFacade {

    @Autowired
    private FacadeAssembler facadeAssembler;

    @Autowired
    private IDomainService<CompensateInquiryRequestDO, OrderStatusEnum> compensateInquiryDomainService;

    @Autowired
    private IDomainService<RequestOrderDO, Map> filterChannelRoutingDomainService;

    @Autowired
    private IDomainService<List<CompensateNotifyRequestDO>, Void> compensateNotifyDomainService;

    @Autowired
    private IDomainService<LockOrderRequestDO, LockOrderResponseDO> lockOrderDomainService;

    @Autowired
    private IDomainService<List<CompensateThreeDSNotifyRequestDO>, Void> compensateThreeDSNotifyDomainService;

    @Override
    public Result<String> compensateInquiry(CompensateInquiryRequest request) {
        // 请求转换
        CompensateInquiryRequestDO requestOrder = facadeAssembler.toCompensateInquiryReq(request);
        // 调用领域服务
        OrderStatusEnum orderStatusEnum = compensateInquiryDomainService.execute(requestOrder);
        // 响应转换
        String resultStatus = null;
        if (orderStatusEnum != null) {
            resultStatus = orderStatusEnum.name();
        }
        return ResultUtil.success(resultStatus);
    }

    @Override
    public Result<Map> channelRouting(ChannelRoutingRequest request) {
        // 请求转换
        RequestOrderDO requestOrder = facadeAssembler.toChannelRoutingReq(request);
        // 调用领域服务
        Map filterData = filterChannelRoutingDomainService.execute(requestOrder);
        // 响应转换
        return ResultUtil.success(filterData);
    }

    @Override
    public Result<Void> compensateNotify(List<CompensateNotifyRequest> request) {
        // 请求转换
        List<CompensateNotifyRequestDO> requestOrder = facadeAssembler.toCompensateNotifyReq(request);
        // 调用领域服务
        compensateNotifyDomainService.execute(requestOrder);
        return ResultUtil.success();
    }

    @Override
    public Result<LockOrderResponse> lockOrder(LockOrderRequest request) {
        // 请求转换
        LockOrderRequestDO requestDO = facadeAssembler.toLockOrderReq(request);
        // 调用领域服务
        LockOrderResponseDO responseDO = lockOrderDomainService.execute(requestDO);
        // 响应转换
        return ResultUtil.success(facadeAssembler.toLockOrderResp(responseDO));
    }

    @Override
    public Result<Void> compensateThreeDSNotify(List<CompensateThreeDSNotifyRequest> request) {
        // 请求转换
        List<CompensateThreeDSNotifyRequestDO> compensateThreeDSNotifyRequest = facadeAssembler.toCompensateThreeDSNotifyRequest(request);
        // 调用领域服务
        compensateThreeDSNotifyDomainService.execute(compensateThreeDSNotifyRequest);
        return ResultUtil.success();
    }
}
