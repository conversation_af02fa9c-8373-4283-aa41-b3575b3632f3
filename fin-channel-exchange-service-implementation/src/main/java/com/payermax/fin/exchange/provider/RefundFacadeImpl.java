package com.payermax.fin.exchange.provider;

import com.alibaba.csp.sentinel.EntryType;
import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.fin.exchange.assembler.FacadeAssembler;
import com.payermax.fin.exchange.common.exception.ExchangeException;
import com.payermax.fin.exchange.domain.inquiry.InquiryOrderRequestDO;
import com.payermax.fin.exchange.domain.inquiry.InquiryOrderResponseDO;
import com.payermax.fin.exchange.domain.trans.ResponseOrderDO;
import com.payermax.fin.exchange.domain.trans.refund.VoidRequestOrderDO;
import com.payermax.fin.exchange.domain.trans.refund.RefundRequestOrderDO;
import com.payermax.fin.exchange.domain.trans.refund.RefundResponseOrderDO;
import com.payermax.fin.exchange.domainservice.IDomainService;
import com.payermax.fin.exchange.domainservice.IV2DomainService;
import com.payermax.fin.exchange.service.facade.RefundFacade;
import com.payermax.fin.exchange.service.request.InquiryVoidRequest;
import com.payermax.fin.exchange.service.request.InquiryRefundRequest;
import com.payermax.fin.exchange.service.request.RefundRequest;
import com.payermax.fin.exchange.service.request.VoidRequest;
import com.payermax.fin.exchange.service.response.InquiryVoidResponse;
import com.payermax.fin.exchange.service.response.InquiryRefundResponse;
import com.payermax.fin.exchange.service.response.RefundResponse;
import com.payermax.fin.exchange.service.response.VoidResponse;
import com.payermax.infra.ionia.log.digest.core.annotation.DigestLog;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.config.annotation.Method;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 退款服务
 *
 * <AUTHOR>
 * @date 2021/12/7 17:55
 */
@DubboService(version = "${exchange.dubbo.api.version}", group = "${exchange.dubbo.api.group-id:}", methods = {
        @Method(name = "voidApply", timeout = 13000),
        @Method(name = "refundApply", timeout = 13000)
})
@Service
public class RefundFacadeImpl implements RefundFacade {

    @Autowired
    private FacadeAssembler facadeAssembler;

    @Autowired
    private IDomainService<InquiryOrderRequestDO, InquiryOrderResponseDO> inquiryDomainService;

    @Autowired
    private IDomainService<RefundRequestOrderDO, RefundResponseOrderDO> refundDomainService;

    @Resource
    private IV2DomainService<VoidRequestOrderDO, ResponseOrderDO> voidDomainService;

    @Override
    @DigestLog(isRecord = true)
    public Result<VoidResponse> voidApply(VoidRequest request) {
        // 请求转换
        VoidRequestOrderDO requestOrder = facadeAssembler.toVoidReqOrder(request);
        // 调用下游服务，如果出现ExchangeException异常，则获取data
        ResponseOrderDO responseOrder;
        try {
            // 调用领域服务
            responseOrder = voidDomainService.execute(requestOrder, ResponseOrderDO.class);
        } catch (ExchangeException e) {
            responseOrder = (ResponseOrderDO) e.getData();
        }

        // 响应转换
        return ResultUtil.success(facadeAssembler.toVoidResp(responseOrder));
    }

    @Override
    @SentinelResource(value = "dubbo:provider:RefundFacade.inquiryVoid", entryType = EntryType.IN)
    public Result<InquiryVoidResponse> inquiryVoid(InquiryVoidRequest request) {
        // 请求转换
        InquiryOrderRequestDO requestOrder = facadeAssembler.toVoidInquiryReqOrder(request);
        // 调用领域服务
        InquiryOrderResponseDO responseOrder = inquiryDomainService.execute(requestOrder);
        // 响应转换
        return ResultUtil.success(facadeAssembler.toVoidInquiryResp(responseOrder));
    }

    @Override
    @DigestLog(isRecord = true)
    public Result<RefundResponse> refundApply(RefundRequest request) {
        // 请求转换
        RefundRequestOrderDO requestOrder = facadeAssembler.toRefundReqOrder(request);
        // 调用领域服务
        RefundResponseOrderDO responseOrder = refundDomainService.execute(requestOrder);
        // 响应转换
        return ResultUtil.success(facadeAssembler.toRefundResp(responseOrder));
    }

    @Override
    @SentinelResource(value = "dubbo:provider:RefundFacade.inquiryRefund", entryType = EntryType.IN)
    public Result<InquiryRefundResponse> inquiryRefund(InquiryRefundRequest request) {
        // 请求转换
        InquiryOrderRequestDO requestOrder = facadeAssembler.toRefundInquiryReqOrder(request);
        // 调用领域服务
        InquiryOrderResponseDO responseOrder = inquiryDomainService.execute(requestOrder);
        // 响应转换
        return ResultUtil.success(facadeAssembler.toRefundInquiryResp(responseOrder));
    }
}
