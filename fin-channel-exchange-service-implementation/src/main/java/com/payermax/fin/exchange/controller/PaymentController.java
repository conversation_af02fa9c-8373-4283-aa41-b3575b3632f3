package com.payermax.fin.exchange.controller;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.fin.exchange.condition.OnHttpCondition;
import com.payermax.fin.exchange.service.facade.PaymentFacade;
import com.payermax.fin.exchange.service.request.*;
import com.payermax.fin.exchange.service.response.*;
import com.payermax.infra.ionia.log.digest.core.annotation.DigestLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequestMapping(value = "/payments")
@Api(value = "支付服务", tags = "支付服务")
@Conditional(OnHttpCondition.class)
public class PaymentController {

    @Autowired
    private PaymentFacade paymentFacade;

    @PostMapping(value = "/pay")
    @ApiOperation(value = "支付申请", notes = "支付申请接口  \n" +
            "SYSTEM_BUSY: 通用内部未知错误---需要定位系统异常  \n" +
            "REQUEST_PARAM_INVALID: 参数错误---检查入参  \n" +
            "CHANNEL_ORDER_EXIST: 订单已存在---幂等校验，检查支付单号是否相同  \n" +
            "CHANNEL_ORDER_IS_NOT_EXIST: 渠道订单不存在---多步交互情况下查不到渠道订单信息，检查渠道单号是否正确  \n" +
            "PAY_METHOD_CONFIG_ERROR: 渠道支付方式配置错误---检查渠道配置数据  \n" +
            "TARGET_MERCHANT_NOT_EXIST: 商户报备信息不存在---检查商户报备信息  \n" +
            "CHANNEL_NO_SUPPORT: 无可用渠道---检查入参条件，是否有匹配渠道可用" +
            "DATE_FORMAT_EXCEPTION: 下游响应数据格式异常---检查下游响应数据  \n" +
            "CHANNEL_FRONT_RPC_EXCEPTION: 下游响应数据为空---检查下游响应  \n" +
            "STATE_TRANSITION_NO_SUPPORT: 不支持的订单状态流转---检查订单状态  \n" +
            "ORDER_UPDATE_FAIL: 订单更新失败---订单状态乐观锁，检查订单状态  \n" +
            "F999: 渠道响应码未配置内部映射码时，默认返回码---检查渠道响应  \n" +
            "其它：渠道响应码配置的内部映射码")
    @DigestLog(isRecord = true)
    public Result<PayResponse> pay(@RequestBody PayRequest request) {
        return paymentFacade.pay(request);
    }

    @PostMapping(value = "/capture")
    @DigestLog(isRecord = true)
    public Result<CaptureResponse> capture(@RequestBody CaptureRequest request) {
        return paymentFacade.capture(request);
    }

    @PostMapping(value = "/unifyControl")
    @ApiOperation(value = "支付统一流程控制", notes = "支付统一流程控制接口，用于控制支付单的状态流程，如OTP验证等  \n" +
            "SYSTEM_BUSY: 通用内部未知错误---需要定位系统异常  \n" +
            "REQUEST_PARAM_INVALID: 参数错误---检查入参  \n" +
            "CHANNEL_ORDER_IS_NOT_EXIST: 渠道订单不存在---多步交互情况下查不到渠道订单信息，检查渠道单号是否正确  \n" +
            "CHANNEL_ORDER_OVER: 渠道订单已是终态---终态订单无需后续流程，检查渠道订单状态  \n" +
            "PAY_METHOD_CONFIG_ERROR: 渠道支付方式配置错误---检查渠道配置数据  \n" +
            "TARGET_MERCHANT_NOT_EXIST: 商户报备信息不存在---检查商户报备信息  \n" +
            "CHANNEL_NO_SUPPORT: 无可用渠道---检查入参条件，是否有匹配渠道可用" +
            "DATE_FORMAT_EXCEPTION: 下游响应数据格式异常---检查下游响应数据  \n" +
            "CHANNEL_FRONT_RPC_EXCEPTION: 下游响应数据为空---检查下游响应  \n" +
            "STATE_TRANSITION_NO_SUPPORT: 不支持的订单状态流转---检查订单状态  \n" +
            "ORDER_UPDATE_FAIL: 订单更新失败---订单状态乐观锁，检查订单状态  \n" +
            "F999: 渠道响应码未配置内部映射码时，默认返回码---检查渠道响应  \n" +
            "其它：渠道响应码配置的内部映射码")
    @DigestLog(isRecord = true)
    public Result<UnifyControlResponse> unifyControl(@RequestBody UnifyControlRequest request) {
        return paymentFacade.unifyControl(request);
    }

    @PostMapping(value = "/inquiryPayment")
    @ApiOperation(value = "支付查询", notes = "支付单信息查询接口  \n" +
            "SYSTEM_BUSY: 通用内部未知错误---需要定位系统异常  \n" +
            "REQUEST_PARAM_INVALID: 参数错误---检查入参  \n" +
            "CHANNEL_ORDER_IS_NOT_EXIST: 渠道订单不存在---检查渠道单号是否正确")
    public Result<InquiryPaymentResponse> inquiryPayment(@RequestBody InquiryPaymentRequest request) {
        return paymentFacade.inquiryPayment(request);
    }

    @PostMapping(value = "/inquiryCapture")
    @DigestLog(isRecord = true)
    public Result<InquiryCaptureResponse> inquiryCapture(@RequestBody InquiryCaptureRequest request) {
        return paymentFacade.inquiryCapture(request);
    }

    @PostMapping(value = "/handlePayCorrectionOrder")
    @ApiOperation(value = "差错订单处理 补入款交易", notes = "差错订单处理接口  \n" +
            "SYSTEM_BUSY: 通用内部未知错误---需要定位系统异常  \n" +
            "REQUEST_PARAM_INVALID: 参数错误---检查入参  \n" +
            "CORRECTION_CODE_NOT_SUPPORT: 差错Code不支持---检查差错场景对应差错码  \n" +
            "CHANNEL_ORDER_NOT_EXIST: 渠道订单不存在---检查渠道单号是否正确")
    public Result<PayResponse> handlePayCorrectionOrder(@RequestBody PayCorrectionOrderRequest request) {
        return paymentFacade.handlePayCorrectionOrder(request);
    }

    @PostMapping(value = "/applyDdc")
    public Result<ApplyDDCResponse> applyDDC(@RequestBody ApplyDDCRequest request) {
        return paymentFacade.applyDDC(request);
    }

    @PostMapping(value = "/tokenUnbinding")
    public Result<TokenUnbindingResponse> tokenUnbinding(@RequestBody TokenUnbindingRequest request) {
        return paymentFacade.tokenUnbinding(request);
    }

    @PostMapping(value = "/closeApply")
    public Result<CloseResponse> closeApply(@RequestBody CloseRequest request) {
        return paymentFacade.closeApply(request);
    }
}
