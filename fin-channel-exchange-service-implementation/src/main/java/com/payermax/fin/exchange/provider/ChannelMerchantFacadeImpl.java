package com.payermax.fin.exchange.provider;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.JsonUtils;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.fin.exchange.dal.entity.ChannelMerchantEntity;
import com.payermax.fin.exchange.domainservice.repository.IChannelMerchantService;
import com.payermax.fin.exchange.service.facade.ChannelMerchantFacade;
import com.payermax.fin.exchange.service.response.ChannelMerchantResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;


@DubboService(version = "${exchange.dubbo.api.version}", group = "${exchange.dubbo.api.group-id:}")
@Service
public class ChannelMerchantFacadeImpl implements ChannelMerchantFacade {

    @Autowired
    private IChannelMerchantService channelMerchantService;

    @Override
    public Result<Map<String, String>> getAccountJsonInfoByCode(String code) {
        ChannelMerchantEntity channelMerchant = channelMerchantService.getByMerchantCodeIgnoreStatus(code);
        if (Optional.ofNullable(channelMerchant).isPresent()) {
            return ResultUtil.success(JsonUtils.toBean(Map.class, channelMerchant.getAccountJson()));
        }
        return ResultUtil.success();
    }

    @Override
    public Result<ChannelMerchantResponse> getByMerchantCodeIgnoreStatus(String merchantCode) {
        ChannelMerchantEntity channelMerchantEntity = channelMerchantService.getByMerchantCodeIgnoreStatus(merchantCode);
        return ResultUtil.success(JsonUtils.toBean(ChannelMerchantResponse.class, JsonUtils.toString(channelMerchantEntity)));
    }

}
