package com.payermax.fin.exchange.config;

import feign.Request;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * feign config
 *
 * <AUTHOR>
 * @date 2021/8/27 14:00
 */
@Configuration
public class FeignAutoConfig {

    private int connectTimeOut = 12;

    private int socketTimeOut = 12;

    @Bean
    public Request.Options options() {
        return new Request.Options(connectTimeOut * 1000, socketTimeOut * 1000);
    }

}
