package com.payermax.fin.exchange.init;

import com.payermax.fin.exchange.domainservice.cache.CacheManager;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 本地缓存初始化
 *
 * <AUTHOR>
 * @date 2025/1/17 13:40
 */
@Component
public class CacheInit implements InitializingBean {

    @Autowired
    private CacheManager cacheManager;

    @Override
    public void afterPropertiesSet() throws Exception {
        cacheManager.refreshAll();
    }
}
