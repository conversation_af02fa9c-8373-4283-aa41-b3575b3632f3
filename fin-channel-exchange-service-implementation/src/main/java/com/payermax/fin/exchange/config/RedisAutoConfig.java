package com.payermax.fin.exchange.config;

import com.payermax.fin.exchange.serializer.MyStringSerializer;
import com.payermax.infra.tool.lock.redisson.UredissonLock;
import com.payermax.infra.tool.lock.redisson.config.RedissonAutoConfiguration;
import com.payermax.infra.tool.lock.redisson.config.UredissonConfig;
import com.payermax.infra.tool.lock.redisson.manage.RedissonClientManager;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.jedis.JedisClientConfiguration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import redis.clients.jedis.JedisPoolConfig;

import java.time.Duration;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2021/8/31 20:22
 */
@Slf4j
@Configuration
@AutoConfigureBefore(value = {RedisAutoConfiguration.class, RedissonAutoConfiguration.class})
public class RedisAutoConfig {

    @Bean("cachePro")
    @ConfigurationProperties(prefix = "shareit.cache.redis")
    public CacheRedisProperties cacheRedisProperties() {
        return new CacheRedisProperties();
    }

    @Bean("newCachePro")
    @ConfigurationProperties(prefix = "new.shareit.cache.redis")
    public CacheRedisProperties newCacheRedisProperties() {
        return new CacheRedisProperties();
    }

    @Bean("redissonConfig")
    @ConfigurationProperties(prefix = "ushareit.redisson.lock.server")
    public UredissonConfig redissonConfig() {
        return new UredissonConfig();
    }

    @Bean("newRedissonConfig")
    @ConfigurationProperties(prefix = "new.ushareit.redisson.lock.server")
    public UredissonConfig newRedissonConfig() {
        return new UredissonConfig();
    }

    /**
     * 老redis实例，只有
     * @param redisProperties
     * @param redisConnectionFactory
     * @return
     */
    @Bean(name = "stringRedisTemplate")
    @ConditionalOnProperty(name = "shareit.redis.use.old", matchIfMissing = true)
    public StringRedisTemplate stringRedisTemplate(@Qualifier("cachePro") CacheRedisProperties redisProperties,
                                                   @Qualifier("redisConnectionFactory") RedisConnectionFactory redisConnectionFactory) {
        StringRedisTemplate template = new StringRedisTemplate();
        template.setEnableTransactionSupport(false);
        // 设置key的自定义序列化方式
        MyStringSerializer stringSerializer = new MyStringSerializer(redisProperties.getPrefix());
        template.setKeySerializer(stringSerializer);
        template.setConnectionFactory(redisConnectionFactory);
        return template;
    }

    @Bean(name = "redisConnectionFactory")
    @ConditionalOnProperty(name = "shareit.redis.use.old", matchIfMissing = true)
    public JedisConnectionFactory redisConnectionFactory(@Qualifier("cachePro") CacheRedisProperties redisProperties) {
        JedisConnectionFactory factory = this.buildJedisConnectionFactory(redisProperties);
        return factory;
    }

    @Bean(name = "newStringRedisTemplate")
    public StringRedisTemplate newStringRedisTemplate(@Qualifier("newCachePro") CacheRedisProperties redisProperties,
                                                      @Qualifier("newRedisConnectionFactory") RedisConnectionFactory redisConnectionFactory) {
        StringRedisTemplate template = new StringRedisTemplate();
        template.setEnableTransactionSupport(false);
        // 设置key的自定义序列化方式
        MyStringSerializer stringSerializer = new MyStringSerializer(redisProperties.getPrefix());
        template.setKeySerializer(stringSerializer);
        template.setConnectionFactory(redisConnectionFactory);
        return template;
    }

    @Bean(name = "newRedisConnectionFactory")
    public JedisConnectionFactory newRedisConnectionFactory(@Qualifier("newCachePro") CacheRedisProperties redisProperties) {
        JedisConnectionFactory factory = this.buildJedisConnectionFactory(redisProperties);
        return factory;
    }

    @Bean("redissonLock")
    @ConditionalOnProperty(name = "shareit.redis.use.old", matchIfMissing = true)
    public UredissonLock redissonLock(@Qualifier("redissonClientManager") RedissonClientManager redissonClientManager) {
        UredissonLock redissonLock = new UredissonLock();
        redissonLock.setRedissonClientManager(redissonClientManager);
        log.info("[RedissonLock]组装完毕");
        return redissonLock;
    }

    @Bean("redissonClientManager")
    @ConditionalOnProperty(name = "shareit.redis.use.old", matchIfMissing = true)
    public RedissonClientManager redissonClientManager(@Qualifier("redissonConfig") UredissonConfig redissonProperties) {
        RedissonClientManager redissonClientManager = new RedissonClientManager(redissonProperties);
        log.info("[RedissonManager]组装完毕,当前连接方式:{},连接地址:{}", redissonProperties.getType(), redissonProperties.getAddress());
        return redissonClientManager;
    }

    @Bean("newRedissonLock")
    public UredissonLock newRedissonLock(@Qualifier("newRedissonClientManager") RedissonClientManager redissonClientManager) {
        UredissonLock redissonLock = new UredissonLock();
        redissonLock.setRedissonClientManager(redissonClientManager);
        log.info("[NewRedissonLock]组装完毕");
        return redissonLock;
    }

    @Bean("newRedissonClientManager")
    public RedissonClientManager newRedissonClientManager(@Qualifier("newRedissonConfig") UredissonConfig redissonProperties) {
        RedissonClientManager redissonClientManager = new RedissonClientManager(redissonProperties);
        log.info("[NewRedissonManager]组装完毕,当前连接方式:{},连接地址:{}", redissonProperties.getType(), redissonProperties.getAddress());
        return redissonClientManager;
    }

    private JedisConnectionFactory buildJedisConnectionFactory(CacheRedisProperties cacheRedisProp) {
        // 连接池配置
        JedisPoolConfig poolConfig = getPoolConfig(cacheRedisProp);

        JedisClientConfiguration.JedisClientConfigurationBuilder jedisClientBuilder = JedisClientConfiguration.builder();
        jedisClientBuilder
                .connectTimeout(Duration.ofMillis(cacheRedisProp.getCommandTimeOut()))
                .readTimeout(Duration.ofMillis(cacheRedisProp.getCommandTimeOut()))
                .usePooling().poolConfig(poolConfig);
        if (cacheRedisProp.isUseSsl()) {
            jedisClientBuilder.useSsl();
        }

        RedisStandaloneConfiguration standaloneConfiguration = new RedisStandaloneConfiguration();
        standaloneConfiguration.setDatabase(cacheRedisProp.getDatabase());
        standaloneConfiguration.setHostName(cacheRedisProp.getHostName());
        standaloneConfiguration.setPort(cacheRedisProp.getPort());
        standaloneConfiguration.setPassword(cacheRedisProp.getPassword());

        // 构建连接工厂
        JedisConnectionFactory factory = new JedisConnectionFactory(standaloneConfiguration, jedisClientBuilder.build());
        factory.setConvertPipelineAndTxResults(true);
        factory.afterPropertiesSet();
        return factory;
    }

    private static JedisPoolConfig getPoolConfig(CacheRedisProperties cacheRedisProp) {
        JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();
        jedisPoolConfig.setMaxTotal(cacheRedisProp.getMaxTotal());
        jedisPoolConfig.setMaxIdle(cacheRedisProp.getMaxIdle());
        jedisPoolConfig.setMinIdle(cacheRedisProp.getMinIdle());
        jedisPoolConfig.setMaxWaitMillis(cacheRedisProp.getMaxWaitMillis());
        jedisPoolConfig.setTestOnBorrow(cacheRedisProp.isTestOnBorrow());
        jedisPoolConfig.setTestWhileIdle(cacheRedisProp.isTestWhileIdle());
        jedisPoolConfig.setTimeBetweenEvictionRunsMillis(cacheRedisProp.getTimeBetweenEvictionRunsMillis());
        return jedisPoolConfig;
    }

    @Data
    public class CacheRedisProperties {
        /**
         * 当前服务缓存统一前缀
         */
        private String prefix;
        /**
         * 单机redis  数据库 database
         */
        private int database;
        /**
         * 单机redis hostName
         */
        private String hostName;
        /**
         * 单机redis post端口
         */
        private int port;

        private String password;
        /**
         * Redis集群节点
         */
        private String cluster;
        /**
         * 链接超时时间
         */
        private int connectionTimeOut;
        /**
         * 超时时间
         */
        private int commandTimeOut;
        /**
         * 连接池总大小
         */
        private int maxTotal;
        /**
         * 连接池最大空闲时间
         */
        private int maxIdle;
        /**
         * 连接池最小空闲时间
         */
        private int minIdle;
        /**
         * 获取链接等待超时时间，获取不到链接时，最大等待多长时间，默认是-1，永不超时，一定要配置
         */
        private long maxWaitMillis;
        /**
         * 是否使用ssl
         */
        private boolean useSsl;
        /**
         * 获取链接时是否进行可用性测试
         */
        private boolean testOnBorrow = false;
        /**
         * 空闲时是否进行可用性测试
         */
        private boolean testWhileIdle = true;
        /**
         * 空闲资源的检测周期
         */
        private long timeBetweenEvictionRunsMillis = -1;
    }

}
