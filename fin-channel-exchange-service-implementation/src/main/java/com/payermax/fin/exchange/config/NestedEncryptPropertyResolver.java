package com.payermax.fin.exchange.config;

import cn.hutool.core.util.StrUtil;
import com.ulisesbocchio.jasyptspringboot.EncryptablePropertyResolver;
import org.jasypt.encryption.StringEncryptor;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 嵌套加密
 *
 * <AUTHOR>
 * @date 2023/9/5 19:19
 */
public class NestedEncryptPropertyResolver implements EncryptablePropertyResolver {

    private static final String REGEX = "ENC\\(([^)]+)\\)";

    private StringEncryptor encryptor;

    public NestedEncryptPropertyResolver(StringEncryptor encryptor) {
        this.encryptor = encryptor;
    }

    @Override
    public String resolvePropertyValue(String value) {

        Pattern pattern = Pattern.compile(REGEX);
        Matcher matcher = pattern.matcher(value);

        while (matcher.find()) {
            String group = matcher.group(1);
            String decrypt = encryptor.decrypt(group);
            value = StrUtil.replace(value, "ENC(" + group + ")", decrypt);
            matcher = Pattern.compile(REGEX).matcher(value);
        }
        return value;
    }
}
