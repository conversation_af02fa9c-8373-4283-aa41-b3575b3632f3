package com.payermax.fin.exchange.config;

import cn.hippo4j.starter.core.DynamicThreadPool;
import cn.hippo4j.starter.toolkit.thread.ThreadPoolBuilder;
import cn.hippo4j.starter.wrapper.DynamicThreadPoolWrapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ThreadPoolExecutor;

import static com.payermax.fin.exchange.common.constants.DynamicThreadPoolConstant.*;

/**
 * <AUTHOR>
 * @date 2022/9/26 22:17
 */
@Configuration
public class DynamicThreadPoolConfig {

    @Bean
    @DynamicThreadPool
    public DynamicThreadPoolWrapper payoutThreadPoolWrapper() {
        ThreadPoolExecutor poolExecutor = ThreadPoolBuilder.builder()
                .dynamicPool()
                .threadFactory(PAYOUT_POOL)
                .build();
        return new DynamicThreadPoolWrapper(PAYOUT_POOL, poolExecutor);
    }

    @Bean
    @DynamicThreadPool
    public DynamicThreadPoolWrapper paymentAsyncThreadPoolWrapper() {
        ThreadPoolExecutor poolExecutor = ThreadPoolBuilder.builder()
                .dynamicPool()
                .threadFactory(PAYMENT_ASYNC)
                .build();
        return new DynamicThreadPoolWrapper(PAYMENT_ASYNC, poolExecutor);
    }

    @Bean
    @DynamicThreadPool
    public DynamicThreadPoolWrapper paymentAsyncCompensateThreadPoolWrapper() {
        ThreadPoolExecutor poolExecutor = ThreadPoolBuilder.builder()
                .dynamicPool()
                .threadFactory(PAYMENT_ASYNC_COMPENSATE)
                .build();
        return new DynamicThreadPoolWrapper(PAYMENT_ASYNC_COMPENSATE, poolExecutor);
    }

}
