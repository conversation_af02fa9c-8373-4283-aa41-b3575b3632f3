package com.payermax.fin.exchange.config;

import com.payermax.common.lang.exception.BusinessException;
import com.payermax.fin.exchange.domain.inquiry.InquiryOrderRequestDO;
import com.payermax.fin.exchange.domain.inquiry.InquiryOrderResponseDO;
import com.payermax.fin.exchange.domainservice.IDomainService;
import com.payermax.fin.exchange.integration.rpc.InternalFrontClient;
import com.payermax.fin.exchange.share.enums.PaymentTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2023/3/21 20:27
 */
@Component
@Slf4j
@DependsOn(value = {"customizableShardingStrategyFactory", "objectOperateUtils", "fastjsonConfig", "grayCompareUtils", "cacheInit"})
public class WarmUpConfig implements InitializingBean {

    @Autowired
    private InternalFrontClient internalFrontClient;

    @Autowired
    private IDomainService<InquiryOrderRequestDO, InquiryOrderResponseDO> inquiryDomainService;

    @Override
    public void afterPropertiesSet() throws Exception {
        init();
    }

    public void init() {
        this.initExchangeOrderDb();
        this.initChannelFrontClient();
    }

    /**
     * 初始化渠道front
     */
    private void initChannelFrontClient() {
        try {
            internalFrontClient.internalExecute(null, null,null, "/", "{}");
        } catch (Exception e) {
            log.error("Init channel front error.", e);
        }
    }

    /**
     * 初始化订单库（主要是shardingJdbc相关配置）
     */
    private void initExchangeOrderDb() {
        long startTime = System.currentTimeMillis();
        try {
            InquiryOrderRequestDO request = new InquiryOrderRequestDO();
            request.setBizOrderNo("20230917134353EP2136185137583152005-60251953");
            request.setPaymentType(PaymentTypeEnum.PAYMENT);
            inquiryDomainService.execute(request);
        } catch (BusinessException e) {
            // 正常业务异常
        } catch (Exception e) {
            log.error("Init exchange db error.", e);
        } finally {
            log.info("Init exchange order db complete. useTime = {}", System.currentTimeMillis() - startTime);
        }
    }

}
