package com.payermax.fin.exchange.controller;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.fin.exchange.service.facade.CompensateFacade;
import com.payermax.fin.exchange.service.request.AccelerateCompensationRequest;
import com.payermax.fin.exchange.service.request.CompensateStatusRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023/4/28 16:34
 */

@RestController
@Slf4j
@RequestMapping(value = "/compensate")
@Api(value = "补偿服务", tags = "补偿服务")
public class CompensateController {

    @Autowired
    private CompensateFacade compensateFacade;

    @PostMapping(value = "/accelerateCompensation")
    @ApiOperation(value = "加速补偿", notes = "加速补偿")
    public Result<Boolean> accelerateCompensation(@RequestBody AccelerateCompensationRequest request) {
        return compensateFacade.accelerateCompensation(request);
    }

    @PostMapping(value = "/compensateOrderStatus")
    @ApiOperation(value = "补偿订单状态", notes = "补偿订单状态")
    public Result<String> compensateOrderStatus(@RequestBody CompensateStatusRequest request) {
        return compensateFacade.compensateOrderStatus(request);
    }
    
    
}
