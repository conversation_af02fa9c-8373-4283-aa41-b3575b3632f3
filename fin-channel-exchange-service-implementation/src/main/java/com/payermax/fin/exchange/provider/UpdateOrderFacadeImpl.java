package com.payermax.fin.exchange.provider;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.fin.exchange.assembler.FacadeAssembler;
import com.payermax.fin.exchange.domain.inquiry.CloseOrderRequestDO;
import com.payermax.fin.exchange.domain.inquiry.UpdateCommitOrderRequestDO;
import com.payermax.fin.exchange.domain.inquiry.UpdateRequestOrderRequestDO;
import com.payermax.fin.exchange.domainservice.IDomainService;
import com.payermax.fin.exchange.service.facade.UpdateOrderFacade;
import com.payermax.fin.exchange.service.request.CloseOrderRequest;
import com.payermax.fin.exchange.service.request.UpdateCommitOrderRequest;
import com.payermax.fin.exchange.service.request.UpdateRequestOrderRequest;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 回调服务
 *
 * <AUTHOR>
 * @date 2021/12/22 14:56
 */
@DubboService(version = "${exchange.dubbo.api.version}", group = "${exchange.dubbo.api.group-id:}")
@Service
public class UpdateOrderFacadeImpl implements UpdateOrderFacade {

    @Autowired
    private FacadeAssembler facadeAssembler;

    @Autowired
    private IDomainService<List<UpdateCommitOrderRequestDO>, Long> updateCommitOrderDomainService;

    @Autowired
    private IDomainService<List<UpdateRequestOrderRequestDO>, Long> updateRequestOrderDomainService;

    @Autowired
    private IDomainService<CloseOrderRequestDO, Void> closeOrderDomainService;

    @Override
    public Result<Long> updateCommitOrder(List<UpdateCommitOrderRequest> request) {
        // 入参转换
        List<UpdateCommitOrderRequestDO> requestInfo = facadeAssembler.toUpdateCommitOrderReq(request);
        // 调用领域服务
        Long response = updateCommitOrderDomainService.execute(requestInfo);
        // 响应转换
        return ResultUtil.success(response);
    }

    @Override
    public Result<Long> updateRequestOrder(List<UpdateRequestOrderRequest> request) {
        // 入参转换
        List<UpdateRequestOrderRequestDO> requestInfo = facadeAssembler.toUpdateRequestOrderReq(request);
        // 调用领域服务
        Long response = updateRequestOrderDomainService.execute(requestInfo);
        // 响应转换
        return ResultUtil.success(response);
    }

    @Override
    public Result<Void> closeOrderNotify(CloseOrderRequest request) {
        // 入参转换
        CloseOrderRequestDO requestInfo = facadeAssembler.toCloseOrderReq(request);
        // 调用领域服务
        closeOrderDomainService.execute(requestInfo);
        // 响应转换
        return ResultUtil.success();
    }
}
