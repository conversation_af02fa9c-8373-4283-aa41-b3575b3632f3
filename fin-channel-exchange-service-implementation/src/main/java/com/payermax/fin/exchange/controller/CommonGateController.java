package com.payermax.fin.exchange.controller;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.fin.exchange.condition.OnHttpCondition;
import com.payermax.fin.exchange.service.facade.CommonGateFacade;
import com.payermax.fin.exchange.service.request.CommonGateRequest;
import com.payermax.fin.exchange.service.response.CommonGateResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequestMapping(value = "/common")
@Api(value = "通用网关转发服务", tags = "通用网关转发服务")
@Conditional(OnHttpCondition.class)
public class CommonGateController {

    @Autowired
    private CommonGateFacade commonGateFacade;

    @PostMapping(value = "/gateForward")
    @ApiOperation(value = "通用网关转发接口", notes = "通用网关转发接口")
    public Result<CommonGateResponse> commonGateForward(@RequestBody CommonGateRequest request) {
        return commonGateFacade.commonGateForward(request);
    }

    @PostMapping(value = "/queryTradeInfo")
    @ApiOperation(value = "交易查询通用接口", notes = "交易查询通用接口")
    public Result<CommonGateResponse> commonQueryTradeInfo(@RequestBody CommonGateRequest request) {
        return commonGateFacade.commonGateForward(request);
    }
}
