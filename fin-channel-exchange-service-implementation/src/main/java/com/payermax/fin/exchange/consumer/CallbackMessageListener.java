package com.payermax.fin.exchange.consumer;

import cn.hutool.core.comparator.CompareUtil;
import com.alibaba.fastjson.JSON;
import com.payermax.fin.exchange.common.util.CacheKeyUtils;
import com.payermax.fin.exchange.domain.callback.CallbackRequestDO;
import com.payermax.fin.exchange.domainservice.IDomainService;
import com.payermax.fin.exchange.domainservice.entity.RocketMqMessage;
import com.payermax.fin.exchange.share.utils.RedisUtils;
import com.payermax.infra.ionia.rocketmq.handler.BaseMqMessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 订单callback消息监听
 *
 * <AUTHOR>
 * @date 2023/2/21 17:29
 */
@Slf4j
@Component
@RocketMQMessageListener(
        topic = "${rocketMq.topic.channel.exchange.callback.delay.topic}",
        consumerGroup = "${rocketmq.consumer.group_id}"
)
public class CallbackMessageListener extends BaseMqMessageListener<RocketMqMessage>
        implements RocketMQListener<RocketMqMessage> {

    @Autowired
    private IDomainService<CallbackRequestDO, String> callbackDomainService;

    @Override
    protected void handleMessage(RocketMqMessage message) throws Exception {
        if (message.getData() == null) {
            return;
        }
        // 参数转换
        CallbackRequestDO requestDO = JSON.parseObject(JSON.toJSONString(message.getData()), CallbackRequestDO.class);
        if (requestDO == null) {
            return;
        }
        String channelPayCommitNo = requestDO.getChannelPayCommitNo();
        if (StringUtils.isBlank(channelPayCommitNo)) {
            return;
        }
        // 校验此callback mq的发送时间是否早于出款重发的时间，如果早于，则忽略此消息
        if (!checkData(message.getSendTime(), channelPayCommitNo)) {
            log.warn("Check callback delay mq data failed. channelCommitNo = {}", channelPayCommitNo);
            return;
        }

        // 执行Callback逻辑
        try {
            callbackDomainService.execute(requestDO);
        } catch (Exception e) {
            log.warn("Callback mq execute fail.", e);
        }
    }

    @Override
    protected void overMaxRetryTimesMessage(RocketMqMessage message) {
        log.error("The order callback mq message processing fail over max retry times, message = {}", JSON.toJSONString(message));
    }

    @Override
    public void onMessage(RocketMqMessage message) {
        super.dispatchMessage(message);
    }

    /**
     * 校验数据*
     * *
     * @param mqSendTime
     * @param channelPayCommitNo
     * @return
     */
    private boolean checkData(Date mqSendTime, String channelPayCommitNo) {
        try {
            // 数据不完整返回通过
            if (mqSendTime == null || StringUtils.isBlank(channelPayCommitNo)) {
                return true;
            }
            String retryCompleteTimeStr = getRetryCompleteTime(channelPayCommitNo);
            // 无重试时间，返回通过
            if (StringUtils.isBlank(retryCompleteTimeStr)) {
                return true;
            }
            long retryCompleteTime = Long.valueOf(retryCompleteTimeStr);
            // 重试完成时间在callback时间之后，则不通过
            if (retryCompleteTime > mqSendTime.getTime()) {
                return false;
            }
        } catch (Exception e) {
            log.error("Check callback delay mq data error. channelCommitNo = {}", channelPayCommitNo, e);
        }
        return true;
    }

    private String getRetryCompleteTime(String channelPayCommitNo) {
        String cacheKey = CacheKeyUtils.getPayoutRetryCompleteTimeKey(channelPayCommitNo);
        // 先查询新redis
        String newRetryCompleteTimeStr = RedisUtils.getNewRedisTemplate().opsForValue().get(cacheKey);
        if (!RedisUtils.isUseOldRedis()) {
            return newRetryCompleteTimeStr;
        }
        String oldRetryCompleteTimeStr = RedisUtils.getRedisTemplate().opsForValue().get(cacheKey);
        return CompareUtil.compare(newRetryCompleteTimeStr, oldRetryCompleteTimeStr) > 0 ? newRetryCompleteTimeStr : oldRetryCompleteTimeStr;
    }

}
