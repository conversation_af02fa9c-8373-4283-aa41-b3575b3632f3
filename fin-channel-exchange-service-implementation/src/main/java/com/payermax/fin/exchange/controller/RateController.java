package com.payermax.fin.exchange.controller;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.fin.exchange.condition.OnHttpCondition;
import com.payermax.fin.exchange.service.facade.RatesFacade;
import com.payermax.fin.exchange.service.request.*;
import com.payermax.fin.exchange.service.response.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequestMapping(value = "/rates")
@Api(value = "报价服务", tags = "报价查询服务")
@Conditional(OnHttpCondition.class)
public class RateController {

    @Autowired
    private RatesFacade ratesFacade;

    @PostMapping(value = "/spotRate")
    @ApiOperation(value = "即期报价查询", notes = "即期报价查询接口")
    public Result<InquirySpotRateResponse> inquirySpotRate(@RequestBody InquirySpotRateRequest request) {
        return ratesFacade.inquirySpotRate(request);
    }

    @PostMapping(value = "/forwardRate")
    @ApiOperation(value = "远期报价查询", notes = "远期报价查询接口")
    public Result<InquiryForwardRateResponse> inquiryForwardRate(@RequestBody InquiryForwardRateRequest request) {
        return ratesFacade.inquiryForwardRate(request);
    }

    @PostMapping(value = "/hedgeAdvice")
    @ApiOperation(value = "HA交易接口", notes = "HA交易接口")
    public Result<HedgeAdviceResponse> hedgeAdvice(@RequestBody HedgeAdviceRequest request) {
        return ratesFacade.hedgeAdvice(request);
    }

    @PostMapping(value = "/tradeAdvice")
    @ApiOperation(value = "TA交易接口", notes = "TA交易接口")
    public Result<TradeAdviceResponse> tradeAdvice(@RequestBody TradeAdviceRequest request) {
        return ratesFacade.tradeAdvice(request);
    }

    @PostMapping(value = "/hedgeCompensate")
    @ApiOperation(value = "HA交易补偿接口", notes = "HA交易补偿接口")
    public Result<HedgeAdviceResponse> hedgeAdviceCompensate(@RequestBody OrderCompensateRequest request) {
        return ratesFacade.hedgeCompensate(request);
    }

    @PostMapping(value = "/tradeCompensate")
    @ApiOperation(value = "TA交易补偿接口", notes = "TA交易补偿接口")
    public Result<TradeAdviceResponse> tradeAdviceCompensate(@RequestBody OrderCompensateRequest request) {
        return ratesFacade.tradeCompensate(request);
    }

    @PostMapping(value = "/forexTrade")
    @ApiOperation(value = "外汇交易接口", notes = "外汇交易接口")
    public Result<ForexTradeResponse> forexTrade(@RequestBody ForexTradeRequest request) {
        return ratesFacade.forexTrade(request);
    }

    @PostMapping(value = "/inquiryForexTrade")
    @ApiOperation(value = "外汇交易查询接口", notes = "外汇交易查询接口")
    public Result<ForexTradeResponse> inquiryForexTrade(@RequestBody InquiryForexTradeRequest request) {
        return ratesFacade.inquiryForexTrade(request);
    }

    @PostMapping(value = "/forexTradeCompensate")
    @ApiOperation(value = "外汇交易补偿接口", notes = "外汇交易补偿接口")
    public Result<ForexTradeResponse> forexTradeCompensate(@RequestBody OrderCompensateRequest request) {
        return ratesFacade.forexTradeCompensate(request);
    }
}
