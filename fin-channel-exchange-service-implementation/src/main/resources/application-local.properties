nacos.config.bootstrap.enable=false
nacos.config.server-addr=
nacos.config.username=
nacos.config.password=
nacos.config.namespace=
# zookeeperå°å
dubbo.registry.address=nacos://${nacos.config.server-addr}?namespace=${nacos.config.namespace}
dubbo.registry.username=${nacos.config.username}
dubbo.registry.password=${nacos.config.password}
dubbo.config-center.address=nacos://${nacos.config.server-addr}?namespace=${nacos.config.namespace}
dubbo.config-center.username=${nacos.config.username}
dubbo.config-center.password=${nacos.config.password}
dubbo.registry.register=false
#è°è¯ç«¯å£ï¼çäº§ä¸è¦éç½®
dubbo.application.qos-port=22220
#çäº§ä¸è¦éç½®
dubbo.application.qos-enable=true
dubbo.application.metadata-type=remote

spring.cloud.nacos.discovery.server-addr=${nacos.config.server-addr}
spring.cloud.nacos.discovery.namespace=${nacos.config.namespace}
spring.cloud.nacos.discovery.username=${nacos.config.username}
spring.cloud.nacos.discovery.password=${nacos.config.password}
spring.cloud.nacos.discovery.register-enabled=false

# æ§å¶é¢æ¿ä¸æ¥å¿è·³å°å,ä¸æ¥çæ§æ°æ®ã
spring.cloud.sentinel.transport.dashboard=sentinel-dev-new.payermax.com
## ä¸æ§å¶çé¢éä¿¡çç«¯å£
spring.cloud.sentinel.transport.port=8791

sharding.datasource.properties.sql.show=true
# è®¢ååçæ°æ®æº
sharding.datasource.cluster[0].name=default_0
sharding.datasource.cluster[0].type=com.alibaba.druid.pool.DruidDataSource
sharding.datasource.cluster[0].properties.driverClassName=software.aws.rds.jdbc.mysql.Driver
sharding.datasource.cluster[0].properties.url=*******************************************************************************************************************************************************************************************************************************************************
sharding.datasource.cluster[0].properties.username=dev_v8
sharding.datasource.cluster[0].properties.password=ENC(N1-6_d663dbbb6e4b0f3500729052d)
sharding.datasource.cluster[5].name=new-default_0
sharding.datasource.cluster[5].type=com.alibaba.druid.pool.DruidDataSource
sharding.datasource.cluster[5].properties.driverClassName=software.aws.rds.jdbc.mysql.Driver
sharding.datasource.cluster[5].properties.url=*******************************************************************************************************************************************************************************************************************************************************
sharding.datasource.cluster[5].properties.username=dev_v8
sharding.datasource.cluster[5].properties.password=ENC(N1-6_d663dbbb6e4b0f3500729052d)

sharding.datasource.cluster[1].name=fin-channel-exchange_0
sharding.datasource.cluster[1].type=com.alibaba.druid.pool.DruidDataSource
sharding.datasource.cluster[1].properties.driverClassName=software.aws.rds.jdbc.mysql.Driver
sharding.datasource.cluster[1].properties.url=***************************************************************************************************************************************************************************************************************************************************************
sharding.datasource.cluster[1].properties.username=dev_v8
sharding.datasource.cluster[1].properties.password=ENC(N1-6_d663dbbb6e4b0f3500729052d)
sharding.datasource.cluster[1].slave[0].name=fin-channel-exchange_0_slave0
sharding.datasource.cluster[1].slave[0].type=com.alibaba.druid.pool.DruidDataSource
sharding.datasource.cluster[1].slave[0].properties.driverClassName=software.aws.rds.jdbc.mysql.Driver
sharding.datasource.cluster[1].slave[0].properties.url=***************************************************************************************************************************************************************************************************************************************************************
sharding.datasource.cluster[1].slave[0].properties.username=dev_v8
sharding.datasource.cluster[1].slave[0].properties.password=ENC(N1-6_d663dbbb6e4b0f3500729052d)
sharding.datasource.cluster[2].name=fin-channel-exchange_1
sharding.datasource.cluster[2].type=com.alibaba.druid.pool.DruidDataSource
sharding.datasource.cluster[2].properties.driverClassName=software.aws.rds.jdbc.mysql.Driver
sharding.datasource.cluster[2].properties.url=***************************************************************************************************************************************************************************************************************************************************************
sharding.datasource.cluster[2].properties.username=dev_v8
sharding.datasource.cluster[2].properties.password=ENC(N1-6_d663dbbb6e4b0f3500729052d)

sharding.datasource.cluster[3].name=fin-channel-exchange_2
sharding.datasource.cluster[3].type=com.alibaba.druid.pool.DruidDataSource
sharding.datasource.cluster[3].properties.driverClassName=software.aws.rds.jdbc.mysql.Driver
sharding.datasource.cluster[3].properties.url=***************************************************************************************************************************************************************************************************************************************************************
sharding.datasource.cluster[3].properties.username=dev_v8
sharding.datasource.cluster[3].properties.password=ENC(N1-6_d663dbbb6e4b0f3500729052d)
sharding.datasource.cluster[3].slave[0].name=fin-channel-exchange_2_slave0
sharding.datasource.cluster[3].slave[0].type=com.alibaba.druid.pool.DruidDataSource
sharding.datasource.cluster[3].slave[0].properties.driverClassName=software.aws.rds.jdbc.mysql.Driver
sharding.datasource.cluster[3].slave[0].properties.url=***************************************************************************************************************************************************************************************************************************************************************
sharding.datasource.cluster[3].slave[0].properties.username=dev_v8
sharding.datasource.cluster[3].slave[0].properties.password=ENC(N1-6_d663dbbb6e4b0f3500729052d)
sharding.datasource.cluster[4].name=fin-channel-exchange_3
sharding.datasource.cluster[4].type=com.alibaba.druid.pool.DruidDataSource
sharding.datasource.cluster[4].properties.driverClassName=software.aws.rds.jdbc.mysql.Driver
sharding.datasource.cluster[4].properties.url=***************************************************************************************************************************************************************************************************************************************************************
sharding.datasource.cluster[4].properties.username=dev_v8
sharding.datasource.cluster[4].properties.password=ENC(N1-6_d663dbbb6e4b0f3500729052d)

shareit.redis.use.old=false
# Redisç¸å³éç½®
shareit.cache.redis.database=10
shareit.cache.redis.redisType = standalone
shareit.cache.redis.prefix = channelExchangeDev:
shareit.cache.redis.hostName = master.pay-token-dev-new.5uzvt6.apse1.cache.amazonaws.com
shareit.cache.redis.port = 6379
shareit.cache.redis.password=ENC(N1-4_d664d67eae4b044a89169f4c9)
shareit.cache.redis.useSsl=true
# shareit.cache.redis.cluster = **************:6379
shareit.cache.redis.connectionTimeOut = 1000
shareit.cache.redis.commandTimeOut = 3000
shareit.cache.redis.maxTotal = 30
shareit.cache.redis.maxIdle = 2
shareit.cache.redis.minIdle = 1
new.shareit.cache.redis.database=10
new.shareit.cache.redis.redisType = standalone
new.shareit.cache.redis.prefix = channelExchangeDev:
new.shareit.cache.redis.hostName = master.pay-token-dev-new.5uzvt6.apse1.cache.amazonaws.com
new.shareit.cache.redis.port = 6379
new.shareit.cache.redis.password=ENC(N1-4_d664d67eae4b044a89169f4c9)
new.shareit.cache.redis.useSsl=true
new.shareit.cache.redis.connectionTimeOut = 1000
new.shareit.cache.redis.commandTimeOut = 3000
new.shareit.cache.redis.maxTotal = 30
new.shareit.cache.redis.maxIdle = 2
new.shareit.cache.redis.minIdle = 1

ushareit.redisson.lock.server.address=${shareit.cache.redis.hostName}:${shareit.cache.redis.port}
ushareit.redisson.lock.server.type=standalone
ushareit.redisson.lock.server.database=${shareit.cache.redis.database}
ushareit.redisson.lock.server.password=${shareit.cache.redis.password}
ushareit.redisson.lock.server.sslEnable=true
new.ushareit.redisson.lock.server.address=${new.shareit.cache.redis.hostName}:${new.shareit.cache.redis.port}
new.ushareit.redisson.lock.server.type=standalone
new.ushareit.redisson.lock.server.password=${new.shareit.cache.redis.password}
new.ushareit.redisson.lock.server.database=${new.shareit.cache.redis.database}
new.ushareit.redisson.lock.server.sslEnable=true

# æ°çåå¸å¼IDï¼rediséç½®ï¼ç¨äºè·åæºå¨ä½
distributed.redis.masterSlave.addr=${new.shareit.cache.redis.hostName}:${new.shareit.cache.redis.port}
distributed.redis.password=${new.shareit.cache.redis.password}
distributed.redis.ssl=true

# j2cacheç¸å³éç½®
finCache.j2cache.broadcast = redis
finCache.j2cache.L1.provider_class = caffeine
finCache.j2cache.L2.config_section = redis
finCache.j2cache.L2.provider_class = redis
finCache.j2cache.serialization = com.payermax.fin.exchange.serializer.J2CacheEncryptFastjsonSerializer
#finCache.caffeine.properties = /caffeine.properties
finCache.caffeine.region.default = 1000, 24h
finCache.caffeine.region.table = 1000, 23h
finCache.caffeine.region.tableEvent = 1000, 25h
finCache.redis.namespace = channelExchangeDev
finCache.redis.storage = generic
## redis pub/sub channel name
finCache.redis.channel = channelExchangeJ2cacheDev
finCache.redis.scheme = redis
finCache.redis.hosts = ${new.shareit.cache.redis.hostName}:${new.shareit.cache.redis.port}
finCache.redis.password =${new.shareit.cache.redis.password}
finCache.redis.database = ${new.shareit.cache.redis.database}
finCache.redis.timeout = 1000
finCache.redis.maxTotal = 100
finCache.redis.maxIdle = 10
finCache.redis.maxWaitMillis = 5000
finCache.redis.minEvictableIdleTimeMillis = 60000
finCache.redis.minIdle = 1
finCache.redis.testOnBorrow = false
finCache.redis.testOnReturn = false
finCache.redis.testWhileIdle = true
finCache.redis.ssl = true

# RocketMqç¸å³éç½®
# rocketmq name sever å°å ; åå²
rocketmq.name-server=************:9876
# rocket mq producer group
rocketmq.producer.group=pg_fin_channel_exchange
# åéæ¶æ¯è¶æ¶æ¶é¿ï¼é»è®¤3000ms
rocketmq.producer.send-message-timeout=3001
# åéåæ­¥æ¶æ¯å¤±è´¥æ¶ï¼éè¯æ¬¡æ°ï¼é»è®¤æ¯ 2
rocketmq.producer.retry-times-when-send-failed=2
# åéå¼æ­¥æ¶æ¯å¤±è´¥æ¶ï¼éè¯æ¬¡æ°ï¼é»è®¤æ¯ 2
rocketmq.producer.retry-times-when-send-async-failed=2
# åéæ¶æ¯ä½ç§¯éå¶ï¼é»è®¤æ¯ 4M
rocketmq.producer.max-message-size=4194304
# åéæ¶æ¯bodyè¶è¿å¤å¤§å¼å§åç¼©ï¼é»è®¤æ¯ 4096å­èï¼Consumeræ¶å°æ¶æ¯ä¼èªå¨è§£åç¼©ï¼
rocketmq.producer.compress-message-body-threshold=4096
# æ¹éææ¶æ¯ï¼ ä¸æ¬¡æå¤æå¤å°æ¡, é»è®¤æ¯ 10
rocketmq.consumer.pull-batch-size=10
#topic
rocketMq.topic.channel.exchange.rate.notify=topic_channel_exchange_rate_notify
rocketMq.topic.channel.exchange.forex.notify=topic_channel_exchange_forex_notify
rocketMq.topic.channel.exchange.cashier.async.notify=topic_channel_exchange_cashier_async_notify
rocketMq.topic.exchange.correction.notify = topic_correction_center_event_push

# kafkaç¸å³éç½®
kafka.producer = true
kafka.producer.servers = 172.20.52.50:9092
#åéå¤±è´¥éè¯æ¬¡æ°ï¼éç½®ä¸ºå¤§äº0çå¼çè¯ï¼å®¢æ·ç«¯ä¼å¨æ¶æ¯åéå¤±è´¥æ¶éæ°åéã
kafka.producer.retries = 0
#å½å¤æ¡æ¶æ¯éè¦åéå°åä¸ä¸ªååºæ¶ï¼çäº§èä¼å°è¯åå¹¶ç½ç»è¯·æ±ãè¿ä¼æé«clientåçäº§èçæçã
kafka.producer.batch.size = 16384
# çäº§èç»å°åéçæ¶æ¯ç»åæåä¸ªæ¹éè¯·æ±
kafka.producer.linger = 0
# çäº§èç¨æ¥ç¼å­ç­å¾åéå°æå¡å¨çæ¶æ¯çåå­æ»å­èæ°ï¼å³32MBçæ¹å¤çç¼å²åº
kafka.producer.buffer.memory = 33554432
# è¯·æ±çæå¤§å¤§å°
kafka.producer.max.request.size = 33554432
# topic
kafka.topic.exchange.pay.status.notify = channel.exchange.pay.status.notify
kafka.topic.exchange.refund.status.notify = channel.exchange.refund.status.notify
kafka.topic.exchange.payout.status.notify = channel.exchange.payout.status.notify
kafka.topic.exchange.authorization.status.notify = channel.exchange.authorization.status.notify
kafka.topic.exchange.rate.response.status.notify = channel-exchange-rate-response-status-notify
kafka.topic.exchange.forex.response.status.notify = channel-exchange-forex-response-status-notify

ribbon.ReadTimeout=10000
ribbon.ConnectTimeout=10000
ribbon.OkToRetryOnAllOperations=false
ribbon.MaxAutoRetriesNextServer=0
ribbon.MaxAutoRetries=0
feign.hystrix.enabled=false
feign.httpclient.enabled = true

# xxl config
xxl.job.admin.addresses=http://pay-xxl-dev-new.payermax.com/xxl-job-admin
#xxl.job.admin.addresses=http://pay-xxl-dev-new.shareitpay.in/xxl-job-admin
xxl.job.executor.appname=fin-channel-exchange
xxl.job.executor.port=8081

# å¨æçº¿ç¨æ± 
dynamic.threadpools.apollo-namespace=application
dynamic.threadpools.executors[0].thread-pool-name=default
dynamic.threadpools.executors[0].core-pool-size=2
dynamic.threadpools.executors[0].maximum-pool-size=4
dynamic.threadpools.executors[0].queue-capacity=3000
dynamic.threadpools.executors[0].fair=false
dynamic.threadpools.executors[0].keep-alive-time=60000
dynamic.threadpools.executors[1].thread-pool-name=payout
dynamic.threadpools.executors[1].core-pool-size=4
dynamic.threadpools.executors[1].maximum-pool-size=8
dynamic.threadpools.executors[1].queue-capacity=3000
dynamic.threadpools.executors[1].fair=true
dynamic.threadpools.executors[1].keep-alive-time=60000
dynamic.threadpools.executors[2].thread-pool-name=filter
dynamic.threadpools.executors[2].core-pool-size=4
dynamic.threadpools.executors[2].maximum-pool-size=8
dynamic.threadpools.executors[2].queue-capacity=3000
dynamic.threadpools.executors[2].fair=false
dynamic.threadpools.executors[2].keep-alive-time=60000
dynamic.threadpools.executors[3].thread-pool-name=grayCompare
dynamic.threadpools.executors[3].core-pool-size=2
dynamic.threadpools.executors[3].maximum-pool-size=4
dynamic.threadpools.executors[3].queue-capacity=1000
dynamic.threadpools.executors[3].fair=false
dynamic.threadpools.executors[3].keep-alive-time=60000

spring.dynamic.thread-pool.password = ENC(d64f15783e4b02d6d001cfba8)

# å¥æ¬¾å¼æ­¥åéç½®
pay.async.supportAsync={"CASHIER_CHECKOUT":{"I_GPAY_P01":["020113838535952","merchantNo1"],"I_GPAY_P01_CARDPAY201_TROY_TR":["merchantNo1"]},"API_ONLY":{"I_GPAY_P01":[]}}

shareit.distribute.id.regionFlag = 00
shareit.distribute.id.ruleCode = channel_commit_order_no
shareit.distribute.id.refreshUrl=http://*************:8089/fintech-base-service 

channel.sign.key = TVCAoozjDRdDnsM4
#internal.front.url = http://**************:8200/in-pay-channel-front
#internal.front.url = http://pay-dev.payermax.com/in-pay-channel-front
#internal.channel.url = http://pay-dev.payermax.com/in-pay-channel
#internal.front.url = localhost:8200/in-pay-channel-front
#internal.channel.url = localhost:8100/in-pay-channel
#in-pay-channel-front.service.url=http://pay-dev.payermax.com
payout.omcQuery.autoRetry = true
project.env=local
# mock
mock.domain = http://alb-pay-yapi-dev-208939230.ap-south-1.elb.amazonaws.com/mock/287
#internal.front.url = http://alb-pay-yapi-dev-208939230.ap-south-1.elb.amazonaws.com/mock/287

# callbackå»¶è¿æ¶è´¹ç»
rocketmq.consumer.group_id=cg_${spring.application.name}_callback_delay

# \u5DEE\u9519\u5904\u7406
correction.code.map = {"CORRECTION_CODE_ONE":"CC001","CORRECTION_CODE_TWO":"CC003","CORRECTION_CODE_THREE":"CC004","CORRECTION_CODE_FIVE":"CC005","CORRECTION_CODE_SIX":"CC010","CORRECTION_CODE_SEVEN":"CC011"}
refund.transfer.correction.errCode = CHANNEL_NOT_SUPPORT_REFUND,EXCEED_REFUNDABLE_TIME,NOT_SUPPORT_PARTIAL_REFUND,NOT_SUPPORT_MUL_REFUND
common.mapRespCodeOnSys.systemIdMap={"RISK_QUERY":"Pay-fin-risk-engine","CORREC_CENTER":"Pay-correction-center","FUNDS_RECEIPT":"funds-receipt"}

mybatis.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

order.verify.strategy={"verifyLevel":3,"subVerifyPropMap":{"APPLY":{"verifyLevel":3,"subVerifyPropMap":{"amountVerifyRule":{"verifyLevel":3,"subVerifyPropMap":{"I_ALIPAYHK_P01_02":{"verifyLevel":0},"I_ALIPAYHK_T01_02":{"verifyLevel":0},"I_XENDIT_P01_01":{"verifyLevel":0},"I_WORLDPAY_P01":{"verifyLevel":0},"I_WORLDPAY_P01_02":{"verifyLevel":0},"I_JMSA01_AMAZONPAYSA01_STCPAYSA01_01":{"verifyLevel":0},"I_MPGS_NI":{"verifyLevel":0}}}}},"CALLBACK":{"verifyLevel":3,"subVerifyPropMap":{"amountVerifyRule":{"verifyLevel":3,"subVerifyPropMap":{"O_PMHK01_JOLLYMAXHK_01":{"verifyLevel":0}}}}},"INQUIRY":{"verifyLevel":3,"subVerifyPropMap":{"amountVerifyRule":{"verifyLevel":3,"subVerifyPropMap":{"O_PMHK01_JOLLYMAXHK_01":{"verifyLevel":0},"I_XENDIT_P01_01":{"verifyLevel":0}}}}}}}
# æ¬ç³»ç»å ç­¾ç§é¥
self.system.sign.privateKey=ENC(d65084995e4b0e4ea4a57ff65)
# bankCode:methodSubCode
queryPayElement.bankCodeMap={"ZIRAATBANK":"ziraat","ISBANK":"isbank","AKBANK":"akbank","DENIZBANK":"denizbank","PTT":"ptt","VAKIFBANK":"vakifbank","YAPIKREDI":"yapikredi","GARABTIBANK":"********","KUVEYTTURK":"********","TURKIYEFINANSKATILIM":"********","SEKERBANK":"********"}
# ç¼å­ç°åº¦éç½®
cache.gray.config=[{"businessKey":"ALL","weight":"100"}]
# encè§£å¯æ¯å¦å¼ºå¶
enc.decrypt.strong=false
# åºæ¬¾éè¦è¿è¡é£æ§å®¡æ ¸çæ¯ä»æ¹å¼ç±»å
payout.risk.payment-method-types=BankTransfer,BANK_TRANSFER
exchange.tokenpay.gray.config={}