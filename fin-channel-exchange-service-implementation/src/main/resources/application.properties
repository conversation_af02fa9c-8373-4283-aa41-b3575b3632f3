spring.main.banner-mode=off
spring.jackson.default-property-inclusion=non_null
spring.main.allow-bean-definition-overriding=true
spring.application.name=fin-channel-exchange
server.port=8080
server.servlet.context-path=/fin-channel-exchange
server.compression.enabled=true
server.compression.mime-types=application/json,text/html
# è®¾ç½®IOçº¿ç¨æ°, å®ä¸»è¦æ§è¡éé»å¡çä»»å¡,å®ä»¬ä¼è´è´£å¤ä¸ªè¿æ¥, é»è®¤è®¾ç½®æ¯ä¸ªCPUæ ¸å¿ä¸ä¸ªçº¿ç¨
# ä¸è¦è®¾ç½®è¿å¤§ï¼å¦æè¿å¤§ï¼å¯å¨é¡¹ç®ä¼æ¥éï¼æå¼æä»¶æ°è¿å¤
server.undertow.threads.io=16
# é»å¡ä»»å¡çº¿ç¨æ± , å½æ§è¡ç±»ä¼¼servletè¯·æ±é»å¡IOæä½, undertowä¼ä»è¿ä¸ªçº¿ç¨æ± ä¸­åå¾çº¿ç¨
# å®çå¼è®¾ç½®åå³äºç³»ç»çº¿ç¨æ§è¡ä»»å¡çé»å¡ç³»æ°ï¼é»è®¤å¼æ¯IOçº¿ç¨æ°*8
server.undertow.threads.worker=256
# ä»¥ä¸çéç½®ä¼å½±åbuffer,è¿äºbufferä¼ç¨äºæå¡å¨è¿æ¥çIOæä½,æç¹ç±»ä¼¼nettyçæ± ååå­ç®¡ç
# æ¯åbufferçç©ºé´å¤§å°,è¶å°çç©ºé´è¢«å©ç¨è¶ååï¼ä¸è¦è®¾ç½®å¤ªå¤§ï¼ä»¥åå½±åå¶ä»åºç¨ï¼åéå³å¯
server.undertow.buffer-size=1024
server.undertow.url-charset=UTF-8
# æ¯å¦åéçç´æ¥åå­(NIOç´æ¥åéçå å¤åå­)
server.undertow.direct-buffers=true
# eurekaæå¡çå°å
dubbo.application.name=Pay-${spring.application.name}
#dubbo.registry.group=${spring.application.name}
dubbo.protocol.name=dubbo
dubbo.protocol.port=-1
exchange.dubbo.api.version = 1.0
# nacos config
spring.cloud.nacos.discovery.enabled=true
spring.cloud.nacos.discovery.group=FEIGN_GROUP
#endpointsæ´é²
management.endpoints.web.exposure.include=online,offline,health,info,prometheus
management.server.port=10108
management.metrics.distribution.percentiles.fusion=0.95,0.99
management.metrics.tags.application=${spring.application.name}
spring.sleuth.enabled=false

# nacos
nacos.config.bootstrap.enable=true
nacos.config.server-addr=${nacos.config.server-addr}
nacos.config.username=${nacos.config.username}
nacos.config.password=${nacos.config.password}
nacos.config.namespace=${nacos.config.namespace}
nacos.config.group=${spring.application.name}
nacos.config.type=properties
nacos.config.data-ids=application.properties,j2cache
nacos.config.remote-first=true
nacos.config.auto-refresh=true
nacos.config.enable-remote-sync-config=true
#éç½®é¡ºåºä¼åçº§ï¼éç½®å è½½é¡ºåºext-config listä»åå¾åï¼åºå·ä»å¤§å¾å°ï¼å è½½ï¼æåå è½½ä¸é¢configéç½®ï¼åå è½½çååå±æ§ä¼è¦çåå è½½çï¼ä¸å¦æåå è½½çéç½®è¦çåé¢çï¼åªè½ä¿®æ¹æåå è½½çéç½®æä»¶æè½çæ
nacos.config.ext-config[0].data-ids=infra-fs.properties
nacos.config.ext-config[0].group=infra_common_config
nacos.config.ext-config[0].type=properties

## ä¸æ§å¶çé¢éä¿¡çç«¯å£
spring.cloud.sentinel.transport.port=8791
## éæµéç½®æ°æ®æº
# éæµ
spring.cloud.sentinel.datasource.flow.nacos.server-addr=${nacos.config.server-addr}
spring.cloud.sentinel.datasource.flow.nacos.username=${nacos.config.username}
spring.cloud.sentinel.datasource.flow.nacos.password=${nacos.config.password}
spring.cloud.sentinel.datasource.flow.nacos.namespace=${nacos.config.namespace}
spring.cloud.sentinel.datasource.flow.nacos.group-id=sentinel-${spring.application.name}
spring.cloud.sentinel.datasource.flow.nacos.data-id=SENTINEL_RULE_DATA_ID
spring.cloud.sentinel.datasource.flow.nacos.rule-type=FLOW
# çæ­
spring.cloud.sentinel.datasource.degrade.nacos.server-addr=${nacos.config.server-addr}
spring.cloud.sentinel.datasource.degrade.nacos.username=${nacos.config.username}
spring.cloud.sentinel.datasource.degrade.nacos.password=${nacos.config.password}
spring.cloud.sentinel.datasource.degrade.nacos.namespace=${nacos.config.namespace}
spring.cloud.sentinel.datasource.degrade.nacos.group-id=sentinel-${spring.application.name}
spring.cloud.sentinel.datasource.degrade.nacos.data-id=SENTINEL_DEGRADE_DATA_ID
spring.cloud.sentinel.datasource.degrade.nacos.rule-type=DEGRADE

# æ°æ®åºç¸å³éç½®
mybatis.mapper-locations=classpath:mapper/*/*.xml
# è·åé¾æ¥åï¼æ£æ¥é¾æ¥æ¯å¦å¯ç¨çè¶æ¶æ¶é´ï¼åä½æ¯ç§ï¼é»è®¤-1
sharding.datasource.global.properties.validationQueryTimeout=2
sharding.datasource.properties.max.connections.size.per.query=8
# åçè§å
sharding.db.number=2
sharding.actualDb.number=4
sharding.table.number=8
sharding.db.offset=0
sharding.strategy.enablePhysicalHint=true
sharding.strategy.shardingStrategy[tb_funding_channel_request_order].shardingKeyStrategy=biz_order_no:substring(20,24);channel_pay_request_no:substring(20,24);payment_type:hash
sharding.strategy.shardingStrategy[tb_funding_channel_request_order].logicDB=fin-channel-exchange
sharding.strategy.shardingStrategy[tb_funding_channel_request_order].dbCount=${sharding.db.number}
sharding.strategy.shardingStrategy[tb_funding_channel_request_order].actualDbCount=${sharding.actualDb.number}
sharding.strategy.shardingStrategy[tb_funding_channel_request_order].tableCount=${sharding.table.number}
sharding.strategy.shardingStrategy[tb_funding_channel_request_order].offset=${sharding.db.offset}
sharding.strategy.shardingStrategy[tb_funding_channel_request_order].assignableClassName=migrationShardingAlgorithmUtils
sharding.strategy.shardingStrategy[tb_funding_channel_commit_order].shardingKeyStrategy=channel_pay_request_no:substring(20,24);channel_pay_commit_no:substring(3,7)
sharding.strategy.shardingStrategy[tb_funding_channel_commit_order].logicDB=fin-channel-exchange
sharding.strategy.shardingStrategy[tb_funding_channel_commit_order].dbCount=${sharding.db.number}
sharding.strategy.shardingStrategy[tb_funding_channel_commit_order].actualDbCount=${sharding.actualDb.number}
sharding.strategy.shardingStrategy[tb_funding_channel_commit_order].tableCount=${sharding.table.number}
sharding.strategy.shardingStrategy[tb_funding_channel_commit_order].offset=${sharding.db.offset}
sharding.strategy.shardingStrategy[tb_funding_channel_commit_order].assignableClassName=migrationShardingAlgorithmUtils
sharding.strategy.shardingStrategy[tb_funding_channel_result_order].shardingKeyStrategy=channel_pay_request_no:substring(20,24);channel_pay_commit_no:substring(3,7)
sharding.strategy.shardingStrategy[tb_funding_channel_result_order].logicDB=fin-channel-exchange
sharding.strategy.shardingStrategy[tb_funding_channel_result_order].dbCount=${sharding.db.number}
sharding.strategy.shardingStrategy[tb_funding_channel_result_order].actualDbCount=${sharding.actualDb.number}
sharding.strategy.shardingStrategy[tb_funding_channel_result_order].tableCount=${sharding.table.number}
sharding.strategy.shardingStrategy[tb_funding_channel_result_order].offset=${sharding.db.offset}
sharding.strategy.shardingStrategy[tb_funding_channel_result_order].assignableClassName=migrationShardingAlgorithmUtils
sharding.strategy.shardingStrategy[tb_funding_order_no_mapping].shardingKeyStrategy=target_order_no:hash;channel_pay_request_no:substring(20,24)
sharding.strategy.shardingStrategy[tb_funding_order_no_mapping].logicDB=fin-channel-exchange
sharding.strategy.shardingStrategy[tb_funding_order_no_mapping].dbCount=${sharding.db.number}
sharding.strategy.shardingStrategy[tb_funding_order_no_mapping].actualDbCount=${sharding.actualDb.number}
sharding.strategy.shardingStrategy[tb_funding_order_no_mapping].tableCount=${sharding.table.number}
sharding.strategy.shardingStrategy[tb_funding_order_no_mapping].offset=${sharding.db.offset}
sharding.strategy.shardingStrategy[tb_funding_order_no_mapping].assignableClassName=migrationShardingAlgorithmUtils
sharding.strategy.shardingStrategy[tb_funding_channel_order_compensate].shardingKeyStrategy=channel_pay_request_no:substring(20,24);payment_type:hash
sharding.strategy.shardingStrategy[tb_funding_channel_order_compensate].logicDB=fin-channel-exchange
sharding.strategy.shardingStrategy[tb_funding_channel_order_compensate].dbCount=${sharding.db.number}
sharding.strategy.shardingStrategy[tb_funding_channel_order_compensate].actualDbCount=${sharding.actualDb.number}
sharding.strategy.shardingStrategy[tb_funding_channel_order_compensate].tableCount=1
sharding.strategy.shardingStrategy[tb_funding_channel_order_compensate].offset=${sharding.db.offset}
sharding.strategy.shardingStrategy[tb_funding_channel_order_compensate].assignableClassName=migrationShardingAlgorithmUtils
sharding.strategy.shardingStrategy[tb_funding_payout_order_compensate].shardingKeyStrategy=channel_pay_request_no:substring(20,24);payment_type:hash
sharding.strategy.shardingStrategy[tb_funding_payout_order_compensate].logicDB=fin-channel-exchange
sharding.strategy.shardingStrategy[tb_funding_payout_order_compensate].dbCount=${sharding.db.number}
sharding.strategy.shardingStrategy[tb_funding_payout_order_compensate].actualDbCount=${sharding.actualDb.number}
sharding.strategy.shardingStrategy[tb_funding_payout_order_compensate].tableCount=1
sharding.strategy.shardingStrategy[tb_funding_payout_order_compensate].offset=${sharding.db.offset}
sharding.strategy.shardingStrategy[tb_funding_payout_order_compensate].assignableClassName=migrationShardingAlgorithmUtils
sharding.strategy.shardingStrategy[tb_funding_channel_payment_token].shardingKeyStrategy=payment_token_id:substring(25,29)
sharding.strategy.shardingStrategy[tb_funding_channel_payment_token].logicDB=fin-channel-exchange
sharding.strategy.shardingStrategy[tb_funding_channel_payment_token].dbCount=${sharding.db.number}
sharding.strategy.shardingStrategy[tb_funding_channel_payment_token].actualDbCount=${sharding.actualDb.number}
sharding.strategy.shardingStrategy[tb_funding_channel_payment_token].tableCount=1
sharding.strategy.shardingStrategy[tb_funding_channel_payment_token].offset=${sharding.db.offset}
sharding.strategy.shardingStrategy[tb_funding_channel_payment_token].assignableClassName=migrationShardingAlgorithmUtils

sharding.strategy.shardingStrategy[tb_authorization].shardingKeyStrategy=internal_auth_code;external_auth_refer_no,create_time
sharding.strategy.shardingStrategy[tb_authorization].logicDB=default
sharding.strategy.shardingStrategy[tb_authorization].databaseAliasPrefix=,new-
sharding.strategy.shardingStrategy[tb_authorization].dbCount=1
sharding.strategy.shardingStrategy[tb_authorization].offset=0
sharding.strategy.shardingStrategy[tb_authorization].assignableClassName=migrationAlgorithmUtils
sharding.strategy.shardingStrategy[tb_bounce_back_order].shardingKeyStrategy=bounce_back_order_no;payout_channel_order_no;pay_order_no
sharding.strategy.shardingStrategy[tb_bounce_back_order].logicDB=default
sharding.strategy.shardingStrategy[tb_bounce_back_order].databaseAliasPrefix=,new-
sharding.strategy.shardingStrategy[tb_bounce_back_order].dbCount=1
sharding.strategy.shardingStrategy[tb_bounce_back_order].offset=0
sharding.strategy.shardingStrategy[tb_bounce_back_order].assignableClassName=migrationAlgorithmUtils

# æ°æ®åºè¿ç§»éç½®
database.migrate.fin.sharding.grayConfig=
database.migrate.fin.job.grayConfig=
database.migrate.pay.start=false
database.migrate.pay.finish=true
database.migrate.fin.finish=true
database.migrate.fin.sharding.notGrapBusiness.switchToNew=false
database.migrate.pay.bounce.limit.id=10000
database.migrate.pay.auth.limit.id=6000000

entity.relation.map = {"T01":"T01","P01":"P01"}
entity.old.relation.map = {"T01":"2","P01":"3","S02":"5","P02":"6","P05":"7","S04":8}
terminal.relation.map = {"WEB":"PC","WAP":"PMA","APP":"PMA"}
params.relation.map = {"cardIdentifierNo":"cardNo,cardHolderName,cardExpirationYear,cardExpirationMonth,cardCategory","cvvToken":"cvv"}
order.compensate.default-retry-rule = 5m,10m,30m,1h,3h,6h,12h,24h
order.compensate.retry-rule.map = {"tmp":""}
ignore.transaction_time.channels = I_MIDTRANS_P01_01,I_MIDTRANS_P01_02
desensitize.flag =
request.sensitive.field.map = {"cvv":".params.cvv"}
commit.sensitive.field.map = {"cvv":".cvv"}
new.request.sensitive.field.map = {".params.cvv":"cvv"}
new.commit.sensitive.field.map = {".cvv":"cvv",".payerExtraDetails.cvv":"cvv"}
db.encrypt.password = ENC(d62a1a9abe4b09559d7fb1475)
current.db.encrypt.token = d62a1a9abe4b09559d7fb1475
current.redis.encrypt.token = d62a1a9abe4b09559d7fb1475
db.encrypt.token.map = {"d62a1a9abe4b09559d7fb1475":"ENC(d62a1a9abe4b09559d7fb1475)"}
handle.instance.param.map = {"CardPay-I_AMAZONPAY_NETWORK_S02": {"!020113833478340": "cardHolderName"}}
channel.method.map = {"tmp":""}
fintech.dingtalk.group.connect-timeout=2000
fintech.dingtalk.group.map-group.payoutNoSubMerchant=https://dingding-prod.payermax.com/robot/send?access_token=07702f19bf2bcfa801b86cae164093e404e78cc38095ccafc5e98a06fae45288
fintech.dingtalk.group.map-group.payoutBounceBack=https://dingding-prod.payermax.com/robot/send?access_token=07702f19bf2bcfa801b86cae164093e404e78cc38095ccafc5e98a06fae45288
fintech.dingtalk.group.map-group.channelStatusInconsistent=https://dingding-prod.payermax.com/robot/send?access_token=a4457610c611e98501d19e5ba06d6eef9d3553a79862f6d06ddb0d0a82904ae6
fintech.dingtalk.group.map-group.retry = https://dingding-prod.payermax.com/robot/send?access_token=9526548a65c09d7e200def2996060d5ac51ae2e6dad95f8fd42e035c78755982
fintech.dingtalk.group.map-group.orderResponseVerify = https://dingding-prod.payermax.com/robot/send?access_token=f26d3182ff6ae2b761988937e447d9af12d56581736afb03e1434efb8e8b2b54
fintech.dingtalk.group.map-group.insufficientBalance = https://dingding-prod.payermax.com/robot/send?access_token=07702f19bf2bcfa801b86cae164093e404e78cc38095ccafc5e98a06fae45288
fintech.dingtalk.group.map-group.limitHold = https://dingding-prod.payermax.com/robot/send?access_token=07702f19bf2bcfa801b86cae164093e404e78cc38095ccafc5e98a06fae45288
fintech.dingtalk.group.map-group.correctionHandleException = https://dingding-prod.payermax.com/robot/send?access_token=ddcd99caf073058e624172e9fa391d7b31256283141dc24accddc2a076d0ed7a
fintech.dingtalk.group.map-group.channelThirdOrderNoInconsistent=https://dingding-prod.payermax.com/robot/send?access_token=a4457610c611e98501d19e5ba06d6eef9d3553a79862f6d06ddb0d0a82904ae6
# é£ä¹¦åè­¦å°åéç½®
infra.ionia.notice.group.mapGroup={"payoutNoSubMerchant":"https://open.feishu.cn/open-apis/bot/v2/hook/fa1afc0c-8c6d-4dc3-a567-da379614e9a2","payoutBounceBack":"https://open.feishu.cn/open-apis/bot/v2/hook/fa1afc0c-8c6d-4dc3-a567-da379614e9a2","channelStatusInconsistent":"https://open.feishu.cn/open-apis/bot/v2/hook/fa1afc0c-8c6d-4dc3-a567-da379614e9a2","retry":"https://open.feishu.cn/open-apis/bot/v2/hook/fa1afc0c-8c6d-4dc3-a567-da379614e9a2","orderResponseVerify":"https://open.feishu.cn/open-apis/bot/v2/hook/fa1afc0c-8c6d-4dc3-a567-da379614e9a2","insufficientBalance":"https://open.feishu.cn/open-apis/bot/v2/hook/fa1afc0c-8c6d-4dc3-a567-da379614e9a2","limitHold":"https://open.feishu.cn/open-apis/bot/v2/hook/fa1afc0c-8c6d-4dc3-a567-da379614e9a2","correctionHandleException":"https://open.feishu.cn/open-apis/bot/v2/hook/fa1afc0c-8c6d-4dc3-a567-da379614e9a2","channelThirdOrderNoInconsistent":"https://open.feishu.cn/open-apis/bot/v2/hook/fa1afc0c-8c6d-4dc3-a567-da379614e9a2"}

#dsnï¼data source nameï¼ä¸ºæ¯ä¸ä¸ªé¡¹ç®çå¯ä¸ç¼å·ï¼åå»ºsentryé¡¹ç®åä¼æä¾ç»ä½ 
sentry.dsn=https://<EMAIL>/53
#ä¸éç½®ç¯å¢åéé»è®¤ä¸ºproduction
#ç°å¨çº¦å®åä¸ªç¯å¢å½åè§å(local,dev,alpha,test,staging,uat,prod)ä¸åè®¸ä¹±åå¶ä»ç¯å¢
#æèä½¿ç¨ä¸é¢è¿ç§æ¹å¼
sentry.environment=${spring.profiles.active}
#å¼å¯ç¨åºæ§è½çæ§ï¼éæ ·ç0.2
sentry.traces-sample-rate=0.1
#åå¸çæ¬éç¥ï¼æ¯æ¬¡è¿­ä»£é½éè¦ä¿®æ¹ï¼ç¨æ¥æ è®°bugæ¯å¨åªä¸ªçæ¬äº§çæ¹ä¾¿å®ä½bug
sentry.release=release20220526
#éè¯¯æ¥å¿éæ ·ç0.0-1.0 é»è®¤æ¯1 æ è¯100%éæ ·
sentry.sample-rate=1
#sentryè¿æ¥æå¡å¨æ¶é´é²æ­¢sentryä¸å´©æºï¼å½±åä¸å¡è¯·æ±
#å½ç¶3sä¹å¯è½ä¼å½±åï¼æ³¨æçµæ´»è°æ´
sentry.connection-timeout-millis=3000
#sentryç­å¾æå¡ç«¯ååºæ¶é´ï¼é²æ­¢sentryä¸å´©æºï¼å½±åä¸å¡è¯·æ±
sentry.read-timeout-millis=3000

#ä¸é¢ä¸¤ä¸ªéç½®é½æ¯é»è®¤çï¼ä¸éè¦éç½®ï¼ä½æ¯ä½ æ³è¦sentryè®°å½infoæ¥å¿çæ¶åå¯ä»¥ä¿®æ¹ä¸
#è¡¨ç¤ºåéç»sentryçlogçº§å«å°±æ¯error
sentry.logging.minimum-event-level=error
#è¿ä¸ªè¡¨ç¤ºæ¯ä¸ªissueéè¾¹erroræ¥å¿æå°çæ¶åï¼åä¸ä¸ªçº¿ç¨çç¸å³çinfoæ¥å¿
sentry.logging.minimum-breadcrumb-level=info

payout.async.supportAsync = true
payout.async.supportMethodStr = [{"paymentMethodType":"","targetOrg":""}]
payout.async.supportMerchantStr = {}

# è®¢åååºæ¥æåæ³æ§æ ¡éªç­ç¥
order.verify.strategy = {"verifyLevel":1}
order.verify.amount.rate.map = {"tmp":0}

rocketMq.rate.flag = false
rocketMq.forex.flag = false
rocketMq.auth.flag = false

spring.dynamic.thread-pool.server-addr = http://hippo4j-dev-new.payermax.com
spring.dynamic.thread-pool.namespace = channel
spring.dynamic.thread-pool.item-id = ${spring.application.name}
spring.dynamic.thread-pool.username = admin
spring.dynamic.thread-pool.enable = true

# productCodeåç¨éæ å°å³ç³»
use-type.relation.map={"3002":"WITHDRAW","SETTLEMENT":"SETTLEMENT","DISBURSEMENT":"ISDRAW_BEHALF","RECHARGE":"RECHARGE"}
bank-inst.relation.map={"CITI":"CITIHK01","DHBK":"DBSHK01"}
clear-system.route.order.map={"RTGS": 10, "CBFT": 20}
country.clear-system.route.order.map={"ID": {"INTERNAL":10,"BIFAST":20,"FAST":20,"ONLINE":30,"OT":30,"LLG":40,"OCBC_RTGS":50,"RTGS":50,"TT":60,"OAT":70}}

gray.business.tables[0]=tb_funding_channel_commit_order
gray.business.currentEnv=${spring.profiles.active}
gray.business.currentTag=${dubbo.provider.tag:}

# æ¸ éèªå¨éç¥¨
callback.auto-bounce-back-map={"ALL":true}

# è±æ
com.payermax.desensitize.mask.fields=shippingPhone|shippingPhoneNumber|billingPhone|buyerPhoneNo|payerPhoneNo|payeePhone|payeeAccount|payerAccount|userEmail|payerEmail|billingEmail|buyerEmail|emailId|cvv|cardNo|payerPanCard|taxNo|pan|ci|cc|curp|cuit|dni_cuit|dni|cnic|cpf|cnpj|cpf_cnpj|idCard|targetMerchantSecretKey|configJson|authorizeCode|channelRespAuthCode|refreshToken|accessToken
# æè¦æ¥å¿å¿½ç¥æ¹æ³
digest.log.ignore.method=ContextProvider.query,ContextProvider.save

# æ¢å·éè¯éç½®
channel.retry.max-count.map={"20":1}
fawry.mail.receiveMap={"test":""}

exchange.instance.return.ext-propertys=ACCOUNT_TYPE,SUPPORT_CARD_ORG,SUPPORT_CARD_ISSUE_COUNTRY,TERMINAL_TYPE,DDC_ORG,CHANNEL_TERMINAL_TYPE,IS_SUPPORT_CARD_ON_FILE
# ddcæºæåddcSessionIdå­æ®µæ å°å³ç³»
ddc.session-field.map={"MPGS":"mpgsDdcSessionMap","I_WORLDPAY_P01":"cardinalDdcSessionMap","HITRUST":"hitrustDdcSessionMap","CYBS":"cybsDdcSessionMap","CARDINAL":"cardinalDdcSessionMap"}
ddc.mandatory.map={"HITRUST":"Y","MPGS":"Y","CYBS":"Y"}
ddc.channel.map={"CYBS":"I_CYBERSOURCE","HITRUST":"I_HITRUST","MPGS":"I_MPGS_NI","CYBS_NI":"I_CYBS_NI"}
# æ°èredisåæ¢ï¼æ¯å¦ä½¿ç¨èrediséç¾¤
shareit.redis.use.old=true
# redissoné
ushareit.redisson.lock.server.type=standalone
# rocketMq topic
rocketmq.consumer.group_id=${spring.application.name}
rocketmq.consumer.update.group_id=cg_${spring.application.name}_update_delay
rocketMq.topic.channel.exchange.transfer.notify=topic_channel_exchange_transfer_notify
rocketMq.topic.channel.exchange.callback.delay.topic=topic_channel_exchange_callback_delay
rocketMq.topic.channel.exchange.update.delay.topic=topic_channel_exchange_update_delay
rocketMq.topic.channel.exchange.resp.code.save.topic=topic_channel_exchange_channel_resp_code_save
rocketMq.topic.exchange.pay.status.notify = topic_channel_exchange_pay_notify
rocketMq.topic.exchange.refund.status.notify = topic_channel_exchange_refund_notify
rocketMq.topic.exchange.payout.status.notify = topic_channel_exchange_payout_notify
rocketMq.topic.exchange.authorization.status.notify = topic_channel_exchange_authorization_notify
rocketMq.topic.channel.exchange.arn.save.topic=topic_card_biz_no_sync
rocketMq.topic.channel.exchange.config.change.topic=topic_channel_exchange_config_change
rocketMq.topic.channel.exchange.execute.3ds.notify.topic=topic_channel_exchange_execute_3ds_notify
# rocketMq consumerGroup
# ä¿å­æ¸ éåå§ååºæ¶è´¹ç»
rocketmq.consumer.channel.resp.code.group_id=cg_${spring.application.name}_channel_resp_code_save
rocketmq.consumer.arn.save.group_id=cg_${spring.application.name}_card_biz_no_sync
rocketmq.consumer.channel.config.change.group_id=cg_${spring.application.name}_channel_config_change
rocketMq.topic.request-type.relation.map={"applyPaymentToken":"topic_channel_exchange_apply_payment_token_notify","capture":"topic_channel_exchange_capture_notify","void":"topic_channel_exchange_void_notify"}

# ç­éè§å-éè¯¯ç æ å°å³ç³»
rule.error_code.relation.map={"TargetMerchantFilterRule":"ONBOARD_ERROR","DdcFilterRule":"DDC_INVALID"}
pay.rule.error_code_msg.map={"DDC_INVALID":"ddc session id is invalid","PARAMETER_INVALID":"request parameter invalid"}

# ææåè®®è¿æé»è®¤å¼ï¼ç¨äºè¡¥è¶³æ²¡æè¿ææ¶é´çåºæ¯ï¼2999-01-01 00:00:00
default.protocol.endTime=***********

get.bill.record.url = f/standard/getTransactionBill
inquiry.auth.userInfo.url = f/standard/inquiryAuthorizationUserInfo

channel.workDay.map = {"O_ENBD":{"holidays": ["2023-06-27~2023-06-30","2023-07-21","2023-09-29","2023-12-01~2023-12-03"], "timezone": "+04:00", "weekends": [7], "workTimes": [{"endTime": "17:00:00","startTime": "09:00:00"}]}}
insufficientBalance.code=D310

support.checkJsonStr={"affinBankCodeList":["ABNAMYKL","AFBQMYKL","AGOBMYKL","AISLMYKL","ALSRMYKL","ARBKMYKL","BIMBMYKL","BKCHMYKL","BKKBMYKL","BKRMMYKL","BMMBMYKL","BNPAMYKL","BOFAMY2X","BOTKMYKX","BSNAMYK1","CHASMYKX","CIBBMYKL","CITIMYKL","CITIMYM1","CTBBMYKL","DEUTMY21","DEUTMYKL","EOBBMYKL","HBMBMYKL","HLBBMYKL","HLIBMYKL","ICBKMYKL","KFHOMYKL","MBBEMYKL","MFBBMYKL","MHCBMYKA","OCBCMYKL","PBBEMYKL","PCBCMYKL","PIBEMYK1","RHBBMYKL","RJHIMYKL","SCBLMYKX","SMBCMYKL","UOVBMYKL","PHBMMYKL"]}
# S3æ¡¶
aws.s3.region=ap-southeast-1
# æå®äº¤ææ­¥éª¤éè¦å¿½ç¥äºçº§åæ·æ ¡éª
target-merchant.ignore.trans-steps=SETTLEMENT,WITHDRAW

# æ¸ éèªå¨åæ¢ç°åº¦ååéç½®
channel.auto.route.gray={"grayMinutes":30,"grayPercent":10,"grayCount":5}
# éè¦ç¼å­ddcæ¸ éä¿¡æ¯çæ¸ éç¼ç 
ddc.cache.channel-codes=I_MPGS_NI

# æ¶é¶äº§åçæ¬
cashier-product.gray.versions=V2.2
cashier-product.new.versions=V2.3,V3
cashier.route.is-filter-when-no-route.enable=false
# è·¯ç±æ¶éè¦æ ¡éªæ­¥éª¤çè¯·æ±ç±»å
route.check.step.request-types=authorizationApply,applyPaymentToken,revokePaymentToken
# è¯·æ±ç±»åå¯¹åºçé»è®¤url
request-type.url.default.map={"pay":"/f/standard/paymentApply","payout":"/f/standard/payoutApply","refund":"/f/standard/refundApply","payRisk":"send3ds","capture":"/f/standard/captureApply","void":"/f/standard/voidApply","capture_inquiry":"/f/standard/captureInquiry","void_inquiry":"/f/standard/voidInquiry","close":"/f/standard/closePaymentApply"}
# ä½¿ç¨æ°è¡¥å¿æ¥è¯¢é»è¾çè¯·æ±ç±»å
use.new.compensate.request-types=capture,void
# ç³è¯·æ¯ä»tokenï¼æ ¹æ®æ¸ ééç½®ä¸åçéé¢
channel.apply.payment.token.default.amount.map={"I_YOOMONEY_P01":1,"I_WORLDPAY_P01":0}
# ç»æåä¿¡æ¯è±æè§å
result.order.desensitize.rule.map={"YOOMONEY":"YOUMI","yoomoney":"youmi","YooMoney":"YouMi"}

# éèäº¤æ¢å¡ç»
channel.exchange.card-orgs=VISA,MASTERCARD,MADA,JCB,AMEX,TROY,KNET,NAPS,CARDPAY,SETTLEORG

exchange.instance.filter.rule-map={"MERCHANT_REPORT":["simpleFilterRule","cashierProductFilterRule","grayRouteStatusUnavailableFilterRule","requestTypeFilterRule","apiTypeFilterRule","extendPropertyFilterRule","customizationRouteFilterRule","specialListFilterRule"],"CASHIER_PRODUCT_REPORT":["simpleFilterRule","cashierProductFilterRule"]}

differences.compare.rulenamemap={"SimpleFilterRule":"compare","SpecialListFilterRule":"compare","SpecialListWeightFilterRule":"compare"}
differences.compare.failed={"SimpleFilterRule":"Y"}

exchange.token.threeds.auth.update.map={"N_MASTERCARD":"PASSKEY"}