<?xml version="1.0" encoding="UTF-8"?>

<!--设置log4j2的自身log级别为error-->
<configuration status="error" packages="org.apache.logging.log4j.core,io.sentry.log4j2,com.payermax.infra.ionia.log.mask.util">
    <Properties>
        <Property name="dir">logs</Property>
        <Property name="logFormat">[%d{yyyy-MM-dd HH:mm:ss:SSS}] [%traceId] [%-5level] [%t] [fin-channel-exchange] [%c{1.}(%L)] %lmc%n</Property>
        <Property name="every_file_size">100MB</Property>
        <Property name="log_level">info</Property>
    </Properties>

    <appenders>
        <console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="${logFormat}"/>
        </console>
        <RollingFile name="RollingFileInfo" fileName="${dir}/info.log" filePattern="${dir}/info-%d{yyyy-MM-dd}-%i.log">
            <PatternLayout pattern="${logFormat}"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="${every_file_size}"/>
            </Policies>
            <DefaultRolloverStrategy max="20">
                <Delete basePath="${dir}" maxDepth="2">
                    <IfFileName glob="*-202*.log"/>
                    <!--30天 -->
                    <IfLastModified age="P30D"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>
        <RollingFile name="RollingFileWarn" fileName="${dir}/warn.log" filePattern="${dir}/warn-%d{yyyy-MM-dd}-%i.log">
            <Filters>
                <ThresholdFilter level="WARN"/>
                <ThresholdFilter level="ERROR" onMatch="DENY" onMismatch="NEUTRAL"/>
            </Filters>
            <PatternLayout pattern="${logFormat}"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="${every_file_size}"/>
            </Policies>
        </RollingFile>
        <RollingFile name="RollingFileError" fileName="${dir}/error.log" filePattern="${dir}/error-%d{yyyy-MM-dd}-%i.log">
            <ThresholdFilter level="ERROR"/>
            <PatternLayout pattern="${logFormat}"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="${every_file_size}"/>
            </Policies>
        </RollingFile>
        <GRPCLogClientAppender name="grpc-log">
            <PatternLayout pattern="%msg"/>
        </GRPCLogClientAppender>

        <Sentry name="Sentry" ></Sentry>
    </appenders>
    <loggers>
        <root level="${log_level}">
            <appender-ref ref="Console"/>
            <appender-ref ref="RollingFileInfo"/>
            <appender-ref ref="RollingFileError"/>
            <Appender-ref ref="Sentry"/>
        </root>

        <logger name="org.springframework.boot" level="error"/>
        <logger name="org.springframework.statemachine" level="error"/>
        <logger name="com.xxl.job" level="error"/>
        <logger name="com.ushareit.components.security" level="error"/>
        <logger name="com.payermax.basic.contexcenter.service.client" level="error"/>
        <logger name="com.payermax.fin.exchange.aspect.GlobalExceptionAspect" level="error"/>
        <logger name="com.payermax.fin.exchange.integration.rpc.proxy" level="error"/>
        <logger name="com.payermax.fin.exchange.domainservice.rpcproxy" level="warn"/>
        <logger name="com.payermax.fin.exchange.domainservice.impl" level="error"/>
        <logger name="com.payermax.fin.exchange.domainservice.impl.ForexTradeDomainServiceImpl" level="warn"/>
        <logger name="com.payermax.fin.exchange.domainservice.impl.InquiryChannelInstDomainServiceImpl" level="warn"/>
        <logger name="com.payermax.fin.exchange.domainservice.impl.PayDomainServiceImpl" level="warn"/>
        <logger name="com.payermax.fin.exchange.domainservice.exchange" level="error"/>
        <logger name="com.payermax.fin.exchange.domainservice.manage.impl.InquiryOrderInfoManageImpl" level="error"/>
        <logger name="com.payermax.fin.exchange.share.client" level="error"/>
        <logger name="com.payermax.risk.engine.client" level="error"/>
        <logger name="com.payermax.fin.exchange.dal.security" level="error"/>
        <logger name="com.ushareit.fintech.parent.interceptor.DecryptInterceptor" level="warn"/>
        <!--数据库迁移相关日志-->
        <logger name="com.payermax.fin.exchange.dal.config.MigrationShardingAlgorithmUtils" level="error"/>
        <logger name="digestLog" level="INFO" additivity="false">
            <appender-ref ref="grpc-log"/>
            <appender-ref ref="RollingFileInfo"/>
        </logger>
        <logger name="com.alibaba.nacos.spring.context.event" level="warn"/>
        <logger name="com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor" level="warn"/>
    </loggers>
</configuration>