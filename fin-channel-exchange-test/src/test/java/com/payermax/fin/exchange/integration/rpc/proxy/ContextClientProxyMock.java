package com.payermax.fin.exchange.integration.rpc.proxy;

import com.alibaba.testable.core.annotation.MockInvoke;
import com.payermax.common.tester.utils.MockUtil;
import com.payermax.basic.contexcenter.service.client.model.trade.TradeNationContextInfo;
import com.payermax.basic.contexcenter.service.client.model.generalized.KeyValueRequestDTO;
import com.payermax.basic.contexcenter.service.client.model.sensitive.SensitiveContextRequestDTO;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.basic.contexcenter.service.client.model.session.SessionContextInfo;
import com.payermax.basic.contexcenter.service.client.model.trade.TradeContextSaveRequestDTO;
import com.payermax.basic.contexcenter.service.client.model.rate.PaymentRateSaveRequestDTO;
import com.payermax.basic.contexcenter.service.client.model.session.SessionContextSaveRequestDTO;
import com.payermax.basic.contexcenter.service.client.model.risk.RiskContextRequestDTO;
import com.payermax.basic.contexcenter.service.client.model.trade.TradeContextInfo;
import com.payermax.basic.contexcenter.service.client.model.rate.PaymentRateContextInfo;
import com.payermax.basic.contexcenter.service.client.model.payment.PaymentContextSaveRequestDTO;
import com.payermax.basic.contexcenter.service.client.model.session.SessionContextRequestDTO;
import com.payermax.basic.contexcenter.service.client.model.trade.TradeContextRequestDTO;
import com.payermax.basic.contexcenter.service.client.ContextCenterManager;
import com.payermax.basic.contexcenter.service.client.model.payment.PaymentContextInfo;
import com.payermax.basic.contexcenter.service.client.model.rate.PaymentRateRequestDTO;
import com.payermax.basic.contexcenter.service.client.model.sensitive.SensitiveContextSaveRequestDTO;
import com.payermax.fin.exchange.service.context.AuthorizationRequestContext;
import com.payermax.basic.contexcenter.service.client.model.payment.PaymentContextRequestDTO;
import com.payermax.basic.contexcenter.service.client.model.authorization.AuthorizationContextRequestDTO;
import java.util.List;
import com.payermax.basic.contexcenter.service.client.model.risk.RiskContextInfo;
import com.payermax.basic.contexcenter.service.client.model.generalized.KeyValueSaveRequestDTO;
import com.payermax.basic.contexcenter.service.client.model.authorization.AuthorizationContextSaveRequestDTO;
import com.payermax.basic.contexcenter.service.client.model.generalized.GeneralizedContextInfo;
import com.payermax.basic.contexcenter.service.client.model.risk.RiskContextSaveRequestDTO;
import com.payermax.basic.contexcenter.service.client.model.sensitive.SensitiveContextInfo;

/**
 * 此类为自动生成，此类的包名必须和 ContextClientProxy 的包名保持一致。
 * 此类的类名必须是 ContextClientProxyMock ，不能重命名，
 * 否则 Mock 不会生效。
 * <AUTHOR> at 2022/7/5 16:45
 **/
public class ContextClientProxyMock {

    @MockInvoke
    public Result queryRiskContext(ContextCenterManager self, RiskContextRequestDTO arg0) throws Throwable {
        return MockUtil.recordAndResponse(ContextCenterManager.class, "queryRiskContext", Result.class,     true,()-> self.queryRiskContext(arg0), arg0);
    }

    @MockInvoke
    public Result saveSensitiveContext(ContextCenterManager self, SensitiveContextInfo arg0, SensitiveContextSaveRequestDTO arg1) throws Throwable {
        return MockUtil.recordAndResponse(ContextCenterManager.class, "saveSensitiveContext", Result.class,     true,()-> self.saveSensitiveContext(arg0, arg1), arg0, arg1);
    }

    @MockInvoke
    public Result querySensitiveContext(ContextCenterManager self, SensitiveContextRequestDTO arg0) throws Throwable {
        return MockUtil.recordAndResponse(ContextCenterManager.class, "querySensitiveContext", Result.class,     true,()-> self.querySensitiveContext(arg0), arg0);
    }

    @MockInvoke
    public Result queryPaymentContext(ContextCenterManager self, PaymentContextRequestDTO arg0) throws Throwable {
        return MockUtil.recordAndResponse(ContextCenterManager.class, "queryPaymentContext", Result.class,     true,()-> self.queryPaymentContext(arg0), arg0);
    }

    @MockInvoke
    public Result savePaymentContext(ContextCenterManager self, PaymentContextInfo arg0, PaymentContextSaveRequestDTO arg1) throws Throwable {
        return MockUtil.recordAndResponse(ContextCenterManager.class, "savePaymentContext", Result.class,     true,()-> self.savePaymentContext(arg0, arg1), arg0, arg1);
    }

    @MockInvoke
    public Result saveTradeContext(ContextCenterManager self, TradeContextInfo arg0, TradeContextSaveRequestDTO arg1) throws Throwable {
        return MockUtil.recordAndResponse(ContextCenterManager.class, "saveTradeContext", Result.class,     true,()-> self.saveTradeContext(arg0, arg1), arg0, arg1);
    }

    @MockInvoke
    public Result queryTradeContext(ContextCenterManager self, TradeContextRequestDTO arg0, List arg1) throws Throwable {
        return MockUtil.recordAndResponse(ContextCenterManager.class, "queryTradeContext", Result.class,     true,()-> self.queryTradeContext(arg0, arg1), arg0, arg1);
    }

    @MockInvoke
    public Result saveTradeNationContext(ContextCenterManager self, TradeNationContextInfo arg0, TradeContextSaveRequestDTO arg1) throws Throwable {
        return MockUtil.recordAndResponse(ContextCenterManager.class, "saveTradeNationContext", Result.class,     true,()-> self.saveTradeNationContext(arg0, arg1), arg0, arg1);
    }

    @MockInvoke
    public Result queryTradeNationContext(ContextCenterManager self, TradeContextRequestDTO arg0, List arg1) throws Throwable {
        return MockUtil.recordAndResponse(ContextCenterManager.class, "queryTradeNationContext", Result.class,     true,()-> self.queryTradeNationContext(arg0, arg1), arg0, arg1);
    }

    @MockInvoke
    public Result deleteTradeContextKey(ContextCenterManager self, TradeContextRequestDTO arg0, List arg1) throws Throwable {
        return MockUtil.recordAndResponse(ContextCenterManager.class, "deleteTradeContextKey", Result.class,     true,()-> self.deleteTradeContextKey(arg0, arg1), arg0, arg1);
    }

    @MockInvoke
    public Result saveRiskContext(ContextCenterManager self, RiskContextInfo arg0, RiskContextSaveRequestDTO arg1) throws Throwable {
        return MockUtil.recordAndResponse(ContextCenterManager.class, "saveRiskContext", Result.class,     true,()-> self.saveRiskContext(arg0, arg1), arg0, arg1);
    }

    @MockInvoke
    public Result deletePaymentContextKey(ContextCenterManager self, PaymentContextRequestDTO arg0) throws Throwable {
        return MockUtil.recordAndResponse(ContextCenterManager.class, "deletePaymentContextKey", Result.class,     true,()-> self.deletePaymentContextKey(arg0), arg0);
    }

    @MockInvoke
    public Result savePaymentRateInstance(ContextCenterManager self, PaymentRateContextInfo arg0, PaymentRateSaveRequestDTO arg1) throws Throwable {
        return MockUtil.recordAndResponse(ContextCenterManager.class, "savePaymentRateInstance", Result.class,     true,()-> self.savePaymentRateInstance(arg0, arg1), arg0, arg1);
    }

    @MockInvoke
    public Result queryPaymentRateInstance(ContextCenterManager self, PaymentRateRequestDTO arg0) throws Throwable {
        return MockUtil.recordAndResponse(ContextCenterManager.class, "queryPaymentRateInstance", Result.class,     true,()-> self.queryPaymentRateInstance(arg0), arg0);
    }

    @MockInvoke
    public Result saveSessionContext(ContextCenterManager self, SessionContextInfo arg0, SessionContextSaveRequestDTO arg1) throws Throwable {
        return MockUtil.recordAndResponse(ContextCenterManager.class, "saveSessionContext", Result.class,     true,()-> self.saveSessionContext(arg0, arg1), arg0, arg1);
    }

    @MockInvoke
    public Result querySessionContext(ContextCenterManager self, SessionContextRequestDTO arg0) throws Throwable {
        return MockUtil.recordAndResponse(ContextCenterManager.class, "querySessionContext", Result.class,     true,()-> self.querySessionContext(arg0), arg0);
    }

    @MockInvoke
    public Result saveGeneralizedContext(ContextCenterManager self, GeneralizedContextInfo arg0, KeyValueSaveRequestDTO arg1) throws Throwable {
        return MockUtil.recordAndResponse(ContextCenterManager.class, "saveGeneralizedContext", Result.class,     true,()-> self.saveGeneralizedContext(arg0, arg1), arg0, arg1);
    }

    @MockInvoke
    public Result queryGeneralizedContext(ContextCenterManager self, KeyValueRequestDTO arg0) throws Throwable {
        return MockUtil.recordAndResponse(ContextCenterManager.class, "queryGeneralizedContext", Result.class,     true,()-> self.queryGeneralizedContext(arg0), arg0);
    }

    @MockInvoke
    public Result saveAuthorizationContext(ContextCenterManager self, AuthorizationRequestContext arg0, AuthorizationContextSaveRequestDTO arg1) throws Throwable {
        return MockUtil.recordAndResponse(ContextCenterManager.class, "saveAuthorizationContext", Result.class,     true,()-> self.saveAuthorizationContext(arg0, arg1), arg0, arg1);
    }

    @MockInvoke
    public Result deleteSensitiveContextKey(ContextCenterManager self, SensitiveContextRequestDTO arg0) throws Throwable {
        return MockUtil.recordAndResponse(ContextCenterManager.class, "deleteSensitiveContextKey", Result.class,     true,()-> self.deleteSensitiveContextKey(arg0), arg0);
    }

    @MockInvoke
    public Result queryAuthorizationContext(ContextCenterManager self, AuthorizationContextRequestDTO arg0) throws Throwable {
        return MockUtil.recordAndResponse(ContextCenterManager.class, "queryAuthorizationContext", Result.class,     true,()-> self.queryAuthorizationContext(arg0), arg0);
    }
}
