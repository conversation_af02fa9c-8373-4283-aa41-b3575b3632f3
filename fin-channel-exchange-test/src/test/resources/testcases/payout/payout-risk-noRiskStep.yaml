# 出款BankTransfer类交易，上送风控
# 1. 使用DOKU渠道，渠道步骤配置无风控步骤，有出款步骤，交易风控提交单和出款提交单支付正常
# 2. 进行原单重发，校验订单（状态）是否正常
# 3. 申请换单重发，校验订单是否正常（原提交单置为失败）
# 4. 执行换单重发，校验订单是否正常（新创建一个提交单）

actionName: setGrayConfig
request: !beanRequest
  args:
    - '[{"businessKey":"ALL","weight":"100"}]'
  beanName: grayCompareUtils
  methodName: setGrayConfig
---

# 1. 风控同步成功
actionName: testBankTransferPayout1
beanName: payoutFacadeImpl
methodName: withdraw
reqs:
  - !!com.payermax.fin.exchange.service.request.PayoutRequest
    amount:
      currency: IDR
      value: 20000.00
    bizIdentify: P0101
    payOrderNo: ${GenUtil.randNumStr(32)}-2
    payRequestNo: ${GenUtil.randNumStr(32)}
    paymentMethodType: BankTransfer
    productCode: '2001'
    requestContext:
      bizSource: null
      orderInfo:
        mcc: '5262'
        merchantNo: 'DOKU010113835103342'
        merchantType: '1'
        outUserId: NA
        remark: 203 Diamonds + 20 Bonus
        userMemberId: P01V070702343069
      params: {
      }
      paymentMethod:
        country: ID
        customerType: '1'
        extendProperties:
          - key: PAYMENT_TOOL
            logicKey: INCLUDE
            value: '1009'
        paymentType: '10'
    requestContextJson: null
    targetOrg: BCA
# mock 配置，类型是一个Map<String,String>
mockConfigs:
  InternalFrontClient.internalExecute:0:
    data:
      country: ${_.request.args[0].requestContext.paymentMethod.country}
      channelTransactionId: ${GenUtil.randStr(20)}
      amount: 20000.00
      pending: true
      channelRespCode: PAYMENT_IN_PROCESS
      channelOrderNo: ${JsonUtil.get($req_4, "$>channelOrderNo")}
      failed: false
      channelRespMsg: Payment is processing.
      userId: P01V070702343069
      channelThirdPartyTxnId: ${GenUtil.randStr(30)}
      channelRespJson:
        result:
          resultStatus: U
          success: false
          resultCode: PAYMENT_IN_PROCESS
          failed: false
          paymentInProcess: true
          resultMessage: Payment is processing.
        paymentId: ${GenUtil.randStr(30)}
        paymentRequestId: ${JsonUtil.get($req_4, "$>channelOrderNo")}
      currency: IDR
      channelCode: DOKU211
      status: PENDING
    bizCode: '0000'
    channelRespCode: PAYMENT_IN_PROCESS
    message: success
    channelRespMsg: Payment is processing.
  ContextCenterManager.queryPaymentContext:0:
    msg: ''
    code: APPLY_SUCCESS
    data: null
  ContextCenterManager.queryRiskContext:0:
    !!com.payermax.common.lang.model.dto.Result
    msg: ''
    code: APPLY_SUCCESS
    data:
      !!com.payermax.basic.contexcenter.service.client.model.risk.RiskContextInfo
      3ds: null
      ddcSessionId: null
      RISK-ENGINE-RESULT: null
      cybsDdcSessionMap: null
      dm: null
      ddcSessionMap: null
      mpgsDdcSessionMap: null
      httpHeaders: null
      cardinalDdcSessionMap: null
      amazonPay: null
      cardToken: null
      hitrustDdcSessionMap: null
      cardBin: null
      cbsSessionId: null
      amazonPaySessionId: null
      dynamic3DS: null
  RiskEngineSyncClient.queryEventResultForFinExchange:0:
    bizAppId: null
    ruleContent: null
    eventId: null
    bizType: null
    data: {
    }
    requestId: null
    riskResult: '0'
    riskCode: '0'
    ruleName: null
    ruleData: {
    }
    checkPoint: null
    riskMessage: PASS
  RiskEngineSyncClient.executeEvent:
    bizAppId: null
    ruleContent: null
    eventId: null
    riskResult: '0'
    riskCode: RISK_PASS
    riskMessage: F_0000
  ContextCenterManager.savePaymentContext:0:
    msg: ''
    code: APPLY_SUCCESS
    data: null
# 期望值，框架会对期望值进行校验
expect:
  MOCK_INVOKE:
    InternalFrontClient.internalExecute:0:
      - EXP:anyString()
      - EXP:anyString()
      - null
      - /f/standard/payoutApply
      - |
        JSON:{
          "appId": "fin-channel-exchange",
          "appVersion": "1.0",
          "merchantTxnId": "REF:_req.args[0].payOrderNo",
          "merchantId": "REF:_req.args[0].requestContext.orderInfo.merchantNo",
          "newChannelCode": "O_DOKU_P01",
          "technicalOrg": "DOKU",
          "channelMethodCode": "O_DOKU_P01_BANKTRANSFER101_BCA_ID",
          "paymentMethodType": "REF:_req.args[0].paymentMethodType",
          "targetOrCardOrg": "REF:_req.args[0].targetOrg",
          "customerType": "REF:_req.args[0].requestContext.paymentMethod.customerType",
          "channelOrderNo": "EXP:anyString()",
          "productCode": "10201",
          "channelCode": "DOKU211",
          "country": "REF:_req.args[0].requestContext.paymentMethod.country",
          "methodCode": "93",
          "methodSubCode": "BCA",
          "currency": "REF:_req.args[0].amount.currency",
          "amount": "REF:_req.args[0].amount.value",
          "userId": "REF:_req.args[0].requestContext.orderInfo.userMemberId",
          "note":"REF:_req.args[0].requestContext.orderInfo.remark",
          "extraJson": {
            "merchantId": "REF:_req.args[0].requestContext.orderInfo.merchantNo"
          },
          "extraParamJson": "{}"
        }
    ContextCenterManager.queryPaymentContext:0:
      - requestNo: REF:_req.args[0].payRequestNo
        type: PAYOUT_REQUEST
    ContextCenterManager.queryRiskContext:0:
      - requestKey: REF:_req.args[0].payRequestNo
    RiskEngineSyncClient.queryEventResultForFinExchange:0:
      - bizType: REF:_req.args[0].bizIdentify
        requestId: REF:_req.args[0].payRequestNo
        eventBody:
          mcc: REF:_req.args[0].requestContext.orderInfo.mcc
          paymentType: REF:_req.args[0].requestContext.paymentMethod.paymentType
      - 200
      - 0
    ContextCenterManager.savePaymentContext:0:
      - payoutResponseInfo:
          country: ID
          channelMethodCode: O_DOKU_P01_BANKTRANSFER101_BCA_ID
          fourthOrgOrderNo: REF:_.mockRecords['InternalFrontClient.internalExecute:0'].response>data.channelThirdPartyTxnId
          payOrderNo: REF:_req.args[0].payOrderNo
          mappingMsg: success
          channelEntity: P01
          mappingCode: APPLY_SUCCESS
          channelPayRequestNo: REF:_req.args[0].payOrderNo
          channelPayCommitNo: REF:_.mockRecords['InternalFrontClient.internalExecute:0'].args[4]>channelOrderNo
          oldMethodCode: '93'
          oldMethodSubCode: 'BCA'
          channelCode: O_DOKU_P01
          status: PENDING
      - isUpdateExpireTime: true
        requestNo: REF:_req.args[0].payRequestNo
        type: PAYOUT_RESPONSE
        timeOut: 1800
  RESPONSE:
    msg: ''
    code: APPLY_SUCCESS
    data:
      country: ID
      fourthOrgOrderNo: REF:_.mockRecords['InternalFrontClient.internalExecute:0'].response>data.channelThirdPartyTxnId
      mappingMsg: success
      channelRespCode: null
      channelRespMsg: null
      mappingCode: APPLY_SUCCESS
      channelBusinessType: null
      fundsAccountId: null
      channelCompleteTime: null
      channelPayCommitNo: REF:_.mockRecords['InternalFrontClient.internalExecute:0'].request[4]>channelOrderNo
      oldMethodSubCode: 'BCA'
      channelCode: O_DOKU_P01
      channelMethodCode: O_DOKU_P01_BANKTRANSFER101_BCA_ID
      correctionLogo: null
      extendResult: {
      }
      payOrderNo: REF:_req.args[0].payOrderNo
      settleCurrency: null
      channelEntity: P01
      completeTime: null
      cardOrg: null
      standardChannelCode: null
      channelPayRequestNo: REF:_req.args[0].payOrderNo
      channelMerchantCode: null
      oldMethodCode: '93'
      status: PENDING
---
# 验证testBankTransferPayout1的结果
actionName: queryCommitOrder1
request: !beanRequest
  beanName: channelCommitOrderServiceImpl
  methodName: getByEntityQueryFromMaster
  args:
    - !!com.payermax.fin.exchange.dal.entity.query.ChannelCommitOrderEntityQuery
      channelPayRequestNo: ${g.actionMap.testBankTransferPayout1.request.args[0].payOrderNo}
expect:
  RESPONSE:
    - requestType: payout
      channelCode: O_DOKU_P01
      channelMethodCode: O_DOKU_P01_BANKTRANSFER101_BCA_ID
      channelEntity: P01
      eventType: APPLY
      mainRequestType: 1
      status: 0
    - requestType: payoutRisk
      channelCode: O_DOKU_P01
      channelMethodCode: O_DOKU_P01_BANKTRANSFER101_BCA_ID
      channelEntity: P01
      eventType: APPLY
      mainRequestType: 3
      status: 1
---
# 验证testBankTransferPayout1的结果
actionName: queryRequestOrder1
request: !beanRequest
  beanName: channelRequestOrderServiceImpl
  methodName: getByRequestOrderNoFromMaster
  args:
    - ${g.actionMap.testBankTransferPayout1.request.args[0].payOrderNo}
expect:
  RESPONSE:
    requestType: payout
    mainRequestType: 1
    status: 0
    merchantNo: '${g.actionMap.testBankTransferPayout1.request.args[0].requestContext.orderInfo.merchantNo}'
    userMemberId: ${g.actionMap.testBankTransferPayout1.request.args[0].requestContext.orderInfo.userMemberId}
    bizOrderNo: '${g.actionMap.testBankTransferPayout1.request.args[0].payOrderNo}'
    sourceBizOrderNo: '${g.actionMap.testBankTransferPayout1.request.args[0].payRequestNo}'
    paymentMethodType: ${g.actionMap.testBankTransferPayout1.request.args[0].paymentMethodType}
    paymentType: ${g.actionMap.testBankTransferPayout1.request.args[0].requestContext.paymentMethod.paymentType}
    country: ${g.actionMap.testBankTransferPayout1.request.args[0].requestContext.paymentMethod.country}
    currency: ${g.actionMap.testBankTransferPayout1.request.args[0].amount.currency}
    targetOrg: ${g.actionMap.testBankTransferPayout1.request.args[0].targetOrg}
---
# 锁单，为后面的原单重发做准备
actionName: lockOrder1
request: !beanRequest
  beanName: omcOrderController
  methodName: lockOrder
  args:
    - !!com.payermax.fin.exchange.service.request.LockOrderRequest
      channelPayCommitNos:
        - ${g.actionMap.testBankTransferPayout1.responseObj.data.channelPayCommitNo}
      operator: system
expect:
  RESPONSE:
    msg: ''
    code: APPLY_SUCCESS
    data:
      successCount: 1
      failedCount: 0

---
# 1. 风控同步拒绝
actionName: testBankTransferPayout2
beanName: payoutFacadeImpl
methodName: withdraw
reqs:
  - !!com.payermax.fin.exchange.service.request.PayoutRequest
    amount:
      currency: IDR
      value: 20000.00
    bizIdentify: P0101
    payOrderNo: ${GenUtil.randNumStr(32)}-2
    payRequestNo: ${GenUtil.randNumStr(32)}
    paymentMethodType: BankTransfer
    productCode: '2001'
    requestContext:
      bizSource: null
      orderInfo:
        mcc: '5262'
        merchantNo: 'DOKU010113835103342'
        merchantType: '1'
        outUserId: NA
        remark: 203 Diamonds + 20 Bonus
        userMemberId: P01V070702343069
      params: {
      }
      paymentMethod:
        country: ID
        customerType: '1'
        extendProperties:
          - key: PAYMENT_TOOL
            logicKey: INCLUDE
            value: '1009'
        paymentType: '10'
    requestContextJson: null
    targetOrg: BCA
# mock 配置，类型是一个Map<String,String>
mockConfigs:
  ContextCenterManager.queryPaymentContext:0:
    msg: ''
    code: APPLY_SUCCESS
    data: null
  ContextCenterManager.queryRiskContext:0:
    !!com.payermax.common.lang.model.dto.Result
    msg: ''
    code: APPLY_SUCCESS
    data:
      !!com.payermax.basic.contexcenter.service.client.model.risk.RiskContextInfo
      3ds: null
      ddcSessionId: null
      RISK-ENGINE-RESULT: null
      cybsDdcSessionMap: null
      dm: null
      ddcSessionMap: null
      mpgsDdcSessionMap: null
      httpHeaders: null
      cardinalDdcSessionMap: null
      amazonPay: null
      cardToken: null
      hitrustDdcSessionMap: null
      cardBin: null
      cbsSessionId: null
      amazonPaySessionId: null
      dynamic3DS: null
  RiskEngineSyncClient.queryEventResultForFinExchange:0:
    bizAppId: null
    ruleContent: null
    eventId: null
    bizType: null
    data: {
    }
    requestId: null
    riskResult: '0'
    riskCode: '0'
    ruleName: null
    ruleData: {
    }
    checkPoint: null
    riskMessage: PASS
  RiskEngineSyncClient.executeEvent:
    bizAppId: null
    ruleContent: null
    eventId: null
    riskResult: 1
    riskCode: RISK_REJECTED
    riskMessage: F_0000
  ContextCenterManager.savePaymentContext:0:
    msg: ''
    code: APPLY_SUCCESS
    data: null
# 期望值，框架会对期望值进行校验
expect:
  MOCK_INVOKE:
    ContextCenterManager.queryPaymentContext:0:
      - requestNo: REF:_req.args[0].payRequestNo
        type: PAYOUT_REQUEST
    ContextCenterManager.queryRiskContext:0:
      - requestKey: REF:_req.args[0].payRequestNo
    RiskEngineSyncClient.queryEventResultForFinExchange:0:
      - bizType: REF:_req.args[0].bizIdentify
        requestId: REF:_req.args[0].payRequestNo
        eventBody:
          mcc: REF:_req.args[0].requestContext.orderInfo.mcc
          paymentType: REF:_req.args[0].requestContext.paymentMethod.paymentType
      - 200
      - 0
  RESPONSE:
    msg: 'F_0000'
    code: RISK_NO_PASSED
    data:
      payOrderNo: REF:_req.args[0].payOrderNo
      channelPayRequestNo: REF:_req.args[0].payOrderNo
      status: FAILED
      mappingCode: RISK_NO_PASSED
      mappingMsg: F_0000
---
# 验证testBankTransferPayout2的结果
actionName: queryCommitOrder2
request: !beanRequest
  beanName: channelCommitOrderServiceImpl
  methodName: getByEntityQueryFromMaster
  args:
    - !!com.payermax.fin.exchange.dal.entity.query.ChannelCommitOrderEntityQuery
      channelPayRequestNo: ${g.actionMap.testBankTransferPayout2.request.args[0].payOrderNo}
expect:
  RESPONSE:
    - requestType: payoutRisk
      channelCode: O_DOKU_P01
      channelMethodCode: O_DOKU_P01_BANKTRANSFER101_BCA_ID
      channelEntity: P01
      eventType: APPLY
      mainRequestType: 3
      status: 2
      mappingCode: RISK_NO_PASSED
      mappingMsg: F_0000
---
# 验证testBankTransferPayout2的结果
actionName: queryRequestOrder2
request: !beanRequest
  beanName: channelRequestOrderServiceImpl
  methodName: getByRequestOrderNoFromMaster
  args:
    - ${g.actionMap.testBankTransferPayout2.request.args[0].payOrderNo}
expect:
  RESPONSE:
    requestType: payout
    mainRequestType: 1
    status: 2
    mappingCode: RISK_NO_PASSED
    mappingMsg: F_0000
    merchantNo: '${g.actionMap.testBankTransferPayout2.request.args[0].requestContext.orderInfo.merchantNo}'
    userMemberId: ${g.actionMap.testBankTransferPayout2.request.args[0].requestContext.orderInfo.userMemberId}
    bizOrderNo: '${g.actionMap.testBankTransferPayout2.request.args[0].payOrderNo}'
    sourceBizOrderNo: '${g.actionMap.testBankTransferPayout2.request.args[0].payRequestNo}'
    paymentMethodType: ${g.actionMap.testBankTransferPayout2.request.args[0].paymentMethodType}
    paymentType: ${g.actionMap.testBankTransferPayout2.request.args[0].requestContext.paymentMethod.paymentType}
    country: ${g.actionMap.testBankTransferPayout2.request.args[0].requestContext.paymentMethod.country}
    currency: ${g.actionMap.testBankTransferPayout2.request.args[0].amount.currency}
    targetOrg: ${g.actionMap.testBankTransferPayout2.request.args[0].targetOrg}

---
# 1. 风控同步review
actionName: testBankTransferPayout3
beanName: payoutFacadeImpl
methodName: withdraw
reqs:
  - !!com.payermax.fin.exchange.service.request.PayoutRequest
    amount:
      currency: IDR
      value: 20000.00
    bizIdentify: P0101
    payOrderNo: ${GenUtil.randNumStr(32)}-2
    payRequestNo: ${GenUtil.randNumStr(32)}
    paymentMethodType: BankTransfer
    productCode: '2001'
    requestContext:
      bizSource: null
      orderInfo:
        mcc: '5262'
        merchantNo: 'DOKU010113835103342'
        merchantType: '1'
        outUserId: NA
        remark: 203 Diamonds + 20 Bonus
        userMemberId: P01V070702343069
      params: {
      }
      paymentMethod:
        country: ID
        customerType: '1'
        extendProperties:
          - key: PAYMENT_TOOL
            logicKey: INCLUDE
            value: '1009'
        paymentType: '10'
    requestContextJson: null
    targetOrg: BCA
# mock 配置，类型是一个Map<String,String>
mockConfigs:
  ContextCenterManager.queryPaymentContext:0:
    msg: ''
    code: APPLY_SUCCESS
    data: null
  ContextCenterManager.queryRiskContext:0:
    !!com.payermax.common.lang.model.dto.Result
    msg: ''
    code: APPLY_SUCCESS
    data:
      !!com.payermax.basic.contexcenter.service.client.model.risk.RiskContextInfo
      3ds: null
      ddcSessionId: null
      RISK-ENGINE-RESULT: null
      cybsDdcSessionMap: null
      dm: null
      ddcSessionMap: null
      mpgsDdcSessionMap: null
      httpHeaders: null
      cardinalDdcSessionMap: null
      amazonPay: null
      cardToken: null
      hitrustDdcSessionMap: null
      cardBin: null
      cbsSessionId: null
      amazonPaySessionId: null
      dynamic3DS: null
  RiskEngineSyncClient.queryEventResultForFinExchange:0:
    bizAppId: null
    ruleContent: null
    eventId: null
    bizType: null
    data: {
    }
    requestId: null
    riskResult: '0'
    riskCode: '0'
    ruleName: null
    ruleData: {
    }
    checkPoint: null
    riskMessage: PASS
  RiskEngineSyncClient.executeEvent:
    bizAppId: null
    ruleContent: null
    eventId: null
    riskResult: 2
    riskCode: RISK_REVIEW
    riskMessage: F_0000
  ContextCenterManager.savePaymentContext:0:
    msg: ''
    code: APPLY_SUCCESS
    data: null
# 期望值，框架会对期望值进行校验
expect:
  MOCK_INVOKE:
    RiskEngineSyncClient.executeEvent:0:
      - bizAppId: fin-channel-exchange
        bizType: P0101
        checkPoint: D0004
        eventBody:
          country: ID
          tradeNo: EXP:anyString()
          accountType: 1
          channelRequestNo: EXP:anyString()
          tradeAmount: '20000.00'
          payeeCountry: ID
          payAmount: '20000.00'
          productCode: 2001
          payCurrency: IDR
          paymentMethod: BankTransfer
          currency: IDR
          entity: P01
          channelCode: O_DOKU_P01
          memberId: P01V070702343069
          merchantNo: 'DOKU010113835103342'
        eventId: D00040001
        requestId: EXP:anyString()
    ContextCenterManager.queryPaymentContext:0:
      - requestNo: REF:_req.args[0].payRequestNo
        type: PAYOUT_REQUEST
    ContextCenterManager.queryRiskContext:0:
      - requestKey: REF:_req.args[0].payRequestNo
    RiskEngineSyncClient.queryEventResultForFinExchange:0:
      - bizType: REF:_req.args[0].bizIdentify
        requestId: REF:_req.args[0].payRequestNo
        eventBody:
          mcc: REF:_req.args[0].requestContext.orderInfo.mcc
          paymentType: REF:_req.args[0].requestContext.paymentMethod.paymentType
      - 200
      - 0
    ContextCenterManager.savePaymentContext:0:
      - payoutResponseInfo:
          channelPayRequestNo: REF:_req.args[0].payOrderNo
          status: PENDING
      - isUpdateExpireTime: true
        requestNo: REF:_req.args[0].payRequestNo
        type: PAYOUT_RESPONSE
        timeOut: 1800
  RESPONSE:
    msg: ''
    code: APPLY_SUCCESS
    data:
      channelPayRequestNo: REF:_req.args[0].payOrderNo
      status: PENDING
---
# 验证testBankTransferPayout3的结果
actionName: queryCommitOrder3
request: !beanRequest
  beanName: channelCommitOrderServiceImpl
  methodName: getByEntityQueryFromMaster
  args:
    - !!com.payermax.fin.exchange.dal.entity.query.ChannelCommitOrderEntityQuery
      channelPayRequestNo: ${g.actionMap.testBankTransferPayout3.request.args[0].payOrderNo}
expect:
  RESPONSE:
    - requestType: payoutRisk
      channelCode: O_DOKU_P01
      channelMethodCode: O_DOKU_P01_BANKTRANSFER101_BCA_ID
      channelEntity: P01
      eventType: APPLY
      mainRequestType: 3
      status: 0
---
# 验证testBankTransferPayout3的结果
actionName: queryRequestOrder3
request: !beanRequest
  beanName: channelRequestOrderServiceImpl
  methodName: getByRequestOrderNoFromMaster
  args:
    - ${g.actionMap.testBankTransferPayout3.request.args[0].payOrderNo}
expect:
  RESPONSE:
    requestType: payout
    mainRequestType: 1
    status: 0
    merchantNo: '${g.actionMap.testBankTransferPayout3.request.args[0].requestContext.orderInfo.merchantNo}'
    userMemberId: ${g.actionMap.testBankTransferPayout3.request.args[0].requestContext.orderInfo.userMemberId}
    bizOrderNo: '${g.actionMap.testBankTransferPayout3.request.args[0].payOrderNo}'
    sourceBizOrderNo: '${g.actionMap.testBankTransferPayout3.request.args[0].payRequestNo}'
    paymentMethodType: ${g.actionMap.testBankTransferPayout3.request.args[0].paymentMethodType}
    paymentType: ${g.actionMap.testBankTransferPayout3.request.args[0].requestContext.paymentMethod.paymentType}
    country: ${g.actionMap.testBankTransferPayout3.request.args[0].requestContext.paymentMethod.country}
    currency: ${g.actionMap.testBankTransferPayout3.request.args[0].amount.currency}
    targetOrg: ${g.actionMap.testBankTransferPayout3.request.args[0].targetOrg}
---
# 风控callback
actionName: payoutRiskCallback3
request: !beanRequest
  args:
    - !!com.payermax.fin.exchange.service.request.CallbackNotifyRequest
      hasBounceBack: false
      notifyType: PAYOUT_CONTROL
      referenceNo: ${g.actionMap.testBankTransferPayout3.mockRecords['RiskEngineSyncClient.executeEvent:0'].args[0].eventBody.channelRequestNo}
      respCode: SUCCESS
      respMsg: success.
      status: SUCCESS
      translateMsg: false
  beanName: callbackFacadeImpl
  methodName: callbackNotify
mockConfigs:
  InternalFrontClient.internalExecute:0:
    data:
      country: ${_.request.args[0].requestContext.paymentMethod.country}
      channelTransactionId: ${GenUtil.randStr(20)}
      amount: 20000.00
      pending: true
      channelRespCode: PAYMENT_IN_PROCESS
      channelOrderNo: ${JsonUtil.get($req_4, "$>channelOrderNo")}
      failed: false
      channelRespMsg: Payment is processing.
      userId: P01V070702343069
      channelThirdPartyTxnId: ${GenUtil.randStr(30)}
      channelRespJson:
        result:
          resultStatus: U
          success: false
          resultCode: PAYMENT_IN_PROCESS
          failed: false
          paymentInProcess: true
          resultMessage: Payment is processing.
        paymentId: ${GenUtil.randStr(30)}
        paymentRequestId: ${JsonUtil.get($req_4, "$>channelOrderNo")}
      currency: IDR
      channelCode: DOKU211
      status: PENDING
    bizCode: '0000'
    channelRespCode: PAYMENT_IN_PROCESS
    message: success
    channelRespMsg: Payment is processing.
  IoniaRocketMqTemplate.sendMsgSync:0:
    sendStatus: 'SEND_OK'
    msgId: ${GenUtil.randStr(30)}
  RiskEngineSyncClient.executeEvent:
    bizAppId: null
    ruleContent: null
    eventId: null
    riskResult: '0'
    riskCode: RISK_PASS
    riskMessage: F_0000
expect:
  MOCK_INVOKE:
    InternalFrontClient.internalExecute:0:
      - EXP:anyString()
      - EXP:anyString()
      - null
      - /f/standard/payoutApply
      - |
        JSON:{
          "appId": "fin-channel-exchange",
          "appVersion": "1.0",
          "merchantTxnId": "REF:g.actionMap.testBankTransferPayout3.request.args[0].payOrderNo",
          "merchantId": "REF:g.actionMap.testBankTransferPayout3.request.args[0].requestContext.orderInfo.merchantNo",
          "newChannelCode": "O_DOKU_P01",
          "technicalOrg": "DOKU",
          "channelMethodCode": "O_DOKU_P01_BANKTRANSFER101_BCA_ID",
          "paymentMethodType": "REF:g.actionMap.testBankTransferPayout3.request.args[0].paymentMethodType",
          "targetOrCardOrg": "REF:g.actionMap.testBankTransferPayout3.request.args[0].targetOrg",
          "customerType": "REF:g.actionMap.testBankTransferPayout3.request.args[0].requestContext.paymentMethod.customerType",
          "channelOrderNo": "EXP:anyString()",
          "productCode": "10201",
          "channelCode": "DOKU211",
          "country": "REF:g.actionMap.testBankTransferPayout3.request.args[0].requestContext.paymentMethod.country",
          "methodCode": "93",
          "methodSubCode": "BCA",
          "currency": "REF:g.actionMap.testBankTransferPayout3.request.args[0].amount.currency",
          "amount": "REF:g.actionMap.testBankTransferPayout3.request.args[0].amount.value",
          "userId": "REF:g.actionMap.testBankTransferPayout3.request.args[0].requestContext.orderInfo.userMemberId",
          "note":"REF:g.actionMap.testBankTransferPayout3.request.args[0].requestContext.orderInfo.remark",
          "extraJson": {
            "merchantId": "REF:g.actionMap.testBankTransferPayout3.request.args[0].requestContext.orderInfo.merchantNo"
          },
          "extraParamJson": "{}"
        }
  RESPONSE:
    code: APPLY_SUCCESS
    data: SUCCESS
---
# 验证testBankTransferPayout3的结果
actionName: queryCommitOrder3_1
request: !beanRequest
  beanName: channelCommitOrderServiceImpl
  methodName: getByEntityQueryFromMaster
  args:
    - !!com.payermax.fin.exchange.dal.entity.query.ChannelCommitOrderEntityQuery
      channelPayRequestNo: ${g.actionMap.testBankTransferPayout3.request.args[0].payOrderNo}
expect:
  RESPONSE:
    - requestType: payout
      channelCode: O_DOKU_P01
      channelMethodCode: O_DOKU_P01_BANKTRANSFER101_BCA_ID
      channelEntity: P01
      eventType: APPLY
      mainRequestType: 1
      status: 0
    - requestType: payoutRisk
      channelCode: O_DOKU_P01
      channelMethodCode: O_DOKU_P01_BANKTRANSFER101_BCA_ID
      channelEntity: P01
      eventType: CALLBACK
      mainRequestType: 3
      status: 1
---
# 验证testBankTransferPayout3的结果
actionName: queryRequestOrder3_1
request: !beanRequest
  beanName: channelRequestOrderServiceImpl
  methodName: getByRequestOrderNoFromMaster
  args:
    - ${g.actionMap.testBankTransferPayout3.request.args[0].payOrderNo}
expect:
  RESPONSE:
    requestType: payout
    mainRequestType: 1
    status: 0
    merchantNo: '${g.actionMap.testBankTransferPayout3.request.args[0].requestContext.orderInfo.merchantNo}'
    userMemberId: ${g.actionMap.testBankTransferPayout3.request.args[0].requestContext.orderInfo.userMemberId}
    bizOrderNo: '${g.actionMap.testBankTransferPayout3.request.args[0].payOrderNo}'
    sourceBizOrderNo: '${g.actionMap.testBankTransferPayout3.request.args[0].payRequestNo}'
    paymentMethodType: ${g.actionMap.testBankTransferPayout3.request.args[0].paymentMethodType}
    paymentType: ${g.actionMap.testBankTransferPayout3.request.args[0].requestContext.paymentMethod.paymentType}
    country: ${g.actionMap.testBankTransferPayout3.request.args[0].requestContext.paymentMethod.country}
    currency: ${g.actionMap.testBankTransferPayout3.request.args[0].amount.currency}
    targetOrg: ${g.actionMap.testBankTransferPayout3.request.args[0].targetOrg}

---
# 锁单，为后面的换单重发做准备
actionName: lockOrder2
request: !beanRequest
  beanName: omcOrderController
  methodName: lockOrder
  args:
    - !!com.payermax.fin.exchange.service.request.LockOrderRequest
      channelPayCommitNos:
        - ${JsonUtil.get($g.actionMap.payoutRiskCallback3.mockRecords['InternalFrontClient.internalExecute:0'], 'request[4]>channelOrderNo')}
      operator: system
expect:
  RESPONSE:
    msg: ''
    code: APPLY_SUCCESS
    data:
      successCount: 1
      failedCount: 0

---
# 原单重发
actionName: retryTransaction1
request: !beanRequest
  beanName: omcPayoutController
  methodName: retryTransaction
  args:
    - !!com.payermax.fin.exchange.service.request.PayoutRetryTransactionRequest
      channelPayCommitNo: ${g.actionMap.testBankTransferPayout1.responseObj.data.channelPayCommitNo}
mockConfigs:
  InternalFrontClient.internalExecute:0:
    data:
      country: ${g.actionMap.testBankTransferPayout1.request.args[0].requestContext.paymentMethod.country}
      channelTransactionId: ${GenUtil.randStr(20)}
      amount: 20000.00
      pending: true
      channelRespCode: PAYMENT_IN_PROCESS
      channelOrderNo: ${g.actionMap.testBankTransferPayout1.responseObj.data.channelPayCommitNo}
      failed: false
      channelRespMsg: Payment is processing.
      userId: P01V070702343069
      channelThirdPartyTxnId: ${GenUtil.randStr(30)}
      channelRespJson:
        result:
          resultStatus: U
          success: false
          resultCode: PAYMENT_IN_PROCESS
          failed: false
          paymentInProcess: true
          resultMessage: Payment is processing.
        paymentId: ${GenUtil.randStr(30)}
        paymentRequestId: ${g.actionMap.testBankTransferPayout1.responseObj.data.channelPayCommitNo}
      currency: IDR
      channelCode: DOKU211
      status: SUCCESS
    bizCode: '0000'
    channelRespCode: PAYMENT_IN_PROCESS
    message: success
    channelRespMsg: Payment is processing.
  IoniaRocketMqTemplate.sendMsgSync:0:
    sendStatus: 'SEND_OK'
    msgId: ${GenUtil.randStr(30)}
  ContextCenterManager.savePaymentContext:0:
    msg: ''
    code: APPLY_SUCCESS
    data: null
expect:
  MOCK_INVOKE:
    InternalFrontClient.internalExecute:0:
      - EXP:anyString()
      - EXP:anyString()
      - null
      - /f/standard/payoutApply
      - |
        JSON:{
          "appId": "fin-channel-exchange",
          "appVersion": "1.0",
          "merchantTxnId": "REF:g.actionMap.testBankTransferPayout1.request.args[0].payOrderNo",
          "merchantId": "REF:g.actionMap.testBankTransferPayout1.request.args[0].requestContext.orderInfo.merchantNo",
          "newChannelCode": "O_DOKU_P01",
          "technicalOrg": "DOKU",
          "channelMethodCode": "O_DOKU_P01_BANKTRANSFER101_BCA_ID",
          "paymentMethodType": "REF:g.actionMap.testBankTransferPayout1.request.args[0].paymentMethodType",
          "targetOrCardOrg": "REF:g.actionMap.testBankTransferPayout1.request.args[0].targetOrg",
          "customerType": "REF:g.actionMap.testBankTransferPayout1.request.args[0].requestContext.paymentMethod.customerType",
          "channelOrderNo": "EXP:anyString()",
          "productCode": "10201",
          "channelCode": "DOKU211",
          "country": "REF:g.actionMap.testBankTransferPayout1.request.args[0].requestContext.paymentMethod.country",
          "methodCode": "93",
          "methodSubCode": "BCA",
          "currency": "REF:g.actionMap.testBankTransferPayout1.request.args[0].amount.currency",
          "amount": "REF:g.actionMap.testBankTransferPayout1.request.args[0].amount.value",
          "userId": "REF:g.actionMap.testBankTransferPayout1.request.args[0].requestContext.orderInfo.userMemberId",
          "note":"REF:g.actionMap.testBankTransferPayout1.request.args[0].requestContext.orderInfo.remark",
          "extraJson": {
            "merchantId": "REF:g.actionMap.testBankTransferPayout1.request.args[0].requestContext.orderInfo.merchantNo"
          },
          "extraParamJson": "{}"
        }
    IoniaRocketMqTemplate.sendMsgSync:0:
      - topic_channel_exchange_payout_notify
      - retryTimes: 0
        data:
          country: REF:g.actionMap.testBankTransferPayout1.request.args[0].requestContext.paymentMethod.country,
          channelMethodCode: O_DOKU_P01_BANKTRANSFER101_BCA_ID
          fourthOrgOrderNo: REF:_.mockRecords['InternalFrontClient.internalExecute:0'].response.data.channelThirdPartyTxnId
          payOrderNo: REF:g.actionMap.testBankTransferPayout1.request.args[0].payOrderNo
          channelEntity: P01
          mappingCode: APPLY_SUCCESS
          mappingMsg: success
          channelPayRequestNo: REF:g.actionMap.testBankTransferPayout1.request.args[0].payOrderNo
          channelPayCommitNo: ${g.actionMap.testBankTransferPayout1.responseObj.data.channelPayCommitNo}
          oldMethodCode: 93
          oldMethodSubCode: BCA
          bizOrderNo: REF:g.actionMap.testBankTransferPayout1.request.args[0].payOrderNo
          channelCode: O_DOKU_P01
          status: SUCCESS
        source: fin-channel-exchange
        key: REF:g.actionMap.testBankTransferPayout1.request.args[0].payOrderNo
    ContextCenterManager.savePaymentContext:0:
      - payoutCallback:
          country: REF:g.actionMap.testBankTransferPayout1.request.args[0].requestContext.paymentMethod.country,
          channelMethodCode: O_DOKU_P01_BANKTRANSFER101_BCA_ID
          fourthOrgOrderNo: REF:_.mockRecords['InternalFrontClient.internalExecute:0'].response.data.channelThirdPartyTxnId
          payOrderNo: REF:g.actionMap.testBankTransferPayout1.request.args[0].payOrderNo
          channelEntity: P01
          mappingCode: APPLY_SUCCESS
          mappingMsg: success
          channelPayRequestNo: REF:g.actionMap.testBankTransferPayout1.request.args[0].payOrderNo
          channelPayCommitNo: ${g.actionMap.testBankTransferPayout1.responseObj.data.channelPayCommitNo}
          oldMethodCode: 93
          oldMethodSubCode: BCA
          bizOrderNo: REF:g.actionMap.testBankTransferPayout1.request.args[0].payOrderNo
          channelCode: O_DOKU_P01
          status: SUCCESS
      - isUpdateExpireTime: true
        type: PAYOUT_CALLBACK
        timeOut: 1800
  RESPONSE:
    msg: ''
    code: APPLY_SUCCESS
    data:
      channelPayRequestNo: REF:g.actionMap.testBankTransferPayout1.request.args[0].payOrderNo
      status: SUCCESS
---
# 验证结果
actionName: queryCommitOrder_retry1
request: !beanRequest
  beanName: channelCommitOrderServiceImpl
  methodName: getByEntityQueryFromMaster
  args:
    - !!com.payermax.fin.exchange.dal.entity.query.ChannelCommitOrderEntityQuery
      channelPayRequestNo: ${g.actionMap.testBankTransferPayout1.request.args[0].payOrderNo}
expect:
  RESPONSE:
    - requestType: payout
      channelCode: O_DOKU_P01
      channelMethodCode: O_DOKU_P01_BANKTRANSFER101_BCA_ID
      channelEntity: P01
      eventType: APPLY
      mainRequestType: 1
      status: 1
    - requestType: payoutRisk
      channelCode: O_DOKU_P01
      channelMethodCode: O_DOKU_P01_BANKTRANSFER101_BCA_ID
      channelEntity: P01
      eventType: APPLY
      mainRequestType: 3
      status: 1
---
# 验证结果
actionName: queryRequestOrder_retry1
request: !beanRequest
  beanName: channelRequestOrderServiceImpl
  methodName: getByRequestOrderNoFromMaster
  args:
    - ${g.actionMap.testBankTransferPayout1.request.args[0].payOrderNo}
expect:
  RESPONSE:
    requestType: payout
    mainRequestType: 1
    status: 1
    merchantNo: '${g.actionMap.testBankTransferPayout1.request.args[0].requestContext.orderInfo.merchantNo}'
    userMemberId: ${g.actionMap.testBankTransferPayout1.request.args[0].requestContext.orderInfo.userMemberId}
    bizOrderNo: '${g.actionMap.testBankTransferPayout1.request.args[0].payOrderNo}'
    sourceBizOrderNo: '${g.actionMap.testBankTransferPayout1.request.args[0].payRequestNo}'
    paymentMethodType: ${g.actionMap.testBankTransferPayout1.request.args[0].paymentMethodType}
    paymentType: ${g.actionMap.testBankTransferPayout1.request.args[0].requestContext.paymentMethod.paymentType}
    country: ${g.actionMap.testBankTransferPayout1.request.args[0].requestContext.paymentMethod.country}
    currency: ${g.actionMap.testBankTransferPayout1.request.args[0].amount.currency}
    targetOrg: ${g.actionMap.testBankTransferPayout1.request.args[0].targetOrg}

---
# 触发换单重发
actionName: pendingTransChangeOrderRetry1
request: !beanRequest
  beanName: omcOrderCorrectionController
  methodName: pendingTransChangeOrderRetry
  args:
    - !!com.payermax.fin.exchange.service.request.PendingTranDataCorrectionRequest
      channelCommitOrderNos:
        - ${JsonUtil.get($g.actionMap.payoutRiskCallback3.mockRecords['InternalFrontClient.internalExecute:0'], 'request[4]>channelOrderNo')}
      channelCode: O_DOKU_P01
      paymentType: 10
      operator: system
mockConfigs:
  FSClient.upload:
    url: http://test.com
---
# 验证换单重发后结果
actionName: queryCommitOrder_abnormal1
request: !beanRequest
  beanName: channelCommitOrderServiceImpl
  methodName: getByEntityQueryFromMaster
  args:
    - !!com.payermax.fin.exchange.dal.entity.query.ChannelCommitOrderEntityQuery
      channelPayRequestNo: ${g.actionMap.testBankTransferPayout3.request.args[0].payOrderNo}
expect:
  RESPONSE:
    - requestType: payout
      channelCode: O_DOKU_P01
      channelMethodCode: O_DOKU_P01_BANKTRANSFER101_BCA_ID
      channelEntity: P01
      eventType: APPLY
      mainRequestType: 1
      status: 2
    - requestType: payoutRisk
      channelCode: O_DOKU_P01
      channelMethodCode: O_DOKU_P01_BANKTRANSFER101_BCA_ID
      channelEntity: P01
      eventType: CALLBACK
      mainRequestType: 3
      status: 1
---
# 验证换单重发后结果
actionName: queryRequestOrder_abnormal1
request: !beanRequest
  beanName: channelRequestOrderServiceImpl
  methodName: getByRequestOrderNoFromMaster
  args:
    - ${g.actionMap.testBankTransferPayout3.request.args[0].payOrderNo}
expect:
  RESPONSE:
    requestType: payout
    mainRequestType: 1
    status: 0
    merchantNo: '${g.actionMap.testBankTransferPayout3.request.args[0].requestContext.orderInfo.merchantNo}'
    userMemberId: ${g.actionMap.testBankTransferPayout3.request.args[0].requestContext.orderInfo.userMemberId}
    bizOrderNo: '${g.actionMap.testBankTransferPayout3.request.args[0].payOrderNo}'
    sourceBizOrderNo: '${g.actionMap.testBankTransferPayout3.request.args[0].payRequestNo}'
    paymentMethodType: ${g.actionMap.testBankTransferPayout3.request.args[0].paymentMethodType}
    paymentType: ${g.actionMap.testBankTransferPayout3.request.args[0].requestContext.paymentMethod.paymentType}
    country: ${g.actionMap.testBankTransferPayout3.request.args[0].requestContext.paymentMethod.country}
    currency: ${g.actionMap.testBankTransferPayout3.request.args[0].amount.currency}
    targetOrg: ${g.actionMap.testBankTransferPayout3.request.args[0].targetOrg}

---
# 执行换单重发
actionName: abnormalPayout1
request: !beanRequest
  beanName: newAbnormalPayoutJobHandler
  methodName: execute
  args:
    - '{"dealModel":"NO_LIMIT","adjustValue":1,"adjustUnit":"HOURS","maxRetryCount":9999,"limit":1,"status":null,"forceChannelCodes":["O_DOKU_P01"]}'
mockConfigs:
  InternalFrontClient.internalExecute:0:
    data:
      country: ${_.request.args[0].requestContext.paymentMethod.country}
      channelTransactionId: ${GenUtil.randStr(20)}
      amount: 20000.00
      pending: true
      channelRespCode: PAYMENT_IN_PROCESS
      channelOrderNo: ${JsonUtil.get($req_4, "$>channelOrderNo")}
      failed: false
      channelRespMsg: Payment is processing.
      userId: P01V070702343069
      channelThirdPartyTxnId: ${GenUtil.randStr(30)}
      channelRespJson:
        result:
          resultStatus: U
          success: false
          resultCode: PAYMENT_IN_PROCESS
          failed: false
          paymentInProcess: true
          resultMessage: Payment is processing.
        paymentId: ${GenUtil.randStr(30)}
        paymentRequestId: ${JsonUtil.get($req_4, "$>channelOrderNo")}
      currency: IDR
      channelCode: DOKU211
      status: PENDING
    bizCode: '0000'
    channelRespCode: PAYMENT_IN_PROCESS
    message: success
    channelRespMsg: Payment is processing.
  ContextCenterManager.queryRiskContext:0:
    !!com.payermax.common.lang.model.dto.Result
    msg: ''
    code: APPLY_SUCCESS
    data:
      !!com.payermax.basic.contexcenter.service.client.model.risk.RiskContextInfo
      3ds: null
      ddcSessionId: null
      RISK-ENGINE-RESULT: null
      cybsDdcSessionMap: null
      dm: null
      ddcSessionMap: null
      mpgsDdcSessionMap: null
      httpHeaders: null
      cardinalDdcSessionMap: null
      amazonPay: null
      cardToken: null
      hitrustDdcSessionMap: null
      cardBin: null
      cbsSessionId: null
      amazonPaySessionId: null
      dynamic3DS: null
  RiskEngineSyncClient.queryEventResultForFinExchange:0:
    bizAppId: null
    ruleContent: null
    eventId: null
    bizType: null
    data: {
    }
    requestId: null
    riskResult: '0'
    riskCode: '0'
    ruleName: null
    ruleData: {
    }
    checkPoint: null
    riskMessage: PASS
  IoniaRocketMqTemplate.sendMsgSync:0:
    sendStatus: 'SEND_OK'
    msgId: ${GenUtil.randStr(30)}
  RiskEngineSyncClient.executeEvent:
    bizAppId: null
    ruleContent: null
    eventId: null
    riskResult: '0'
    riskCode: RISK_PASS
    riskMessage: F_0000
  ShardingUtil.getShardingVo:
    index: 0
    total: 1
postExecuteScript: |
  sleep(3000)
---
# 验证testBankTransferPayout3的结果
actionName: queryCommitOrder_abnormal2
request: !beanRequest
  beanName: channelCommitOrderServiceImpl
  methodName: getByEntityQueryFromMaster
  args:
    - !!com.payermax.fin.exchange.dal.entity.query.ChannelCommitOrderEntityQuery
      channelPayRequestNo: ${g.actionMap.testBankTransferPayout3.request.args[0].payOrderNo}
expect:
  RESPONSE:
    - requestType: payout
      channelCode: O_DOKU_P01
      channelMethodCode: O_DOKU_P01_BANKTRANSFER101_BCA_ID
      channelEntity: P01
      eventType: APPLY
      mainRequestType: 1
      status: 0
    - requestType: payout
      channelCode: O_DOKU_P01
      channelMethodCode: O_DOKU_P01_BANKTRANSFER101_BCA_ID
      channelEntity: P01
      mainRequestType: 1
      status: 2
      eventType: APPLY
    - requestType: payoutRisk
      channelCode: O_DOKU_P01
      channelMethodCode: O_DOKU_P01_BANKTRANSFER101_BCA_ID
      channelEntity: P01
      mainRequestType: 3
      status: 1
      eventType: CALLBACK
---
# 验证testBankTransferPayout3的结果
actionName: queryRequestOrder_abnormal2
request: !beanRequest
  beanName: channelRequestOrderServiceImpl
  methodName: getByRequestOrderNoFromMaster
  args:
    - ${g.actionMap.testBankTransferPayout3.request.args[0].payOrderNo}
expect:
  RESPONSE:
    requestType: payout
    mainRequestType: 1
    status: 0
    merchantNo: '${g.actionMap.testBankTransferPayout3.request.args[0].requestContext.orderInfo.merchantNo}'
    userMemberId: ${g.actionMap.testBankTransferPayout3.request.args[0].requestContext.orderInfo.userMemberId}
    bizOrderNo: '${g.actionMap.testBankTransferPayout3.request.args[0].payOrderNo}'
    sourceBizOrderNo: '${g.actionMap.testBankTransferPayout3.request.args[0].payRequestNo}'
    paymentMethodType: ${g.actionMap.testBankTransferPayout3.request.args[0].paymentMethodType}
    paymentType: ${g.actionMap.testBankTransferPayout3.request.args[0].requestContext.paymentMethod.paymentType}
    country: ${g.actionMap.testBankTransferPayout3.request.args[0].requestContext.paymentMethod.country}
    currency: ${g.actionMap.testBankTransferPayout3.request.args[0].amount.currency}
    targetOrg: ${g.actionMap.testBankTransferPayout3.request.args[0].targetOrg}
