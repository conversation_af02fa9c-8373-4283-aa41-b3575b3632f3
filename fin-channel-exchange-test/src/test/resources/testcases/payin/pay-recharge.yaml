## 步骤：setGrayConfig -> 充值支付
actionName: setGrayConfig
request: !beanRequest
  args:
    - '[{"businessKey":"ALL","grayModel":"NEW","weight":"100"}]'
  beanName: grayCompareUtils
  methodName: setGrayConfig
---
## 凭证充值：路由到线下渠道，直接成功
actionName: testPayRecharge
request: !beanRequest
  args:
    - !!com.payermax.fin.exchange.service.request.PayRequest
      amount:
        currency: USD
        value: 190.0
      bizIdentify: P0101
      payOrderNo: ${GenUtil.randNumStr(32)}-1
      payRequestNo: ${GenUtil.randNumStr(32)}
      paymentMethodType: BankTransfer
      productCode: '3002'
      requestContext:
        thirdOrgOrderNo: "1513IT9128264"
        orderInfo:
          mcc: '5262'
          merchantNo: '***************'
          outUserId: P01V070702343069
          userMemberId: P01V070702343069
        params: {
          channelTradeNo: "1513IT9128264",
          fundsAccountId: "DBSHK01_*********_HK_USD"
        }
        paymentMethod:
          country: HK
          customerType: 2
          isMatchEntity: true
          paymentFlow: OFFLINE
          paymentType: 20
          extendProperties:
            - key: SUPPORT_CARD_ORG
              logicKey: INCLUDE
              value: 'SETTLEORG'
      serviceEntity: PMHK01
      serviceMode: SD
      targetOrg: '*'
  beanName: paymentFacadeImpl
  methodName: pay
mockConfigs:
  ContextCenterManager.queryPaymentContext:0:
    msg: ''
    code: APPLY_SUCCESS
    data: null
  ContextCenterManager.queryRiskContext:0:
    !!com.payermax.common.lang.model.dto.Result
    msg: ''
    code: APPLY_SUCCESS
    data:
      !!com.payermax.basic.contexcenter.service.client.model.risk.RiskContextInfo
      3ds: null
      ddcSessionId: null
      RISK-ENGINE-RESULT: null
      cybsDdcSessionMap: null
      dm: null
      ddcSessionMap: null
      mpgsDdcSessionMap: null
      httpHeaders: null
      cardinalDdcSessionMap: null
      amazonPay: null
      cardToken: null
      hitrustDdcSessionMap: null
      cardBin: null
      cbsSessionId: null
      amazonPaySessionId: null
      dynamic3DS: null
  RiskEngineSyncClient.queryEventResultForFinExchange:0:
    bizAppId: null
    ruleContent: null
    eventId: null
    bizType: null
    data: {
    }
    requestId: null
    riskResult: '0'
    riskCode: '0'
    ruleName: null
    ruleData: {
    }
    checkPoint: null
    riskMessage: PASS
  ContextCenterManager.savePaymentContext:0:
    msg: ''
    code: APPLY_SUCCESS
    data: null
  FundsAccountFacade.queryAccountDetailById:
    !!com.payermax.common.lang.model.dto.Result
    msg: ''
    code: APPLY_SUCCESS
    data:
      !!com.payermax.channel.inst.center.facade.response.QueryAccountDetailByIdResponse
      accountId: DBSHK01_*********_HK_USD
      accountName: PMmax Technology Limited
      accountNo: *********
      accountType: BANK_ACCOUNT
      bankName: DBS Bank Hong Kong Limited
      country: HK
      currency: USD
      entity: P01
      instCode: DBSHK01
      swiftCode: DHBKHKHH
  MerchantQueryFacade.total:0:
    !!com.payermax.common.lang.model.dto.Result
    msg: ''
    code: APPLY_SUCCESS
    data:
      !!com.payermax.merchant.omc.facade.resp.MerchantBaseResp
      MerchantBase:
        merchantSource: 10
expect:
  MOCK_INVOKE:
    ContextCenterManager.queryPaymentContext:0:
      - requestNo: REF:_req.args[0].payRequestNo
    ContextCenterManager.queryRiskContext:0:
      - requestKey: REF:_req.args[0].payRequestNo
    RiskEngineSyncClient.queryEventResultForFinExchange:0:
      - bizType: REF:_req.args[0].bizIdentify
        requestId: REF:_req.args[0].payRequestNo
        eventBody:
          mcc: REF:_req.args[0].requestContext.orderInfo.mcc
          paymentType: REF:_req.args[0].requestContext.paymentMethod.paymentType
      - 200
      - 0
    ContextCenterManager.savePaymentContext:0:
      - payResponseInfo:
          country: REF:_req.args[0].requestContext.paymentMethod.country,
          channelMethodCode: I_OFFLINE_DBS_BANKTRANSFER20_SETTLEORG_*_USD
          payOrderNo: REF:_req.args[0].payOrderNo
          channelEntity: P01
          mappingCode: APPLY_SUCCESS
          isSupportRefund: '0'
          asyncFlag: false
          channelPayRequestNo: REF:_req.args[0].payOrderNo
          paidAmount:
            currency: USD
            value: REF:_req.args[0].amount.value
          channelCode: I_OFFLINE_DBS
          status: SUCCESS
      - isUpdateExpireTime: true
        requestNo: REF:_req.args[0].payRequestNo
        type: PAYMENT_RESPONSE
        timeOut: 1800
  RESPONSE:
    msg: ''
    code: APPLY_SUCCESS
    data:
      country: REF:_req.args[0].requestContext.paymentMethod.country,
      mappingCode: APPLY_SUCCESS
      channelBusinessType: null
      fundsAccountId: null
      isSupport3ds: null
      channelCompleteTime: null
      outExtendResult: null
      asyncFinishFlag: null
      nextRequestType: null
      channelCode: I_OFFLINE_DBS
      nextApiType: null
      channelMethodCode: I_OFFLINE_DBS_BANKTRANSFER20_SETTLEORG_*_USD
      correctionLogo: null
      extendResult: null
      payOrderNo: REF:_req.args[0].payOrderNo
      settleCurrency: null
      channelEntity: P01
      completeTime: null
      cardOrg: null
      isSupportRefund: '0'
      expireTime: null
      standardChannelCode: null
      asyncFlag: false
      channelPayRequestNo: REF:_req.args[0].payOrderNo
      channelMerchantCode: null
      paidAmount:
        currency: REF:_req.args[0].amount.currency
        value:  REF:_req.args[0].amount.value
      status: SUCCESS
---

## VA充值，路由到线上来账服务
actionName: testPayRechargeFromReceipt
request: !beanRequest
  args:
    - !!com.payermax.fin.exchange.service.request.PayRequest
      amount:
        currency: USD
        value: 190.0
      bizIdentify: P0101
      payOrderNo: ${GenUtil.randNumStr(32)}-1
      payRequestNo: ${GenUtil.randNumStr(32)}
      paymentMethodType: BankTransfer
      productCode: '1101'
      requestContext:
        thirdOrgOrderNo: "1513IT9128264"
        orderInfo:
          mcc: '5262'
          merchantNo: '***************'
          outUserId: P01V070702343069
          userMemberId: P01V070702343069
        params: {
          channelTradeNo: "1513IT9128264",
          fundsAccountId: "DBSHK01_*********_HK_USD"
        }
        paymentMethod:
          country: HK
          customerType: 2
          isMatchEntity: true
          paymentFlow: DIRECT
          paymentType: 20
          extendProperties:
            - key: SUPPORT_CARD_ORG
              logicKey: INCLUDE
              value: 'SETTLEORG'
      serviceEntity: PMHK01
      serviceMode: SD
      targetOrg: '*'
  beanName: paymentFacadeImpl
  methodName: pay
mockConfigs:
  ReceiptOrderFacade.queryOrderDetail:0:
    !!com.payermax.common.lang.model.dto.Result
    msg: ''
    code: APPLY_SUCCESS
    data:
      !!com.payermax.funds.receipt.facade.response.OrderDetailQueryResponse
      productCode: 1101
      receiptOrderNo: ${_.request.args[0].requestContext.thirdOrgOrderNo}
      status: ASSIGN_SUCCESS
      useType: RECEIVE_PAY
  ContextCenterManager.queryPaymentContext:0:
    msg: ''
    code: APPLY_SUCCESS
    data: null
  ContextCenterManager.queryRiskContext:0:
    !!com.payermax.common.lang.model.dto.Result
    msg: ''
    code: APPLY_SUCCESS
    data:
      !!com.payermax.basic.contexcenter.service.client.model.risk.RiskContextInfo
      3ds: null
      ddcSessionId: null
      RISK-ENGINE-RESULT: null
      cybsDdcSessionMap: null
      dm: null
      ddcSessionMap: null
      mpgsDdcSessionMap: null
      httpHeaders: null
      cardinalDdcSessionMap: null
      amazonPay: null
      cardToken: null
      hitrustDdcSessionMap: null
      cardBin: null
      cbsSessionId: null
      amazonPaySessionId: null
      dynamic3DS: null
  RiskEngineSyncClient.queryEventResultForFinExchange:0:
    bizAppId: null
    ruleContent: null
    eventId: null
    bizType: null
    data: {
    }
    requestId: null
    riskResult: '0'
    riskCode: '0'
    ruleName: null
    ruleData: {
    }
    checkPoint: null
    riskMessage: PASS
  ContextCenterManager.savePaymentContext:0:
    msg: ''
    code: APPLY_SUCCESS
    data: null
  FundsAccountFacade.queryAccountDetailById:
    !!com.payermax.common.lang.model.dto.Result
    msg: ''
    code: APPLY_SUCCESS
    data:
      !!com.payermax.channel.inst.center.facade.response.QueryAccountDetailByIdResponse
      accountId: DBSHK01_*********_HK_USD
      accountName: PMmax Technology Limited
      accountNo: *********
      accountType: BANK_ACCOUNT
      bankName: DBS Bank Hong Kong Limited
      country: HK
      currency: USD
      entity: P01
      instCode: DBSHK01
      swiftCode: DHBKHKHH
  MerchantQueryFacade.total:0:
    !!com.payermax.common.lang.model.dto.Result
    msg: ''
    code: APPLY_SUCCESS
    data:
      !!com.payermax.merchant.omc.facade.resp.MerchantBaseResp
      MerchantBase:
        merchantSource: 10
expect:
  MOCK_INVOKE:
    ReceiptOrderFacade.queryOrderDetail:0:
      - receiptOrderNo: REF:_req.args[0].requestContext.thirdOrgOrderNo
    ContextCenterManager.queryPaymentContext:0:
      - requestNo: REF:_req.args[0].payRequestNo
    ContextCenterManager.queryRiskContext:0:
      - requestKey: REF:_req.args[0].payRequestNo
    RiskEngineSyncClient.queryEventResultForFinExchange:0:
      - bizType: REF:_req.args[0].bizIdentify
        requestId: REF:_req.args[0].payRequestNo
        eventBody:
          mcc: REF:_req.args[0].requestContext.orderInfo.mcc
          paymentType: REF:_req.args[0].requestContext.paymentMethod.paymentType
      - 200
      - 0
    ContextCenterManager.savePaymentContext:0:
      - payResponseInfo:
          country: REF:_req.args[0].requestContext.paymentMethod.country,
          channelMethodCode: I_DBS_BANKTRANSFER20_SETTLEORG_*_USD
          payOrderNo: REF:_req.args[0].payOrderNo
          channelEntity: P01
          mappingCode: APPLY_SUCCESS
          isSupportRefund: '0'
          asyncFlag: false
          channelPayRequestNo: REF:_req.args[0].payOrderNo
          paidAmount:
            currency: USD
            value: REF:_req.args[0].amount.value
          channelCode: I_DBS
          status: SUCCESS
      - isUpdateExpireTime: true
        requestNo: REF:_req.args[0].payRequestNo
        type: PAYMENT_RESPONSE
        timeOut: 1800
  RESPONSE:
    msg: ''
    code: APPLY_SUCCESS
    data:
      country: REF:_req.args[0].requestContext.paymentMethod.country,
      mappingCode: APPLY_SUCCESS
      channelBusinessType: null
      fundsAccountId: null
      isSupport3ds: null
      channelCompleteTime: null
      outExtendResult: null
      asyncFinishFlag: null
      nextRequestType: null
      channelCode: I_DBS
      nextApiType: null
      channelMethodCode: I_DBS_BANKTRANSFER20_SETTLEORG_*_USD
      correctionLogo: null
      extendResult: null
      payOrderNo: REF:_req.args[0].payOrderNo
      settleCurrency: null
      channelEntity: P01
      completeTime: null
      cardOrg: null
      isSupportRefund: '0'
      expireTime: null
      standardChannelCode: null
      asyncFlag: false
      channelPayRequestNo: REF:_req.args[0].payOrderNo
      channelMerchantCode: null
      paidAmount:
        currency: REF:_req.args[0].amount.currency
        value:  REF:_req.args[0].amount.value
      status: SUCCESS
---

actionName: setGrayConfig3
request: !beanRequest
  args:
    - '[{"businessKey":"ALL","weight":"100"}]'
  beanName: grayCompareUtils
  methodName: setGrayConfig