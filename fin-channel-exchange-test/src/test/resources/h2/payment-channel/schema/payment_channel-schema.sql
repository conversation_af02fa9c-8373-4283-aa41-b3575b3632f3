--
-- Table structure for tb_authorization
--
CREATE TABLE  if not exists tb_authorization
(
 id int NOT NULL AUTO_INCREMENT,
 product_code varchar(16) DEFAULT NULL COMMENT '收单产品编码',
 channel_code varchar(20) NOT NULL COMMENT '授权渠道代码',
 external_auth_refer_no varchar(64) NOT NULL COMMENT '外部授权请求流水号',
 internal_auth_code varchar(64) NOT NULL COMMENT '内部授权码',
 channel_auth_code varchar(256) DEFAULT NULL COMMENT '渠道端授权码',
 channel_auth_code_md5 varchar(128) DEFAULT NULL COMMENT '渠道端授权码MD5',
 authorization_type tinyINT NOT NULL COMMENT '授权类型(0:协议代扣,1:订阅代扣)',
 method_code varchar(16) NOT NULL COMMENT '授权方式编码',
 method_sub_code varchar(32) NOT NULL COMMENT '授权方式二级编码',
 country char(2) NOT NULL COMMENT '国家编号',
 user_id varchar(64) NOT NULL COMMENT '用户唯一标识',
 psp_customer_id varchar(64) DEFAULT NULL COMMENT '服务商用户 ID',
 psp_customer_name varchar(32) DEFAULT NULL COMMENT '服务商用户名称',
 channel_auth_txn_id varchar(64) DEFAULT NULL COMMENT '渠道端授权流水号',
 revoke_apply_time datetime DEFAULT NULL COMMENT '授权撤销申请时间',
 authorization_desc varchar(256) DEFAULT NULL COMMENT '授权描述',
 status tinyint NOT NULL DEFAULT '0' COMMENT '授权状态(0:处理中,1:授权成功,2:授权失败,3:授权撤销,4:授权过期,9:初始化)',
 deduct_interval varchar(16) DEFAULT NULL COMMENT '若为订阅扣款，此代扣间隔为每一个扣款周期值',
 expiry_time datetime DEFAULT NULL COMMENT '授权失效时间',
 unauthorized_expiry_time datetime DEFAULT NULL COMMENT '未授权的过期时间',
 amount decimal(15, 0) DEFAULT NULL COMMENT '若为订阅扣款此金额为订阅扣款金额',
 event_code varchar(10) NOT NULL DEFAULT 'INIT' COMMENT '状态机事件码',
 extra_json mediumtext COMMENT 'JSON扩展字段',
 add_field1 varchar(32) DEFAULT NULL COMMENT '扩展字段1',
 add_field2 varchar(128) DEFAULT NULL COMMENT '扩展字段2',
 add_field3 varchar(128) DEFAULT NULL COMMENT '扩展字段3',
 add_field4 varchar(32) DEFAULT NULL COMMENT '扩展字段4',
 add_field5 varchar(64) DEFAULT NULL COMMENT '扩展字段5',
 add_field6 varchar(256) DEFAULT NULL COMMENT '扩展字段6',
 add_field7 text COMMENT '扩展字段7',
 creator varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间UTC',
 modifier varchar(50) NOT NULL DEFAULT 'system' COMMENT '修改人',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间UTC',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '是否逻辑删除(N：未删除，Y：已删除)',
 user_name varchar(64) DEFAULT NULL COMMENT '用户名',
 certificate_type varchar(20) DEFAULT NULL COMMENT '证件类型',
 certificate_number varchar(64) DEFAULT NULL COMMENT '证件号',
 phone_number varchar(32) DEFAULT NULL COMMENT '手机号',
 email varchar(64) DEFAULT NULL COMMENT '邮箱',
 notify_url varchar(256) DEFAULT NULL COMMENT '通知地址',
 encrypt_key_token varchar(128) DEFAULT NULL COMMENT '安全服务加密token',
 PRIMARY KEY (id) ,
 UNIQUE KEY INDEX_77055476b1f148dbaf6321e28dfc0f17 (external_auth_refer_no) ,
 UNIQUE KEY INDEX_b6e7a06975ac49ecac3341599a796819 (internal_auth_code) ,
 KEY INDEX_faabe36796a042e4867e8615b2a1d866 (channel_auth_code_md5)
) COMMENT='支付授权表';

--
-- Table structure for tb_authorization_log
--
CREATE TABLE  if not exists tb_authorization_log
(
 id bigint NOT NULL AUTO_INCREMENT,
 track_no varchar(64) DEFAULT NULL COMMENT '系统跟踪号',
 channel_code varchar(16) NOT NULL COMMENT '授权渠道编码',
 biz_type tinyint NOT NULL COMMENT '业务类别:(0:授权申请,1:授权撤销,2:授权刷新)',
 external_auth_refer_no varchar(64) DEFAULT NULL COMMENT '外部授权请求流水号',
 internal_auth_code varchar(32) NOT NULL COMMENT '内部授权码',
 channel_refer_no varchar(64) NOT NULL COMMENT '请求渠道授权流水号',
 country char(2) NOT NULL COMMENT '国家编号',
 user_id varchar(64) NOT NULL COMMENT '用户唯一标识',
 response_code varchar(64) DEFAULT NULL COMMENT '请求渠道返回code',
 mapped_code varchar(10) DEFAULT NULL COMMENT '统一映射错误码',
 response_msg varchar(256) DEFAULT NULL COMMENT '请求渠道返回msg',
 response_json mediumtext COMMENT '请求渠道返回json',
 redirect_url varchar(1024) DEFAULT NULL,
 add_field1 varchar(32) DEFAULT NULL COMMENT '扩展字段1',
 add_field2 varchar(64) DEFAULT NULL COMMENT '扩展字段2',
 add_field3 varchar(128) DEFAULT NULL COMMENT '扩展字段3',
 add_field4 varchar(32) DEFAULT NULL COMMENT '扩展字段4',
 add_field5 varchar(64) DEFAULT NULL COMMENT '扩展字段5',
 add_field6 varchar(256) DEFAULT NULL COMMENT '扩展字段6',
 creator varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间UTC',
 modifier varchar(50) NOT NULL DEFAULT 'system' COMMENT '修改人',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间UTC',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '是否逻辑删除(N：未删除，Y：已删除)',
 remark varchar(256) DEFAULT NULL COMMENT '备注',
 PRIMARY KEY (id) ,
 UNIQUE KEY INDEX_b2a09d1f3b8e452fae0e0ddcb8ed0933 (channel_refer_no),
 KEY INDEX_61da4c149b024eeaa331e27c687460b2 (internal_auth_code,biz_type)
) COMMENT='授权日志信息表';

--
-- Table structure for tb_auto_route_record
--
CREATE TABLE  if not exists tb_auto_route_record
(
 id int NOT NULL AUTO_INCREMENT COMMENT 'id',
 architecture_type char(2) NOT NULL DEFAULT 'v2' COMMENT '新老架构（v1:老架构,v2:新架构）',
 country char(2) NOT NULL COMMENT '国家',
 currency char(3) NOT NULL COMMENT '币种',
 payment_method_type varchar(32) NOT NULL COMMENT '支付方式类型',
 target_org varchar(64) NOT NULL COMMENT '目标机构',
 card_org varchar(64) DEFAULT NULL COMMENT '卡组织',
 payment_type varchar(8) NOT NULL COMMENT '支付类型 10:出款 20:入款 30:汇兑 50:转账',
 channel_code varchar(64) NOT NULL COMMENT '渠道编码',
 channel_method_code varchar(128) NOT NULL COMMENT '渠道支付方式编码',
 main_request_type tinyint NOT NULL DEFAULT '1' COMMENT '请求大类：1.支付类，2.控制类，3.风控',
 request_type varchar(64) DEFAULT NULL COMMENT '请求类型',
 monitor_data text NOT NULL COMMENT '监控指标，成功率，失败率，callback耗时，接口耗时等',
 notify tinyINT DEFAULT '0' COMMENT '0:未通知，1:已通知',
 enable_type tinyINT NOT NULL DEFAULT '0' COMMENT '开启路由切换方式（0：自动切换）',
 disabled_type tinyINT DEFAULT NULL COMMENT '关闭路由切换方式（0：自动切换，1：手动切换）',
 status tinyINT NOT NULL DEFAULT '0' COMMENT '状态 0：开启切换 1：关闭切换',
 utc_create_time datetime DEFAULT CURRENT_TIMESTAMP COMMENT '开启切换时间',
 creator varchar(32) DEFAULT 'system' COMMENT '开启切换人',
 utc_terminator_time datetime DEFAULT NULL COMMENT '关闭切换时间',
 terminator varchar(32) DEFAULT NULL COMMENT '关闭切换人',
 PRIMARY KEY (id),
 KEY INDEX_abb003ebf9aa41f38e6da11c8924cc1b (status,channel_method_code) ,
 KEY INDEX_ac68abeb25a84b2499f717eeee9e5fcd (country,currency,payment_method_type,payment_type,target_org,card_org,channel_code)
) COMMENT='自动化路由切换记录表';

--
-- Table structure for tb_batch_detail
--
CREATE TABLE  if not exists tb_batch_detail
(
 id int NOT NULL AUTO_INCREMENT,
 batch_id varchar(64) NOT NULL COMMENT '批次号',
 biz_channel_order_no varchar(64) NOT NULL COMMENT '业务渠道订单号',
 channel_code varchar(16) DEFAULT NULL COMMENT '渠道编码',
 status char(2) NOT NULL COMMENT '状态，0-初始，待生成文件；1-已生成文件；2-未生成文件',
 remark varchar(512) DEFAULT NULL COMMENT '备注',
 creator varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间UTC',
 modifier varchar(50) NOT NULL DEFAULT 'system' COMMENT '修改人',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间UTC',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '是否逻辑删除(N：未删除，Y：已删除)',
 PRIMARY KEY (id) ,
 UNIQUE KEY INDEX_a5c2b824178444b086820a6a6bf963ce (batch_id,biz_channel_order_no)
) COMMENT='批次详情表';

--
-- Table structure for tb_batch_file
--
CREATE TABLE  if not exists tb_batch_file
(
 id int NOT NULL AUTO_INCREMENT,
 batch_id varchar(64) NOT NULL COMMENT '批次号',
 file_name varchar(128) DEFAULT NULL COMMENT '文件名称',
 file_date varchar(16) DEFAULT NULL COMMENT '文件日期，ex.2020-03-01',
 batch_type char(2) NOT NULL COMMENT '批次文件类型，0-批量出款，1-批量出款回执',
 file_path varchar(255) DEFAULT NULL COMMENT '文件路径',
 transfer_type varchar(32) NOT NULL COMMENT '传输类型，sftp，邮件',
 channel_code varchar(32) NOT NULL COMMENT '渠道编码',
 country char(2) DEFAULT NULL COMMENT '国家二字码',
 payment_method varchar(16) DEFAULT NULL COMMENT '一级支付方式',
 payment_sub_method varchar(32) DEFAULT NULL COMMENT '二级支付方式',
 status char(2) DEFAULT NULL COMMENT '状态，0-待上送；1-上送成功；2-上送失败；3-待解析；4-解析成功；5-解析失败；6-回调完成',
 total_count varchar(8) DEFAULT NULL COMMENT '批次文件数据条数',
 deal_count varchar(8) DEFAULT NULL COMMENT '批次文件处理条数',
 remark varchar(255) DEFAULT NULL COMMENT '备注',
 creator varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间UTC',
 modifier varchar(50) NOT NULL DEFAULT 'system' COMMENT '修改人',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间UTC',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '是否逻辑删除(N：未删除，Y：已删除)',
 PRIMARY KEY (id) ,
 UNIQUE KEY INDEX_b585121493774ec0a94cfd24beb1cb12 (batch_id)
) COMMENT='批次文件表';

--
-- Table structure for tb_batch_receipt_detail
--
CREATE TABLE  if not exists tb_batch_receipt_detail
(
 id int NOT NULL AUTO_INCREMENT,
 batch_id varchar(64) NOT NULL COMMENT '批次号',
 biz_channel_order_no varchar(64) NOT NULL COMMENT '业务渠道订单号',
 channel_code varchar(16) DEFAULT NULL COMMENT '渠道编码',
 content mediumtext COMMENT '批次文件内容',
 status char(2) NOT NULL COMMENT '状态，1-待处理；2-处理完成；3-处理失败;4-校验拒绝',
 biz_status tinyint DEFAULT NULL COMMENT '渠道支付状态码,(0：pending，1：success，2：fail)',
 result_code varchar(64) DEFAULT NULL COMMENT '处理结果码',
 result_msg varchar(256) DEFAULT NULL COMMENT '处理结果描述',
 response_code varchar(64) DEFAULT NULL COMMENT '请求渠道返回code',
 response_msg varchar(256) DEFAULT NULL COMMENT '请求渠道返回msg',
 third_channel_no varchar(128) DEFAULT NULL COMMENT '第三方渠道流水号',
 third_bank_txn_id varchar(128) DEFAULT NULL COMMENT '第三银行txnid',
 remark varchar(512) DEFAULT NULL COMMENT '备注',
 creator varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间UTC',
 modifier varchar(50) NOT NULL DEFAULT 'system' COMMENT '修改人',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间UTC',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '是否逻辑删除(N：未删除，Y：已删除)',
 PRIMARY KEY (id) ,
 UNIQUE KEY INDEX_aaeb6f5df74545fb93e2fa802974691c (batch_id,biz_channel_order_no) ,
 KEY INDEX_db76a5ae7f8d4eeb8e7a7b796acc5fc8 (biz_channel_order_no)
) COMMENT='批次详情表';

--
-- Table structure for tb_bounce_back_order
--
CREATE TABLE  if not exists tb_bounce_back_order
(
 id bigint NOT NULL AUTO_INCREMENT,
 bounce_back_order_no varchar(64) NOT NULL COMMENT '退票流水号',
 payout_channel_order_no varchar(64) NOT NULL COMMENT '付款渠道流水号',
 pay_order_no varchar(64) NOT NULL COMMENT '付款支付单流水号',
 channel_code varchar(20) NOT NULL COMMENT '付款渠道编码',
 response_code varchar(64) DEFAULT NULL COMMENT '渠道原始错误码',
 response_msg text COMMENT '渠道原始错误描述',
 external_response_code varchar(64) DEFAULT NULL COMMENT '对外错误码',
 external_response_msg text COMMENT '对外错误描述',
 type tinyint NOT NULL DEFAULT '0' COMMENT '退票通知类型(0:系统,1:人工)',
 status tinyint NOT NULL DEFAULT '0' COMMENT '退票处理状态码(0:待审核,1:初审拒绝,2:待复核,3:复审拒绝,4:审核通过,5:无需审核,6:处理完成)',
 preliminary_review_notification_voucher text COMMENT '初审通知凭证',
 preliminary_review_remarks text COMMENT '初审备注',
 preliminary_review_modifier varchar(50) DEFAULT NULL COMMENT '初审人员',
 review_notification_voucher text COMMENT '复审通知凭证',
 review_remarks text COMMENT '复审备注',
 review_modifier varchar(50) DEFAULT NULL COMMENT '复审人员',
 add_field1 varchar(32) DEFAULT NULL COMMENT '扩展字段1',
 add_field2 varchar(64) DEFAULT NULL COMMENT '扩展字段2',
 add_field3 varchar(128) DEFAULT NULL COMMENT '扩展字段3',
 special_mapping_id bigint NOT NULL COMMENT '特殊状态关联id',
 order_complete_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '原订单终态时间',
 channel_arrive_time datetime DEFAULT CURRENT_TIMESTAMP COMMENT '渠道到账时间',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
 complete_time datetime DEFAULT NULL COMMENT '退票订单完成时间',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '是否逻辑删除',
 PRIMARY KEY (id) ,
 UNIQUE KEY INDEX_f937569af22c4c9da804b1656b6c02b1 (bounce_back_order_no) ,
 KEY INDEX_e45cef73834f4d72b4fe22eff5268d1b (payout_channel_order_no) ,
 KEY INDEX_3429da0f2f4240b692c73ec4561c2bd7 (create_time,status,channel_code)
) COMMENT='退票通知单表';

--
-- Table structure for tb_business_ext_info
--
CREATE TABLE  if not exists tb_business_ext_info
(
 id bigint NOT NULL AUTO_INCREMENT,
 business_no varchar(64) NOT NULL COMMENT '业务单号',
 ext_json text COMMENT '扩展信息',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (id) ,
 KEY INDEX_bc2b7a9d956e4e9586f446f156c5e7d3 (business_no)
) COMMENT='业务扩展信息表';

--
-- Table structure for tb_carrier_billing_products
--
CREATE TABLE  if not exists tb_carrier_billing_products
(
 id int NOT NULL AUTO_INCREMENT,
 channel_id varchar(32) DEFAULT NULL COMMENT '渠道标识',
 country char(2) NOT NULL COMMENT '国家编号',
 operator_code varchar(64) NOT NULL COMMENT '内部运营商号',
 operator_id varchar(32) DEFAULT NULL COMMENT '运营商ID',
 operator_name varchar(128) DEFAULT NULL COMMENT '运营商名称',
 product_id varchar(32) NOT NULL COMMENT '产品编号',
 product_name varchar(128) DEFAULT NULL COMMENT '产品名称',
 product_type varchar(32) DEFAULT NULL COMMENT '产品类型',
 benefit_type varchar(32) DEFAULT NULL COMMENT '收益类型',
 product_currency char(3) NOT NULL COMMENT '产品币种',
 product_amount_type varchar(32) DEFAULT NULL COMMENT '产品金额类型',
 product_min_amount decimal(15, 0) NOT NULL COMMENT '产品最小金额',
 product_max_amount decimal(15, 0) NOT NULL COMMENT '产品最大金额',
 status tinyint DEFAULT '0' COMMENT '产品状态(0:可用,1:不可用)',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间UTC',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间UTC',
 PRIMARY KEY (id) ,
 KEY INDEX_aa5f0811dc404d138f4ce9b59e133003 (product_id) ,
 KEY INDEX_b9c5077783dc4628a02593876283882a (channel_id,country,product_min_amount,product_max_amount)
) COMMENT='运营商计费产品表';

--
-- Table structure for tb_channel_certification_info
--
CREATE TABLE  if not exists tb_channel_certification_info
(
 id bigint NOT NULL AUTO_INCREMENT,
 product_code varchar(32) DEFAULT NULL COMMENT '收单产品编码',
 biz_product_code varchar(64) DEFAULT NULL COMMENT '业务产品码',
 merchant_type varchar(32) DEFAULT NULL COMMENT '商户类型',
 merchant_id varchar(64) DEFAULT NULL COMMENT '商户号',
 track_no varchar(64) DEFAULT 'NA' COMMENT '系统跟踪号',
 acquire_main_order_no varchar(64) DEFAULT NULL COMMENT '收单主订单',
 merchant_order_no varchar(64) DEFAULT NULL COMMENT '商户订单号',
 channel_certification_no varchar(64) NOT NULL COMMENT '渠道认证流水号',
 out_certification_no varchar(64) NOT NULL COMMENT '外部认证流水号',
 channel_code varchar(20) NOT NULL COMMENT '支付渠道编码',
 method_code varchar(16) NOT NULL COMMENT '支付方式编码',
 method_sub_code varchar(32) NOT NULL COMMENT '支付方式二级编码',
 country char(2) NOT NULL COMMENT '国家编号',
 currency char(3) NOT NULL COMMENT '交易币种',
 pay_amount decimal(15, 0) NOT NULL COMMENT '支付金额(以分为单位)',
 purchase_info varchar(256) DEFAULT NULL COMMENT '消费信息',
 certification_type varchar(4) DEFAULT NULL COMMENT '认证类型：1 手机号认证 2 email认证 3 账户认证',
 payer_extra_details mediumtext COMMENT '付款人扩展详情',
 third_certification_no varchar(128) DEFAULT NULL COMMENT '第三方渠道认证流水号',
 fourth_party_certification_no varchar(128) DEFAULT NULL COMMENT '第四方流水号',
 user_id varchar(64) DEFAULT NULL COMMENT '用户唯一标识',
 remark varchar(256) DEFAULT NULL COMMENT '交易备注',
 status tinyint NOT NULL DEFAULT '0' COMMENT '认证状态码,(0:pending; 1:success; 2:failed)',
 event_code varchar(10) NOT NULL DEFAULT 'INIT' COMMENT '状态机事件码',
 mapped_code varchar(10) DEFAULT NULL COMMENT '统一映射错误码',
 response_code varchar(64) DEFAULT NULL COMMENT '请求渠道返回code',
 response_msg varchar(256) DEFAULT NULL COMMENT '请求渠道返回msg',
 send_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '认证发送时间',
 response_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '响应时间',
 add_field1 varchar(32) DEFAULT NULL COMMENT '扩展字段1',
 add_field2 varchar(64) DEFAULT NULL COMMENT '扩展字段2',
 add_field3 varchar(128) DEFAULT NULL COMMENT '扩展字段3',
 add_field4 varchar(32) DEFAULT NULL COMMENT '扩展字段4',
 add_field5 varchar(64) DEFAULT NULL COMMENT '扩展字段5',
 add_field6 varchar(256) DEFAULT NULL COMMENT '扩展字段6',
 creator varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间UTC',
 modifier varchar(50) NOT NULL DEFAULT 'system' COMMENT '修改人',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间UTC',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '是否逻辑删除(N：未删除，Y：已删除)',
 pass_info_to_channel mediumtext COMMENT '收单通过支付引擎透传到渠道的json格式信息',
 payer_account varchar(64) DEFAULT NULL COMMENT '接收验证信息的手机号、邮箱或者其他账号',
 PRIMARY KEY (id) ,
 UNIQUE KEY INDEX_80e99c006b794815ad083702f21de6e1 (channel_certification_no) ,
 UNIQUE KEY INDEX_e8a2bfa765074f52b33933da1087c3d3 (out_certification_no)
) COMMENT='认证信息表';

--
-- Table structure for tb_channel_check_sign
--
CREATE TABLE  if not exists tb_channel_check_sign
(
 id bigint NOT NULL AUTO_INCREMENT,
 trans_code varchar(255) NOT NULL DEFAULT '' COMMENT '交易编码',
 sign_field varchar(255) NOT NULL DEFAULT '' COMMENT '签名字段',
 sign_field_location varchar(255) NOT NULL DEFAULT '' COMMENT '签名位置',
 sign_field_script varchar(255) NOT NULL COMMENT '签名取值脚本',
 script_id bigint DEFAULT NULL COMMENT '验签脚本',
 script_parameter varchar(255) DEFAULT NULL COMMENT '验签参数',
 direction varchar(255) NOT NULL DEFAULT '' COMMENT '方向：client，server',
 creator varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间UTC',
 modifier varchar(50) NOT NULL DEFAULT 'system' COMMENT '修改人',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间UTC',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '是否逻辑删除(N：未删除，Y：已删除)',
 PRIMARY KEY (id)
) COMMENT='验签';

--
-- Table structure for tb_channel_endpoint
--
CREATE TABLE  if not exists tb_channel_endpoint
(
 id bigint NOT NULL AUTO_INCREMENT,
 trans_code varchar(255) NOT NULL DEFAULT '' COMMENT '交易编码',
 parent_trans_code varchar(255) NOT NULL DEFAULT '' COMMENT '前置请求交易编码',
 parent_id bigint DEFAULT NULL COMMENT '前置请求id',
 next_id bigint DEFAULT NULL COMMENT '后置请求id',
 http_method varchar(255) NOT NULL DEFAULT '' COMMENT 'http请求方法',
 content_type varchar(255) NOT NULL DEFAULT '' COMMENT '请求头',
 api_path varchar(2) DEFAULT '',
 domain_url varchar(255) NOT NULL DEFAULT '' COMMENT '域名',
 url varchar(255) NOT NULL DEFAULT '' COMMENT '资源路径',
 url_type varchar(255) NOT NULL COMMENT 'url类型：STATIC，DYNAMIC',
 direction varchar(255) NOT NULL COMMENT '方向：client，server',
 charset varchar(255) NOT NULL COMMENT '字符编码',
 timeout int DEFAULT NULL COMMENT '超时时间',
 timeout_strategy varchar(255) DEFAULT NULL COMMENT '超时策略',
 check_sign bit(1) NOT NULL COMMENT '是否签名',
 current_version varchar(255) NOT NULL DEFAULT '' COMMENT '当前版本',
 config_ext varchar(255) DEFAULT '',
 creator varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间UTC',
 modifier varchar(50) NOT NULL DEFAULT 'system' COMMENT '修改人',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间UTC',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '是否逻辑删除(N：未删除，Y：已删除)',
 PRIMARY KEY (id),
 UNIQUE KEY INDEX_b05d925f059d4ba8bebf9c73cc5b7f5c (trans_code)
) ;

--
-- Table structure for tb_channel_endpoint_version
--
CREATE TABLE  if not exists tb_channel_endpoint_version
(
 id bigint NOT NULL AUTO_INCREMENT,
 trans_code varchar(255) NOT NULL DEFAULT '' COMMENT '交易编码',
 mapping_version varchar(255) NOT NULL DEFAULT '' COMMENT '版本号',
 mapping_detail text NOT NULL COMMENT '详细内容',
 mapping_desc varchar(255) NOT NULL DEFAULT '' COMMENT '版本描述',
 status tinyint NOT NULL DEFAULT '0' COMMENT '状态(0：不启用， 1：启用)',
 creator varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间UTC',
 modifier varchar(50) NOT NULL DEFAULT 'system' COMMENT '修改人',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间UTC',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '是否逻辑删除(N：未删除，Y：已删除)',
 PRIMARY KEY (id),
 UNIQUE KEY INDEX_66da5a401e7349078541b9f831ea59ce (trans_code,mapping_version)
) ;

--
-- Table structure for tb_channel_fee_config
--
CREATE TABLE  if not exists tb_channel_fee_config
(
 id int NOT NULL AUTO_INCREMENT,
 channel_code varchar(32) NOT NULL COMMENT '渠道编码',
 country char(2) NOT NULL COMMENT '国家二字码',
 payment_method varchar(16) NOT NULL COMMENT '一级支付方式',
 payment_sub_method varchar(32) NOT NULL COMMENT '二级支付方式',
 payment_type char(2) NOT NULL COMMENT '支付类型，00-入款；01-出款；02-退款',
 fee_calculate_type tinyint NOT NULL COMMENT '1：单笔定额 2：单笔阶梯定额 3：固定百分比 4：阶梯百分比 5：阶梯百分比+上下限 6：百分比+固定金额+上下限 7：阶梯百分比+固定金额+上下限',
 fee_currency char(3) NOT NULL COMMENT '手续费币种',
 fee_rate varchar(512) NOT NULL COMMENT '手续费计费规则，JSON格式',
 tax_type varchar(16) NOT NULL COMMENT '税费类型，NOTAX，VAT，GST',
 tax_calculate_type tinyint DEFAULT NULL COMMENT '1：单笔定额 2：单笔阶梯定额 3：固定百分比 4：阶梯百分比 5：阶梯百分比+上下限 6：百分比+固定金额+上下限 7：阶梯百分比+固定金额+上下限',
 tax_currency varchar(3) DEFAULT NULL COMMENT '税费币种',
 tax_rate varchar(512) DEFAULT NULL COMMENT '税费计费规则，JSON格式',
 invoice_way char(2) NOT NULL COMMENT '发票提供方式，01-PayerMax;02-渠道方',
 remark varchar(512) DEFAULT NULL COMMENT '备注',
 creator varchar(64) NOT NULL COMMENT '创建者',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间UTC',
 modifier varchar(64) NOT NULL COMMENT '修改者',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间UTC',
 is_deleted char(1) NOT NULL COMMENT '是否逻辑删除(N：未删除，Y：已删除)',
 PRIMARY KEY (id),
 KEY INDEX_fcb16e47cd4b45d1adb53e99ded620a5 (country,payment_method,payment_sub_method,channel_code,payment_type)
) COMMENT='渠道成本配置表';

--
-- Table structure for tb_channel_fee_record
--
CREATE TABLE  if not exists tb_channel_fee_record
(
 id int NOT NULL AUTO_INCREMENT,
 order_no varchar(64) NOT NULL COMMENT '00-入款订单号 01-出款订单号 02-退款订单号',
 payment_type char(2) NOT NULL COMMENT '支付类型，00-入款；01-出款；02-退款',
 fee_amount decimal(15, 0) NOT NULL COMMENT '手续费本币金额',
 fee_currency char(3) NOT NULL COMMENT '手续费外币币种',
 fee_currency_amount decimal(15, 0) NOT NULL COMMENT '手续费外币金额',
 tax_amount decimal(15, 0) NOT NULL DEFAULT '0' COMMENT '税费本币金额',
 tax_currency char(3) NOT NULL DEFAULT 'N' COMMENT '税费外币币种',
 tax_currency_amount decimal(15, 0) NOT NULL COMMENT '税费外币金额',
 tax_type int DEFAULT NULL COMMENT '算费模式 0:不收税NOTAX 1：GST(含税), 2:VAT(Transaction) 3:GMF 先算税后算费 4：VAT(Fee) 同GST 5 : IOF 6: ITF：VAT(Fee) 同GST',
 rate varchar(150) NOT NULL DEFAULT '{}' COMMENT '汇率json',
 creator varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建者',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 modifier varchar(50) NOT NULL DEFAULT 'system' COMMENT '修改者',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '是否逻辑删除(N/Y)',
 PRIMARY KEY (id),
 KEY INDEX_c25ea53ce1ac403487f842c2b56dfb98 (order_no,payment_type)
) COMMENT='渠道成本记录表';

--
-- Table structure for tb_channel_gw_api_def
--
CREATE TABLE  if not exists tb_channel_gw_api_def
(
 id varchar(128) NOT NULL COMMENT '0-0-0 id',
 cluster varchar(64) NOT NULL DEFAULT 'PAYERMAX' COMMENT '0-0-0 集群',
 inst_code varchar(64) NOT NULL COMMENT '4-2-1 机构编码',
 code varchar(512) DEFAULT NULL COMMENT '4-2-1 code',
 name varchar(64) NOT NULL COMMENT '4-2-1 接口名',
 type varchar(64) DEFAULT NULL COMMENT '0-0-0 接口类型',
 scene varchar(64) DEFAULT 'CHANNEL' COMMENT '0-0-0 业务场景',
 version varchar(16) NOT NULL COMMENT '4-2-1 接口版本',
 description mediumtext COMMENT '4-2-1 接口描述',
 direction varchar(8) DEFAULT NULL COMMENT '0-0-0 方向',
 doc_url varchar(1024) DEFAULT NULL COMMENT '4-2-1 接口文档地址',
 api_spec_id varchar(128) NOT NULL COMMENT '0-0-0 接口标准id',
 request_fields json DEFAULT NULL COMMENT '4-2-1 请求字段定义',
 response_fields json DEFAULT NULL COMMENT '4-2-1 响应字段定义',
 field_processors json DEFAULT NULL COMMENT '4-2-1 字段处理',
 field_validators json DEFAULT NULL COMMENT '4-2-1 请求字段校验规则',
 ext_config json DEFAULT NULL COMMENT '4-2-1 扩展配置',
 tags varchar(256) DEFAULT NULL COMMENT '4-2-1 标签',
 status int NOT NULL DEFAULT '1' COMMENT '0-0-0 状态，0：不可用，1：可用',
 creator varchar(64) DEFAULT NULL COMMENT '0-0-0 创建者',
 modifier varchar(64) DEFAULT NULL COMMENT '0-0-0 最后更新人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0 创建时间',
 update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0 更新时间',
 PRIMARY KEY (id),
 UNIQUE KEY INDEX_ba024e78d19a4d638227a421e888797b (inst_code,code,version),
 KEY INDEX_9a65cca1d7824e32abb63911b4b40859 (inst_code)
) COMMENT='接口定义';

--
-- Table structure for tb_channel_gw_api_instance
--
CREATE TABLE  if not exists tb_channel_gw_api_instance
(
 id varchar(128) NOT NULL COMMENT '0-0-0 id',
 cluster varchar(64) NOT NULL DEFAULT 'PAYERMAX' COMMENT '0-0-0 集群',
 inst_code varchar(64) NOT NULL COMMENT '4-2-1 机构编码',
 direction varchar(8) DEFAULT NULL COMMENT '0-0-0 方向',
 api_def_id varchar(128) NOT NULL COMMENT '4-2-1 接口定义 id',
 communication_point_id varchar(128) NOT NULL COMMENT '4-2-1 通信点 id',
 api_spec_id varchar(128) NOT NULL COMMENT '4-2-1 接口标准 id',
 properties json DEFAULT NULL COMMENT '4-2-1 属性',
 status int NOT NULL DEFAULT '1' COMMENT '0-0-0 状态，0：不可用，1：可用',
 creator varchar(64) DEFAULT NULL COMMENT '0-0-0 创建者',
 modifier varchar(64) DEFAULT NULL COMMENT '0-0-0 最后更新人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0 创建时间',
 update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0 更新时间',
 unique_code varchar(256) DEFAULT NULL COMMENT '4-2-1 唯一标识',
 PRIMARY KEY (id),
 UNIQUE KEY INDEX_f0514c0cefb64d9f829611cb49f24590 (api_def_id,communication_point_id),
 KEY INDEX_7056cae5938346d99d3f2b119b6edd52 (inst_code)
) COMMENT='接口实例';

--
-- Table structure for tb_channel_gw_api_mapping
--
CREATE TABLE  if not exists tb_channel_gw_api_mapping
(
 id varchar(128) NOT NULL COMMENT '0-0-0 id',
 cluster varchar(64) NOT NULL DEFAULT 'PAYERMAX' COMMENT '0-0-0 集群',
 inst_code varchar(32) NOT NULL COMMENT '4-2-1 机构编码',
 in_api_def_id varchar(128) NOT NULL COMMENT '0-0-0 入口接口定义 id',
 out_api_def_id varchar(128) NOT NULL COMMENT '0-0-0 出口接口定义 id',
 request_field_mappings json DEFAULT NULL COMMENT '4-2-1 请求字段映射',
 response_field_mappings json DEFAULT NULL COMMENT '4-2-1 响应字段映射',
 status int NOT NULL DEFAULT '1' COMMENT '0-0-0 状态，0：不可用，1：可用',
 creator varchar(64) DEFAULT NULL COMMENT '0-0-0 创建者',
 modifier varchar(64) DEFAULT NULL COMMENT '0-0-0 最后更新人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0 创建时间',
 update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0 更新时间',
 PRIMARY KEY (id),
 UNIQUE KEY INDEX_34a63dc26e3c48639bc9132ece5db78c (in_api_def_id,out_api_def_id),
 KEY INDEX_8164e39ef71945aaaa789ee3d4d2b2c0 (inst_code)
) COMMENT='接口字段映射';

--
-- Table structure for tb_channel_gw_api_router
--
CREATE TABLE  if not exists tb_channel_gw_api_router
(
 id varchar(128) NOT NULL COMMENT '0-0-0 id',
 cluster varchar(64) NOT NULL DEFAULT 'PAYERMAX' COMMENT '0-0-0 集群',
 inst_code varchar(64) NOT NULL COMMENT '0-0-0 机构编码',
 in_api_instance_id varchar(128) NOT NULL COMMENT '0-0-0 入口接口实例 id',
 rules json DEFAULT NULL COMMENT '4-2-1 路由规则',
 status int NOT NULL DEFAULT '1' COMMENT '0-0-0 状态，0：不可用，1：可用',
 description varchar(512) DEFAULT NULL COMMENT '0-0-0 描述',
 creator varchar(64) DEFAULT NULL COMMENT '0-0-0 创建者',
 modifier varchar(64) DEFAULT NULL COMMENT '0-0-0 更新人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0 创建时间',
 update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0 更新时间',
 PRIMARY KEY (id),
 UNIQUE KEY INDEX_ba6ef96cab544b55a81c09d4039c6d28 (inst_code,in_api_instance_id)
) COMMENT='接口路由';

--
-- Table structure for tb_channel_gw_api_spec
--
CREATE TABLE  if not exists tb_channel_gw_api_spec
(
 id varchar(128) NOT NULL COMMENT '0-0-0 id',
 cluster varchar(64) NOT NULL DEFAULT 'PAYERMAX' COMMENT '0-0-0 集群',
 inst_code varchar(64) NOT NULL COMMENT '0-0-0 机构编码',
 name varchar(128) NOT NULL COMMENT '0-0-0 名称',
 direction varchar(4) NOT NULL COMMENT '0-0-0 方向： IN/OUT， IN: 调用渠道网关，OUT: 渠道网关调出去',
 component_config json NOT NULL COMMENT '4-2-1 组件配置',
 common_request_fields json DEFAULT NULL COMMENT '4-2-1 通用的请求字段',
 description varchar(512) DEFAULT NULL COMMENT '4-2-1 描述',
 status int NOT NULL DEFAULT '1' COMMENT '0-0-0 状态，0：不可用，1：可用',
 creator varchar(64) DEFAULT NULL COMMENT '0-0-0 创建人',
 modifier varchar(64) DEFAULT NULL COMMENT '0-0-0 最后更新人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0 创建时间',
 update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0 更新时间',
 PRIMARY KEY (id),
 UNIQUE KEY INDEX_4f5d3bf9f8a540f9a60320ecdf1aa545 (name),
 KEY INDEX_c5d295945d774a42b1c4d273e8efe9c4 (inst_code)
) COMMENT='接口标准';

--
-- Table structure for tb_channel_gw_client
--
CREATE TABLE  if not exists tb_channel_gw_client
(
 id varchar(64) NOT NULL COMMENT '0-0-0 id',
 cluster varchar(64) NOT NULL DEFAULT 'PAYERMAX' COMMENT '0-0-0 集群',
 inst_code varchar(64) DEFAULT NULL COMMENT '0-0-0 技术集成机构',
 client_id varchar(256) NOT NULL COMMENT '0-0-0 客户端 id',
 client_config json DEFAULT NULL COMMENT '4-2-1 配置',
 description varchar(512) DEFAULT NULL COMMENT '0-0-0 描述',
 env varchar(32) DEFAULT NULL COMMENT '0-0-0 环境',
 status int NOT NULL DEFAULT '1' COMMENT '0-0-0 状态，1:有效，0:无效',
 creator varchar(64) DEFAULT NULL COMMENT '0-0-0 创建者',
 modifier varchar(64) DEFAULT NULL COMMENT '0-0-0 更新人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0 创建时间',
 update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0 更新时间',
 PRIMARY KEY (id),
 UNIQUE KEY INDEX_9dc31598f227456da5907b7bf0134f66 (inst_code,client_id),
 KEY INDEX_38c2c3d2e265415191b40bf01cce4a1c (inst_code)
) COMMENT='渠道客户端端';

--
-- Table structure for tb_channel_gw_communication_point
--
CREATE TABLE  if not exists tb_channel_gw_communication_point
(
 id varchar(128) NOT NULL COMMENT '0-0-0 id',
 cluster varchar(64) NOT NULL DEFAULT 'PAYERMAX' COMMENT '0-0-0 集群',
 inst_code varchar(64) NOT NULL COMMENT '0-0-0 机构编码',
 direction varchar(4) NOT NULL COMMENT '0-0-0 方向：IN/OUT，IN: 入口，OUT:出口',
 api_spec_id varchar(128) NOT NULL COMMENT '0-0-0 接口标准 id',
 protocol varchar(16) NOT NULL COMMENT '0-0-0 通信协议',
 path varchar(512) DEFAULT NULL COMMENT '4-2-1 路径，入口使用',
 urls json DEFAULT NULL COMMENT '4-2-1 调用地址，出口使用',
 properties json DEFAULT NULL COMMENT '4-2-1 属性',
 description varchar(512) DEFAULT NULL COMMENT '0-0-0 描述',
 status int NOT NULL DEFAULT '1' COMMENT '0-0-0 状态，0：不可用，1：可用',
 creator varchar(64) DEFAULT NULL COMMENT '0-0-0 创建者',
 modiier varchar(64) DEFAULT NULL COMMENT '0-0-0 更新人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0 创建时间',
 update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0 更新时间',
 PRIMARY KEY (id),
 KEY INDEX_af7269f1c06b45f7a1df1f2622ee9624 (inst_code)
) ;

--
-- Table structure for tb_channel_gw_domain
--
CREATE TABLE  if not exists tb_channel_gw_domain
(
 id varchar(32) NOT NULL COMMENT '0-0-0 id',
 cluster varchar(64) NOT NULL DEFAULT 'PAYERMAX' COMMENT '0-0-0 集群',
 inst_code varchar(64) NOT NULL COMMENT '0-0-0 机构编码',
 host varchar(128) NOT NULL COMMENT '4-2-1 域名',
 port int DEFAULT NULL COMMENT '4-2-1 端口',
 scheme varchar(8) NOT NULL COMMENT '4-2-1 http/https',
 properties json DEFAULT NULL COMMENT '4-2-1 tls 属性',
 proxy_host varchar(128) DEFAULT NULL COMMENT '4-2-1 代理域名',
 proxy_port int DEFAULT NULL COMMENT '4-2-1 代理端口',
 status int DEFAULT '1' COMMENT '0-0-0 状态: 0:不可用，1:可用',
 description varchar(512) DEFAULT NULL COMMENT '4-2-1 描述',
 creator varchar(64) DEFAULT NULL COMMENT '0-0-0 创建者',
 modifier varchar(64) DEFAULT NULL COMMENT '0-0-0 更新人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0 创建时间',
 update_time datetime DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0 更新时间',
 PRIMARY KEY (id),
 UNIQUE KEY INDEX_3337e1f729214481b53b037aa09a6b43 (scheme,host,port)
) COMMENT='域名配置';

--
-- Table structure for tb_channel_gw_inst_info
--
CREATE TABLE  if not exists tb_channel_gw_inst_info
(
 id varchar(32) NOT NULL COMMENT '0-0-0 id',
 cluster varchar(64) NOT NULL DEFAULT 'PAYERMAX' COMMENT '0-0-0 集群',
 code varchar(64) NOT NULL COMMENT '0-0-0 机构编码',
 name varchar(64) NOT NULL COMMENT '4-2-1 机构名称',
 status int NOT NULL DEFAULT '1' COMMENT '0-0-0 状态，0：不可用，1：可用',
 properties json DEFAULT NULL COMMENT '4-2-1 扩展属性',
 creator varchar(64) DEFAULT NULL COMMENT '0-0-0 创建者',
 modifier varchar(64) DEFAULT NULL COMMENT '0-0-0 更新人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0 创建时间',
 update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0 更新时间',
 PRIMARY KEY (id),
 UNIQUE KEY INDEX_2570b4688b614085b671261d6973b801 (code),
 UNIQUE KEY INDEX_b7d86d3140984ea999154e6e1929455c (id)
) COMMENT='机构信息';

--
-- Table structure for tb_channel_item
--
CREATE TABLE  if not exists tb_channel_item
(
 id bigint NOT NULL AUTO_INCREMENT,
 channel_id varchar(64) DEFAULT NULL COMMENT '渠道标识',
 is_differ_merchant char(1) NOT NULL DEFAULT 'N' COMMENT '是否区分商户',
 merchant_id varchar(64) DEFAULT NULL COMMENT '商户号',
 amount decimal(15, 0) NOT NULL COMMENT '付款金额',
 item_code varchar(32) DEFAULT NULL COMMENT '商品code',
 item_name varchar(32) DEFAULT NULL COMMENT '商品name',
 config_json tinytext COMMENT '商品扩展字段',
 creator varchar(50) NOT NULL DEFAULT 'system',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
 modifier varchar(50) NOT NULL DEFAULT 'system',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ,
 PRIMARY KEY (id)
) COMMENT='渠道商品表';

--
-- Table structure for tb_channel_merchant
--
CREATE TABLE  if not exists tb_channel_merchant
(
 id bigint NOT NULL AUTO_INCREMENT,
 merchant_id varchar(64) NOT NULL COMMENT '商户号',
 merchant_name varchar(255) DEFAULT NULL COMMENT '商户名',
 channel_code varchar(64) NOT NULL COMMENT '渠道code',
 product_code varchar(255) DEFAULT '*' COMMENT '支持的产品码列表 * 或用,号分隔',
 country varchar(255) DEFAULT '*' COMMENT '支持的国家列表 * 或用,号分隔',
 method_code varchar(255) DEFAULT '*' COMMENT '支付的一级支付方式 * 或,分隔',
 method_sub_code varchar(255) DEFAULT '*' COMMENT '支付二级支付方式 * 或,号分隔',
 status char(2) NOT NULL COMMENT '状态 1-支持 2-不支持',
 weights smallint DEFAULT '0' COMMENT '权重值',
 desc varchar(255) DEFAULT NULL COMMENT '描述',
 creator varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间UTC',
 modifier varchar(50) NOT NULL DEFAULT 'system' COMMENT '修改人',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间UTC',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '是否逻辑删除(N：未删除，Y：已删除)',
 PRIMARY KEY (id)
) COMMENT='渠道商户关联表';

--
-- Table structure for tb_channel_order_no_mapping
--
CREATE TABLE  if not exists tb_channel_order_no_mapping
(
 id int NOT NULL AUTO_INCREMENT,
 inner_order_no varchar(64) NOT NULL COMMENT '内部订单号',
 outer_order_no varchar(64) NOT NULL COMMENT '外部订单号',
 channel_id char(32) NOT NULL COMMENT '渠道标识',
 inner_order_no_type char(2) NOT NULL COMMENT '内部订单号类型，支付类型，00-入款；01-出款；02-退款',
 outer_order_no_type char(1) NOT NULL COMMENT '外部订单号类型，0-三方单号；1-四方单号',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间UTC',
 PRIMARY KEY (id) ,
 KEY INDEX_64b3fe7d5eec4b53b084daef45dddc3c (outer_order_no,channel_id,inner_order_no_type,outer_order_no_type)
) COMMENT='渠道订单号映射表';

--
-- Table structure for tb_channel_request_mapping
--
CREATE TABLE  if not exists tb_channel_request_mapping
(
 id bigint NOT NULL AUTO_INCREMENT,
 trans_code varchar(255) NOT NULL COMMENT '交易编码',
 field_name varchar(255) NOT NULL COMMENT '字段名称',
 field_path varchar(255) NOT NULL DEFAULT '' COMMENT 'jsonpath',
 field_type varchar(255) NOT NULL COMMENT '字段类型',
 field_parent varchar(255) NOT NULL COMMENT '字段父级',
 field_ext varchar(1024) DEFAULT '' COMMENT '字段拓展',
 field_location varchar(255) NOT NULL COMMENT '字段位置',
 field_default_value varchar(255) DEFAULT '' COMMENT '字段默认值',
 field_desc varchar(255) NOT NULL DEFAULT '' COMMENT '字段描述',
 direction varchar(255) NOT NULL COMMENT '方向',
 mapping_strategy varchar(255) NOT NULL COMMENT '映射策略',
 script_id bigint DEFAULT '0' COMMENT '脚本id',
 script_parameter varchar(255) DEFAULT '' COMMENT '脚本参数',
 origin_field varchar(255) DEFAULT '' COMMENT '原始字段取值脚本',
 origin_field_ext varchar(255) DEFAULT '' COMMENT '原始字段拓展',
 mapping_order tinyint NOT NULL DEFAULT '0' COMMENT '映射顺序',
 is_sign bit(1) NOT NULL DEFAULT '0' COMMENT '是否签名字段',
 creator varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间UTC',
 modifier varchar(50) NOT NULL DEFAULT 'system' COMMENT '修改人',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间UTC',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '是否逻辑删除(N：未删除，Y：已删除)',
 PRIMARY KEY (id),
 KEY INDEX_000f10114be34d0f99349c67bc588fe4 (trans_code)
) ;

--
-- Table structure for tb_channel_resp_code
--
CREATE TABLE  if not exists tb_channel_resp_code
(
 id int NOT NULL AUTO_INCREMENT,
 map_code_group_id varchar(64) NOT NULL COMMENT '映射码分组ID',
 channel_code varchar(64) DEFAULT NULL COMMENT '渠道编码',
 channel_resp_code varchar(64) NOT NULL COMMENT '渠道响应码',
 channel_resp_msg varchar(512) NOT NULL COMMENT '渠道响应描述',
 operate varchar(32) DEFAULT NULL COMMENT '操作',
 operate_status varchar(32) NOT NULL COMMENT '操作状态，to_process-待处理；to_review-待审核；processed-处理完成；',
 status tinyint DEFAULT NULL COMMENT '记录状态，0-无效；1-有效',
 creator varchar(64) DEFAULT NULL COMMENT '创建者',
 create_time datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间，utc',
 modifier varchar(64) DEFAULT NULL COMMENT '修改者',
 modified_time datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间，utc',
 PRIMARY KEY (id),
 KEY INDEX_962eec54f58741f3a4afe95f2b74477b (map_code_group_id,channel_resp_code)
) COMMENT='渠道响应码描述';

--
-- Table structure for tb_channel_response_mapping
--
CREATE TABLE  if not exists tb_channel_response_mapping
(
 id bigint NOT NULL AUTO_INCREMENT,
 trans_code varchar(255) NOT NULL DEFAULT '' COMMENT '交易编码',
 field_name varchar(255) NOT NULL DEFAULT '' COMMENT '字段名称',
 field_type varchar(255) NOT NULL DEFAULT '' COMMENT '字段类型',
 field_parent varchar(255) NOT NULL DEFAULT '' COMMENT '字段父级',
 field_ext varchar(255) DEFAULT '' COMMENT '字段拓展',
 field_location varchar(255) NOT NULL DEFAULT 'BODY' COMMENT '字段位置',
 field_desc varchar(255) NOT NULL DEFAULT '' COMMENT '字段描述',
 direction varchar(255) NOT NULL DEFAULT '' COMMENT '方向：client，server',
 field_transfer varchar(255) DEFAULT '' COMMENT '字段转换',
 mapping_strategy varchar(255) NOT NULL DEFAULT '' COMMENT '映射策略',
 script_id bigint DEFAULT NULL COMMENT '脚本id',
 script_parameter varchar(255) DEFAULT NULL COMMENT '脚本参数',
 origin_field_location varchar(255) NOT NULL COMMENT '原始字段位置',
 origin_field_script varchar(255) NOT NULL DEFAULT '' COMMENT '原始字段取值脚本',
 creator varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间UTC',
 modifier varchar(50) NOT NULL DEFAULT 'system' COMMENT '修改人',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间UTC',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '是否逻辑删除(N：未删除，Y：已删除)',
 PRIMARY KEY (id),
 KEY INDEX_a2ebb2a41297495085b76d44a9df222e (trans_code)
) ;

--
-- Table structure for tb_channel_script
--
CREATE TABLE  if not exists tb_channel_script
(
 id bigint NOT NULL AUTO_INCREMENT,
 script_rule text NOT NULL COMMENT '脚本规则',
 para_names varchar(255) NOT NULL DEFAULT '' COMMENT '参数列表',
 script_type varchar(255) NOT NULL DEFAULT '' COMMENT '脚本类型',
 script_ext varchar(255) DEFAULT '' COMMENT '拓展',
 script_desc varchar(255) NOT NULL DEFAULT '' COMMENT '描述',
 creator varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间UTC',
 modifier varchar(50) NOT NULL DEFAULT 'system' COMMENT '修改人',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间UTC',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '是否逻辑删除(N：未删除，Y：已删除)',
 PRIMARY KEY (id)
) ;

--
-- Table structure for tb_channel_status_mapping
--
CREATE TABLE  if not exists tb_channel_status_mapping
(
 id bigint NOT NULL AUTO_INCREMENT,
 channel_id varchar(64) NOT NULL COMMENT '渠道标识',
 interface_name varchar(128) NOT NULL COMMENT '接口名称',
 type tinyint NOT NULL COMMENT '渠道类型(0:入款,1:出款,2:其他)',
 preconditions mediumtext COMMENT '前置条件',
 external_field varchar(128) NOT NULL COMMENT '外部接口状态字段',
 external_value varchar(128) NOT NULL COMMENT '外部接口状态字段匹配值',
 internal_status tinyint NOT NULL COMMENT '内部订单状态码，0-进行中；1-成功；2-失败；4-退票',
 status tinyint NOT NULL DEFAULT '1' COMMENT '状态，0-有效；1-无效',
 retryable_type tinyint DEFAULT NULL COMMENT '0：无，1：可换单重发',
 creator varchar(50) NOT NULL DEFAULT 'system',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（UTC）',
 modifier varchar(50) NOT NULL DEFAULT 'system',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间（UTC）',
 is_deleted varchar(20) NOT NULL DEFAULT 'N' COMMENT '是否逻辑删除(N：未删除，已删除会记录删除时间)',
 PRIMARY KEY (id) ,
 UNIQUE KEY INDEX_0ad10de4919b4124a23b85e3888c166d (channel_id,interface_name,type,external_field,external_value,internal_status,is_deleted)
) ;

--
-- Table structure for tb_channel_trans_code_mapping
--
CREATE TABLE  if not exists tb_channel_trans_code_mapping
(
 id bigint NOT NULL AUTO_INCREMENT,
 product_code varchar(16) DEFAULT NULL COMMENT '收单产品编码',
 country char(2) DEFAULT NULL COMMENT '国家',
 method_code varchar(16) DEFAULT NULL COMMENT '一级支付方式',
 method_sub_code varchar(32) DEFAULT NULL COMMENT '二级支付方式',
 channel_code varchar(255) DEFAULT NULL COMMENT '渠道编码',
 event_code varchar(255) DEFAULT NULL COMMENT '事件类型(PAYMENT,PAYMENT_INQUIRY,PAYOUT,PAYOUT_INQUIRY)',
 trans_code varchar(255) NOT NULL COMMENT '交易编码',
 creator varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间UTC',
 modifier varchar(50) NOT NULL DEFAULT 'system' COMMENT '修改人',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间UTC',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '是否逻辑删除(N：未删除，Y：已删除)',
 PRIMARY KEY (id) ,
 UNIQUE KEY INDEX_b867d909b8284b709b42893147852dec (product_code,country,method_code,method_sub_code,channel_code,event_code,trans_code),
 KEY INDEX_ff61901bb9ca4ec38a48b27d31413af9 (trans_code)
) ;

--
-- Table structure for tb_common_code_map
--
CREATE TABLE  if not exists tb_common_code_map
(
 id int NOT NULL AUTO_INCREMENT,
 code varchar(64) DEFAULT NULL,
 map_code_group_id varchar(64) DEFAULT NULL COMMENT '映射码分组ID',
 channel_code varchar(64) DEFAULT NULL COMMENT '渠道编码',
 channel_resp_code varchar(64) NOT NULL,
 channel_resp_msg varchar(512) DEFAULT NULL,
 channel_resp_desc varchar(256) DEFAULT NULL,
 is_final_status char(1) NOT NULL DEFAULT 'Y',
 is_alert char(1) DEFAULT 'N' COMMENT '是否告警',
 creator_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
 creator varchar(50) NOT NULL DEFAULT 'system',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ,
 modifier varchar(50) NOT NULL DEFAULT 'system',
 is_deleted char(1) NOT NULL DEFAULT 'N',
 channel_resp_code_category varchar(64) DEFAULT NULL COMMENT '渠道响应码分类',
 match_type varchar(32) DEFAULT 'equal' COMMENT '匹配类型，equal-等值匹配；regular-正则匹配',
 operate_status varchar(32) DEFAULT NULL COMMENT '操作状态，to_process-待处理；to_review-待审核；processed-处理完成；',
 config_json varchar(512) DEFAULT NULL COMMENT '扩展配置',
 PRIMARY KEY (id) ,
 KEY INDEX_26fc41bbee0f46c08f2302aefea875a4 (map_code_group_id,channel_resp_code) ,
 KEY INDEX_cfadd9875a6f4545aa8816dfd55acf0d (channel_code,channel_resp_code)
) ;

--
-- Table structure for tb_common_code_map_uat
--
CREATE TABLE  if not exists tb_common_code_map_uat
(
 id int NOT NULL AUTO_INCREMENT,
 code varchar(64) DEFAULT NULL,
 map_code_group_id varchar(64) DEFAULT NULL COMMENT '映射码分组ID',
 channel_code varchar(64) DEFAULT NULL COMMENT '渠道编码',
 channel_resp_code varchar(64) NOT NULL,
 channel_resp_msg varchar(512) DEFAULT NULL,
 channel_resp_desc varchar(256) DEFAULT NULL,
 is_final_status char(1) NOT NULL DEFAULT 'Y',
 is_alert char(1) DEFAULT 'N' COMMENT '是否告警',
 creator_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
 creator varchar(50) NOT NULL DEFAULT 'system',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ,
 modifier varchar(50) NOT NULL DEFAULT 'system',
 is_deleted char(1) NOT NULL DEFAULT 'N',
 channel_resp_code_category varchar(64) DEFAULT NULL COMMENT '渠道响应码分类',
 match_type varchar(32) DEFAULT 'equal' COMMENT '匹配类型，equal-等值匹配；regular-正则匹配',
 operate_status varchar(32) DEFAULT NULL COMMENT '操作状态，to_process-待处理；to_review-待审核；processed-处理完成；',
 config_json varchar(512) DEFAULT NULL COMMENT '扩展配置',
 PRIMARY KEY (id),
 KEY INDEX_05221cfd89a0485cbd63740895f7794a (channel_code,channel_resp_code),
 KEY INDEX_0e6f6886e82b4063b2cb59742631682e (map_code_group_id,channel_resp_code)
) ;

--
-- Table structure for tb_common_code_map_uat2test
--
CREATE TABLE  if not exists tb_common_code_map_uat2test
(
 id int NOT NULL AUTO_INCREMENT,
 code varchar(64) DEFAULT NULL,
 map_code_group_id varchar(64) DEFAULT NULL COMMENT '映射码分组ID',
 channel_code varchar(64) DEFAULT NULL COMMENT '渠道编码',
 channel_resp_code varchar(64) NOT NULL,
 channel_resp_msg varchar(512) DEFAULT NULL,
 channel_resp_desc varchar(256) DEFAULT NULL,
 is_final_status char(1) NOT NULL DEFAULT 'Y',
 is_alert char(1) DEFAULT 'N' COMMENT '是否告警',
 creator_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
 creator varchar(50) NOT NULL DEFAULT 'system',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ,
 modifier varchar(50) NOT NULL DEFAULT 'system',
 is_deleted char(1) NOT NULL DEFAULT 'N',
 channel_resp_code_category varchar(64) DEFAULT NULL COMMENT '渠道响应码分类',
 match_type varchar(32) DEFAULT 'equal' COMMENT '匹配类型，equal-等值匹配；regular-正则匹配',
 operate_status varchar(32) DEFAULT NULL COMMENT '操作状态，to_process-待处理；to_review-待审核；processed-处理完成；',
 config_json varchar(512) DEFAULT NULL COMMENT '扩展配置',
 PRIMARY KEY (id) ,
 KEY INDEX_63ba8491f6aa4f5b8ffde8cb8a327257 (map_code_group_id,channel_resp_code) ,
 KEY INDEX_9f849a064cef4c3087a09344eecc90ff (channel_code,channel_resp_code)
) ;

--
-- Table structure for tb_common_codes
--
CREATE TABLE  if not exists tb_common_codes
(
 id int NOT NULL AUTO_INCREMENT,
 type smallint NOT NULL COMMENT '响应码类型(0:渠道响应码,1:系统响应码)',
 code varchar(64) NOT NULL,
 message varchar(512) NOT NULL DEFAULT '' COMMENT '4-2-1-响应信息',
 status tinyint NOT NULL DEFAULT '0' COMMENT '响应码状态(0:可用，1:不可用)',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
 creator varchar(50) NOT NULL DEFAULT 'system',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ,
 modifier varchar(50) NOT NULL DEFAULT 'system',
 is_deleted char(1) NOT NULL DEFAULT 'N',
 new_code varchar(64) DEFAULT NULL COMMENT '新响应码',
 new_message varchar(512) DEFAULT NULL COMMENT '新响应信息',
 code_category varchar(64) DEFAULT NULL COMMENT '标准码分类',
 PRIMARY KEY (id) ,
 UNIQUE KEY INDEX_67e873388d30456cacc0bce56df8b524 (code)
) ;

--
-- Table structure for tb_common_codes_uat2test
--
CREATE TABLE  if not exists tb_common_codes_uat2test
(
 id int NOT NULL AUTO_INCREMENT,
 type smallint NOT NULL COMMENT '响应码类型(0:渠道响应码,1:系统响应码)',
 code varchar(64) NOT NULL,
 message varchar(128) NOT NULL,
 status tinyint NOT NULL DEFAULT '0' COMMENT '响应码状态(0:可用，1:不可用)',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
 creator varchar(50) NOT NULL DEFAULT 'system',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ,
 modifier varchar(50) NOT NULL DEFAULT 'system',
 is_deleted char(1) NOT NULL DEFAULT 'N',
 new_code varchar(64) DEFAULT NULL COMMENT '新响应码',
 new_message varchar(512) DEFAULT NULL COMMENT '新响应信息',
 code_category varchar(64) DEFAULT NULL COMMENT '标准码分类',
 PRIMARY KEY (id) ,
 UNIQUE KEY INDEX_4c2ffa1aca5943e2a1a03fbd15069b0e (code)
) ;

--
-- Table structure for tb_common_country
--
CREATE TABLE  if not exists tb_common_country
(
 id int NOT NULL AUTO_INCREMENT,
 country_cn_name varchar(32) DEFAULT NULL,
 country_en_name varchar(64) DEFAULT NULL,
 iso_country_alpha2_code char(2) DEFAULT NULL,
 iso_country_alpha3_code char(3) DEFAULT NULL,
 iso_country_numeric_code char(3) DEFAULT NULL,
 currency_cn_name varchar(64) DEFAULT NULL,
 iso_currency_code char(3) DEFAULT NULL,
 iso_currency_numeric_code char(3) DEFAULT NULL,
 iso_currency_minor_unit char(2) NOT NULL COMMENT '小数位数',
 status tinyint DEFAULT NULL,
 region tinyint DEFAULT NULL,
 time_zone varchar(32) NOT NULL COMMENT '如,CHINA:+08:00/GMT:UTC/INDIA:+05:30/TURKEY:+03:00/South Africa:+02:00/LEBANON:+02:00',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
 creator varchar(50) NOT NULL DEFAULT 'system',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ,
 modifier varchar(50) NOT NULL DEFAULT 'system',
 is_deleted char(1) NOT NULL DEFAULT 'N',
 PRIMARY KEY (id)
) COMMENT='国家代码表';

--
-- Table structure for tb_compensate_channel_config
--
CREATE TABLE  if not exists tb_compensate_channel_config
(
 id bigint NOT NULL AUTO_INCREMENT,
 channel_code varchar(20) NOT NULL DEFAULT '' COMMENT '渠道编码',
 priority tinyint NOT NULL DEFAULT '0' COMMENT '优先级',
 offset_seconds int NOT NULL DEFAULT '0' COMMENT '偏移量',
 interval_seconds int NOT NULL DEFAULT '0' COMMENT '间隔时间，单位：秒',
 max_retries int NOT NULL DEFAULT '0' COMMENT '最大重试次数（0：无限制）',
 max_number_tasks int NOT NULL COMMENT '最大（0：无限制）',
 ext_field varchar(100) NOT NULL DEFAULT '' COMMENT '扩展字段',
 creator varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建者',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 modifier varchar(50) NOT NULL DEFAULT 'system' COMMENT '修改者',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '是否逻辑删除(N/Y)',
 PRIMARY KEY (id),
 UNIQUE KEY INDEX_2523d5ddba344f9491686c1d5f306cff (channel_code)
) COMMENT='渠道单补偿配置表';

--
-- Table structure for tb_compensate_order
--
CREATE TABLE  if not exists tb_compensate_order
(
 id bigint NOT NULL AUTO_INCREMENT,
 channel_order_no varchar(64) NOT NULL DEFAULT '' COMMENT '渠道订单号',
 channel_code varchar(20) NOT NULL DEFAULT '' COMMENT '渠道编码',
 method_type tinyint NOT NULL COMMENT '支付方式类型(0:入款,1:出款)',
 priority tinyint NOT NULL DEFAULT '0' COMMENT '优先级',
 start_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
 retries int NOT NULL DEFAULT '0' COMMENT '重试次数',
 max_retries int NOT NULL DEFAULT '0' COMMENT '最大重试次数（0：无限制）',
 first_executed_time datetime DEFAULT NULL COMMENT '第一次执行时间',
 completed_time datetime DEFAULT NULL COMMENT '完成时间',
 status tinyint NOT NULL DEFAULT '0' COMMENT '(0:待处理，1：处理中，2：处理成功，-1：处理失败)',
 order_desc varchar(100) NOT NULL DEFAULT '' COMMENT '描述',
 ext_field varchar(100) NOT NULL DEFAULT '' COMMENT '扩展字段',
 creator varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建者',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 modifier varchar(50) NOT NULL DEFAULT 'system' COMMENT '修改者',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '是否逻辑删除(N/Y)',
 PRIMARY KEY (id),
 UNIQUE KEY INDEX_4d67cb3ac7404f5d80903b2962d44a4f (channel_order_no) ,
 KEY INDEX_750a9c04371b4e3e95668815cef7540e (channel_code,start_time,status,priority)
) COMMENT='渠道单补偿表';

--
-- Table structure for tb_funding_channel_additional_tax
--
CREATE TABLE  if not exists tb_funding_channel_additional_tax
(
 id bigint NOT NULL AUTO_INCREMENT,
 additional_tax_code varchar(64) NOT NULL COMMENT '渠道税费配置唯一标识',
 tax_type varchar(8) DEFAULT NULL COMMENT '税费类型，1-GST; 2-VAT(Transaction); 3-GMF; 4-VAT(Fee); 5-IOF; 6-ITF; 7-WHT1; 8-WHT2',
 tax_calculate_type tinyint DEFAULT NULL COMMENT '1：单笔定额 2：单笔阶梯定额 3：固定百分比 4：阶梯百分比 5：阶梯百分比+上下限 6：百分比+固定金额+上下限 7：阶梯百分比+固定金额+上下限',
 tax_currency char(3) DEFAULT NULL COMMENT '税费币种',
 tax_rate varchar(1024) NOT NULL COMMENT '税费计费规则，JSON格式',
 status tinyINT NOT NULL COMMENT '状态 1：可用 0：不可用',
 remark varchar(512) DEFAULT NULL COMMENT '备注',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
 creator varchar(64) NOT NULL COMMENT '创建者',
 modifier varchar(64) NOT NULL COMMENT '修改者',
 PRIMARY KEY (id) ,
 UNIQUE KEY INDEX_995450c0fc364bbdac0f890d7f4500a6 (additional_tax_code)
) ;

--
-- Table structure for tb_funding_channel_config_mapping
--
CREATE TABLE  if not exists tb_funding_channel_config_mapping
(
 id bigint NOT NULL AUTO_INCREMENT,
 channel_code varchar(64) NOT NULL COMMENT '渠道编码',
 channel_method_code varchar(128) NOT NULL COMMENT '渠道支付方式编码',
 old_product_code varchar(16) NOT NULL COMMENT '老系统收单产品编码',
 old_method_code varchar(16) NOT NULL COMMENT '老系统一级支付方式编码',
 old_method_sub_code varchar(32) NOT NULL COMMENT '老系统二级支付方式编码',
 old_channel_code varchar(16) NOT NULL COMMENT '老系统渠道编码',
 config_json text COMMENT '扩展配置',
 status tinyINT NOT NULL DEFAULT '1' COMMENT '状态 1：可用 0：不可用',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (id) ,
 UNIQUE KEY INDEX_4d82c8b48a6f45fabb857ed0b6a75793 (channel_method_code) ,
 KEY INDEX_897f69cebcdf45b5b3a1cabb51463a9f (channel_code)
) COMMENT='资金渠道配置映射表';

--
-- Table structure for tb_funding_channel_data_check
--
CREATE TABLE  if not exists tb_funding_channel_data_check
(
 id bigint NOT NULL AUTO_INCREMENT,
 app_id varchar(64) DEFAULT NULL COMMENT '当前系统ID',
 channel_code varchar(64) DEFAULT NULL COMMENT '渠道编码',
 method_code varchar(64) DEFAULT NULL COMMENT '一级支付方式',
 method_sub_code varchar(64) DEFAULT NULL COMMENT '二级支付方式',
 client_app_id varchar(64) DEFAULT NULL COMMENT '调用方系统ID',
 user_id varchar(64) DEFAULT NULL COMMENT '用户ID',
 orig_request_body mediumtext COMMENT '原始请求报文',
 request_body mediumtext COMMENT '请求报文',
 tmp_request_body mediumtext COMMENT '请求报文',
 response_body mediumtext COMMENT '老front响应',
 status tinyINT DEFAULT '1' COMMENT '状态 1：可用 0：不可用',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 country varchar(64) DEFAULT NULL COMMENT '国家',
 currency varchar(64) DEFAULT NULL COMMENT '币种',
 PRIMARY KEY (id),
 KEY INDEX_7c5b64b2b7974f96a61d0346653aea07 (create_time)
) COMMENT='渠道流量复制数据核对表';

--
-- Table structure for tb_funding_channel_event
--
CREATE TABLE  if not exists tb_funding_channel_event
(
 id bigint NOT NULL AUTO_INCREMENT,
 org_code varchar(64) DEFAULT NULL COMMENT '机构code',
 channel_code varchar(64) NOT NULL COMMENT '渠道编码',
 channel_method_code varchar(128) DEFAULT NULL COMMENT '渠道支付方式编码',
 event_type tinyint NOT NULL COMMENT '事件类型 1：临时不可用',
 start_time datetime NOT NULL COMMENT '事件开始时间',
 end_time datetime NOT NULL COMMENT '事件结束时间',
 remark varchar(256) DEFAULT NULL COMMENT '事件说明',
 status tinyINT NOT NULL DEFAULT '1' COMMENT '状态 1:正常 0:已过期',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (id) ,
 KEY INDEX_21259108d3034a949cd649c6e34c8794 (channel_code)
) COMMENT='资金渠道临时事件表';

--
-- Table structure for tb_funding_channel_ext_config
--
CREATE TABLE  if not exists tb_funding_channel_ext_config
(
 id bigint NOT NULL AUTO_INCREMENT,
 org_code varchar(64) DEFAULT NULL COMMENT '机构code',
 entity varchar(32) DEFAULT NULL COMMENT '签约主体',
 channel_code varchar(64) DEFAULT NULL COMMENT '4-1-1-渠道编码，机构维度时配置',
 channel_merchant_code varchar(128) DEFAULT NULL COMMENT '渠道商户标识',
 channel_method_code varchar(128) DEFAULT NULL COMMENT '4-1-1-渠道支付方式编码，机构维度时配置',
 factor_key varchar(64) NOT NULL COMMENT '属性key',
 factor_value varchar(2048) NOT NULL COMMENT '属性value，ALL：所有，NON：没有',
 logic_key varchar(64) NOT NULL COMMENT '操作符',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (id) ,
 KEY INDEX_8df92b91a920468d8dbcba7228daceb8 (channel_method_code)
) COMMENT='资金渠道配置扩展表';

--
-- Table structure for tb_funding_channel_info
--
CREATE TABLE  if not exists tb_funding_channel_info
(
 id bigint NOT NULL AUTO_INCREMENT,
 channel_code varchar(64) NOT NULL COMMENT '渠道编码',
 standard_channel_code varchar(64) DEFAULT NULL COMMENT '标准渠道编码',
 entity varchar(32) DEFAULT NULL COMMENT '签约主体ID，例：P01/T01',
 technical_org varchar(32) DEFAULT NULL COMMENT '技术对接机构',
 funds_settle_org varchar(32) DEFAULT NULL COMMENT '资金结算机构',
 biz_handle_org varchar(32) DEFAULT NULL COMMENT '业务处理机构',
 channel_type varchar(8) DEFAULT NULL COMMENT '渠道类型 I:入款 O:出款 T:资金调拨 F:外汇 R:风控合规',
 interactive_mode tinyint DEFAULT NULL COMMENT '交互模式 0:正常 1:异步 2:人工 3:虚拟 4:线下',
 is_need_report_merchant tinyINT NOT NULL DEFAULT '0' COMMENT '是否需要报备商户 1：是 0：否 2：可报可不报',
 trans_code_map_group_id varchar(64) DEFAULT NULL COMMENT '交易码映射配置组',
 response_code_map_group_id varchar(64) DEFAULT NULL COMMENT '响应码映射配置组',
 config_json text COMMENT '扩展配置',
 status tinyINT NOT NULL DEFAULT '1' COMMENT '状态 1：可用 0：不可用',
 funds_settle_inst_code varchar(64) DEFAULT NULL COMMENT '资金结算机构标识-机构中心',
 biz_handle_inst_code varchar(64) DEFAULT NULL COMMENT '业务处理机构标识-机构中心',
 data_model varchar(15) DEFAULT 'V1' COMMENT '数据模型：V1：老数据模型，V2:新数据模型',
 am_id varchar(64) DEFAULT NULL COMMENT '渠道AM',
 bd_id varchar(64) DEFAULT NULL COMMENT '渠道BD',
 pd_id varchar(64) DEFAULT NULL COMMENT '渠道PD',
 rd_id varchar(64) DEFAULT NULL COMMENT '研发负责人',
 is_support_callback varchar(2) DEFAULT NULL COMMENT '是否支持callback Y：是 N：否',
 is_need_third_no_inquiry varchar(2) DEFAULT NULL COMMENT '补偿查询是否需要三方单号 Y：是 N：否',
 is_support_balance_inquiry varchar(2) DEFAULT NULL COMMENT '是否支持余额查询 Y：是 N：否',
 is_support_bounce varchar(2) DEFAULT NULL COMMENT '是否支持退票 Y：是 N：否',
 issue_notify_type varchar(32) DEFAULT NULL COMMENT '运维通知方式 EMAIL、WEBHOOK',
 issue_notifier varchar(256) DEFAULT NULL COMMENT '运维通知人(地址)',
 issue_notify_tz varchar(8) DEFAULT NULL COMMENT '运维通知时间时区',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (id) ,
 UNIQUE KEY INDEX_7fca0f650a944ff882efa99fad4304e1 (channel_code)
) COMMENT='资金渠道信息表';

--
-- Table structure for tb_funding_channel_limit
--
CREATE TABLE  if not exists tb_funding_channel_limit
(
 id bigint NOT NULL AUTO_INCREMENT,
 channel_code varchar(64) NOT NULL COMMENT '渠道编码',
 channel_entity varchar(64) DEFAULT NULL COMMENT '4-2-1-渠道签约主体',
 channel_merchant_code varchar(64) DEFAULT NULL COMMENT '4-2-1-渠道商户标识',
 config_dimension mediumtext NOT NULL COMMENT '配置维度',
 time_zone varchar(6) NOT NULL DEFAULT '+00:00' COMMENT '时区偏移量',
 limit_date_type varchar(16) NOT NULL COMMENT '限制日期类型(FIX:固定时间,CONTINUED:连续时间)',
 limit_date_start varchar(10) DEFAULT NULL COMMENT '限制起始日期',
 limit_date_end varchar(10) DEFAULT NULL COMMENT '限制结束日期',
 limit_time_window_start varchar(8) DEFAULT NULL COMMENT '限制时间窗起始时间',
 limit_time_window_end varchar(8) DEFAULT NULL COMMENT '限制时间窗结束时间',
 limit_time_window_type varchar(16) DEFAULT NULL COMMENT '限制时间窗类型(FIX_WINDOW:固定,SLIDE_WINDOW:滑动)',
 limit_time_window_length int DEFAULT NULL COMMENT '限制时间窗长度',
 limit_time_window_unit varchar(8) DEFAULT NULL COMMENT '限制时间窗单位(SECONDS:秒，MINUTES:分钟，HOURS:小时，DAYS:天)',
 limit_group_condition varchar(256) DEFAULT NULL COMMENT '限制分组条件',
 transactions_number_limit int DEFAULT NULL COMMENT '交易笔数限制',
 normal_transactions_number_limit int DEFAULT NULL COMMENT '4-2-1正常交易笔数限制',
 retry_transactions_number_limit int DEFAULT NULL COMMENT '4-2-1重发交易笔数限制',
 transactions_currency_limit varchar(3) DEFAULT NULL COMMENT '交易币种限制',
 transactions_amount_limit decimal(20, 4) DEFAULT NULL COMMENT '交易总金额限制',
 hit_strategy varchar(32) NOT NULL COMMENT '命中策略(ORIG_CHANNEL_INIT:原渠道等待重发(不主动路由备用渠道),INIT:等待重发,FAILED:交易失败)',
 response_code varchar(64) DEFAULT NULL COMMENT '错误码',
 response_msg mediumtext COMMENT '错误描述',
 status varchar(1) NOT NULL DEFAULT 'Y' COMMENT '状态，Y-有效；N-无效',
 creator varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建者',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（UTC）',
 modifier varchar(50) NOT NULL DEFAULT 'system' COMMENT '修改者',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间（UTC）',
 is_deleted varchar(20) NOT NULL DEFAULT 'N' COMMENT '是否逻辑删除(N：未删除，已删除会记录删除时间)',
 PRIMARY KEY (id) ,
 KEY INDEX_fb1b3e874f574b27aac7225105feb501 (channel_code)
) COMMENT='渠道限流配置表';

--
-- Table structure for tb_funding_channel_merchant
--
CREATE TABLE  if not exists tb_funding_channel_merchant
(
 code varchar(128) NOT NULL COMMENT '商户标识',
 channel_code varchar(64) DEFAULT NULL COMMENT '4-2-1-渠道编码',
 inst_code varchar(64) DEFAULT NULL COMMENT '4-2-1-机构编码',
 entity varchar(32) NOT NULL COMMENT '签约主体ID，例：P01/T01',
 mid varchar(64) NOT NULL COMMENT '商户号',
 name varchar(256) NOT NULL COMMENT '商户名称',
 account_json text,
 biz_handle_inst_code varchar(64) DEFAULT NULL COMMENT '业务处理机构标识-机构中心',
 funds_settle_inst_code varchar(64) DEFAULT NULL COMMENT '资金结算机构标识-机构中心',
 contract_inst_code_type tinyint DEFAULT NULL COMMENT '0：业务处理机构标识，1：资金清算机构标识',
 status tinyINT NOT NULL DEFAULT '1' COMMENT '状态 1：可用 0：不可用',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 channel_type varchar(16) NOT NULL DEFAULT '' COMMENT '渠道类型 I:入款 O:出款 F:外汇 可使用逗号进行分隔',
 related_merchant_type tinyint DEFAULT NULL,
 related_merchant_detail varchar(256) DEFAULT NULL,
 assign_type tinyint DEFAULT '0' COMMENT '4-1-1-分配类型 0: 不限制 1：白名单 2：优先',
 channel_id varchar(64) DEFAULT NULL COMMENT '4-2-1-渠道报备编码',
 PRIMARY KEY (code),
 KEY INDEX_c416de9825f64d6bba891b52c0ab5e1d (inst_code) ,
 KEY INDEX_d7f116bad5c24e8e9f5207ff21fa6975 (channel_code)
) COMMENT='渠道商户信息表';

--
-- Table structure for tb_funding_channel_merchant_contract
--
CREATE TABLE  if not exists tb_funding_channel_merchant_contract
(
 id bigint NOT NULL AUTO_INCREMENT,
 channel_merchant_code varchar(128) NOT NULL COMMENT '渠道商户标识',
 channel_method_code varchar(128) NOT NULL COMMENT '渠道支付方式编码',
 cost_factor int DEFAULT NULL COMMENT '成本系数',
 success_rate_factor int DEFAULT NULL COMMENT '成功率系数',
 stability_factor int DEFAULT NULL COMMENT '稳定性系数',
 status tinyINT NOT NULL DEFAULT '1' COMMENT '状态 1：可用 0：不可用',
 is_need_report_merchant tinyINT DEFAULT NULL COMMENT '是否需要报备商户 1：是 0：否 2：可报可不报',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 country_currency_pair text COMMENT '国家币种对',
 PRIMARY KEY (id),
 KEY INDEX_890c7e94a275445b9477f79a5924c9fa (channel_merchant_code) ,
 KEY INDEX_840d5c0c661445f2be851c32daa25e19 (channel_method_code)
) COMMENT='渠道商户合约表';

--
-- Table structure for tb_funding_channel_method
--
CREATE TABLE  if not exists tb_funding_channel_method
(
 id bigint NOT NULL AUTO_INCREMENT,
 channel_code varchar(64)   NOT NULL COMMENT '渠道编码',
 channel_method_code varchar(128)   NOT NULL COMMENT '渠道支付方式编码',
 payment_method_type varchar(64)     DEFAULT NULL,
 product_code varchar(64)     DEFAULT NULL COMMENT '标准产品编码',
 new_product_code varchar(64)   DEFAULT NULL COMMENT '新标准产品编码',
 currency varchar(512)     DEFAULT NULL COMMENT '币种（可多个，使用英文逗号分割）',
 country varchar(512)     DEFAULT NULL COMMENT '国家（可多个，使用英文逗号分割）',
 target_org varchar(512)   NOT NULL COMMENT '目标机构/卡组（可多个，使用英文逗号分割）',
 card_org varchar(64)     DEFAULT NULL COMMENT '卡组织',
 data_model varchar(15)    DEFAULT NULL COMMENT '4-1-1-支付方式数据模型',
 payment_tool varchar(16)   DEFAULT NULL COMMENT '支付工具类型 1001:现金  1002:银行卡  1003:电子钱包  1004:手机话费  1005:ATM卡  1006:预付费卡(点券)  1007:会员积分  1008:会员点数',
 customer_type varchar(8)   DEFAULT NULL COMMENT '客户类型 1：ToCustomer  2:ToBusiness',
 payment_flow varchar(16)   DEFAULT NULL COMMENT '支付流程',
 expire_time int DEFAULT NULL COMMENT '关单时间 单位：秒',
 valid_refund_time int DEFAULT NULL COMMENT '退款有效时间 单位：天',
 is_support_refund tinyint(1) DEFAULT NULL COMMENT '是否支持退款 1:支持，0:不支持',
 is_partial_refund tinyint(1) DEFAULT NULL COMMENT '是否支持部分退款 1:支持，0:不支持',
 is_support_cancel tinyint(1) DEFAULT NULL COMMENT '是否支持撤销  1:支持，0:不支持',
 additional_tax_code varchar(64)   DEFAULT NULL COMMENT '渠道税费配置唯一标识',
 funding_cost int DEFAULT NULL COMMENT '成本，相对值',
 config_json text   COMMENT '扩展配置',
 status tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 1：可用 0：不可用',
 is_need_report_merchant tinyint(1) DEFAULT NULL COMMENT '是否需要报备商户 1：是 0：否 2：可报可不报',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
 channel_id varchar(32)   DEFAULT NULL COMMENT '渠道标识',
 country_currency_pair text   COMMENT '国家币种对',
 clear_system varchar(32)     DEFAULT NULL COMMENT '清算体系',
 payment_method_id varchar(64)   DEFAULT NULL COMMENT '标准支付方式ID',
 is_allow_auto_switch varchar(1)  DEFAULT NULL COMMENT '是否允许自动切换(其他渠道异常时)到此渠道支付方式 Y：是 N：否',
 is_support_close_order  tinyint(1) DEFAULT NULL COMMENT '是否支持主动关单  1:支持，0:不支持',
 PRIMARY KEY (id) ,
 UNIQUE KEY INDEX_b3fe1746a12e4337a4c97fb71b3d6bf1 (channel_method_code) ,
 KEY INDEX_b1df6e7737d84e38b2647686e27daab7 (channel_code)
) COMMENT='资金渠道配置表';

CREATE TABLE  if not exists tb_funding_channel_function_config (
  id bigint NOT NULL AUTO_INCREMENT,
  channel_code varchar(64)  DEFAULT NULL COMMENT '4-1-1-渠道编码，机构维度时配置',
  channel_merchant_code varchar(128)  DEFAULT NULL COMMENT '渠道商户标识',
  channel_method_code varchar(128)  DEFAULT NULL COMMENT '4-1-1-渠道支付方式编码，机构维度时配置',
  config text    COMMENT '配置Json',
  create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (id),
  KEY tb_funding_channel_function_config_IDX_CHANNEL_CODE (channel_code),
  KEY tb_funding_channel_function_config_IDX_CHANNEL_METHOD_CODE (channel_method_code),
  KEY tb_funding_channel_function_config_IDX_CHANNEL_MERCHANT_CODE (channel_merchant_code)
) COMMENT='渠道技术配置表';

--
-- Table structure for tb_funding_channel_product_customization_route
--
CREATE TABLE  if not exists tb_funding_channel_product_customization_route
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT '0-0-0-主键',
 name varchar(64) DEFAULT NULL COMMENT '4-2-1-规则名称',
 cashier_product_code varchar(64) NOT NULL COMMENT '4-2-1-收银产品',
 merchants text COMMENT '4-2-1-商户号,多个逗号分隔',
 rule_content text COMMENT '4-2-1-规则配置内容',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '0-0-0-是否逻辑删除: Y-是 N-否',
 creator varchar(64) NOT NULL DEFAULT 'system' COMMENT '0-0-0-创建人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0-创建时间',
 modifier varchar(64) NOT NULL DEFAULT 'system' COMMENT '0-0-0-修改人',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0-更新时间',
 PRIMARY KEY (id),
 KEY INDEX_0399cd597c374d8c8633366fd7f2939d (cashier_product_code)
) COMMENT='产品定制路由规则';

--
-- Table structure for tb_funding_channel_product_gray_route
--
CREATE TABLE  if not exists tb_funding_channel_product_gray_route
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT '0-0-0-主键',
 cashier_product_code varchar(64) NOT NULL COMMENT '4-2-1-收银产品',
 channel_code varchar(64) NOT NULL COMMENT '4-2-1-渠道编码',
 channel_merchant_code varchar(128) NOT NULL COMMENT '4-2-1-渠道商户标识',
 merchants text COMMENT '4-2-1-商户号,多个逗号分隔',
 gray_rate int DEFAULT NULL COMMENT '4-2-1-灰度占比',
 router_condition text COMMENT '4-2-1-路由条件',
 router_convert_condition text COMMENT '4-2-1-路由转换后的条件存储格式',
 remark varchar(512) DEFAULT NULL COMMENT '4-2-1-备注',
 status tinyINT NOT NULL DEFAULT '1' COMMENT '4-2-1-灰度状态开关0：关闭 1：打开 2:完成',
 gray_open_time datetime DEFAULT NULL COMMENT '4-2-1-灰度开启时间',
 gray_complete_time datetime DEFAULT NULL COMMENT '4-2-1-灰度完成时间',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '0-0-0-是否逻辑删除: Y-是 N-否',
 creator varchar(64) NOT NULL DEFAULT 'system' COMMENT '0-0-0-创建人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0-创建时间',
 modifier varchar(64) NOT NULL DEFAULT 'system' COMMENT '0-0-0-修改人',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0-更新时间',
 PRIMARY KEY (id),
 KEY INDEX_e9c473e764e348869f6cc9fa2508bf39 (cashier_product_code)
) COMMENT='收银产品灰度路由规则';

--
-- Table structure for tb_funding_channel_product_info
--
CREATE TABLE  if not exists tb_funding_channel_product_info
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
 product_code varchar(64) NOT NULL COMMENT '产品编码',
 payment_method_type varchar(32) NOT NULL COMMENT '支付方式类型',
 payment_type varchar(8) NOT NULL COMMENT '支付类型 10:出款 20:入款 30:汇兑 50:转账',
 customer_type varchar(8) DEFAULT NULL COMMENT '客户类型 1:toC 2:toB',
 status tinyINT NOT NULL COMMENT '状态 1：可用 0：不可用',
 create_time datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 modified_time datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (id)
) COMMENT='渠道标准产品信息表';

--
-- Table structure for tb_funding_channel_product_route
--
CREATE TABLE  if not exists tb_funding_channel_product_route
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT '0-0-0-主键',
 cashier_product_code varchar(128) NOT NULL COMMENT '3-2-5-收银产品编码',
 currency char(3) DEFAULT NULL COMMENT '4-2-1-币种',
 channel_code varchar(128) NOT NULL COMMENT '4-2-1-渠道编码',
 channel_entity varchar(8) DEFAULT NULL COMMENT '4-2-1-渠道签约主体',
 channel_merchant_code varchar(256) DEFAULT NULL COMMENT '4-2-1-渠道商户编码（MID）',
 result_type varchar(32) DEFAULT NULL COMMENT '路由结果类型 CLEAR_SYSTEM:清算系统',
 result_data varchar(255) DEFAULT NULL COMMENT '路由结果数据',
 priority int DEFAULT NULL COMMENT '0-0-0-优先级，值越小优先级越高',
 weight int DEFAULT NULL COMMENT '0-0-0-权重',
 status int DEFAULT '1' COMMENT '0-0-0-状态 1：可用 0：不可用',
 create_time datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
 update_time datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
 router_status varchar(12) NOT NULL DEFAULT 'open' COMMENT '4-2-1-open-开量 close-关闭 gray-灰度 test-测试',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '0-0-0-是否逻辑删除: Y-是 N-否',
 PRIMARY KEY (id),
 KEY INDEX_0ae54d0575564db0937ea0e233f7b9f9 (cashier_product_code)
) COMMENT='收银产品渠道路由表';

--
-- Table structure for tb_funding_channel_proxy
--
CREATE TABLE  if not exists tb_funding_channel_proxy
(
 id int NOT NULL AUTO_INCREMENT COMMENT '0-0-0-主键',
 channel_code varchar(64) DEFAULT NULL COMMENT '4-2-1-渠道编码',
 target_type varchar(50) NOT NULL COMMENT '4-2-1-目标类型，API/SFTP/MAILBOX',
 target_host varchar(100) NOT NULL COMMENT '4-2-1-目标host',
 proxy_code varchar(50) NOT NULL COMMENT '4-2-1-代理编码',
 weight tinyint NOT NULL COMMENT '0-0-0-权重',
 expire_time datetime DEFAULT NULL COMMENT '0-0-0-过期时间',
 remark varchar(255) DEFAULT NULL COMMENT '4-2-1-备注',
 status tinyint NOT NULL DEFAULT '0' COMMENT '0-0-0-状态，1-可用/0-不可用',
 creator varchar(50) NOT NULL COMMENT '0-0-0-创建人',
 create_time datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
 modifier varchar(50) NOT NULL COMMENT '0-0-0-修改人',
 modified_time datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ,
 PRIMARY KEY (id),
 UNIQUE KEY INDEX_dc88cf92ac56489faec3c7ba607bb5f3 (target_host,proxy_code)
) COMMENT='渠道代理';

--
-- Table structure for tb_funding_channel_retry_config
--
CREATE TABLE  if not exists tb_funding_channel_retry_config
(
 id bigint NOT NULL AUTO_INCREMENT,
 channel_code varchar(64) NOT NULL COMMENT '渠道编码',
 channel_method_code varchar(128) NOT NULL COMMENT '渠道支付方式编码',
 payment_type varchar(8) NOT NULL COMMENT '支付类型 10:出款 20:入款 30:汇兑 50:转账',
 error_code varchar(255) NOT NULL COMMENT '重试错误码',
 error_msg varchar(255) NOT NULL COMMENT '重试错误信息',
 commit_order_status varchar(8) DEFAULT NULL COMMENT '4-2-1-重试提交单状态：2-失败，4-退票',
 request_order_valid_period bigint DEFAULT NULL COMMENT '4-2-1-重试请求单有效期（创建多长时间内允许重试），单位：分',
 retry_condition text COMMENT '0-0-0-重试条件，MVEL表达式',
 retry_channel_code varchar(64) DEFAULT NULL COMMENT '重试渠道',
 status tinyINT NOT NULL COMMENT '状态 1：可用 0：不可用',
 retry_delay_time int DEFAULT NULL COMMENT '重试延迟时间，单位为分钟',
 ignore_token_pay tinyint(1) DEFAULT NULL COMMENT '4-2-1-是否忽略token支付1：是 0：否',
 create_time datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 modified_time datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (id),
 KEY INDEX_f9647164336d4895b6d285b83e2402dc (channel_code,channel_method_code)
) COMMENT='渠道换号重试配置表';

--
-- Table structure for tb_funding_channel_route
--
CREATE TABLE  if not exists tb_funding_channel_route
(
 id bigint NOT NULL AUTO_INCREMENT,
 country char(3) NOT NULL COMMENT '国家',
 currency char(3) NOT NULL COMMENT '币种',
 payment_method_type varchar(32) NOT NULL COMMENT '支付方式类型',
 target_org varchar(64) DEFAULT NULL COMMENT '目标机构',
 card_org varchar(64) DEFAULT NULL COMMENT '卡组织',
 payment_type varchar(8) NOT NULL COMMENT '支付类型 0:入款 1:出款',
 channel_code varchar(64) NOT NULL COMMENT '渠道编码',
 channel_merchant_code varchar(128) DEFAULT NULL COMMENT '渠道商户标识',
 priority int NOT NULL COMMENT '优先级，值越小优先级越高',
 weight int NOT NULL COMMENT '权重',
 result_type varchar(32) COMMENT '路由结果类型 CLEAR_SYSTEM:清算系统',
 result_data varchar(255) COMMENT '路由结果数据',
 status tinyINT DEFAULT '1' COMMENT '状态 1：可用 0：不可用',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 new_payment_method_type varchar(32) DEFAULT NULL,
 PRIMARY KEY (id) ,
 KEY INDEX_567e5250555147e6ac13ab0b6329f0b6 (channel_code) ,
 KEY INDEX_a636844e36af4831849e76c3ea888b8d (country,currency,payment_method_type,target_org)
) COMMENT='资金渠道路由表';

--
-- Table structure for tb_funding_channel_special_route
--
CREATE TABLE  if not exists tb_funding_channel_special_route
(
 id bigint NOT NULL AUTO_INCREMENT,
 country char(2) NOT NULL COMMENT '国家',
 currency char(3) NOT NULL COMMENT '币种',
 payment_method_type varchar(32) NOT NULL COMMENT '支付方式类型',
 new_payment_method_type varchar(32) DEFAULT NULL COMMENT '0-0-0-新支付方式类型',
 target_org varchar(64) DEFAULT NULL COMMENT '目标机构',
 card_org varchar(64) DEFAULT NULL COMMENT '卡组织',
 payment_type varchar(8) NOT NULL COMMENT '支付类型 0:入款 1:出款',
 special_type varchar(32) NOT NULL COMMENT '特殊业务类型 1：商户 2：金额 3：MCC 4：业务身份 5：CardBin 9：组合类型',
 special_data text COMMENT '特殊业务数据',
 condition_editor text COMMENT '4-1-1-条件编辑器生成的内容,用于转MVEL,和页面回显',
 route_type tinyINT NOT NULL COMMENT '路由类型 1：筛选白名单 2：筛选黑名单 3：路由优先名单',
 priority int NOT NULL DEFAULT '5' COMMENT '优先级，值越小优先级越高',
 channel_code varchar(64) NOT NULL COMMENT '渠道编码',
 channel_merchant_code varchar(128) DEFAULT NULL COMMENT '渠道商户标识',
 weight int DEFAULT NULL COMMENT '权重',
 status tinyINT DEFAULT '1' COMMENT '状态 1：可用 0：不可用',
 valid_mode varchar(16) DEFAULT 'PERMANENT' COMMENT '有效期模式 PERMANENT:永久路由 TEMPORARY:临时路由',
 valid_time_zone varchar(8) DEFAULT NULL COMMENT '有效期时区',
 valid_start_time datetime DEFAULT NULL COMMENT '有效期开始时间',
 valid_end_time datetime DEFAULT NULL COMMENT '有效期结束时间',
 reason varchar(128) DEFAULT NULL COMMENT '路由原因',
 remark varchar(512) DEFAULT NULL COMMENT '备注',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 modified_time datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 is_test char(1) NOT NULL DEFAULT 'N' COMMENT '4-1-1-是否测试商户 Y:是 N:否',
 PRIMARY KEY (id) ,
 KEY INDEX_d42d1d011dc94897ad80c2159fd33a1b (channel_code) ,
 KEY INDEX_3acd936d1bcf435989176280ccbfcf21 (country,currency,payment_method_type,target_org)
) COMMENT='资金渠道路由表';

--
-- Table structure for tb_funding_channel_status
--
CREATE TABLE  if not exists tb_funding_channel_status
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
 title varchar(512) DEFAULT NULL COMMENT '0-0-0-标题',
 event_key varchar(64) NOT NULL COMMENT '事件唯一key',
 channel_name varchar(64) DEFAULT NULL COMMENT '渠道名称',
 event_type varchar(32) NOT NULL COMMENT '0-0-0-事件类型 MAINTENANCE:计划维护 ISSUE:紧急故障',
 time_zone varchar(16) DEFAULT NULL COMMENT '时区',
 start_time datetime DEFAULT NULL COMMENT '预计开始时间',
 end_time datetime DEFAULT NULL COMMENT '预计结束时间',
 real_start_time datetime DEFAULT NULL COMMENT '真实开始时间',
 real_end_time datetime DEFAULT NULL COMMENT '真实结束时间',
 has_impact varchar(1) DEFAULT 'Y' COMMENT '是否影响交易 Y：是 N：否',
 event_source varchar(32) DEFAULT NULL COMMENT '事件来源',
 event_notifier varchar(128) DEFAULT NULL COMMENT '事件通知人',
 event_notify_time datetime DEFAULT NULL COMMENT '事件通知时间',
 attachment varchar(512) DEFAULT NULL COMMENT '附件',
 content text COMMENT '事件内容',
 normal_content text COMMENT '0-0-0-去除html字符后的事件内容',
 remark text COMMENT '说明',
 status varchar(16) NOT NULL DEFAULT 'INIT' COMMENT '0-0-0-状态 INIT:初始 UNSUBMIT:待提交 SUBMITED:已提交 IGNORE_REVIEW:已提交，不提交审批 IN_REVIEW:审批中 REVIEW_REJECT:审批拒绝 REVIEW_AGREE:审批通过 NOTIFIED:已通知商户 IGNORE:忽略',
 ignore_reason varchar(128) DEFAULT NULL COMMENT '0-0-0-忽略原因',
 process_id varchar(64) DEFAULT NULL COMMENT '审批流程ID',
 report_event_id varchar(64) DEFAULT NULL COMMENT '通知中心事件ID',
 creator varchar(32) NOT NULL DEFAULT 'SYSTEM' COMMENT '创建人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 add_field1 varchar(32) DEFAULT NULL COMMENT '0-0-0-扩展字段1',
 add_field2 varchar(64) DEFAULT NULL COMMENT '0-0-0-扩展字段2',
 add_field3 varchar(128) DEFAULT NULL COMMENT '0-0-0-扩展字段3',
 PRIMARY KEY (id) ,
 UNIQUE KEY INDEX_bf0de7f93332458a8042bdfc1e9775b1 (event_key) ,
 KEY INDEX_b19cab6c799d43e48457a420166281bb (create_time)
) COMMENT='渠道运维信息表';

--
-- Table structure for tb_funding_channel_status_item
--
CREATE TABLE  if not exists tb_funding_channel_status_item
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
 status_id bigint NOT NULL COMMENT '渠道运维信息ID',
 channel_code varchar(64) DEFAULT NULL COMMENT '渠道编码',
 payment_type varchar(4) DEFAULT NULL COMMENT '支付类型',
 payment_method_type varchar(64) DEFAULT NULL COMMENT '支付方式类型',
 country varchar(128) DEFAULT NULL COMMENT '国家',
 target varchar(128) DEFAULT NULL COMMENT '目标机构/卡组',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (id),
 KEY INDEX_bffa6b8192c24575ba21fbb201a37429 (status_id)
) COMMENT='渠道运维信息项';

--
-- Table structure for tb_funding_channel_step
--
CREATE TABLE  if not exists tb_funding_channel_step
(
 id bigint NOT NULL AUTO_INCREMENT,
 channel_code varchar(64) NOT NULL COMMENT '渠道编码',
 channel_method_code varchar(64) DEFAULT NULL COMMENT '渠道支付方式编码',
 step tinyint DEFAULT NULL COMMENT '请求步骤',
 step_type tinyINT DEFAULT NULL COMMENT '请求步骤类型 1:支付类 2:控制类',
 step_code varchar(64) DEFAULT NULL COMMENT '请求步骤标识',
 ext_result_params text COMMENT '扩展返回数据',
 ext_collect_params text COMMENT '扩展采集数据',
 special_collect_params text COMMENT '特定条件下采集数据',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (id) ,
 KEY INDEX_bcf0b158ba0f4f7fb47cd6aeabbe184f (channel_code)
) COMMENT='资金渠道支付步骤表';

--
-- Table structure for tb_funding_channel_step_api_mapping
--
CREATE TABLE  if not exists tb_funding_channel_step_api_mapping
(
 id bigint NOT NULL AUTO_INCREMENT,
 channel_code varchar(64) NOT NULL COMMENT '渠道编码',
 channel_method_code varchar(64) DEFAULT NULL COMMENT '渠道支付方式编码',
 parent_step_code varchar(64) DEFAULT NULL COMMENT '上游步骤code，对应tb_funding_channel_step.step_code',
 step tinyint DEFAULT NULL COMMENT '请求步骤',
 step_type varchar(8) DEFAULT NULL COMMENT '请求步骤类型 1:支付类 2:控制类 3:风控',
 step_code varchar(64) DEFAULT NULL COMMENT '请求步骤标识',
 trigger_condition text COMMENT '触发条件',
 is_optional tinyINT NOT NULL DEFAULT '0' COMMENT '是否可选步骤 1:是 0:否',
 backend_api_url varchar(256) DEFAULT NULL COMMENT '后端接口地址',
 params_mapping text COMMENT '入参数据映射',
 result_mapping text COMMENT '结果数据映射',
 result_type varchar(512) DEFAULT NULL COMMENT '结果类型',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 api_mode tinyint DEFAULT NULL COMMENT '渠道对接模式',
 execute_strategy tinyINT NOT NULL DEFAULT '0' COMMENT '执行策略(0-每次必执行;1-仅需要执行一次)',
 PRIMARY KEY (id) ,
 KEY INDEX_12b0fc73aa374591866c3d5037ddd5e0 (channel_code)
) COMMENT='资金渠道支付步骤表';

--
-- Table structure for tb_gray_config
--
CREATE TABLE  if not exists tb_gray_config
(
 id bigint NOT NULL AUTO_INCREMENT,
 config_key varchar(32) NOT NULL,
 config_val varchar(64) DEFAULT NULL,
 memo varchar(128) DEFAULT NULL,
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
 update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
 PRIMARY KEY (id)
) COMMENT='灰度配置表';

--
-- Table structure for tb_gray_order_info
--
CREATE TABLE  if not exists tb_gray_order_info
(
 id bigint NOT NULL AUTO_INCREMENT,
 order_no varchar(256) DEFAULT NULL COMMENT '订单号',
 op_sys varchar(64) DEFAULT NULL,
 memo text COMMENT '备注信息',
 create_time datetime DEFAULT CURRENT_TIMESTAMP,
 PRIMARY KEY (id),
 UNIQUE KEY INDEX_5e9ec1cf9046470fa6c95ca8ec10a46a (order_no,op_sys)
) COMMENT='灰度单据记录表';

--
-- Table structure for tb_inst_account
--
CREATE TABLE  if not exists tb_inst_account
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT '账号标识',
 inst_id bigint NOT NULL COMMENT '机构标识',
 requirement_order_id bigint NOT NULL COMMENT '机构集成需求单号',
 type varchar(64) DEFAULT NULL COMMENT '账号类型',
 env varchar(16) DEFAULT NULL COMMENT '账号环境',
 entity varchar(64) DEFAULT NULL COMMENT '我司主体',
 account varchar(64) DEFAULT NULL COMMENT '账号',
 key_type varchar(16) DEFAULT NULL COMMENT '密钥类型',
 key_value text COMMENT '密钥值',
 encrypt_type varchar(16) DEFAULT NULL COMMENT '加密方法',
 is_expire char(1) DEFAULT NULL COMMENT '是否有过期时间;0-否;1-是；',
 expire_time datetime DEFAULT NULL COMMENT '过期时间',
 remark varchar(512) DEFAULT NULL COMMENT '备注',
 status char(1) NOT NULL DEFAULT 'Y' COMMENT '状态 Y:启用 N:停用',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (id) ,
 KEY INDEX_db2b5f4b7640473ca10d9bf08460c1da (inst_id)
) COMMENT='机构账号';

--
-- Table structure for tb_inst_account_key
--
CREATE TABLE  if not exists tb_inst_account_key
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT '账号密钥标识',
 account_id bigint NOT NULL COMMENT '账号id',
 key_type varchar(32) DEFAULT NULL COMMENT '密钥类型',
 key_value text COMMENT '密钥值',
 is_need_encrypt char(1) DEFAULT NULL COMMENT '是否需要加密(0-否;1-是)',
 encrypt_type varchar(16) DEFAULT NULL COMMENT '加密方法',
 remark varchar(512) DEFAULT NULL COMMENT '备注',
 status char(1) NOT NULL DEFAULT 'Y' COMMENT '状态 Y:启用 N:停用',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (id) ,
 KEY INDEX_88cda12de7394326a093b41fd470eea0 (account_id)
) COMMENT='机构账号密钥';

--
-- Table structure for tb_inst_account_number_segment_mapping
--
CREATE TABLE  if not exists tb_inst_account_number_segment_mapping
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
 account_id varchar(64) NOT NULL COMMENT '机构帐号标识',
 number_segment_id varchar(128) NOT NULL COMMENT '机构帐号号段标识',
 status char(1) NOT NULL DEFAULT 'Y' COMMENT '状态：N：不可用，Y：可用',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (id),
 UNIQUE KEY INDEX_147e87ddfeeb4bde9781d0011fe41608 (account_id,number_segment_id),
 KEY INDEX_a349b4a4f0e14527a2d628f1aa823389 (number_segment_id)
) COMMENT='机构账号号段映射关系表';

--
-- Table structure for tb_inst_apply_order
--
CREATE TABLE  if not exists tb_inst_apply_order
(
 apply_no varchar(64) NOT NULL COMMENT '申请单号',
 bd_id varchar(64) NOT NULL COMMENT '申请人(BD)',
 bd_name varchar(64) DEFAULT NULL COMMENT '申请人名称(BD)',
 inst_brand_id bigint NOT NULL COMMENT '机构品牌标识',
 inst_id bigint NOT NULL COMMENT '机构标识',
 apply_type varchar(16) NOT NULL COMMENT '申请类型 NEW：新机构，NEW_ENTITY：新主体，NEW_PRODUCT：已存在机构下的新产品',
 shareit_entity char(3) DEFAULT NULL COMMENT '我司主体',
 cooperation_mode varchar(64) DEFAULT NULL COMMENT '合作模式 MERCHANT/PSP',
 countrys varchar(256) DEFAULT NULL COMMENT '接入国家',
 products text COMMENT '接入产品',
 reason varchar(512) DEFAULT NULL COMMENT '接入价值',
 priority char(2) DEFAULT NULL COMMENT '优先级 P0:高 P1:中 P2:低',
 expect_release_time date DEFAULT NULL COMMENT '期望投产日期',
 am_id varchar(64) DEFAULT NULL COMMENT 'AM',
 am_name varchar(64) DEFAULT NULL COMMENT 'AM名称',
 status varchar(32) NOT NULL COMMENT '状态 INIT:初始态 AUDITING:审核中 AUDIT_AGREE:审批通过 AUDIT_REFUSE:审批拒绝 AUDIT_RETURN:审批驳回 NDA_COMPLETED:NDA完成 DD_OR_KYC_COMPLETED:DD或KYC完成 DD_KYC_COMPLETED:DD&KYC完成 CONTRACT_COMPLETED:合同签署完成 COMPLETED:完成 ABANDON:放弃 ',
 stage_status text COMMENT '阶段状态，JSON格式，用于记录申请单所处阶段及对应状态',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (apply_no) ,
 KEY INDEX_04b35eda413841b19f4cc985f99a4ead (utc_create)
) COMMENT='接入申请单';

--
-- Table structure for tb_inst_associated_company
--
CREATE TABLE  if not exists tb_inst_associated_company
(
 id bigint NOT NULL AUTO_INCREMENT,
 inst_id bigint NOT NULL COMMENT '机构标识',
 register_name varchar(128) DEFAULT NULL COMMENT '公司注册名称',
 entity_country char(2) DEFAULT NULL COMMENT '主体所在地',
 stock_rate decimal(20, 4) DEFAULT NULL COMMENT '持股比例',
 extra_info text COMMENT '扩展字段',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '逻辑删除标识',
 PRIMARY KEY (id) ,
 KEY INDEX_a504d7041c0e411db8e4b9d264449b08 (inst_id)
) COMMENT='机构关联公司';

--
-- Table structure for tb_inst_attach
--
CREATE TABLE  if not exists tb_inst_attach
(
 id bigint NOT NULL AUTO_INCREMENT,
 attach_name varchar(256) DEFAULT NULL COMMENT '附件名称',
 attach_url varchar(256) DEFAULT NULL COMMENT '附件URL',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (id)
) COMMENT='文件附件信息';

--
-- Table structure for tb_inst_audit_data
--
CREATE TABLE  if not exists tb_inst_audit_data
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT '资料标识',
 business_type varchar(16) NOT NULL COMMENT '关联业务类型 KYC：KYC审核，DD：DD审核',
 business_no varchar(64) NOT NULL COMMENT '关联业务单号',
 data_type varchar(32) DEFAULT NULL COMMENT '资料类型',
 data_attach_id varchar(256) DEFAULT NULL COMMENT '资料附件',
 id_number varchar(64) DEFAULT NULL COMMENT '证件号',
 id_validity_date date DEFAULT NULL COMMENT '证件有效期',
 has_watermark_word char(1) DEFAULT NULL COMMENT '是否已有水印 Y：是，N：否',
 remark varchar(512) DEFAULT NULL COMMENT '备注',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (id) ,
 KEY INDEX_9d55009520f4426a8666136b08bca513 (business_type,business_no)
) COMMENT='审核资料清单';

--
-- Table structure for tb_inst_audit_result
--
CREATE TABLE  if not exists tb_inst_audit_result
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT '审核结果单号',
 business_type varchar(16) NOT NULL COMMENT '业务类型',
 business_no varchar(64) NOT NULL COMMENT '业务主键',
 auditor varchar(64) DEFAULT NULL COMMENT '审核人',
 status varchar(32) DEFAULT NULL COMMENT '审核结果 AGREE：同意，REFUSE：拒绝，RETURN：驳回',
 remark varchar(512) DEFAULT NULL COMMENT '审核说明',
 extra_info varchar(512) DEFAULT NULL COMMENT '扩展字段',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (id) ,
 KEY INDEX_16aff0b2c91f4966baead88f2d1ad94b (business_no)
) COMMENT='审核结果单';

--
-- Table structure for tb_inst_bank_account
--
CREATE TABLE  if not exists tb_inst_bank_account
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT '账户标识',
 inst_id bigint NOT NULL COMMENT '机构标识',
 country char(2) DEFAULT NULL COMMENT '地区',
 currency char(3) DEFAULT NULL COMMENT '币种',
 bank_code varchar(64) DEFAULT NULL COMMENT '开户银行',
 bank_name varchar(128) DEFAULT NULL COMMENT '开户银行名称',
 account_name varchar(64) DEFAULT NULL COMMENT '开户名称',
 account varchar(128) DEFAULT NULL COMMENT '废弃字段，意义不明确',
 account_no varchar(128) DEFAULT NULL COMMENT '银行账号',
 branch varchar(64) DEFAULT NULL COMMENT '分支行',
 branch_address varchar(256) DEFAULT NULL COMMENT '分支行地址',
 switf_code varchar(64) DEFAULT NULL COMMENT 'switf code',
 swift_code varchar(128) DEFAULT NULL COMMENT '废弃字段，拼写有误',
 iban varchar(128) DEFAULT NULL COMMENT 'iban',
 account_use varchar(256) DEFAULT NULL COMMENT '账户用途',
 recharge_can_use_ccy varchar(256) DEFAULT NULL COMMENT '我方渠道充值场景下，可充值的币种列表',
 recharge_use_order varchar(16) DEFAULT NULL COMMENT '我方渠道充值场景下，当前账户的优先级',
 remark varchar(512) DEFAULT NULL COMMENT '备注',
 status char(1) NOT NULL DEFAULT 'Y' COMMENT '状态 Y:启用 N:停用',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (id) ,
 KEY INDEX_c9e27933617845f2a7a8968ae7b15857 (inst_id)
) COMMENT='机构银行账户信息';

--
-- Table structure for tb_inst_base_info
--
CREATE TABLE  if not exists tb_inst_base_info
(
 inst_id bigint NOT NULL AUTO_INCREMENT COMMENT '机构标识',
 inst_code varchar(64) DEFAULT NULL COMMENT '机构编码',
 inst_brand_id bigint NOT NULL COMMENT '机构品牌标识',
 inst_name varchar(64) NOT NULL COMMENT '机构名称',
 inst_types varchar(512) DEFAULT NULL COMMENT '机构类型，可多个',
 entity_country char(2) DEFAULT NULL COMMENT '主体所在地',
 is_fatf_member char(1) DEFAULT NULL COMMENT '是否FATF成员国 Y:是 N:否',
 bd_id varchar(64) DEFAULT NULL COMMENT '渠道BD',
 am_id varchar(64) DEFAULT NULL COMMENT '渠道AM',
 pd_id varchar(64) DEFAULT NULL COMMENT '渠道PD',
 remark varchar(512) DEFAULT NULL COMMENT '简介',
 status char(1) NOT NULL DEFAULT 'Y' COMMENT '状态 Y:启用 N:停用',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (inst_id) ,
 UNIQUE KEY INDEX_1ee5d729562c4217bca71220ee74701d (inst_name) ,
 UNIQUE KEY INDEX_5090857967994fde9bfcbaef70d01e4d (inst_code) ,
 KEY INDEX_3832dca21e494dc9ba7b14836f0b22f3 (inst_brand_id)
) COMMENT='机构信息';

--
-- Table structure for tb_inst_biz_agreement
--
CREATE TABLE  if not exists tb_inst_biz_agreement
(
 biz_agreement_no varchar(64) NOT NULL COMMENT '4-2-1-业务协议单号',
 name varchar(128) NOT NULL COMMENT '4-2-1-协议名称',
 type varchar(64) NOT NULL COMMENT '4-2-1-协议类型',
 initiator varchar(64) NOT NULL COMMENT '4-2-1-协议发起方',
 counter varchar(64) NOT NULL COMMENT '4-2-1-协议对手方',
 contract_no varchar(32) DEFAULT NULL COMMENT '4-2-1-机构合约号-外部机构协议时不为空',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-更新时间',
 PRIMARY KEY (biz_agreement_no) ,
 UNIQUE KEY INDEX_8654b7ea60a44e6890e3819569066e45 (initiator,counter,type)
) COMMENT='业务协议';

--
-- Table structure for tb_inst_brand
--
CREATE TABLE  if not exists tb_inst_brand
(
 brand_id bigint NOT NULL AUTO_INCREMENT COMMENT '品牌标识',
 brand_code varchar(64) DEFAULT NULL COMMENT '品牌编码',
 brand_name varchar(64) DEFAULT NULL COMMENT '机构品牌',
 bd_id varchar(64) DEFAULT NULL COMMENT 'BD负责人',
 bd_name varchar(64) DEFAULT NULL COMMENT 'BD负责人名称',
 status char(1) NOT NULL DEFAULT 'Y' COMMENT '状态 Y：启用，N：停用',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (brand_id) ,
 UNIQUE KEY INDEX_eb02c429ca4541ab966ad6ca638d98c7 (brand_name)
) COMMENT='机构品牌';

--
-- Table structure for tb_inst_business_dict
--
CREATE TABLE  if not exists tb_inst_business_dict
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT '机构行业标识',
 business_type varchar(16) NOT NULL COMMENT '业务类型 INST_MCC:机构行业',
 business_no varchar(64) DEFAULT NULL COMMENT '业务主键',
 dict_code varchar(64) NOT NULL COMMENT '字典编码',
 dict_name varchar(64) DEFAULT NULL COMMENT '字典名称',
 extra_info text COMMENT '扩展信息',
 status char(1) NOT NULL DEFAULT 'Y' COMMENT '状态 Y:启用 N:停用',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (id) ,
 KEY INDEX_f5bdb82d280540949744d61610b98219 (business_type,business_no)
) COMMENT='机构业务字典信息';

--
-- Table structure for tb_inst_contact
--
CREATE TABLE  if not exists tb_inst_contact
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT '联系人标识',
 inst_id bigint NOT NULL COMMENT '机构标识',
 name varchar(64) DEFAULT NULL COMMENT '联系人姓名',
 position varchar(16) DEFAULT NULL COMMENT '联系人职能',
 title varchar(16) DEFAULT NULL COMMENT '联系人头衔',
 email varchar(64) DEFAULT NULL COMMENT '联系人邮箱',
 phone_no varchar(64) DEFAULT NULL COMMENT '联系人电话',
 im_no varchar(64) DEFAULT NULL COMMENT '联系人IM',
 remark varchar(512) DEFAULT NULL COMMENT '备注',
 status char(1) NOT NULL DEFAULT 'Y' COMMENT '状态 Y:启用 N:停用',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (id) ,
 KEY INDEX_dc2b8008d8ce43a7bb9c567ed08a03ca (inst_id)
) COMMENT='机构联系人信息';

--
-- Table structure for tb_inst_contract
--
CREATE TABLE  if not exists tb_inst_contract
(
 real_contract_no varchar(64) DEFAULT NULL,
 contract_no varchar(64) NOT NULL COMMENT '合同单号',
 inst_id bigint NOT NULL COMMENT '机构标识',
 apply_no varchar(64) DEFAULT NULL COMMENT '申请单号',
 funds_settle_inst varchar(64) DEFAULT NULL COMMENT '资金处理机构标识',
 service_purchaser varchar(64) DEFAULT NULL COMMENT '甲方(服务采购方)',
 service_provider varchar(64) DEFAULT NULL COMMENT '乙方(服务提供方)',
 start_date date DEFAULT NULL COMMENT '合同开始时间',
 end_date date DEFAULT NULL COMMENT '合同终止时间',
 sign_flag varchar(8) DEFAULT NULL COMMENT '合同签署情况',
 contract_attach_id varchar(16) DEFAULT NULL COMMENT '合同影印件',
 cooperation_mode varchar(64) DEFAULT NULL COMMENT '合作模式 MERCHANT/PSP',
 remark varchar(512) DEFAULT NULL COMMENT '备注',
 status varchar(32) DEFAULT NULL COMMENT '状态',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (contract_no) ,
 KEY INDEX_15d7524faa124f1bbd4e15efaf6494ba (inst_id)
) COMMENT='合同签约单';

--
-- Table structure for tb_inst_contract_draft
--
CREATE TABLE  if not exists tb_inst_contract_draft
(
 draft_id varchar(256) NOT NULL COMMENT '草稿id，唯一主键',
 draft_data longtext NOT NULL COMMENT '草稿数据',
 business_type varchar(32) NOT NULL COMMENT '业务类型',
 action_type varchar(32) NOT NULL COMMENT '行为名称',
 owner varchar(32) NOT NULL COMMENT '所属编辑者',
 inst_code varchar(64) NOT NULL COMMENT '机构编码',
 contract_entity varchar(64) DEFAULT NULL COMMENT '我方主体',
 inst_product_name varchar(512) DEFAULT NULL COMMENT '4-2-1-机构产品名称',
 contract_no varchar(256) DEFAULT NULL COMMENT '合同编号',
 effective_time timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
 operator varchar(512) DEFAULT NULL COMMENT '标准化人员',
 business_key varchar(256) NOT NULL COMMENT '业务key',
 extend_fields json DEFAULT NULL COMMENT '扩展字段',
 status varchar(64) NOT NULL COMMENT '状态，新增init，新增待审核...',
 retry_count int DEFAULT '0' COMMENT '重试次数',
 utc_create timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
 utc_modified timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ,
 UNIQUE KEY INDEX_78699f6a95534a46a7862f1dc39b9c1a (draft_id) ,
 KEY INDEX_15e163fb55a34c649a979e0ca935a798 (draft_id,business_type,business_key,status,utc_create,utc_modified)
) COMMENT='机构合同草稿表';

--
-- Table structure for tb_inst_contract_product
--
CREATE TABLE  if not exists tb_inst_contract_product
(
 contract_no varchar(64) NOT NULL COMMENT '合同单号',
 inst_product_code varchar(64) NOT NULL COMMENT '机构产品编码',
 inst_product_capability_code varchar(64) NOT NULL COMMENT '机构产品能力编码',
 fee_group_id varchar(64) DEFAULT NULL COMMENT '费用组标识',
 version varchar(64) DEFAULT NULL COMMENT '操作版本',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (contract_no, inst_product_code, inst_product_capability_code)
) COMMENT='合同签约产品';

--
-- Table structure for tb_inst_contract_product_settlement
--
CREATE TABLE  if not exists tb_inst_contract_product_settlement
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
 contract_no varchar(64) NOT NULL COMMENT '合同单号',
 inst_product_code varchar(64) NOT NULL COMMENT '机构产品编码',
 inst_product_capability_code varchar(64) NOT NULL COMMENT '机构产品能力编码',
 settlement_info longtext COMMENT '结算信息',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (id) ,
 UNIQUE KEY INDEX_fd6efcaa41f344e1a7d04e7d84bb2cf1 (contract_no,inst_product_code,inst_product_capability_code)
) COMMENT='合同签约产品结算信息';

--
-- Table structure for tb_inst_contract_product_trans_fee
--
CREATE TABLE  if not exists tb_inst_contract_product_trans_fee
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
 contract_no varchar(64) NOT NULL COMMENT '合同单号',
 inst_product_code varchar(64) NOT NULL COMMENT '机构产品编码',
 inst_product_capability_code varchar(64) NOT NULL COMMENT '机构产品能力编码',
 fee_type varchar(16) DEFAULT NULL COMMENT '费用类型',
 mcc varchar(512) NOT NULL COMMENT '行业分类(多个)',
 calculate_type varchar(16) DEFAULT NULL COMMENT '算费方式',
 fee_detail longtext COMMENT '费用详情',
 min_fee decimal(20, 4) DEFAULT NULL COMMENT '保底费用',
 max_fee decimal(20, 4) DEFAULT NULL COMMENT '封顶费用',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (id) ,
 KEY INDEX_91a5f4f6c9454f5d982d3cf387f03d2b (contract_no,inst_product_code,inst_product_capability_code,fee_type)
) COMMENT='合同签约产品交易费用信息';

--
-- Table structure for tb_inst_dd
--
CREATE TABLE  if not exists tb_inst_dd
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT 'DD审核单号',
 inst_id bigint NOT NULL COMMENT '机构标识',
 apply_no varchar(64) DEFAULT NULL COMMENT '申请单号',
 register_name varchar(64) DEFAULT NULL COMMENT '公司注册名称',
 register_no varchar(64) DEFAULT NULL COMMENT '公司注册号',
 register_date date DEFAULT NULL COMMENT '公司注册日期',
 register_address varchar(256) DEFAULT NULL COMMENT '公司注册地址',
 company_scale varchar(32) DEFAULT NULL COMMENT '公司规模',
 validity_date date DEFAULT NULL COMMENT '营业有效期',
 corporate_name varchar(64) DEFAULT NULL COMMENT '法人姓名',
 corporate_birth_date date DEFAULT NULL COMMENT '法人出生日期',
 corporate_address varchar(256) DEFAULT NULL COMMENT '法人证件地址',
 website varchar(256) DEFAULT NULL COMMENT '官网地址',
 business_scope varchar(512) DEFAULT NULL COMMENT '经营范围',
 bank_code varchar(64) DEFAULT NULL COMMENT '开户银行',
 branch_no varchar(64) DEFAULT NULL COMMENT '分支行',
 ifsc varchar(64) DEFAULT NULL COMMENT '国际金融服务中心',
 swift_code varchar(64) DEFAULT NULL COMMENT 'swiftCode',
 clear_system varchar(64) DEFAULT NULL COMMENT '清算体系',
 status varchar(32) NOT NULL COMMENT '状态 INIT:初始态 AUDITING:合规审核中 AUDIT_RETURN:审核驳回 AUDIT_AGREE:审核通过 CONDITIONAL_AGREE:有条件通过 COMPLETED:完成',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 utc_complete datetime DEFAULT NULL COMMENT '完成时间',
 PRIMARY KEY (id) ,
 KEY INDEX_b10058449eab4d589881a81b5752cc96 (inst_id)
) COMMENT='机构DD审核单';

--
-- Table structure for tb_inst_dd_survey
--
CREATE TABLE  if not exists tb_inst_dd_survey
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT '问卷标识',
 dd_id bigint NOT NULL COMMENT 'DD审核单号',
 has_payment_license char(1) DEFAULT NULL COMMENT '是否有支付牌照 Y:是 N:否',
 has_trans_monitor char(1) DEFAULT NULL COMMENT '是否有交易监控 Y:是 N:否',
 has_due_diligence char(1) DEFAULT NULL COMMENT '是否有客户尽职调查 Y:是 N:否',
 has_sanctions_scan char(1) DEFAULT NULL COMMENT '是否有制裁扫描 Y:是 N:否',
 has_pci_certification char(1) DEFAULT NULL COMMENT '是否有PCI认证 Y:是 N:否',
 pci_certification_date date DEFAULT NULL COMMENT 'PCI认证时间',
 survey_attach_id bigint DEFAULT NULL COMMENT '问卷文件',
 remark varchar(512) DEFAULT NULL COMMENT '备注',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (id) ,
 KEY INDEX_7b417433ece642a8b4028cd50aac34d7 (dd_id)
) COMMENT='DD调研问卷';

--
-- Table structure for tb_inst_director_info
--
CREATE TABLE  if not exists tb_inst_director_info
(
 id bigint NOT NULL AUTO_INCREMENT,
 inst_id bigint NOT NULL COMMENT '机构标识',
 user_first_name varchar(256) DEFAULT NULL COMMENT '姓',
 user_middle_name varchar(256) DEFAULT NULL COMMENT '中间名',
 user_last_name varchar(256) DEFAULT NULL COMMENT '尾名',
 nationality char(2) DEFAULT NULL COMMENT '国籍',
 live_country char(2) DEFAULT NULL COMMENT '居住国家',
 cert_type varchar(20) DEFAULT NULL COMMENT '证件类型',
 cert_no varchar(64) DEFAULT NULL COMMENT '证件号',
 birthday date DEFAULT NULL COMMENT '生日',
 expire_date varchar(64) DEFAULT NULL COMMENT '有效期',
 live_address varchar(512) DEFAULT NULL COMMENT '居住地址',
 cert_photo1 varchar(1024) DEFAULT NULL COMMENT '证件照片1',
 cert_photo2 varchar(1024) DEFAULT NULL COMMENT '证件照片2',
 extra_info text COMMENT '扩展字段',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '逻辑删除标识',
 PRIMARY KEY (id) ,
 KEY INDEX_e16ba245900c41019677d8c8de1d6472 (inst_id)
) COMMENT='机构董事信息';

--
-- Table structure for tb_inst_financial_calendar
--
CREATE TABLE  if not exists tb_inst_financial_calendar
(
 calendar_id varchar(64) NOT NULL COMMENT '4-2-1-日历ID',
 calendar_year varchar(32) NOT NULL COMMENT '4-2-1-日历年份',
 calendar_type varchar(64) NOT NULL COMMENT '4-2-1-日历类型，国家/币种/银行(国家&币种)',
 country char(2) DEFAULT NULL COMMENT '4-2-1-国家',
 currency char(3) DEFAULT NULL COMMENT '4-2-1-币种',
 weekend_list varchar(64) NOT NULL DEFAULT 'SATURDAY,SUNDAY' COMMENT '4-2-1-周末列表',
 source_calendar varchar(64) DEFAULT NULL COMMENT '4-2-1-源日历（被引用日历）',
 target_calendar varchar(64) DEFAULT NULL COMMENT '4-2-1-目标日历（引用此日历的日历）',
 status varchar(32) NOT NULL COMMENT '4-2-1-日历状态',
 owner varchar(64) NOT NULL COMMENT '4-2-1-负责人',
 description varchar(512) DEFAULT NULL COMMENT '4-2-1-描述',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-修改时间',
 PRIMARY KEY (calendar_id),
 UNIQUE KEY INDEX_90ef787deb7a4121b7787917ad7ce949 (country,currency,calendar_year,calendar_type)
) COMMENT='机构中心-金融日历-日历';

--
-- Table structure for tb_inst_financial_calendar_holiday
--
CREATE TABLE  if not exists tb_inst_financial_calendar_holiday
(
 holiday_id bigint NOT NULL AUTO_INCREMENT COMMENT '0-0-自增ID',
 calendar_id varchar(64) NOT NULL COMMENT '4-2-1-日历ID',
 holiday_date date NOT NULL COMMENT '4-2-1-节假日日期',
 holiday_month varchar(32) NOT NULL COMMENT '4-2-1-日历月份',
 holiday_operate varchar(32) NOT NULL COMMENT '4-2-1-节假日操作类型，新增/取消',
 is_workday int NOT NULL DEFAULT '0' COMMENT '4-2-1-是否工作日, 1 表示工作日, 0 表示非工作日',
 description varchar(512) DEFAULT NULL COMMENT '4-2-1-描述',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-修改时间',
 PRIMARY KEY (holiday_id),
 UNIQUE KEY INDEX_ff2749b981794ccb8316bdb3ba2f29cc (calendar_id,holiday_date)
) COMMENT='机构中心-金融日历-节假日';

--
-- Table structure for tb_inst_follow_up_record
--
CREATE TABLE  if not exists tb_inst_follow_up_record
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
 inst_id bigint NOT NULL COMMENT '机构标识',
 content varchar(512) NOT NULL COMMENT '内容',
 file_ids varchar(256) DEFAULT NULL COMMENT '文件，可多个',
 operator varchar(64) DEFAULT NULL COMMENT '操作人',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (id)
) COMMENT='机构跟进记录';

--
-- Table structure for tb_inst_funds_account
--
CREATE TABLE  if not exists tb_inst_funds_account
(
 account_id varchar(64) NOT NULL COMMENT '机构帐号标识（机构标识_机构账号_国家_币种）',
 inst_code varchar(32) NOT NULL COMMENT '机构标识',
 entity varchar(8) NOT NULL COMMENT '签约主体',
 account_no varchar(64) NOT NULL COMMENT '机构账号',
 account_type varchar(64) NOT NULL COMMENT '机构账号类型：银行账户：BANK_ACCOUNT、渠道支付账户：CHANNEL_PAY_ACCOUNT',
 account_name varchar(256) DEFAULT NULL COMMENT '4-2-1-机构开户名称',
 use_type varchar(256) NOT NULL COMMENT '机构账号用途：充值：RECHARGE、收款：RECEIVE_PAY、结算：SETTLEMENT、充退：CHARGE_BACK、代发：ISSUED_ON_BEHALF、保证金：SECURITY_DEPOSIT,支持多个使用,分割',
 scenes varchar(256) DEFAULT '' COMMENT '场景：TRADE：B2B贸易、ADS：广告费收款、COD：COD费用收款、LIVE：直播平台分销、ECOM：电商平台收款',
 is_support_sub_account char(1) NOT NULL DEFAULT 'N' COMMENT '是否支持子级账号 N:不支持，1:支持',
 support_sub_account_type varchar(256) DEFAULT NULL COMMENT '0-0-0-支持子级账号类型,VA_RECHARGE：VA充值，VA_RECEIVE_PAY：VA收款',
 is_support_pre_apply char(1) NOT NULL DEFAULT 'N' COMMENT '是否支持预申请 N:不支持，Y：支持',
 sub_account_mode varchar(32) DEFAULT NULL COMMENT '子级账号申请模式：号段模式：NUMBER_SEGMENT，API模式：API，线下模式：OFFLINE',
 sub_account_rule varchar(512) DEFAULT NULL COMMENT '子级账号生成规则，示例：citi${accountNo}${subAccouuntNo}',
 sub_account_limit bigint DEFAULT NULL COMMENT '最大生成子级账号数,空则无限制',
 country char(2) NOT NULL COMMENT '国家',
 currency char(3) NOT NULL COMMENT '币种',
 status char(1) NOT NULL DEFAULT 'N' COMMENT '状态 N：不可用，Y：可用',
 priority int DEFAULT NULL COMMENT '优先级，值越大优先级越高',
 weight int DEFAULT NULL COMMENT '权重',
 is_support_custom_name char(1) NOT NULL DEFAULT 'N' COMMENT '是否支持名称自定义：N：不支持，Y：支持',
 is_need_activation char(1) NOT NULL DEFAULT 'N' COMMENT '是否需要激活：N:不需要，Y:需要',
 activation_mode varchar(32) DEFAULT NULL COMMENT '激活模式 API模式：API，线下模式：OFFLINE',
 is_support_close_account varchar(1) NOT NULL DEFAULT 'N' COMMENT '0-0-0-是否支持关闭机构子级账号：‘N’:不支持，‘Y’:支持',
 close_mode varchar(32) DEFAULT NULL COMMENT '0-0-0-关闭模式 API模式：API，线下模式：OFFLINE',
 inst_mid varchar(64) DEFAULT NULL COMMENT '关联机构MID',
 bank_name varchar(128) DEFAULT NULL COMMENT '银行名称',
 bank_address varchar(255) DEFAULT NULL COMMENT '银行地址',
 account_json text COMMENT '扩展配置',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 account_alias varchar(64) DEFAULT NULL COMMENT '账户别名{我司主体简称}_{机构简称}_{币种}_{账号后4位}',
 account_category varchar(64) DEFAULT NULL COMMENT '账户类别current account /saving account /call account',
 account_nature varchar(64) DEFAULT NULL COMMENT '账户性质(在岸户/离岸户)',
 iban varchar(64) DEFAULT NULL COMMENT '国际银行账号',
 account_opening_time datetime DEFAULT NULL COMMENT '开户时间',
 authorized_officer varchar(256) DEFAULT NULL COMMENT '授权签字人',
 file_list varchar(1024) DEFAULT NULL COMMENT '账户相关文件',
 swift_code varchar(64) DEFAULT NULL COMMENT 'swiftCode',
 memo varchar(1024) DEFAULT NULL COMMENT '备注',
 bank_operator varchar(4096) DEFAULT NULL COMMENT '网银操作人',
 biz_type varchar(64) DEFAULT NULL COMMENT '适用业务',
 is_deleted char(1) DEFAULT 'N' COMMENT 'Y: 已删除, N: 未删除',
 bank_ability varchar(1024) DEFAULT NULL COMMENT '0-0-0 银行能力',
 financial_attribute varchar(256) DEFAULT NULL COMMENT '0-0-0 财务属性',
 bank_address_json json DEFAULT NULL COMMENT '4-2-1-银行地址JSON',
 PRIMARY KEY (account_id),
 UNIQUE KEY INDEX_5001c2b215f2407f87d700fe0d0ad227 (inst_code,account_no,country,currency),
 KEY INDEX_b9de3f6b80e24ef38f8eb411e32d43f8 (status,country,currency,use_type,is_need_activation)
) COMMENT='机构资金账号表';

--
-- Table structure for tb_inst_funds_account_bucket
--
CREATE TABLE  if not exists tb_inst_funds_account_bucket
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
 account_id varchar(64) NOT NULL COMMENT '机构帐号标识',
 buckets_id bigint DEFAULT NULL COMMENT '关联计数桶',
 key_name varchar(64) NOT NULL DEFAULT 'COUNT' COMMENT '键',
 key_value varchar(64) NOT NULL DEFAULT 'COUNT' COMMENT '值',
 total int DEFAULT NULL COMMENT '可使用号码总数',
 status char(1) NOT NULL DEFAULT 'Y' COMMENT '状态：Y:可用，N:不可用',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 PRIMARY KEY (id),
 UNIQUE KEY INDEX_e8bb1db626154b47951d1ac07ab97515 (account_id,key_value,key_name),
 KEY INDEX_56aba9859f5743eab79e637b69bd5067 (buckets_id)
) COMMENT='机构子级资金账号创建参数桶表';

--
-- Table structure for tb_inst_funds_account_ext
--
CREATE TABLE  if not exists tb_inst_funds_account_ext
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
 account_id varchar(64) NOT NULL COMMENT '机构帐号标识（机构标识_机构账号_国家_币种）',
 clear_system varchar(64) DEFAULT NULL COMMENT '清算体系:ACH、CBFT,多个使用,分割',
 ext_key varchar(64) DEFAULT NULL COMMENT '扩展属性key：SwiftCode,IBAN,IFSC,BankCode,BranchNo',
 ext_value varchar(256) DEFAULT NULL COMMENT '扩展属性val',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (id),
 KEY INDEX_aa33a3c1016a4ba5b28fdc43edfdd6e1 (account_id)
) COMMENT='机构资金账号扩展属性表';

--
-- Table structure for tb_inst_funds_account_mid_mapping
--
CREATE TABLE  if not exists tb_inst_funds_account_mid_mapping
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT '0-0-0-主键',
 account_id varchar(64) NOT NULL COMMENT '4-2-1-机构帐号标识（机构标识_机构账号_国家_币种）',
 mid varchar(256) DEFAULT NULL COMMENT '4-2-1-渠道mid',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '4-2-1-创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '4-2-1-更新时间',
 PRIMARY KEY (id),
 KEY INDEX_b3c66961dc1847aba49ab6a8dd87fa0d (account_id)
) COMMENT='账号MID映射关系';

--
-- Table structure for tb_inst_funds_account_prod
--
CREATE TABLE  if not exists tb_inst_funds_account_prod
(
 account_id varchar(64) NOT NULL COMMENT '机构帐号标识（机构标识_机构账号_国家_币种）',
 is_support_direct_link char(1) DEFAULT 'N' COMMENT '是否支持银企直联 N:不支持，Y:支持',
 is_support_realtime_balance char(1) DEFAULT 'N' COMMENT '是否支持实时余额查询 N:不支持，Y:支持',
 is_support_intraday_balance char(1) DEFAULT 'N' COMMENT '是否支持日间余额查询 N:不支持，Y:支持',
 is_support_day_end_balance char(1) DEFAULT 'N' COMMENT '是否支持日终余额查询 N:不支持，Y:支持',
 is_support_direct_payment char(1) DEFAULT 'N' COMMENT '是否支持直联付款 N:不支持，Y:支持',
 day_end_balance_config varchar(4096) DEFAULT NULL COMMENT '日终余额获取配置',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (account_id)
) COMMENT='机构资金账户开立产品表';

--
-- Table structure for tb_inst_funds_agreement
--
CREATE TABLE  if not exists tb_inst_funds_agreement
(
 funds_agreement_no varchar(64) NOT NULL COMMENT '4-2-1-资金协议单号',
 biz_agreement_no varchar(64) NOT NULL COMMENT '4-2-1-业务协议单号',
 name varchar(128) NOT NULL COMMENT '4-2-1-协议名称',
 type varchar(64) NOT NULL COMMENT '4-2-1-协议类型',
 clearing_ccy varchar(3) NOT NULL COMMENT '4-2-1-清算币种',
 clearing_pattern varchar(64) NOT NULL COMMENT '4-2-1-清算模式',
 payment_method varchar(64) DEFAULT NULL COMMENT '4-2-1-支付方式',
 target_org varchar(64) DEFAULT NULL COMMENT '4-2-1-目标机构',
 card_org varchar(64) DEFAULT NULL COMMENT '4-2-1-卡组',
 mid varchar(64) DEFAULT NULL COMMENT '4-2-1-渠道MID',
 status varchar(64) DEFAULT NULL COMMENT '4-2-1-状态-VALID/INVALID',
 memo varchar(1024) DEFAULT NULL COMMENT '4-2-1-备注',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-更新时间',
 PRIMARY KEY (funds_agreement_no) ,
 UNIQUE KEY INDEX_d4c29ffcb62f46409e90c1ed4f919ffc (biz_agreement_no,type,clearing_ccy,clearing_pattern,payment_method,target_org,card_org,mid,status)
) COMMENT='资金协议';

--
-- Table structure for tb_inst_funds_settle_rule
--
CREATE TABLE  if not exists tb_inst_funds_settle_rule
(
 settle_rule_no varchar(64) NOT NULL COMMENT '4-2-1-清算规则编号',
 funds_agreement_no varchar(64) NOT NULL COMMENT '4-2-1-资金协议单号',
 timezone varchar(64) NOT NULL COMMENT '4-2-1-时区-例:UTC+8',
 cutoff_time varchar(64) NOT NULL COMMENT '4-2-1-切点-例:16:00:00',
 clearing_range_start varchar(32) NOT NULL COMMENT '4-2-1-清分范围开始',
 clearing_range_end varchar(32) NOT NULL COMMENT '4-2-1-清分范围结束',
 clearing_pattern varchar(64) NOT NULL COMMENT '4-2-1-清分模式-主动/被动',
 clear_off_pattern varchar(64) NOT NULL COMMENT '4-2-1-清偿模式-主动/被动',
 clear_off_time varchar(64) NOT NULL COMMENT '4-2-1-清偿时间-出账单的规则-D+1',
 clear_off_type varchar(64) NOT NULL COMMENT '4-2-1-清偿类型-外部清偿/内部清偿',
 settle_min_amount decimal(24, 4) NOT NULL COMMENT '4-2-1-起结金额',
 settle_ccy varchar(3) NOT NULL COMMENT '4-2-1-结算币种',
 settle_account varchar(64) NOT NULL COMMENT '4-2-1-结算账户',
 settle_arrived varchar(64) DEFAULT NULL COMMENT '4-2-1-到账时间-D+3WD',
 settle_exchange varchar(64) DEFAULT NULL COMMENT '4-2-1-换汇时间-D+3WD',
 settle_payment varchar(64) DEFAULT NULL COMMENT '4-2-1-打款时间-D+3WD',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-更新时间',
 PRIMARY KEY (settle_rule_no),
 UNIQUE KEY INDEX_8ca36cd8a5814098963377fcd6714f4a (funds_agreement_no,clearing_pattern,clear_off_pattern,clear_off_type,settle_ccy)
) COMMENT='清算规则';

--
-- Table structure for tb_inst_kyc
--
CREATE TABLE  if not exists tb_inst_kyc
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT 'KYC审核单号',
 inst_id bigint NOT NULL COMMENT '机构标识',
 apply_no varchar(64) DEFAULT NULL COMMENT '申请单号',
 watermark_word varchar(64) DEFAULT NULL COMMENT '水印文字',
 survey_attach_id varchar(16) DEFAULT NULL COMMENT '调研问卷文件',
 remark varchar(512) DEFAULT NULL COMMENT '备注',
 status varchar(32) NOT NULL COMMENT '状态 INIT:初始态 DATA_COMPLETED:已提供材料 AUDITING:机构审核中 AUDIT_RETURN:机构审核驳回-待追加材料 AUDIT_AGREE:机构审批通过 COMPLETED:完成',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 utc_complete datetime DEFAULT NULL COMMENT '完成时间',
 PRIMARY KEY (id) ,
 KEY INDEX_feeb7c22c9a142ac87daf8dd659283b4 (inst_id)
) COMMENT='机构KYC审核单';

--
-- Table structure for tb_inst_mcc
--
CREATE TABLE  if not exists tb_inst_mcc
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT '机构行业标识',
 inst_mcc_code varchar(64) NOT NULL COMMENT '机构行业编码',
 inst_mcc_name varchar(64) DEFAULT NULL COMMENT '机构行业名称',
 status char(1) NOT NULL DEFAULT 'Y' COMMENT '状态 Y:启用 N:停用',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (id) ,
 KEY INDEX_60765f522ef64c81b802e7e375a10194 (inst_mcc_code)
) COMMENT='机构行业信息';

--
-- Table structure for tb_inst_nda
--
CREATE TABLE  if not exists tb_inst_nda
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT 'NDA单号',
 inst_id bigint NOT NULL COMMENT '机构标识',
 apply_no varchar(64) DEFAULT NULL COMMENT '申请单号',
 shareit_entity char(3) DEFAULT NULL COMMENT '我司主体',
 version varchar(16) DEFAULT NULL COMMENT 'NDA版本 IN:内版 OUT:外版',
 nda_attach_id varchar(16) DEFAULT NULL COMMENT 'NDA原始文件',
 inst_sign_flag varchar(4) DEFAULT NULL COMMENT '机构是否签署 Y:是 N:否',
 shareit_sign_flag varchar(4) DEFAULT NULL COMMENT '我司是否签署 Y:是 N:否',
 inst_sign_attach_id varchar(16) DEFAULT NULL COMMENT '机构单签文件',
 shareit_sign_attach_id varchar(16) DEFAULT NULL COMMENT '我司单签文件',
 pair_sign_attach_id varchar(16) DEFAULT NULL COMMENT '双签文件',
 remark varchar(512) DEFAULT NULL COMMENT '备注',
 status varchar(32) NOT NULL COMMENT '状态 INIT:初始态 STARTED:已启动沟通 CONFIRMED_VERSION:已确认版本 OUR_SINGED:我司签署完成 INST_SIGNED:机构签署完成 COMPLETED:完成',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 utc_complete datetime DEFAULT NULL COMMENT '完成时间',
 PRIMARY KEY (id) ,
 KEY INDEX_6a1af9f376984ad9b47b2b2f344009cf (inst_id)
) COMMENT='NDA签订单';

--
-- Table structure for tb_inst_new_contract_base_info
--
CREATE TABLE  if not exists tb_inst_new_contract_base_info
(
 contract_no varchar(64) NOT NULL COMMENT '合同号',
 inst_code varchar(128) NOT NULL COMMENT '机构标识',
 contract_entity varchar(64) NOT NULL COMMENT '我方签约主体',
 cooperation_mode char(1) NOT NULL COMMENT '合作模式',
 inst_product_type char(1) NOT NULL COMMENT '机构产品类型，I/O/T',
 status varchar(64) NOT NULL COMMENT '合同状态',
 utc_create_time datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
 utc_modify_time datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ,
 PRIMARY KEY (contract_no)
) COMMENT='合同基本信息';

--
-- Table structure for tb_inst_new_contract_fee_item
--
CREATE TABLE  if not exists tb_inst_new_contract_fee_item
(
 inst_contract_fee_item_no varchar(64) NOT NULL COMMENT '合同费用条款标识',
 inst_origin_product_no varchar(64) NOT NULL COMMENT '机构原始产品码',
 pay_currency char(3) DEFAULT NULL COMMENT '支付币种',
 origin_mid varchar(512) DEFAULT NULL COMMENT '录入的合同原始mid',
 channel_merchant_no varchar(512) DEFAULT NULL COMMENT '我方内部生成的渠道商户号',
 mcc_logic varchar(64) DEFAULT NULL COMMENT 'INCLUDE包含，EXCLUDE不包含',
 origin_mcc varchar(512) DEFAULT NULL COMMENT '录入的合同原始mcc',
 standard_mcc varchar(512) DEFAULT NULL COMMENT '我方内部标准mcc',
 funding_source varchar(64) DEFAULT NULL COMMENT '资金来源',
 fee_config json DEFAULT NULL COMMENT '单笔手续费配置',
 tax_config json DEFAULT NULL COMMENT '税费配置',
 accumulated_fee_config json DEFAULT NULL COMMENT '累计手续费配置',
 fx_spread decimal(24, 4) DEFAULT NULL COMMENT '外汇加点',
 currency_exchange_time varchar(128) DEFAULT NULL COMMENT '换汇时机',
 rounding_scale tinyint DEFAULT NULL COMMENT '保留小数位',
 rounding_mode varchar(256) DEFAULT NULL COMMENT '四舍五入模式',
 utc_create_time datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
 utc_modify_time datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ,
 card_issue_country varchar(32) DEFAULT NULL COMMENT '4-2-1-发卡国家',
 transaction_country varchar(32) DEFAULT NULL COMMENT '4-2-1-交易发生国家',
 accumulation_cycle varchar(64) DEFAULT NULL COMMENT '4-2-1-大阶梯周期范围(月/年)',
 accumulation_type varchar(64) DEFAULT NULL COMMENT '4-2-1-大阶梯累计类型(数量/金额)',
 accumulation_method varchar(64) DEFAULT NULL COMMENT '4-2-1-大阶梯生效时机(当前/下一周期)',
 accumulation_range varchar(64) DEFAULT NULL COMMENT '4-2-1-大阶梯生效范围(全部/部分)',
 accumulation_deduct_time varchar(64) DEFAULT NULL COMMENT '4-2-1-大阶梯扣费时机(实时/汇总)',
 accumulation_join varchar(64) DEFAULT NULL COMMENT '4-2-1-大阶梯是否参与累积',
 accumulation_key varchar(64) DEFAULT NULL COMMENT '4-2-1-大阶梯累积标识',
 accumulation_mode varchar(128) DEFAULT NULL COMMENT '4-1-1 累积模式-key的规则',
 sub_merchant_no varchar(128) DEFAULT NULL COMMENT '二级商户号',
 clear_network varchar(128) DEFAULT NULL COMMENT '4-2-1-清算网络',
 fee_bearer varchar(64) DEFAULT NULL COMMENT '4-2-1-费用承担方',
 customer_type varchar(32) DEFAULT NULL COMMENT '4-2-1-客户类型',
 fee_business_key varchar(768) DEFAULT NULL COMMENT '4-2-1-业务唯一标识',
 PRIMARY KEY (inst_contract_fee_item_no),
 KEY INDEX_7021169995b4497c81ecf947c283dc93 (inst_origin_product_no)
) COMMENT='合同费用条款';

--
-- Table structure for tb_inst_new_contract_mid_mappig
--
CREATE TABLE  if not exists tb_inst_new_contract_mid_mappig
(
 inst_type varchar(64) NOT NULL COMMENT '机构类型',
 inst_code varchar(64) NOT NULL COMMENT '机构编码',
 entity varchar(32) NOT NULL COMMENT '机构签约主体',
 channel_merchant_code varchar(64) NOT NULL COMMENT '我方内部生成的渠道商户号',
 contract_no varchar(64) NOT NULL COMMENT '对应合同号',
 PRIMARY KEY (channel_merchant_code),
 KEY INDEX_08345947bc97444f8b76c1e3992a63e5 (contract_no)
) COMMENT='合同MID映射表';

--
-- Table structure for tb_inst_new_contract_mid_mapping
--
CREATE TABLE  if not exists tb_inst_new_contract_mid_mapping
(
 inst_type varchar(64) NOT NULL COMMENT '机构类型',
 inst_code varchar(64) NOT NULL COMMENT '机构编码',
 entity varchar(32) NOT NULL COMMENT '机构签约主体',
 channel_merchant_code varchar(64) NOT NULL COMMENT '我方内部生成的渠道商户号',
 contract_no varchar(64) DEFAULT NULL COMMENT '对应合同号',
 PRIMARY KEY (channel_merchant_code, inst_type),
 KEY INDEX_edc773d9ee974aab8c3d110e9b3627ae (contract_no)
) COMMENT='合同MID映射表';

--
-- Table structure for tb_inst_new_contract_operate_log
--
CREATE TABLE  if not exists tb_inst_new_contract_operate_log
(
 log_no varchar(64) NOT NULL COMMENT '4-2-1-日志编号',
 business_type varchar(32) NOT NULL COMMENT '4-2-1-业务类型',
 module_name varchar(64) DEFAULT NULL COMMENT '4-2-1-模块名称',
 business_unique_key varchar(64) NOT NULL COMMENT '4-2-1-业务唯一键',
 log_info json DEFAULT NULL COMMENT '4-2-1-json方式记录日志数据',
 operator varchar(64) NOT NULL COMMENT '4-2-1-操作人',
 operate_content varchar(1024) NOT NULL COMMENT '4-2-1-操作说明',
 operate_type varchar(16) NOT NULL COMMENT '4-2-1-新增-ADD/修改-UPDATE/失效-INVALID',
 operate_res varchar(16) NOT NULL COMMENT '4-2-1-操作结果，成功-SUCCESS,失败-FAIL',
 error_code varchar(512) DEFAULT NULL COMMENT '4-2-1-错误码',
 error_msg varchar(1024) DEFAULT NULL COMMENT '4-2-1-错误详细信息',
 operate_time timestamp(6) NOT NULL COMMENT '4-2-1-操作时间',
 utc_create_time datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
 utc_modify_time datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ,
 PRIMARY KEY (log_no),
 KEY INDEX_be212f47aa66400b8a628b8a7d8785e7 (business_type,module_name,business_unique_key)
) COMMENT='操作日志（用于记录过程数据）';

--
-- Table structure for tb_inst_new_contract_origin_product
--
CREATE TABLE  if not exists tb_inst_new_contract_origin_product
(
 inst_origin_product_no varchar(64) NOT NULL COMMENT '机构原始产品码',
 contract_no varchar(64) NOT NULL COMMENT '合同号',
 contract_version varchar(128) NOT NULL COMMENT '合同版本号',
 inst_product_name varchar(512) NOT NULL COMMENT '机构产品名称',
 utc_create_time datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
 utc_modify_time datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ,
 PRIMARY KEY (inst_origin_product_no),
 UNIQUE KEY INDEX_3286373235d1428b89066029cfd40bf3 (contract_no,contract_version,inst_product_name)
) COMMENT='机构原始产品';

--
-- Table structure for tb_inst_new_contract_settlement_item
--
CREATE TABLE  if not exists tb_inst_new_contract_settlement_item
(
 inst_contract_settlement_item_no varchar(64) NOT NULL COMMENT '合同结算条款标识',
 inst_origin_product_no varchar(64) NOT NULL COMMENT '机构原始产品码',
 pay_currency char(3) DEFAULT NULL COMMENT '支付币种',
 origin_mid varchar(512) DEFAULT '' COMMENT '4-2-1-录入的合同原始mid',
 channel_merchant_no varchar(512) DEFAULT NULL COMMENT '我方内部生成的渠道商户号',
 mcc_logic varchar(64) DEFAULT NULL COMMENT 'INCLUDE包含，EXCLUDE不包含',
 origin_mcc varchar(512) DEFAULT NULL COMMENT '录入的合同原始mcc',
 standard_mcc varchar(512) DEFAULT NULL COMMENT '我方内部标准mcc',
 settle_currency char(3) DEFAULT NULL COMMENT '结算币种',
 settle_fee_config json DEFAULT NULL COMMENT '结算手续费配置',
 settle_date_config json DEFAULT NULL COMMENT '结算周期配置',
 settle_payment_config json DEFAULT NULL COMMENT '结算打款配置',
 utc_create_time datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
 utc_modify_time datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ,
 PRIMARY KEY (inst_contract_settlement_item_no),
 UNIQUE KEY INDEX_b8ffbeb2c1f34922ae1b638c8844147a (inst_origin_product_no,pay_currency,channel_merchant_no,settle_currency),
 KEY INDEX_0ee3c972485741a0a90f5c418ac5c70f (inst_origin_product_no)
) COMMENT='合同结算条款';

--
-- Table structure for tb_inst_new_contract_standard_product
--
CREATE TABLE  if not exists tb_inst_new_contract_standard_product
(
 inst_standard_product_no varchar(64) NOT NULL COMMENT '机构标准产品码',
 inst_origin_product_no varchar(64) NOT NULL COMMENT '机构原始产品码',
 contract_no varchar(64) NOT NULL COMMENT '合同号',
 contract_version varchar(128) NOT NULL COMMENT '合同版本号',
 payment_method_type varchar(128) NOT NULL COMMENT '支付方式类型',
 target_org varchar(128) NOT NULL COMMENT '目标机构',
 card_org varchar(128) DEFAULT NULL COMMENT '卡组',
 utc_create_time datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
 utc_modify_time datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ,
 PRIMARY KEY (inst_standard_product_no),
 UNIQUE KEY INDEX_66a26abef5c04057bdfff8a280d6c786 (contract_no,contract_version,payment_method_type,target_org,card_org)
) COMMENT='机构标准化产品';

--
-- Table structure for tb_inst_new_contract_version_info
--
CREATE TABLE  if not exists tb_inst_new_contract_version_info
(
 contract_no varchar(64) NOT NULL COMMENT '合同号',
 contract_version varchar(128) NOT NULL COMMENT '合同版本号',
 group_contract_nos varchar(512) DEFAULT NULL COMMENT '集团分配合同号列表',
 contract_attach_id varchar(512) DEFAULT NULL COMMENT '影印件id',
 effect_start_time datetime(6) NOT NULL COMMENT '版本开始生效时间',
 effect_end_time datetime(6) DEFAULT NULL COMMENT '版本结束生效时间',
 status varchar(64) NOT NULL COMMENT '当前版本状态',
 utc_create_time datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
 utc_modify_time datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ,
 PRIMARY KEY (contract_no, contract_version)
) COMMENT='合同版本相关信息';

--
-- Table structure for tb_inst_process_dock
--
CREATE TABLE  if not exists tb_inst_process_dock
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT '流程对接ID',
 process_id varchar(64) DEFAULT NULL COMMENT '流程ID',
 business_type varchar(16) DEFAULT NULL COMMENT '业务类型',
 business_id varchar(64) DEFAULT NULL COMMENT '业务ID',
 process_status varchar(16) NOT NULL DEFAULT 'PROCESSING' COMMENT 'PROCESSING：审批中，PASS：通过，REJECT：拒绝，REVOKE：撤回，STOP：终止',
 form_content json DEFAULT NULL COMMENT '表单数据',
 original_form_content json DEFAULT NULL COMMENT '原始表单数据',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 create_user varchar(64) NOT NULL DEFAULT 'INST_ADMIN' COMMENT '创建人',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 update_user varchar(64) NOT NULL DEFAULT 'INST_ADMIN' COMMENT '修改人',
 PRIMARY KEY (id) ,
 UNIQUE KEY INDEX_5d1b31ab926741b4ab5375728bf049d1 (process_id),
 KEY INDEX_0ebb0803bf0741d481ac5bcf99effcf0 (business_type,business_id)
) COMMENT='机构流程对接表';

--
-- Table structure for tb_inst_product
--
CREATE TABLE  if not exists tb_inst_product
(
 product_code varchar(64) NOT NULL COMMENT '产品编码',
 inst_id bigint NOT NULL COMMENT '机构标识',
 product_name varchar(64) DEFAULT NULL COMMENT '产品名称',
 channel_type varchar(8) DEFAULT NULL COMMENT '渠道类型',
 main_payment_type varchar(16) DEFAULT NULL COMMENT '支付方式大类：APM;CARD;VA',
 payment_method_type varchar(64) DEFAULT NULL COMMENT '支付方式类型',
 payment_tool varchar(16) DEFAULT NULL COMMENT '支付工具类型 1001:现金 1002:银行卡 1003:电子钱包 1004:手机话费 1005:ATM卡 1006:预付费卡(点券) 1007:会员积分 1008:会员点数',
 customer_type char(3) DEFAULT NULL COMMENT '客户类型',
 is_limit_mcc char(1) DEFAULT NULL COMMENT '是否行业限制 Y:是 N:否',
 mcc_detail text COMMENT '行业限制',
 is_divide_mid varchar(32) DEFAULT NULL COMMENT '是否分开商户号',
 extra_info text COMMENT '扩展属性',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (product_code) ,
 KEY INDEX_134c5222aa144dc8a782922af0f990dc (inst_id)
) COMMENT='机构产品';

--
-- Table structure for tb_inst_product_account
--
CREATE TABLE  if not exists tb_inst_product_account
(
 product_capability_code varchar(64) NOT NULL COMMENT '机构产品能力编码',
 account_id bigint NOT NULL COMMENT '机构账号标识',
 requirement_order_id bigint NOT NULL COMMENT '机构集成需求单号',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (product_capability_code, account_id)
) COMMENT='机构产品账号映射';

--
-- Table structure for tb_inst_product_capability
--
CREATE TABLE  if not exists tb_inst_product_capability
(
 capability_code varchar(64) NOT NULL COMMENT '产品能力编码',
 inst_product_code varchar(64) NOT NULL COMMENT '机构产品编码',
 target_org varchar(64) DEFAULT NULL COMMENT '目标机构',
 card_org varchar(64) DEFAULT NULL COMMENT '卡组织',
 country char(2) DEFAULT NULL COMMENT '国家',
 currency char(3) DEFAULT NULL COMMENT '币种',
 customer_type varchar(8) DEFAULT NULL COMMENT '客户类型 1：ToCustomer 2:ToBusiness',
 is_limit_mcc char(1) DEFAULT NULL COMMENT '是否行业限制:0-否;1-是',
 mcc_detail varchar(64) DEFAULT NULL COMMENT '行业限制',
 support_card_type varchar(32) DEFAULT NULL COMMENT '支持的卡类型:CC:Credit Card;DC:Debit Card',
 is_support_direct_pay char(1) DEFAULT NULL COMMENT '是否支持直接支付',
 is_support_pre_auth char(1) DEFAULT NULL COMMENT '是否支持预授权',
 is_support_3ds char(1) DEFAULT NULL COMMENT '是否支持3DS',
 is_support_refund char(1) DEFAULT NULL COMMENT '是否支持退款：0-不支持退款;1-仅支持全额退款;2-支持全额&部分退款',
 is_support_cancel char(1) DEFAULT NULL COMMENT '是否支持退款：0-不支持退款;1-仅支持全额退款;2-支持全额&部分退款',
 is_limit_amount char(1) DEFAULT NULL COMMENT '是否限额：0-无;1-区间限额;2-指定金额;3-指定金额的倍数',
 limit_amount_info varchar(256) DEFAULT NULL COMMENT '限额信息',
 valid_refund_time varchar(16) DEFAULT NULL COMMENT '退款有效时间',
 expire_time varchar(16) DEFAULT NULL COMMENT '关单时间',
 terminal_type varchar(32) DEFAULT NULL COMMENT '支持的设备终端:WEB;APP;WAP',
 payment_scene_terminal varchar(64) DEFAULT NULL COMMENT '付款场景终端:N-无;OFFLINE-线下便利店;ATM;BANK-银行柜台',
 operating_system varchar(32) DEFAULT NULL COMMENT '设备操作系统:ANDROID;IOS',
 mid varchar(32) DEFAULT NULL COMMENT 'MID',
 pay_collection_info text COMMENT '收集信息',
 receive_time char(1) DEFAULT NULL COMMENT '出款-到账时间:0-准实时;1-当天;2-其他',
 receive_time_value varchar(16) DEFAULT NULL COMMENT '到账时间为其他时，具体到账时间',
 receive_time_unit varchar(16) DEFAULT NULL COMMENT '到账时间为其他时，到账时间单位',
 version varchar(64) DEFAULT NULL,
 amount_multiple_limit varchar(64) DEFAULT NULL,
 amount_single_limit varchar(256) DEFAULT NULL COMMENT '单笔限额',
 amount_day_limit varchar(64) DEFAULT NULL COMMENT '日累计限额',
 amount_month_limit varchar(64) DEFAULT NULL COMMENT '月累计限额',
 payment_tool varchar(16) DEFAULT NULL COMMENT '支付工具',
 payment_flow varchar(16) DEFAULT NULL COMMENT '支付流程',
 available_time varchar(128) DEFAULT NULL COMMENT '服务时间段',
 settlement_extra_info longtext COMMENT '结算扩展属性',
 extra_info longtext COMMENT '扩展属性',
 ext_field varchar(64) DEFAULT NULL COMMENT '扩展字段',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (capability_code) ,
 KEY INDEX_8ba7a90d6d7847c08e602d02feb361af (inst_product_code)
) COMMENT='机构产品能力';

--
-- Table structure for tb_inst_product_fee
--
CREATE TABLE  if not exists tb_inst_product_fee
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT '费用配置标识',
 contract_no varchar(64) NOT NULL COMMENT '合同单号',
 inst_product_code varchar(64) NOT NULL COMMENT '机构产品编码',
 inst_product_capability_code varchar(64) NOT NULL COMMENT '机构产品能力编码',
 is_need_exchange char(1) DEFAULT NULL COMMENT '是否需要换汇',
 base_rate varchar(64) DEFAULT NULL COMMENT '汇率基准',
 rate_time_type varchar(16) DEFAULT NULL COMMENT '汇率时间类型',
 rate_time varchar(64) DEFAULT NULL COMMENT '汇率时间',
 base_rate_source varchar(64) DEFAULT NULL COMMENT '基准来源',
 is_use_ndf char(1) DEFAULT NULL COMMENT '是否使用NDF Y:是 N:否',
 ndf_currency varchar(256) DEFAULT NULL COMMENT 'NDF币种',
 has_access_fee char(1) DEFAULT NULL COMMENT '是否有接入费 Y:是 N:否',
 access_fee_detail varchar(512) DEFAULT NULL COMMENT '接入费详情',
 is_charge_afterward char(1) DEFAULT NULL COMMENT '是否费用后收 Y:是 N:否',
 charge_afterward_detail varchar(512) DEFAULT NULL COMMENT '后收费详情',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (id) ,
 UNIQUE KEY INDEX_933fc7b80ad643ed98581b2f8c626a81 (contract_no,inst_product_code,inst_product_capability_code)
) COMMENT='费用配置';

--
-- Table structure for tb_inst_reconcile
--
CREATE TABLE  if not exists tb_inst_reconcile
(
 id bigint NOT NULL AUTO_INCREMENT,
 requirement_order_id bigint DEFAULT NULL COMMENT '机构集成需求单号',
 inst_id bigint NOT NULL COMMENT '机构标识',
 channel_type varchar(64) DEFAULT NULL COMMENT '渠道类型',
 invoice_provider varchar(16) DEFAULT NULL COMMENT '发票提供方 INST:机构,OUR:我司',
 reconcile_template varchar(256) DEFAULT NULL COMMENT '对账模板',
 reconcile_method varchar(16) DEFAULT NULL COMMENT '对账方式 API:接口,EMAIL:邮件,FTP:文件服务器',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (id) ,
 KEY INDEX_3fe2dc62b02c4716b850f3f170d9fa96 (requirement_order_id)
) COMMENT='机构对账信息';

--
-- Table structure for tb_inst_report_channel
--
CREATE TABLE  if not exists tb_inst_report_channel
(
 id bigint NOT NULL AUTO_INCREMENT,
 channel_id varchar(64) NOT NULL COMMENT '4-2-1-渠道报备编码',
 channel_report_code varchar(64) NOT NULL COMMENT '4-2-1-渠道报备编码',
 target_channel_id varchar(32) DEFAULT NULL COMMENT '4-2-1-二级商户的渠道ID',
 payment_type varchar(8) NOT NULL COMMENT '4-2-1-渠道类型 I:入款 O:出款 T:资金调拨 F:外汇 R:风控合规',
 country char(2) NOT NULL COMMENT '4-2-1-国家',
 currency char(3) NOT NULL COMMENT '4-2-1-币种',
 payment_method_type varchar(32) NOT NULL COMMENT '4-2-1-支付方式类型',
 target_org varchar(64) NOT NULL COMMENT '4-2-1-目标机构(卡组)',
 entity varchar(32) NOT NULL COMMENT '4-2-1-签约主体:P01, P03 ...',
 channel_code varchar(64) DEFAULT NULL COMMENT '4-2-1-渠道编码',
 status tinyINT NOT NULL DEFAULT '0' COMMENT '状态：启用1，停用0',
 creator varchar(32) NOT NULL DEFAULT 'system' COMMENT '创建人',
 modifier varchar(32) NOT NULL DEFAULT 'system' COMMENT '修改人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
 is_deleted tinyINT NOT NULL DEFAULT '0' COMMENT '0-0-0-是否删除(0:否、1:是)',
 PRIMARY KEY (id)
) COMMENT='报备支付方式';

--
-- Table structure for tb_inst_report_channel_config
--
CREATE TABLE  if not exists tb_inst_report_channel_config
(
 id bigint NOT NULL AUTO_INCREMENT,
 channel_id varchar(64) NOT NULL COMMENT '4-2-1-渠道报备编码',
 channel_report_code varchar(64) DEFAULT NULL COMMENT '4-2-1-渠道报备编码',
 target_channel_id varchar(32) DEFAULT NULL COMMENT '4-2-1-二级商户的渠道ID',
 entity varchar(32) DEFAULT 'P01' COMMENT '4-2-1-签约主体:P01, P03 ...',
 is_verify tinyINT NOT NULL DEFAULT '0' COMMENT '4-2-1-是否需要验证(0:否、1:是)',
 is_auto_verify tinyINT NOT NULL DEFAULT '0' COMMENT '4-2-1-是否自动验证',
 is_verify_stuck tinyINT NOT NULL DEFAULT '0' COMMENT '4-2-1-是否有验证卡点(0:否、1:是)',
 verify_check_point char(32) DEFAULT NULL COMMENT '4-2-1-报备验证校验点',
 verify_special_route_id int DEFAULT NULL COMMENT '4-2-1-验证特殊路由ID',
 verify_response_code_map_group_id varchar(64) DEFAULT NULL COMMENT '4-2-1-报备验证映射码分组',
 is_auto_report tinyINT NOT NULL DEFAULT '0' COMMENT '4-2-1-是否自动报备(0:否、1:是)',
 is_create_sub_merchant tinyINT NOT NULL DEFAULT '1' COMMENT '4-2-1-是否生成二级商户配置(0:否、1:是)',
 inst_code varchar(100) NOT NULL DEFAULT '' COMMENT '机构编码',
 report_mode varchar(100) NOT NULL DEFAULT '' COMMENT '报备模式：前置报备pre，后置报备post',
 report_way varchar(100) NOT NULL DEFAULT '' COMMENT '报备方式：线上online，线下offline',
 report_process_time varchar(32) DEFAULT NULL COMMENT '报备时效',
 template varchar(100) NOT NULL DEFAULT '' COMMENT '报备模版',
 report_file_format varchar(100) DEFAULT '' COMMENT '报备文件格式：xls，pdf',
 trans_required char(1) DEFAULT NULL COMMENT '报备时效',
 template_mapping_group varchar(64) DEFAULT NULL COMMENT '4-2-1-上送模板脚本规则',
 target_merchant_mapping_group varchar(64) DEFAULT NULL COMMENT '4-2-1-回填模板脚本规则',
 filter_mapping_group varchar(64) DEFAULT NULL COMMENT '4-2-1-过滤脚本规则',
 init_mapping_group varchar(64) DEFAULT NULL COMMENT '4-2-1-初始化脚本规则',
 tag_mapping_group varchar(64) DEFAULT NULL COMMENT '4-2-1-打标脚本规则',
 is_api_async tinyINT NOT NULL DEFAULT '0' COMMENT '4-2-1-是否异步调用(0:否、1:是)',
 is_api_inquiry tinyINT NOT NULL DEFAULT '0' COMMENT '4-2-1-是否支持主动查询报备结果(0:否、1:是)',
 is_api_notify tinyINT NOT NULL DEFAULT '0' COMMENT '4-2-1-是否支持渠道异步回调返回(0:否、1:是)',
 is_sign_contract tinyINT NOT NULL DEFAULT '0' COMMENT '4-2-1-是否校验合同文件(0:否、1:是)',
 config_json text COMMENT '拓展配置',
 cam varchar(100) NOT NULL DEFAULT '' COMMENT '渠道AM',
 remark varchar(512) DEFAULT NULL COMMENT '备注',
 status tinyINT NOT NULL DEFAULT '0' COMMENT '状态：启用1，停用0',
 channel_sla int NOT NULL DEFAULT '0' COMMENT '4-2-1-渠道sla',
 our_sla int NOT NULL DEFAULT '0' COMMENT '4-1-1-我司SLA',
 creator varchar(32) NOT NULL DEFAULT 'system' COMMENT '3-2-5-创建人',
 modifier varchar(32) NOT NULL DEFAULT 'system' COMMENT '3-2-5-修改人',
 create_time datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
 modified_time datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ,
 is_deleted tinyINT NOT NULL DEFAULT '0' COMMENT '0-0-0-是否删除(0:否、1:是)',
 related_channel_code varchar(256) DEFAULT NULL COMMENT '4-2-1-关联渠道编码',
 is_create_channel_merchant tinyINT NOT NULL DEFAULT '0' COMMENT '4-2-1-是否自动创建渠道商户',
 PRIMARY KEY (id),
 UNIQUE KEY INDEX_0818c875a3e749c296ba54962aa09105 (channel_id)
) COMMENT='渠道报备配置表';

--
-- Table structure for tb_inst_report_channel_template_mapping
--
CREATE TABLE  if not exists tb_inst_report_channel_template_mapping
(
 id bigint NOT NULL AUTO_INCREMENT,
 mapping_group varchar(100) DEFAULT '' COMMENT '映射组',
 field varchar(100) NOT NULL DEFAULT '' COMMENT '目标字段',
 script text COMMENT '脚本',
 sort_order int NOT NULL DEFAULT '1' COMMENT '排序',
 source_field varchar(100) NOT NULL DEFAULT '' COMMENT '源字段，多个按","分隔',
 config_json text COMMENT '拓展配置',
 status tinyINT NOT NULL DEFAULT '1' COMMENT '状态：0：不启用，1：启用',
 creator varchar(32) NOT NULL DEFAULT 'system' COMMENT '创建人',
 modifier varchar(32) NOT NULL DEFAULT 'system' COMMENT '修改人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
 PRIMARY KEY (id)
) COMMENT='报备渠道模板字段映射';

--
-- Table structure for tb_inst_report_merchant
--
CREATE TABLE  if not exists tb_inst_report_merchant
(
 id bigint NOT NULL AUTO_INCREMENT,
 requirement_order_id bigint DEFAULT NULL COMMENT '机构集成需求单号',
 inst_id bigint NOT NULL COMMENT '机构标识',
 channel_type varchar(64) DEFAULT NULL COMMENT '渠道类型',
 is_need_report_merchant char(1) DEFAULT NULL COMMENT '是否需商户报备 Y:是,N:否',
 report_require varchar(32) DEFAULT NULL COMMENT '报备要求 WHOLE:整体报备,DIVIDE:分开报备(出入)',
 report_type varchar(32) DEFAULT NULL COMMENT '报备方式 API:接口,EMAIL:邮件,CHAT:聊天工具',
 report_process_time varchar(32) DEFAULT NULL COMMENT '处理时效 REALTIME:近实时,DAILY:天,WEEKLY:周,MONTHLY:月',
 report_template varchar(256) DEFAULT NULL COMMENT '报备模板',
 report_fields varchar(256) DEFAULT NULL COMMENT '需报备字段信息',
 mid varchar(32) DEFAULT NULL COMMENT 'mid',
 remark varchar(512) DEFAULT NULL COMMENT 'remark',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (id) ,
 KEY INDEX_69bd5515767e452b9f2b38f90131ec70 (requirement_order_id)
) COMMENT='机构商户报备信息';

--
-- Table structure for tb_inst_report_order
--
CREATE TABLE  if not exists tb_inst_report_order
(
 id bigint NOT NULL AUTO_INCREMENT,
 report_no varchar(100) NOT NULL DEFAULT '' COMMENT '报备单号',
 channel_id varchar(100) NOT NULL DEFAULT '' COMMENT '报备渠道',
 merchant_no varchar(100) NOT NULL DEFAULT '' COMMENT '商户号',
 merchant_name varchar(100) NOT NULL DEFAULT '' COMMENT '商户名称',
 merchant_level char(8) DEFAULT '' COMMENT '3-1-1-商户等级',
 report_way varchar(100) NOT NULL DEFAULT '' COMMENT '报备方式：线上online，线下offline',
 report_mode varchar(100) NOT NULL DEFAULT '' COMMENT '报备模式：前置报备pre，后置报备post',
 cam varchar(100) NOT NULL DEFAULT '' COMMENT '渠道AM',
 bd varchar(100) NOT NULL DEFAULT '' COMMENT '商户bd',
 report_status tinyINT NOT NULL DEFAULT '0' COMMENT '报备状态：待报备0，报备中1，报备通过2，报备拒绝3',
 sub_status varchar(64) DEFAULT NULL COMMENT '报备子状态',
 tag_source tinyint NOT NULL DEFAULT '0' COMMENT '3-2-5-标签来源, 0: 无标 1: 系统标 2: 人工标',
 report_file varchar(100) NOT NULL DEFAULT '' COMMENT '报备文件路径',
 reject_reason varchar(100) NOT NULL DEFAULT '' COMMENT '拒绝原因',
 start_time datetime DEFAULT NULL COMMENT '报备开始时间',
 end_time datetime DEFAULT NULL COMMENT '报备结束时间',
 forward text COMMENT '4-2-1-上送信息',
 creator varchar(32) NOT NULL DEFAULT 'system' COMMENT '3-2-5-创建者',
 updater varchar(32) NOT NULL DEFAULT 'system' COMMENT '3-2-5-修改者',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 merchant_sync_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '商户信息刷新时间',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
 is_manual_generate char(1) DEFAULT '0' COMMENT '是否手动生成：0-否;1-是',
 PRIMARY KEY (id),
 UNIQUE KEY INDEX_9800ad0e31534b18ac233d6eb973e0da (report_no),
 KEY INDEX_25953e776bbe4d1fa09fac72b79881d0 (cam,channel_id) ,
 KEY INDEX_55412abeac7346ad9d07038ffcf002e4 (channel_id,merchant_no,is_deleted)
) COMMENT='报备申请单';

--
-- Table structure for tb_inst_report_order_tag
--
CREATE TABLE  if not exists tb_inst_report_order_tag
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT '0-0-0-主键',
 report_no varchar(100) NOT NULL COMMENT '4-2-1-报备单号',
 tag_code varchar(50) NOT NULL COMMENT '4-2-1-标签code',
 tag_source varchar(50) NOT NULL COMMENT '4-2-1-标签来源：SYSTEM/MANUAL/CHANNEL',
 remark varchar(1000) DEFAULT NULL COMMENT '4-2-1-备注',
 status tinyint NOT NULL DEFAULT '1' COMMENT '0-0-0-状态，1-生效/0-失效',
 reason_type varchar(256) DEFAULT NULL COMMENT '3-1-1-类型',
 sub_reason_type varchar(256) DEFAULT NULL COMMENT '3-1-1-原因',
 creator varchar(50) NOT NULL DEFAULT 'system' COMMENT '4-2-1-创建者',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0-创建时间',
 modifier varchar(50) NOT NULL DEFAULT 'system' COMMENT '4-2-1-修改者',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0-修改时间',
 PRIMARY KEY (id),
 KEY INDEX_0418eeee3fc54251bb06c811a8f51ae8 (report_no,status)
) COMMENT='报备单的标签';

--
-- Table structure for tb_inst_requirement_order
--
CREATE TABLE  if not exists tb_inst_requirement_order
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT '需求单号',
 apply_no varchar(64) DEFAULT NULL COMMENT '申请单号',
 inst_id bigint NOT NULL COMMENT '机构标识',
 api_doc_id varchar(64) DEFAULT NULL COMMENT '接口文档',
 api_doc_url varchar(256) DEFAULT NULL COMMENT '接口文档url',
 platform_url varchar(512) DEFAULT NULL COMMENT '平台地址',
 prod_platform_url varchar(512) DEFAULT NULL,
 pay_doc_id varchar(16) DEFAULT NULL COMMENT '支付流程',
 pay_doc_url varchar(256) DEFAULT NULL COMMENT '支付流程文档url',
 refund_doc_id varchar(16) DEFAULT NULL COMMENT '退款流程',
 refund_doc_url varchar(256) DEFAULT NULL COMMENT '退款流程文档url',
 dispute_doc_id varchar(16) DEFAULT NULL COMMENT '争议流程',
 dispute_doc_url varchar(256) DEFAULT NULL COMMENT '争议流程文档url',
 bill_doc_id varchar(16) DEFAULT NULL COMMENT '对账流程',
 bill_doc_url varchar(256) DEFAULT NULL COMMENT '对账流程文档url',
 problem_handle_doc_id varchar(16) DEFAULT NULL COMMENT '日常处理流程',
 problem_handle_doc_url varchar(256) DEFAULT NULL COMMENT '日常处理流程文档url',
 release_require_doc_id varchar(16) DEFAULT NULL COMMENT '上线要求',
 release_require_doc_url varchar(256) DEFAULT NULL COMMENT '上线要求文档url',
 remark varchar(512) DEFAULT NULL COMMENT '备注',
 status varchar(16) NOT NULL COMMENT '状态 INIT:待提交 PD_ANALYSIS:产品调研',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (id),
 KEY INDEX_6211c320ed3343df94a79278493c7c37 (inst_id) ,
 KEY INDEX_a7ae692cc90c41df922626ce3c46c93c (apply_no)
) COMMENT='机构集成需求单';

--
-- Table structure for tb_inst_requirement_schedule
--
CREATE TABLE  if not exists tb_inst_requirement_schedule
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT '产品排期标识',
 requirement_order_id bigint NOT NULL COMMENT '机构集成需求单号',
 inst_product_code varchar(64) DEFAULT NULL COMMENT '机构产品编码',
 pd_id varchar(64) DEFAULT NULL COMMENT '产品负责人',
 pd_name varchar(64) DEFAULT NULL COMMENT '产品负责人名称',
 is_start char(1) DEFAULT NULL COMMENT '是否启动',
 priority char(2) DEFAULT NULL COMMENT '优先级',
 tb_url varchar(256) DEFAULT NULL COMMENT '需求链接',
 plan_release_date date DEFAULT NULL COMMENT '计划投产日期',
 remark varchar(512) DEFAULT NULL COMMENT '备注',
 status varchar(32) NOT NULL COMMENT '状态',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (id)
) COMMENT='机构集成产品排期';

--
-- Table structure for tb_inst_requirement_schedule_detail
--
CREATE TABLE  if not exists tb_inst_requirement_schedule_detail
(
 requirement_schedule_id bigint NOT NULL COMMENT '机构集成排期单号',
 inst_product_code varchar(64) NOT NULL COMMENT '机构产品编码',
 inst_product_capability_code varchar(64) NOT NULL COMMENT '机构产品能力编码',
 is_configured char(1) DEFAULT 'N' COMMENT '是否已配置 Y:是 N:否',
 version varchar(64) DEFAULT NULL COMMENT '操作版本',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (requirement_schedule_id, inst_product_code, inst_product_capability_code)
) COMMENT='合同签约产品';

--
-- Table structure for tb_inst_stakeholder_info
--
CREATE TABLE  if not exists tb_inst_stakeholder_info
(
 id bigint NOT NULL AUTO_INCREMENT,
 inst_id bigint NOT NULL COMMENT '机构标识',
 user_first_name varchar(256) DEFAULT NULL COMMENT '姓',
 user_middle_name varchar(256) DEFAULT NULL COMMENT '中间名',
 user_last_name varchar(256) DEFAULT NULL COMMENT '尾名',
 stock_rate decimal(20, 4) DEFAULT NULL COMMENT '持股比例',
 nationality char(2) DEFAULT NULL COMMENT '国籍',
 live_country char(2) DEFAULT NULL COMMENT '居住国家',
 cert_type varchar(20) DEFAULT NULL COMMENT '证件类型',
 cert_no varchar(64) DEFAULT NULL COMMENT '证件号',
 birthday date DEFAULT NULL COMMENT '生日',
 expire_date varchar(64) DEFAULT NULL COMMENT '有效期',
 live_address varchar(256) DEFAULT NULL COMMENT '居住地址',
 cert_photo1 varchar(1024) DEFAULT NULL COMMENT '证件照片1',
 cert_photo2 varchar(1024) DEFAULT NULL COMMENT '证件照片2',
 extra_info text COMMENT '扩展字段',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '逻辑删除标识',
 PRIMARY KEY (id) ,
 KEY INDEX_e3fb67c6fabc40efb034922c7605f2e7 (inst_id)
) COMMENT='机构受益人信息';

--
-- Table structure for tb_inst_sub_funds_account
--
CREATE TABLE  if not exists tb_inst_sub_funds_account
(
 sub_account_id varchar(128) NOT NULL COMMENT '子级账号标识(机构标识_机构账号_国家_币种_子级账号号码)',
 business_key varchar(64) NOT NULL COMMENT '业务申请唯一键',
 account_id varchar(64) NOT NULL COMMENT '机构帐号标识',
 bucket_id bigint DEFAULT NULL COMMENT '子级机构账号创建总数标识',
 number_segment_id varchar(128) DEFAULT NULL COMMENT '机构帐号号段标识',
 number_segment_no varchar(128) DEFAULT NULL COMMENT '号段生成的账号号码',
 sub_use_type varchar(32) NOT NULL COMMENT '用途：充值：RECHARGE、收款：RECEIVE_PAY、结算：SETTLEMENT、充退：CHARGE_BACK、代发：ISSUED_ON_BEHALF、保证金：SECURITY_DEPOSIT',
 sub_account_no varchar(128) DEFAULT NULL COMMENT '子级账号号码',
 sub_account_name varchar(256) DEFAULT NULL COMMENT '子级账号名称',
 b_sub_account_no varchar(128) DEFAULT NULL COMMENT '子级账号号码BBAN',
 merchant_no varchar(64) NOT NULL COMMENT '申请子级账号的商户号',
 sub_merchant_no varchar(64) DEFAULT NULL COMMENT '申请子级账号的子商户号',
 status int NOT NULL DEFAULT '9' COMMENT '状态 0：未激活，1：激活中，2：已激活，3：已停用，4：申请中，9：初始化',
 scenes varchar(256) DEFAULT NULL COMMENT '申请子级账号使用场景：TRADE：B2B贸易、ADS：广告费收款、COD：COD费用收款、LIVE：直播平台分销、ECOM：电商平台收款',
 account_json text COMMENT '账号相关拓展信息，json格式',
 request_body text COMMENT '请求参数，json格式',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (sub_account_id),
 UNIQUE KEY INDEX_1f2686e9e35948bf9c6ba3c15bb34791 (business_key),
 KEY INDEX_e21faf65ad294f9fb35af411728ecd84 (account_id,sub_account_no)
) COMMENT='机构子级资金账号表';

--
-- Table structure for tb_inst_sub_funds_account_bucket
--
CREATE TABLE  if not exists tb_inst_sub_funds_account_bucket
(
 sub_account_id varchar(128) NOT NULL COMMENT '子级账号标识(机构标识_机构账号_国家_币种_子级资金账号号码或17位数字)',
 account_id varchar(64) NOT NULL COMMENT '机构帐号标识',
 bucket_id bigint DEFAULT NULL COMMENT '子级机构账号创建总数标识',
 number_segment_id varchar(128) DEFAULT NULL COMMENT '生成机构子级帐号号段标识',
 number_segment_no varchar(128) DEFAULT NULL COMMENT '机构子级帐号号段生成号码',
 sub_use_type varchar(32) NOT NULL COMMENT '用途：充值：RECHARGE、收款：RECEIVE_PAY、结算：SETTLEMENT、充退：CHARGE_BACK、代发：ISSUED_ON_BEHALF、保证金：SECURITY_DEPOSIT',
 sub_account_no varchar(128) DEFAULT NULL COMMENT '子级账号号码',
 sub_account_name varchar(256) DEFAULT NULL COMMENT '子级账号名称',
 b_sub_account_no varchar(128) DEFAULT NULL COMMENT '子级账号号码BBAN',
 sub_account_status int NOT NULL DEFAULT '9' COMMENT '状态 0：未激活，1：激活中，2：已激活，3：已停用，4：申请中，9：初始化',
 merchant_no varchar(64) DEFAULT NULL COMMENT '商户号',
 sub_merchant_no varchar(64) DEFAULT NULL COMMENT '申请子级账号的子商户号',
 status char(1) DEFAULT NULL COMMENT '状态 Y:可分配，N：已分配',
 apply_type varchar(32) NOT NULL DEFAULT 'PRE_APPLY' COMMENT '0-0-0-申请类型，MERCHANT_APPLY：商户申请，PRE_APPLY：预申请',
 account_json text COMMENT '账号相关拓展信息，json格式',
 request_body text COMMENT '请求参数，json格式',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (sub_account_id),
 KEY INDEX_43096cb601c64c8a928511c45a78926b (account_id,sub_use_type,status,merchant_no)
) COMMENT='机构子级资金账号预申请账号表';

--
-- Table structure for tb_inst_sub_number_segment
--
CREATE TABLE  if not exists tb_inst_sub_number_segment
(
 id varchar(128) NOT NULL COMMENT '号段标识（机构标识_号段序号）',
 name varchar(64) NOT NULL COMMENT '号段名称',
 number_start varchar(64) NOT NULL COMMENT '号段号码开始（包含）',
 number_end varchar(64) NOT NULL COMMENT '号段号码结束（包含）',
 max_used varchar(64) DEFAULT NULL COMMENT '号段最近使用的号码',
 use_type varchar(256) NOT NULL COMMENT '机构帐号号段用途：充值：RECHARGE、收款：RECEIVE_PAY、结算：SETTLEMENT、充退：CHARGE_BACK、代发：ISSUED_ON_BEHALF、保证金：SECURITY_DEPOSIT,支持多个使用,分割',
 status char(1) NOT NULL DEFAULT 'Y' COMMENT '状态：N：不可用，Y：可用',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (id),
 KEY INDEX_f0454441fbb04f6e926da7de5cab0209 (id,status,use_type),
 KEY INDEX_dc5e8a9e42c44039ab8529a4a0dccb45 (status,max_used,number_end,use_type)
) COMMENT='机构账号子级号段信息表';

--
-- Table structure for tb_inst_target_org
--
CREATE TABLE  if not exists tb_inst_target_org
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT '目标机构标识',
 target_org_code varchar(32) NOT NULL COMMENT '目标机构编码',
 target_org_name varchar(128) DEFAULT NULL COMMENT '目标机构名称',
 status char(1) NOT NULL DEFAULT 'Y' COMMENT '状态 Y：启用，N：停用',
 creator varchar(32) DEFAULT NULL COMMENT '创建人',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (id) ,
 UNIQUE KEY INDEX_17157e1a16114005b6ef6520b49d8153 (target_org_code)
) COMMENT='目标机构';

--
-- Table structure for tb_inst_trans_fee
--
CREATE TABLE  if not exists tb_inst_trans_fee
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT '费用标识',
 fee_group_id varchar(64) NOT NULL COMMENT '费用组标识',
 fee_type varchar(16) DEFAULT NULL COMMENT '费用类型',
 mcc varchar(512) NOT NULL COMMENT '行业分类(多个)',
 calculate_type varchar(16) DEFAULT NULL COMMENT '算费方式',
 fee_detail longtext COMMENT '费用详情',
 min_fee decimal(20, 4) DEFAULT NULL COMMENT '保底费用',
 max_fee decimal(20, 4) DEFAULT NULL COMMENT '封顶费用',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (id) ,
 KEY INDEX_0f7ecc8ff0c047178e93d8bfc3740bfd (fee_group_id)
) COMMENT='费用明细';

--
-- Table structure for tb_merchant_contract_payment_available
--
CREATE TABLE  if not exists tb_merchant_contract_payment_available
(
 merchant_no varchar(64) NOT NULL DEFAULT '' COMMENT '3-5-1-商户号',
 merchant_name varchar(255) NOT NULL DEFAULT '' COMMENT '3-5-1-商户名',
 merchant_level varchar(32) NOT NULL DEFAULT '' COMMENT '3-5-1-商户等级',
 payment_type char(1) NOT NULL DEFAULT '' COMMENT '3-2-5-支付类型',
 cashier_product_code varchar(64) NOT NULL DEFAULT '' COMMENT '3-2-5-收银产品编码',
 cashier_product_name varchar(255) NOT NULL DEFAULT '' COMMENT '3-2-5-收银产品名称',
 payment_method_no varchar(64) NOT NULL DEFAULT '' COMMENT '3-2-5-支付方式编码',
 payment_method_name varchar(64) NOT NULL DEFAULT '' COMMENT '3-2-5-支付方式名称',
 country varchar(32) NOT NULL DEFAULT '' COMMENT '3-2-5-国家',
 bd_contacter varchar(32) NOT NULL DEFAULT '' COMMENT '4-2-1-BD',
 is_black_merchant int NOT NULL DEFAULT '-1' COMMENT '3-5-1-黑名单商户',
 can_use int NOT NULL DEFAULT '-1' COMMENT '0-0-0-是否可用',
 start_time datetime DEFAULT NULL COMMENT '3-5-1-合同生效开始时间',
 creator varchar(50) NOT NULL DEFAULT '' COMMENT '0-0-0-创建人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0-创建时间UTC',
 modifier varchar(50) NOT NULL DEFAULT '' COMMENT '0-0-0-修改人',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0-修改时间UTC',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '0-0-0-是否逻辑删除(N:未删除,Y:已删除)',
 am varchar(32) NOT NULL DEFAULT '' COMMENT '4-2-1-AM',
 PRIMARY KEY (merchant_no, merchant_level, payment_type, cashier_product_code, payment_method_no,
 country, bd_contacter, is_black_merchant)
) COMMENT='商户签约可用性查询-BD';

--
-- Table structure for tb_merchant_contract_payment_available_channel
--
CREATE TABLE  if not exists tb_merchant_contract_payment_available_channel
(
 merchant_no varchar(64) NOT NULL DEFAULT '' COMMENT '3-5-1-商户号',
 merchant_name varchar(255) NOT NULL DEFAULT '' COMMENT '3-5-1-商户名',
 can_use int DEFAULT NULL COMMENT '0-0-0-是否可用',
 is_need_report_merchant int NOT NULL DEFAULT '-1' COMMENT '4-2-1-是否需要报备',
 report_mode varchar(32) NOT NULL DEFAULT '' COMMENT '4-2-1-报备模式',
 report_status int DEFAULT NULL COMMENT '4-2-1-报备状态',
 verify_status varchar(32) NOT NULL DEFAULT '' COMMENT '4-2-1-验证状态',
 country varchar(32) NOT NULL DEFAULT '' COMMENT '3-2-5-国家',
 payment_type char(1) NOT NULL DEFAULT '' COMMENT '3-2-5-支付类型',
 payment_method_type varchar(32) NOT NULL DEFAULT '' COMMENT '3-2-5-支付方式类型',
 target varchar(32) NOT NULL DEFAULT '' COMMENT '3-2-5-目标机构/卡组',
 r_channel_id varchar(32) NOT NULL DEFAULT '' COMMENT '4-2-1-报备渠道编码',
 channel_id varchar(32) NOT NULL DEFAULT '' COMMENT '4-2-1-二级商户报备渠道编码',
 standard_channel_code varchar(32) NOT NULL DEFAULT '' COMMENT '4-2-1-标准渠道编码',
 channel_priority int DEFAULT NULL COMMENT '4-2-1-渠道优先级',
 reject_reason varchar(255) NOT NULL DEFAULT '' COMMENT '4-2-1-报备备注',
 report_order_create_time datetime DEFAULT NULL COMMENT '4-2-1-报备单创建时间',
 start_time datetime DEFAULT NULL COMMENT '4-2-1-报备开始时间',
 end_time datetime DEFAULT NULL COMMENT '4-2-1-报备结束时间',
 channel_sla int NOT NULL DEFAULT '0' COMMENT '4-2-1-渠道sla',
 report_cost_time varchar(32) NOT NULL DEFAULT '' COMMENT '4-2-1-报备耗时',
 cam varchar(32) NOT NULL DEFAULT '' COMMENT '4-2-1-cam',
 bd_contacter varchar(32) NOT NULL DEFAULT '' COMMENT '4-2-1-bd',
 entity varchar(32) NOT NULL DEFAULT '' COMMENT '3-5-1-签约主体',
 creator varchar(50) NOT NULL DEFAULT '' COMMENT '0-0-0-创建人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0-创建时间UTC',
 modifier varchar(50) NOT NULL DEFAULT '' COMMENT '0-0-0-修改人',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0-修改时间UTC',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '0-0-0-是否逻辑删除(N:未删除,Y:已删除)',
 PRIMARY KEY (merchant_no, bd_contacter, country, payment_type, payment_method_type, target,
 standard_channel_code, r_channel_id, is_need_report_merchant, channel_id, entity)
) COMMENT='商户签约可用性查询-CAM';

--
-- Table structure for tb_merchant_contract_payment_available_item
--
CREATE TABLE  if not exists tb_merchant_contract_payment_available_item
(
 payment_method_no varchar(64) NOT NULL DEFAULT '' COMMENT '4-2-1-支付方式编码',
 country varchar(32) NOT NULL DEFAULT '' COMMENT '4-2-1-国家',
 payment_type char(1) NOT NULL DEFAULT '' COMMENT '4-2-1-支付类型',
 cashier_product_code varchar(64) NOT NULL DEFAULT '' COMMENT '4-2-1-收银产品编码',
 can_use int DEFAULT NULL COMMENT '0-0-0-是否可用',
 merchant_no varchar(64) NOT NULL DEFAULT '' COMMENT '3-5-1-商户号',
 merchant_name varchar(255) DEFAULT NULL,
 master_or_slave varchar(64) NOT NULL DEFAULT '' COMMENT '4-2-1-主/备渠道(master_channel:主渠道,slave_channel-备渠道)',
 is_need_report_merchant int NOT NULL DEFAULT '-1' COMMENT '4-2-1-是否需要报备',
 standard_channel_code varchar(128) NOT NULL DEFAULT '' COMMENT '4-2-1-标准渠道编码',
 limit_amt varchar(255) NOT NULL DEFAULT '' COMMENT '4-2-1-渠道限额',
 channel_id varchar(32) NOT NULL DEFAULT '' COMMENT '4-2-1-二级商户报备渠道编码',
 entity varchar(32) NOT NULL DEFAULT '' COMMENT '3-5-1-签约主体',
 sub_merchant_status char(1) NOT NULL DEFAULT '' COMMENT '4-2-1-二级商户是否可用(N:不可用,Y:可用)',
 sub_merchant_verify_status varchar(32) NOT NULL DEFAULT '' COMMENT '4-2-1-二级商户验证状态',
 sub_merchant_id varchar(64) NOT NULL DEFAULT '' COMMENT '3-5-1-二级商户商户号',
 sub_merchant_remark varchar(64) NOT NULL DEFAULT '' COMMENT '3-5-1-二级商户备注',
 report_status int DEFAULT NULL COMMENT '4-2-1-报备状态',
 report_sub_status varchar(64) NOT NULL DEFAULT '' COMMENT '4-2-1-报备子状态',
 reject_reason varchar(255) DEFAULT NULL,
 report_create_time datetime DEFAULT NULL COMMENT '4-2-1-报备单创建时间',
 report_start_time datetime DEFAULT NULL COMMENT '4-2-1-报备开始时间',
 report_end_time datetime DEFAULT NULL COMMENT '4-2-1-报备结束时间',
 report_update_time datetime DEFAULT NULL COMMENT '4-2-1-报备中更新时间',
 channel_sla int NOT NULL DEFAULT '0' COMMENT '4-2-1-渠道sla',
 cam varchar(32) NOT NULL DEFAULT '' COMMENT '4-2-1-cam',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0-创建时间UTC',
 modifier varchar(50) NOT NULL DEFAULT '' COMMENT '0-0-0-修改人',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0-修改时间UTC',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '0-0-0-是否逻辑删除(N:未删除,Y:已删除)',
 remark text,
 report_cost_time varchar(64) DEFAULT '' COMMENT '4-2-1-报备耗时',
 PRIMARY KEY (merchant_no, country, payment_type, cashier_product_code, payment_method_no,
 standard_channel_code, is_need_report_merchant, channel_id, entity)
) COMMENT='商户支付方式可用性查询-item';

--
-- Table structure for tb_merchant_info
--
CREATE TABLE  if not exists tb_merchant_info
(
 merchant_no varchar(64) NOT NULL DEFAULT '' COMMENT '3-5-1-商户号',
 old_merchant_no varchar(64) NOT NULL DEFAULT '' COMMENT '3-5-1-老商户号',
 merchant_name varchar(255) NOT NULL DEFAULT '' COMMENT '3-5-1-商户名',
 merchant_level varchar(32) NOT NULL DEFAULT '' COMMENT '3-5-1-商户等级',
 member_id varchar(32) NOT NULL DEFAULT '' COMMENT '3-5-1-会员ID',
 group_no varchar(32) NOT NULL DEFAULT '' COMMENT '3-5-1-集团ID',
 group_name text NOT NULL COMMENT '3-5-1-集团名称',
 group_level varchar(32) NOT NULL DEFAULT '' COMMENT '3-5-1-集团等级',
 merchant_source varchar(32) NOT NULL DEFAULT '' COMMENT '3-5-1-商户来源',
 visit_url text NOT NULL COMMENT '4-5-1-访问地址URL',
 industry_category varchar(255) NOT NULL DEFAULT '' COMMENT '3-5-1-行业大类',
 mmc_id varchar(255) NOT NULL DEFAULT '' COMMENT '3-5-1-MCC信息，行业大类的儿子',
 sub_mcc_id varchar(255) NOT NULL DEFAULT '' COMMENT '3-5-1-MCC信息子信息，MCC的儿子',
 biz_type varchar(255) NOT NULL DEFAULT '' COMMENT '3-5-1-业务类型 01:数娱标准支付 02: 电商支付 99:JollyMax充值站',
 merchant_type varchar(255) NOT NULL DEFAULT '' COMMENT '3-5-1-商户类型 1：特约商户2：平台商户',
 entity varchar(255) NOT NULL DEFAULT '' COMMENT '3-5-1-我司主体 T00:Shareit-IN T01: Funtech P01:PMmax C01: CyberShell',
 first_approve_time datetime DEFAULT NULL COMMENT '3-5-1-提交初审时间',
 grade_finish_time datetime DEFAULT NULL COMMENT '3-5-1-评级完成时间',
 entry_approve_time datetime DEFAULT NULL COMMENT '3-5-1-入驻审批时间',
 entry_success_time datetime DEFAULT NULL COMMENT '3-5-1-入驻成功时间',
 merchant_create_time datetime DEFAULT NULL COMMENT '3-5-1-商户创建时间',
 app varchar(255) NOT NULL DEFAULT '' COMMENT '3-5-1-产品名',
 register_no varchar(255) NOT NULL DEFAULT '' COMMENT '3-5-1-商户注册号',
 legal_name varchar(255) NOT NULL DEFAULT '' COMMENT '3-5-1-商户注册名',
 biz_entity_address text NOT NULL COMMENT '3-5-1-主体地址所在地',
 register_address text NOT NULL COMMENT '3-5-1-注册地址',
 corp_register_time varchar(255) NOT NULL DEFAULT '' COMMENT '3-5-1-注册时间',
 legal_birth varchar(255) NOT NULL DEFAULT '' COMMENT '2-2-1-负责人出生日期',
 legal_first_name varchar(255) NOT NULL DEFAULT '' COMMENT '1-3-1-负责人姓',
 legal_middle_name varchar(255) NOT NULL DEFAULT '' COMMENT '1-3-1-负责人中间名',
 legal_last_name varchar(255) NOT NULL DEFAULT '' COMMENT '1-3-1-负责人名',
 legal_cert_address text NOT NULL COMMENT '2-2-1-负责人证件地址',
 legal_country varchar(255) NOT NULL DEFAULT '' COMMENT '1-3-1-企业会员董事居住国家',
 nationality varchar(255) NOT NULL DEFAULT '' COMMENT '1-3-1-企业会员董事国籍',
 live_address text NOT NULL COMMENT '1-3-2-企业会员董事居住地址',
 cert_type varchar(255) NOT NULL DEFAULT '' COMMENT '2-2-3-企业会员董事证件类型',
 cert_no varchar(255) NOT NULL DEFAULT '' COMMENT '2-2-1-企业会员董事证件号',
 director_name varchar(255) NOT NULL DEFAULT '' COMMENT '1-3-1-企业会员董事全名',
 director_first_name varchar(255) NOT NULL DEFAULT '' COMMENT '1-3-1-企业会员董事姓',
 director_middle_name varchar(255) NOT NULL DEFAULT '' COMMENT '1-3-1-企业会员董事中间名',
 director_last_name varchar(255) NOT NULL DEFAULT '' COMMENT '1-3-1-企业会员董事名',
 director_birth varchar(255) NOT NULL DEFAULT '' COMMENT '2-2-1-企业会员董事生日',
 expire_date varchar(255) NOT NULL DEFAULT '' COMMENT '2-2-2-企业会员董事有效期',
 expire_start_date varchar(255) NOT NULL DEFAULT '' COMMENT '2-2-2-企业会员董事有效期开始时间',
 expire_end_date varchar(255) NOT NULL DEFAULT '' COMMENT '2-2-2-企业会员董事有效期结束时间',
 bd varchar(255) NOT NULL DEFAULT '' COMMENT '3-5-1-商户BD',
 am varchar(255) NOT NULL DEFAULT '' COMMENT '3-5-1-商户AM',
 card_token varchar(255) NOT NULL DEFAULT '' COMMENT '1-1-2-商户卡卡号token',
 C010 text NOT NULL COMMENT '3-5-1-C010',
 C011 text NOT NULL COMMENT '3-5-1-C011',
 C012 text NOT NULL COMMENT '3-5-1-C012',
 C013 text NOT NULL COMMENT '3-5-1-C013',
 C036 text NOT NULL COMMENT '3-5-1-C036',
 C099 text NOT NULL COMMENT '3-5-1-C099',
 C100_1 text NOT NULL COMMENT '3-5-1-C100_1',
 C100_2 text NOT NULL COMMENT '3-5-1-C100_2',
 C100_3 text NOT NULL COMMENT '3-5-1-C100_3',
 C100_4 text NOT NULL COMMENT '3-5-1-C100_4',
 attachment_urls text NOT NULL COMMENT '4-5-1-合同附件URL',
 trade_count varchar(255) NOT NULL DEFAULT '' COMMENT '3-5-1-交易量',
 trade_amount varchar(255) NOT NULL DEFAULT '' COMMENT '3-5-1-交易额',
 is_url_invalid varchar(255) NOT NULL DEFAULT '' COMMENT '3-5-1-是否无效URL',
 is_dormant varchar(255) NOT NULL DEFAULT '' COMMENT '3-5-1-是否休眠商户',
 is_risk_offline varchar(255) NOT NULL DEFAULT '' COMMENT '3-5-1-是否风控下线',
 creator varchar(50) NOT NULL DEFAULT '' COMMENT '0-0-0-创建人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0-创建时间UTC',
 modifier varchar(50) NOT NULL DEFAULT '' COMMENT '0-0-0-修改人',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0-修改时间UTC',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '0-0-0-是否逻辑删除(N:未删除,Y:已删除)',
 PRIMARY KEY (merchant_no)
) COMMENT='商户信息';

--
-- Table structure for tb_merchant_kyb_info
--
CREATE TABLE  if not exists tb_merchant_kyb_info
(
 id int NOT NULL AUTO_INCREMENT COMMENT '0-0-0-主键',
 merchant_no varchar(64) NOT NULL DEFAULT '' COMMENT '3-1-1-商户号',
 kyb_no varchar(128) NOT NULL DEFAULT '' COMMENT '3-1-1-KYB NO',
 app varchar(256) NOT NULL DEFAULT '' COMMENT 'APP',
 visit_url text COMMENT '4-5-1-产品链接',
 old_merchant_no varchar(64) DEFAULT NULL COMMENT '3-1-1-老商户号',
 member_id varchar(64) NOT NULL DEFAULT '' COMMENT '3-1-1-会员号',
 entity varchar(32) NOT NULL DEFAULT '' COMMENT '3-1-1-我司主体',
 biz_type varchar(32) DEFAULT NULL COMMENT '3-1-1-业务类型',
 merchant_name varchar(256) NOT NULL DEFAULT '' COMMENT '商户内部简称(非标)',
 merchant_source varchar(32) DEFAULT NULL COMMENT '4-1-1-商户来源',
 merchant_type varchar(32) DEFAULT NULL COMMENT '4-1-1-商户类型',
 merchant_level varchar(32) NOT NULL DEFAULT '' COMMENT '3-1-1-商户等级',
 industry_category varchar(32) NOT NULL DEFAULT '' COMMENT '2-3-3-行业类别-我司大类',
 mcc_id varchar(32) NOT NULL DEFAULT '' COMMENT '2-3-3-MCC',
 sub_mcc_id varchar(32) NOT NULL DEFAULT '' COMMENT '2-3-3-SUB MCC',
 group_no varchar(64) DEFAULT NULL COMMENT '3-1-1-集团号',
 group_name varchar(255) DEFAULT NULL COMMENT '3-1-1-集团名',
 group_level varchar(32) DEFAULT NULL COMMENT '3-1-1-集团等级',
 activate_time datetime DEFAULT NULL COMMENT '3-1-1-客户激活时间',
 register_no varchar(255) DEFAULT NULL COMMENT '3-1-1-商户注册号',
 legal_name varchar(255) DEFAULT NULL COMMENT '3-1-1-商户注册名',
 biz_entity_address varchar(8) DEFAULT NULL COMMENT '3-1-1-商户主体所在地',
 register_address varchar(512) DEFAULT NULL COMMENT '3-1-1-商户注册地址',
 register_time date DEFAULT NULL COMMENT '3-1-1-商户注册时间',
 head_name_birth date DEFAULT NULL COMMENT '1-3-1-法人出生日期',
 head_full_name varchar(512) DEFAULT NULL COMMENT '法人名',
 head_name_cert_address varchar(512) DEFAULT NULL COMMENT '法人身份地址',
 tax_no varchar(255) DEFAULT NULL COMMENT '3-1-1-税号',
 risk_note text COMMENT '2-3-3-风控备注',
 risk_tag varchar(255) DEFAULT NULL COMMENT '2-3-3-风控标签',
 merchant_email varchar(128) DEFAULT NULL COMMENT '2-2-2-商户联系邮箱',
 merchant_phone varchar(128) DEFAULT NULL COMMENT '2-2-2-商户电话',
 director_name varchar(512) DEFAULT NULL COMMENT '董事名',
 director_live_country varchar(8) DEFAULT NULL COMMENT '2-2-1-董事居住地址',
 director_nationality varchar(8) DEFAULT NULL COMMENT '2-2-1-董事国家',
 director_cert_no varchar(255) DEFAULT NULL COMMENT '2-2-1-董事身份证明',
 director_cert_address varchar(255) DEFAULT NULL COMMENT '2-2-1-董事身份地址',
 director_birth date DEFAULT NULL COMMENT '2-2-1-董事出生日期',
 director_expire_start_date datetime DEFAULT NULL COMMENT '2-2-1-董事生效日期',
 director_expire_end_date datetime DEFAULT NULL COMMENT '2-2-1-董事过期日期',
 bd varchar(255) DEFAULT NULL COMMENT '1-3-2-商户BD',
 am varchar(255) DEFAULT NULL COMMENT '1-3-2-商户AM',
 stakeholder_name varchar(255) DEFAULT NULL COMMENT '2-2-3-受益人名',
 stakeholder_nationality varchar(255) DEFAULT NULL COMMENT '2-2-3-受益人国家',
 stakeholder_live_country varchar(255) DEFAULT NULL COMMENT '2-2-3-受益人居住国家',
 stakeholder_cert_no varchar(255) DEFAULT NULL COMMENT '2-2-3-受益人身份证明',
 stakeholder_cert_address varchar(255) DEFAULT NULL COMMENT '2-2-3-受益人身份地址',
 stakeholder_expire_start_date datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '2-2-3-受益人会员生效日期',
 stakeholder_expire_end_date datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '2-2-3-受益人会员过期日期',
 card_identity_no varchar(255) DEFAULT NULL COMMENT '3-2-5-卡标识',
 card_token varchar(255) DEFAULT NULL COMMENT '3-2-5-卡Token',
 c001 mediumtext COMMENT '2-1-1-(仅CN)三证合一(工商营业执照，组织机构代码证，税务登记证)',
 c005 mediumtext COMMENT '2-1-1-开户证明',
 c010 mediumtext COMMENT '2-1-1-公司注册证明',
 c011 mediumtext COMMENT '2-1-1-(仅HK)商业登记证',
 c012 mediumtext COMMENT '2-1-1-董事身份证明',
 c013 mediumtext COMMENT '2-1-1-公司章程',
 c036 mediumtext COMMENT '2-1-1-(仅HK)NNC1法团成立文件/NAR1周年报',
 c037 mediumtext COMMENT '2-1-1-股东的身份证明和地址证明',
 c038 mediumtext COMMENT '2-1-1-公司注册纸',
 c039 mediumtext COMMENT '2-1-1-Certificate of Incumbency或其他可以显示董事和股东的有效文件',
 c040 mediumtext COMMENT '2-1-1-股权架构图',
 c041 mediumtext COMMENT '2-1-1-授权签字人的身份证明',
 c042 mediumtext COMMENT '2-1-1-授权书/董事会决议/其他授权文件',
 c043 mediumtext COMMENT '2-1-1-申请表',
 c099 mediumtext COMMENT '2-3-3-APP审核报告地址',
 cm0001 mediumtext COMMENT '2-1-1-User Flow',
 cm0002 mediumtext COMMENT '2-1-1-英文的业务描述',
 cm0003 mediumtext COMMENT '2-1-1-特定商户引用法页面',
 cm0004 mediumtext COMMENT '2-1-1-日文引用条款',
 cm0005 mediumtext COMMENT '2-1-1-收银台页面截图',
 cm0006 mediumtext COMMENT '2-1-1-预留字段',
 cm0007 mediumtext COMMENT '2-1-1-预留字段',
 cm0008 mediumtext COMMENT '2-1-1-预留字段',
 cm0009 mediumtext COMMENT '2-1-1-预留字段',
 cm0010 mediumtext COMMENT '2-1-1-预留字段',
 cm0011 mediumtext COMMENT '2-1-1-预留字段',
 cm0012 mediumtext COMMENT '2-1-1-预留字段',
 cm0013 mediumtext COMMENT '2-1-1-预留字段',
 cm0014 mediumtext COMMENT '2-1-1-预留字段',
 cm0015 mediumtext COMMENT '2-1-1-预留字段',
 ca0001 mediumtext COMMENT '2-1-1-预留字段',
 ca0002 mediumtext COMMENT '2-1-1-预留字段',
 ca0003 mediumtext COMMENT '2-1-1-预留字段',
 ca0004 mediumtext COMMENT '2-1-1-预留字段',
 ca0005 mediumtext COMMENT '2-1-1-预留字段',
 ca0006 mediumtext COMMENT '2-1-1-Privacy Terms中显示主体名的截图 ',
 ca0007 mediumtext COMMENT '2-1-1-亮总特批邮件截图 ',
 ca0008 mediumtext COMMENT '2-1-1-拒付率',
 ca0009 mediumtext COMMENT '2-1-1-预计上线时间',
 ca0010 mediumtext COMMENT '2-1-1-预计交易量(USD)',
 agreement_address mediumtext COMMENT '3-5-1-我司协议',
 trade_count bigint NOT NULL DEFAULT '0' COMMENT '3-2-5-商户交易量',
 trade_amount decimal(30, 10) NOT NULL DEFAULT '0.0000000000' COMMENT '3-2-5-商户交易额USD',
 is_url_invalid tinyINT NOT NULL DEFAULT '0' COMMENT '4-1-1-是否无效URL',
 is_dormant tinyINT NOT NULL DEFAULT '0' COMMENT '4-1-1-是否休眠商户',
 is_risk_offline tinyINT NOT NULL DEFAULT '0' COMMENT '4-1-1-是否风控拦截',
 is_sign_contract tinyINT NOT NULL DEFAULT '0' COMMENT '4-1-1-是否有我司合同',
 creator varchar(32) NOT NULL DEFAULT 'system' COMMENT '4-1-1-创建人',
 modifier varchar(32) NOT NULL DEFAULT 'system' COMMENT '4-1-1-修改人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '4-1-1-创建时间',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '4-1-1-修改时间',
 is_deleted tinyINT NOT NULL DEFAULT '0' COMMENT '4-1-1-逻辑删除(0:未删除,1:已删除)',
 PRIMARY KEY (id),
 KEY INDEX_ee973b6ac8a9489fa7b857a6b9683c13 (merchant_no,is_deleted),
 KEY INDEX_e993c9b2b26b444f8b759651f01ecf91 (kyb_no,is_deleted)
) COMMENT='商户KYB信息';

--
-- Table structure for tb_method_item_mapping
--
CREATE TABLE  if not exists tb_method_item_mapping
(
 id bigint NOT NULL AUTO_INCREMENT,
 method_id int NOT NULL COMMENT '支付方式表数据的id',
 channel_item_id bigint NOT NULL COMMENT '渠道商品表数据的id',
 creator varchar(50) NOT NULL DEFAULT 'system',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
 modifier varchar(50) NOT NULL DEFAULT 'system',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ,
 PRIMARY KEY (id)
) COMMENT='渠道商品与支付方式映射表';

--
-- Table structure for tb_pay_channel_order
--
CREATE TABLE  if not exists tb_pay_channel_order
(
 id bigint NOT NULL AUTO_INCREMENT,
 product_code varchar(16) DEFAULT NULL COMMENT '收单产品编码',
 pay_order_no varchar(64) NOT NULL COMMENT '支付单流水号',
 pay_channel_order_no varchar(64) NOT NULL COMMENT '渠道流水号',
 pay_channel_code varchar(20) NOT NULL COMMENT '支付渠道编码',
 track_no varchar(64) DEFAULT 'NA' COMMENT '系统跟踪号',
 method_code varchar(16) NOT NULL COMMENT '支付方式编码',
 method_sub_code varchar(32) NOT NULL COMMENT '支付方式二级编码',
 country char(2) NOT NULL COMMENT '国家编号',
 currency char(3) NOT NULL COMMENT '交易币种',
 user_id varchar(64) DEFAULT NULL COMMENT '用户唯一标识',
 pay_amount decimal(15, 0) NOT NULL COMMENT '支付金额(以分为单位)',
 expiry_seconds int DEFAULT '0' COMMENT '交易过期秒数,0代表未设置过期秒数',
 payer_account varchar(64) DEFAULT NULL COMMENT '付款人账户',
 payer_account_name varchar(64) DEFAULT NULL COMMENT '付款人账户名称',
 payer_bank_name varchar(64) DEFAULT NULL COMMENT '付款人银行名称',
 payer_extra_details mediumtext COMMENT '付款人扩展详情',
 purchase_info varchar(256) DEFAULT NULL COMMENT '消费信息',
 remark varchar(256) DEFAULT NULL COMMENT '交易备注',
 status tinyint NOT NULL DEFAULT '0' COMMENT '渠道支付状态码,(0：待支付，1：支付成功，2：支付失败)',
 pay_third_channel_no varchar(128) DEFAULT NULL COMMENT '第三方支付渠道流水号',
 pay_third_bank_txn_id varchar(128) DEFAULT NULL COMMENT '第三银行txnid',
 event_code varchar(10) NOT NULL DEFAULT 'INIT' COMMENT '状态机事件码',
 response_code varchar(64) DEFAULT NULL COMMENT '请求渠道返回code',
 mapped_code varchar(10) DEFAULT NULL COMMENT '统一映射错误码',
 response_msg varchar(256) DEFAULT NULL COMMENT '请求渠道返回msg',
 response_json mediumtext COMMENT '请求渠道返回json',
 add_field1 varchar(32) DEFAULT NULL COMMENT '扩展字段1',
 add_field2 varchar(64) DEFAULT NULL COMMENT '扩展字段2',
 add_field3 varchar(128) DEFAULT NULL COMMENT '扩展字段3',
 add_field4 varchar(32) DEFAULT NULL COMMENT '扩展字段4',
 add_field5 varchar(64) DEFAULT NULL COMMENT '扩展字段5',
 add_field6 varchar(256) DEFAULT NULL COMMENT '扩展字段6',
 creator varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间UTC',
 modifier varchar(50) NOT NULL DEFAULT 'system' COMMENT '修改人',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间UTC',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '是否逻辑删除(0：未删除，1：已删除)',
 PRIMARY KEY (id) ,
 UNIQUE KEY INDEX_80d1d45a0b31481a8b18e7125e762ad6 (pay_order_no) ,
 UNIQUE KEY INDEX_4e607613bcff4cd6b483713f6facfb78 (pay_channel_order_no) ,
 KEY INDEX_6f6c5113956440b88ff7afeb1ea7ebdc (create_time,status,pay_channel_code)
) COMMENT='支付渠道流水表';

--
-- Table structure for tb_pay_channel_order_history
--
CREATE TABLE  if not exists tb_pay_channel_order_history
(
 id bigint NOT NULL AUTO_INCREMENT,
 product_code varchar(16) DEFAULT NULL COMMENT '收单产品编码',
 pay_order_no varchar(64) NOT NULL COMMENT '支付单流水号',
 pay_channel_order_no varchar(64) NOT NULL COMMENT '渠道流水号',
 pay_channel_code varchar(20) NOT NULL COMMENT '支付渠道编码',
 track_no varchar(64) DEFAULT 'NA' COMMENT '系统跟踪号',
 method_code varchar(16) NOT NULL COMMENT '支付方式编码',
 method_sub_code varchar(32) NOT NULL COMMENT '支付方式二级编码',
 country char(2) NOT NULL COMMENT '国家编号',
 currency char(3) NOT NULL COMMENT '交易币种',
 user_id varchar(64) DEFAULT NULL COMMENT '用户唯一标识',
 pay_amount decimal(15, 0) NOT NULL COMMENT '支付金额(以分为单位)',
 expiry_seconds int DEFAULT '0' COMMENT '交易过期秒数,0代表未设置过期秒数',
 payer_account varchar(64) DEFAULT NULL COMMENT '付款人账户',
 purchase_info varchar(256) DEFAULT NULL COMMENT '消费信息',
 remark varchar(256) DEFAULT NULL COMMENT '交易备注',
 status tinyint NOT NULL DEFAULT '0' COMMENT '渠道支付状态码,(0:待支付，1:支付成功，2:支付失败，9:初始化待跳转)',
 pay_third_channel_no varchar(128) DEFAULT NULL COMMENT '第三方支付渠道流水号',
 pay_third_bank_txn_id varchar(128) DEFAULT NULL COMMENT '第三方银行流水号',
 event_code varchar(10) NOT NULL DEFAULT 'INIT' COMMENT '状态机事件码',
 payer_account_name varchar(64) DEFAULT NULL COMMENT '付款人账户名称',
 payer_bank_name varchar(64) DEFAULT NULL COMMENT '付款人银行名称',
 payer_extra_details mediumtext COMMENT '付款人扩展详情',
 response_code varchar(64) DEFAULT NULL COMMENT '请求渠道返回code',
 mapped_code varchar(10) DEFAULT NULL COMMENT '统一映射错误码',
 response_msg varchar(256) DEFAULT NULL COMMENT '请求渠道返回msg',
 response_json mediumtext COMMENT '请求渠道返回json',
 add_field1 varchar(32) DEFAULT NULL COMMENT '扩展字段1',
 add_field2 varchar(64) DEFAULT NULL COMMENT '扩展字段2',
 add_field3 varchar(128) DEFAULT NULL COMMENT '扩展字段3',
 add_field4 varchar(32) DEFAULT NULL COMMENT '扩展字段4',
 add_field5 varchar(64) DEFAULT NULL COMMENT '扩展字段5',
 add_field6 varchar(256) DEFAULT NULL COMMENT '扩展字段6',
 creator varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间UTC',
 modifier varchar(50) NOT NULL DEFAULT 'system' COMMENT '修改人',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间UTC',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '是否逻辑删除(N：未删除，Y：已删除)',
 PRIMARY KEY (id, create_time) ,
 UNIQUE KEY INDEX_c2b4dbb8574645518c6e74cc5bd3eec8 (pay_order_no,create_time) ,
 UNIQUE KEY INDEX_b1d84bc8e26d400dac46359ad84cebcc (pay_channel_order_no,create_time) ,
 KEY INDEX_4733b758e89945bbbe83992f5f356e05 (pay_third_channel_no,pay_channel_code,create_time)
) COMMENT='支付渠道流水表'
/*!50500 PARTITION BY RANGE COLUMNS(create_time)
(PARTITION p0 VALUES LESS THAN ('2020-01-01') ,
 PARTITION p1 VALUES LESS THAN ('2020-02-01') ,
 PARTITION p2 VALUES LESS THAN ('2020-03-01') ,
 PARTITION p3 VALUES LESS THAN ('2020-04-01') ,
 PARTITION p4 VALUES LESS THAN ('2020-05-01') ,
 PARTITION p5 VALUES LESS THAN ('2020-06-01') ,
 PARTITION p6 VALUES LESS THAN ('2020-07-01') ,
 PARTITION p7 VALUES LESS THAN ('2020-08-01') ,
 PARTITION p8 VALUES LESS THAN ('2020-09-01') ,
 PARTITION p9 VALUES LESS THAN ('2020-10-01') ,
 PARTITION p10 VALUES LESS THAN ('2020-11-01') ,
 PARTITION p11 VALUES LESS THAN ('2020-12-01') ,
 PARTITION p12 VALUES LESS THAN ('2021-01-01') ,
 PARTITION p13 VALUES LESS THAN ('2021-02-01') ,
 PARTITION p14 VALUES LESS THAN ('2021-03-01') ,
 PARTITION p15 VALUES LESS THAN ('2021-04-01') ,
 PARTITION p16 VALUES LESS THAN ('2021-05-01') ,
 PARTITION p17 VALUES LESS THAN ('2021-06-01') ,
 PARTITION p18 VALUES LESS THAN ('2021-07-01') ,
 PARTITION p19 VALUES LESS THAN ('2021-08-01') ,
 PARTITION p20 VALUES LESS THAN ('2021-09-01') ,
 PARTITION p21 VALUES LESS THAN ('2021-10-01') ,
 PARTITION p22 VALUES LESS THAN ('2021-11-01') ,
 PARTITION p23 VALUES LESS THAN ('2021-12-01') ,
 PARTITION p24 VALUES LESS THAN ('2022-01-01') ,
 PARTITION p25 VALUES LESS THAN ('2022-02-01') ,
 PARTITION p26 VALUES LESS THAN ('2022-03-01') ,
 PARTITION p27 VALUES LESS THAN ('2022-04-01') ,
 PARTITION p28 VALUES LESS THAN ('2022-05-01') ,
 PARTITION p29 VALUES LESS THAN ('2022-06-01') ,
 PARTITION p30 VALUES LESS THAN ('2022-07-01') ,
 PARTITION p31 VALUES LESS THAN ('2022-08-01') ,
 PARTITION p32 VALUES LESS THAN ('2022-09-01') ,
 PARTITION p33 VALUES LESS THAN ('2022-10-01') ,
 PARTITION p34 VALUES LESS THAN ('2022-11-01') ,
 PARTITION p35 VALUES LESS THAN ('2022-12-01') ,
 PARTITION p36 VALUES LESS THAN ('2023-01-01') ,
 PARTITION p37 VALUES LESS THAN ('2023-02-01') ,
 PARTITION p38 VALUES LESS THAN ('2023-03-01') ,
 PARTITION p39 VALUES LESS THAN ('2023-04-01') ,
 PARTITION p40 VALUES LESS THAN ('2023-05-01') ,
 PARTITION p41 VALUES LESS THAN ('2023-06-01') ,
 PARTITION p42 VALUES LESS THAN ('2023-07-01') ,
 PARTITION p43 VALUES LESS THAN ('2023-08-01') ,
 PARTITION p44 VALUES LESS THAN ('2023-09-01') ,
 PARTITION p45 VALUES LESS THAN ('2023-10-01') ,
 PARTITION p46 VALUES LESS THAN ('2023-11-01') ,
 PARTITION p47 VALUES LESS THAN ('2023-12-01') ,
 PARTITION p48 VALUES LESS THAN ('2024-01-01') ,
 PARTITION p49 VALUES LESS THAN ('2024-02-01') ,
 PARTITION p50 VALUES LESS THAN ('2024-03-01') ,
 PARTITION p51 VALUES LESS THAN ('2024-04-01') ,
 PARTITION p52 VALUES LESS THAN ('2024-05-01') ,
 PARTITION p53 VALUES LESS THAN ('2024-06-01') ,
 PARTITION p54 VALUES LESS THAN ('2024-07-01') ,
 PARTITION p55 VALUES LESS THAN ('2024-08-01') ,
 PARTITION p56 VALUES LESS THAN ('2024-09-01') ,
 PARTITION p57 VALUES LESS THAN ('2024-10-01') ,
 PARTITION p58 VALUES LESS THAN ('2024-11-01') ,
 PARTITION p59 VALUES LESS THAN ('2024-12-01') ,
 PARTITION p60 VALUES LESS THAN (MAXVALUE) ) */;

--
-- Table structure for tb_payment_channel_info
--
CREATE TABLE  if not exists tb_payment_channel_info
(
 id int NOT NULL AUTO_INCREMENT,
 channel_id varchar(32) DEFAULT NULL COMMENT '渠道标识',
 en_name varchar(64) NOT NULL COMMENT '英文名称',
 cn_name varchar(32) NOT NULL COMMENT '中文名称',
 support_merchant_type varchar(255) DEFAULT NULL COMMENT '支持的商户类型,多类型时用逗号分开，空值则表示都支持，由产品进行维护',
 desc varchar(128) DEFAULT NULL,
 type tinyint DEFAULT NULL COMMENT '渠道类型(0:入款,1:出款,2:其他)',
 code varchar(16) NOT NULL COMMENT '渠道编码',
 support_sub_merchant_mode varchar(64) DEFAULT NULL COMMENT '是否支持二级商户，N，不支持；Y，支持',
 create_sub_merchant_mode varchar(32) DEFAULT NULL COMMENT '创建二级商户模式,ONLINE-线上;OFFLINE-线下',
 contracting_subject varchar(64) DEFAULT NULL COMMENT '签约主体，1-Cybershell，2-Funtech，3-PMMax, 5-DigiMax，6-Silot，7-PTSmart, 8-Ampay',
 refund_support tinyint NOT NULL COMMENT '是否支持退款(0:支持，1:不支持)',
 cancel_support tinyint NOT NULL DEFAULT '1' COMMENT '是否支持撤销(0:支持，1:不支持)',
 trans_qry_support tinyint NOT NULL COMMENT '是否支持交易查询(0:支持,1:不支持)',
 callback_check_signature tinyint NOT NULL DEFAULT '0' COMMENT '回调通知是否验签(0:验签,1:不验签)',
 status tinyint NOT NULL COMMENT '渠道状态(0:可用,1:不可用)',
 account_json text COMMENT '账户相关信息，json格式',
 config_json text NOT NULL COMMENT '扩展字段，json格式',
 map_code_group_id varchar(64) DEFAULT NULL COMMENT '映射响应码分组ID',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
 creator varchar(50) NOT NULL DEFAULT 'system',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ,
 modifier varchar(50) NOT NULL DEFAULT 'system',
 is_deleted char(1) NOT NULL DEFAULT 'N',
 PRIMARY KEY (id) ,
 UNIQUE KEY INDEX_72eb66a0543041e0a703120e23e897dd (code,type)
) ;

--
-- Table structure for tb_payment_method
--
CREATE TABLE  if not exists tb_payment_method
(
 id int NOT NULL AUTO_INCREMENT,
 product_code varchar(16) DEFAULT NULL COMMENT '收单产品编码',
 country char(2) DEFAULT NULL,
 method_code varchar(16) DEFAULT NULL,
 method_sub_code varchar(32) DEFAULT NULL,
 method_type tinyint NOT NULL COMMENT '支付方式类型(0:入款,1:出款)',
 desc varchar(128) DEFAULT NULL,
 category varchar(64) DEFAULT NULL,
 method_en_name varchar(64) DEFAULT NULL,
 method_cn_name varchar(64) DEFAULT NULL,
 priority tinyint NOT NULL DEFAULT '0',
 channel_code varchar(16) DEFAULT NULL,
 config_json tinytext ,
 status tinyint DEFAULT '0' COMMENT '支付方式状态(0:可用,1:不可用)',
 work_time_type tinyint DEFAULT '0' COMMENT '0-无限制 1-每天固定时间 2-工作日24小时(周一至周五) 3-工作日的固定时间',
 work_day_start_time varchar(8) DEFAULT NULL COMMENT '当天的开始时间(HH:mm:ss)-当地时间',
 work_day_end_time varchar(8) DEFAULT NULL COMMENT '当天的结束时间(HH:mm:ss)-当地时间',
 zone_time_for_hours varchar(4) DEFAULT NULL COMMENT '相对UTC时间的偏移量，单位为小时，支持一位小数，可为null',
 maintenance_start_time datetime DEFAULT NULL COMMENT '维护开始时间，可为null',
 maintenance_end_time datetime DEFAULT NULL COMMENT '维护结束时间，可为null',
 amount_limit_type tinyint DEFAULT '0' COMMENT '0-为无限制 1-上下限制(包含上下限值) 2-为定额(AmountLimit) 3-为固定额度的倍数(AmountLimit)',
 maximum_amount bigint DEFAULT '0' COMMENT '最大金额，0为无限制，单位为分',
 minimum_amount bigint DEFAULT '0' COMMENT '最小金额，0为无限制，单位为分',
 amount_limit varchar(255) DEFAULT NULL COMMENT 'amountLimitType为2时，用“,”号分开，单位为分',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
 creator varchar(50) NOT NULL DEFAULT 'system',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ,
 modifier varchar(50) NOT NULL DEFAULT 'system',
 is_deleted char(1) NOT NULL DEFAULT 'N',
 monitor_status tinyint DEFAULT '0' COMMENT '监控状态 0:正常 1:已下线 2:维护中 3:渠道异常',
 monitor_remark varchar(512) DEFAULT NULL COMMENT '监控备注',
 PRIMARY KEY (id) ,
 UNIQUE KEY INDEX_1515255ed76a4c64a3bca1fb2b1c2f94 (country,method_code,method_sub_code,channel_code,product_code)
) ;

--
-- Table structure for tb_payment_settlement
--
CREATE TABLE  if not exists tb_payment_settlement
(
 id bigint NOT NULL AUTO_INCREMENT,
 product_code varchar(16) DEFAULT NULL COMMENT '收单产品编码',
 pay_order_no varchar(64) NOT NULL COMMENT '支付单流水号',
 pay_channel_order_no varchar(64) NOT NULL COMMENT '渠道流水号',
 pay_channel_code varchar(20) NOT NULL COMMENT '支付渠道编码',
 track_no varchar(64) DEFAULT 'NA' COMMENT '系统跟踪号',
 method_code varchar(16) NOT NULL COMMENT '支付方式编码',
 method_sub_code varchar(32) NOT NULL COMMENT '支付方式二级编码',
 country char(2) NOT NULL COMMENT '国家编号',
 currency char(3) NOT NULL COMMENT '交易币种',
 user_id varchar(64) DEFAULT NULL COMMENT '用户唯一标识',
 pay_amount decimal(15, 0) NOT NULL COMMENT '支付金额(以分为单位)',
 status tinyint NOT NULL DEFAULT '0' COMMENT '结算状态码,(9:待申请，0 申请中 1:申请成功，2:申请失败)',
 pay_third_channel_no varchar(128) DEFAULT NULL COMMENT '第三方支付渠道流水号',
 third_channel_state_no varchar(128) DEFAULT NULL COMMENT '第三方结算订单号',
 third_channel_state_txn_id varchar(256) DEFAULT NULL COMMENT '第三方结算流水号',
 event_code varchar(10) NOT NULL DEFAULT 'INIT' COMMENT '状态机事件码',
 response_code varchar(64) DEFAULT NULL COMMENT '请求渠道返回code',
 response_msg varchar(256) DEFAULT NULL COMMENT '请求渠道返回msg',
 response_json mediumtext COMMENT '请求渠道返回json',
 add_field1 varchar(32) DEFAULT NULL COMMENT '扩展字段1',
 add_field2 varchar(64) DEFAULT NULL COMMENT '扩展字段2',
 add_field3 varchar(128) DEFAULT NULL COMMENT '扩展字段3',
 add_field4 varchar(32) DEFAULT NULL COMMENT '扩展字段4',
 add_field5 varchar(64) DEFAULT NULL COMMENT '扩展字段5',
 add_field6 varchar(256) DEFAULT NULL COMMENT '扩展字段6',
 creator varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间UTC',
 modifier varchar(50) NOT NULL DEFAULT 'system' COMMENT '修改人',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间UTC',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '是否逻辑删除(N：未删除，Y：已删除)',
 PRIMARY KEY (id) ,
 UNIQUE KEY INDEX_62eab7217485454da7dc99669e7d812d (pay_channel_order_no) ,
 KEY INDEX_af24b3c93f8142ba8b914c8f0466f257 (third_channel_state_no)
) COMMENT='支付渠道结算表';

--
-- Table structure for tb_payment_swift_notify_info
--
CREATE TABLE  if not exists tb_payment_swift_notify_info
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT '0-0-0 id',
 creator varchar(32) NOT NULL DEFAULT 'system' COMMENT '0-0-0 创建者',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0 创建时间',
 modifier varchar(32) NOT NULL DEFAULT 'system' COMMENT '0-0-0 修改者',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0 修改时间',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '0-0-0 是否逻辑删除(N/Y)',
 source varchar(32) DEFAULT NULL COMMENT '3-2-5 信息来源',
 org_id varchar(64) DEFAULT NULL COMMENT '3-2-5 我方主体名',
 update_time datetime(3) DEFAULT NULL COMMENT '3-2-5 更新时间',
 country varchar(32) DEFAULT NULL COMMENT '3-2-5 账号国家',
 payment_type varchar(16) DEFAULT NULL COMMENT '3-2-5 交易方向(INCOMING/OUTGOING)',
 biz_order_no varchar(100) DEFAULT NULL COMMENT '3-2-5 交易订单号',
 bank_order_no varchar(100) DEFAULT NULL COMMENT '3-2-5 银行订单号',
 channel_pay_commit_no varchar(100) DEFAULT NULL COMMENT '3-2-5 渠道提交单号',
 response_code varchar(32) DEFAULT NULL COMMENT '3-2-5 响应码',
 response_msg varchar(100) DEFAULT NULL COMMENT '3-2-5 响应描述',
 intermediary_bank varchar(100) DEFAULT NULL COMMENT '3-2-5 所到中间行',
 amount decimal(20, 4) DEFAULT NULL COMMENT '3-2-5 交易金额',
 currency varchar(10) DEFAULT NULL COMMENT '3-2-5 交易币种',
 payer_name varchar(100) DEFAULT NULL COMMENT '3-2-1 付款方账户',
 payer_bic varchar(100) DEFAULT NULL COMMENT '3-2-5 付款方BIC',
 account_no varchar(100) DEFAULT NULL COMMENT '3-2-1 收款方账号',
 account_currency varchar(10) DEFAULT NULL COMMENT '3-2-5 收款方币种',
 request_body mediumtext COMMENT '3-2-5 请求体',
 PRIMARY KEY (id),
 UNIQUE KEY INDEX_f7f529dc36ec46b38be966f39dece997 (source,biz_order_no),
 KEY INDEX_08cca5bcc381488ab11ac2841d18dd4e (biz_order_no),
 KEY INDEX_af98d63df3f64abbb14fe1ae337cd828 (bank_order_no),
 KEY INDEX_2f4e36e8cb5d44b084a457425aad8f4f (channel_pay_commit_no)
) COMMENT='swift回调通知信息表';

--
-- Table structure for tb_payout_channel_order
--
CREATE TABLE  if not exists tb_payout_channel_order
(
 id bigint NOT NULL AUTO_INCREMENT,
 product_code varchar(16) DEFAULT NULL COMMENT '收单产品编码',
 payout_channel_order_no varchar(64) NOT NULL COMMENT '付款渠道流水号',
 pay_order_no varchar(64) NOT NULL COMMENT '付款支付单流水号',
 channel_code varchar(20) NOT NULL COMMENT '付款渠道编码',
 track_no varchar(64) DEFAULT 'NA' COMMENT '系统跟踪号',
 method_code varchar(16) NOT NULL,
 method_sub_code varchar(32) NOT NULL,
 user_id varchar(64) DEFAULT NULL COMMENT '用户唯一标识',
 amount decimal(15, 0) NOT NULL COMMENT '付款金额',
 currency char(3) NOT NULL COMMENT '交易币种',
 country char(2) NOT NULL COMMENT '国家编号',
 expiry_seconds int DEFAULT '0' COMMENT '交易过期秒数,0代表未设置过期秒数',
 remark varchar(256) DEFAULT NULL COMMENT '备注字段',
 note varchar(128) DEFAULT NULL COMMENT '出款的备注',
 status tinyint NOT NULL DEFAULT '0' COMMENT '渠道付款状态码,(0：待付款，1：付款成功，2：付款失败)',
 pay_third_channel_no varchar(128) DEFAULT NULL COMMENT '第三方支付渠道流水号',
 pay_third_bank_txn_id varchar(128) DEFAULT NULL COMMENT '第三银行txnid',
 event_code varchar(10) NOT NULL DEFAULT 'INIT',
 response_code varchar(64) DEFAULT NULL COMMENT '请求渠道返回支付结果对应code',
 mapped_code varchar(10) DEFAULT NULL COMMENT '统一映射错误码',
 response_msg mediumtext COMMENT '请求渠道返回msg',
 response_json mediumtext COMMENT '请求渠道返回json',
 payee_type varchar(10) DEFAULT NULL COMMENT '收款人账号类型(0-UPI,1-银行卡)',
 payee_account varchar(64) NOT NULL COMMENT '收款人账户',
 payee_bank_name varchar(64) DEFAULT NULL,
 payee_bank_code varchar(64) DEFAULT NULL,
 payee_account_name varchar(64) DEFAULT NULL COMMENT '收款人账户名称',
 extra_json mediumtext,
 add_field1 varchar(32) DEFAULT NULL COMMENT '扩展字段1',
 add_field2 varchar(64) DEFAULT NULL COMMENT '扩展字段2',
 add_field3 varchar(128) DEFAULT NULL COMMENT '扩展字段3',
 add_field4 varchar(32) DEFAULT NULL COMMENT '扩展字段4',
 add_field5 varchar(64) DEFAULT NULL COMMENT '扩展字段5',
 add_field6 varchar(256) DEFAULT NULL COMMENT '扩展字段6',
 creator varchar(50) NOT NULL DEFAULT 'system',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
 modifier varchar(50) NOT NULL DEFAULT 'system',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ,
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '是否逻辑删除',
 PRIMARY KEY (id) ,
 UNIQUE KEY INDEX_8a3a208f17404a8ab33aa4e596c6e429 (payout_channel_order_no) ,
 UNIQUE KEY INDEX_e2ef5fa1c8934b74a078c0e508f0552f (pay_order_no),
 KEY INDEX_29fdf65bac5444ae853b2383fd0a8d7f (create_time,status,channel_code)
) COMMENT='付款渠道流水表';

--
-- Table structure for tb_payout_channel_order_history
--
CREATE TABLE  if not exists tb_payout_channel_order_history
(
 id bigint NOT NULL AUTO_INCREMENT,
 product_code varchar(16) DEFAULT NULL COMMENT '收单产品编码',
 payout_channel_order_no varchar(64) NOT NULL COMMENT '付款渠道流水号',
 pay_order_no varchar(64) NOT NULL COMMENT '付款支付单流水号',
 channel_code varchar(20) NOT NULL COMMENT '付款渠道编码',
 track_no varchar(64) DEFAULT 'NA' COMMENT '系统跟踪号',
 method_code varchar(16) NOT NULL,
 method_sub_code varchar(32) NOT NULL,
 user_id varchar(64) DEFAULT NULL COMMENT '用户唯一标识',
 amount decimal(15, 0) NOT NULL COMMENT '付款金额',
 currency char(3) NOT NULL COMMENT '交易币种',
 country char(2) NOT NULL COMMENT '国家编号',
 expiry_seconds int DEFAULT '0' COMMENT '交易过期秒数,0代表未设置过期秒数',
 remark varchar(256) DEFAULT NULL COMMENT '备注字段',
 note varchar(128) DEFAULT NULL COMMENT '出款的备注',
 status tinyint NOT NULL DEFAULT '0' COMMENT '渠道付款状态码,(0：待付款，1：付款成功，2：付款失败)',
 pay_third_channel_no varchar(128) DEFAULT NULL COMMENT '第三方支付渠道流水号',
 pay_third_bank_txn_id varchar(128) DEFAULT NULL COMMENT '第三银行txnid',
 event_code varchar(10) NOT NULL DEFAULT 'INIT',
 response_code varchar(64) DEFAULT NULL COMMENT '请求渠道返回支付结果对应code',
 mapped_code varchar(10) DEFAULT NULL COMMENT '统一映射错误码',
 response_msg mediumtext COMMENT '请求渠道返回msg',
 response_json mediumtext COMMENT '请求渠道返回json',
 payee_type varchar(10) DEFAULT NULL COMMENT '收款人账号类型(0-UPI,1-银行卡)',
 payee_account varchar(64) NOT NULL COMMENT '收款人账户',
 payee_bank_name varchar(64) DEFAULT NULL,
 payee_bank_code varchar(64) DEFAULT NULL,
 payee_account_name varchar(64) DEFAULT NULL COMMENT '收款人账户名称',
 extra_json mediumtext,
 add_field1 varchar(32) DEFAULT NULL COMMENT '扩展字段1',
 add_field2 varchar(64) DEFAULT NULL COMMENT '扩展字段2',
 add_field3 varchar(128) DEFAULT NULL COMMENT '扩展字段3',
 add_field4 varchar(32) DEFAULT NULL COMMENT '扩展字段4',
 add_field5 varchar(64) DEFAULT NULL COMMENT '扩展字段5',
 add_field6 varchar(256) DEFAULT NULL COMMENT '扩展字段6',
 creator varchar(50) NOT NULL DEFAULT 'system',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
 modifier varchar(50) NOT NULL DEFAULT 'system',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ,
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '是否逻辑删除',
 PRIMARY KEY (id, create_time) ,
 UNIQUE KEY INDEX_39e638716b464be7b837961f4b54ec2d (payout_channel_order_no,create_time) ,
 UNIQUE KEY INDEX_1cd65472d08649c99076bd88a15bf7fd (pay_order_no,create_time),
 KEY INDEX_f37c8fe373cc4a81a71e6279fe4f8438 (create_time,status,channel_code)
) COMMENT='付款渠道流水表'
/*!50500 PARTITION BY RANGE COLUMNS(create_time)
(PARTITION p0 VALUES LESS THAN ('2020-01-01') ,
 PARTITION p1 VALUES LESS THAN ('2020-02-01') ,
 PARTITION p2 VALUES LESS THAN ('2020-03-01') ,
 PARTITION p3 VALUES LESS THAN ('2020-04-01') ,
 PARTITION p4 VALUES LESS THAN ('2020-05-01') ,
 PARTITION p5 VALUES LESS THAN ('2020-06-01') ,
 PARTITION p6 VALUES LESS THAN ('2020-07-01') ,
 PARTITION p7 VALUES LESS THAN ('2020-08-01') ,
 PARTITION p8 VALUES LESS THAN ('2020-09-01') ,
 PARTITION p9 VALUES LESS THAN ('2020-10-01') ,
 PARTITION p10 VALUES LESS THAN ('2020-11-01') ,
 PARTITION p11 VALUES LESS THAN ('2020-12-01') ,
 PARTITION p12 VALUES LESS THAN ('2021-01-01') ,
 PARTITION p13 VALUES LESS THAN ('2021-02-01') ,
 PARTITION p14 VALUES LESS THAN ('2021-03-01') ,
 PARTITION p15 VALUES LESS THAN ('2021-04-01') ,
 PARTITION p16 VALUES LESS THAN ('2021-05-01') ,
 PARTITION p17 VALUES LESS THAN ('2021-06-01') ,
 PARTITION p18 VALUES LESS THAN ('2021-07-01') ,
 PARTITION p19 VALUES LESS THAN ('2021-08-01') ,
 PARTITION p20 VALUES LESS THAN ('2021-09-01') ,
 PARTITION p21 VALUES LESS THAN ('2021-10-01') ,
 PARTITION p22 VALUES LESS THAN ('2021-11-01') ,
 PARTITION p23 VALUES LESS THAN ('2021-12-01') ,
 PARTITION p24 VALUES LESS THAN ('2022-01-01') ,
 PARTITION p25 VALUES LESS THAN ('2022-02-01') ,
 PARTITION p26 VALUES LESS THAN ('2022-03-01') ,
 PARTITION p27 VALUES LESS THAN ('2022-04-01') ,
 PARTITION p28 VALUES LESS THAN ('2022-05-01') ,
 PARTITION p29 VALUES LESS THAN ('2022-06-01') ,
 PARTITION p30 VALUES LESS THAN ('2022-07-01') ,
 PARTITION p31 VALUES LESS THAN ('2022-08-01') ,
 PARTITION p32 VALUES LESS THAN ('2022-09-01') ,
 PARTITION p33 VALUES LESS THAN ('2022-10-01') ,
 PARTITION p34 VALUES LESS THAN ('2022-11-01') ,
 PARTITION p35 VALUES LESS THAN ('2022-12-01') ,
 PARTITION p36 VALUES LESS THAN ('2023-01-01') ,
 PARTITION p37 VALUES LESS THAN ('2023-02-01') ,
 PARTITION p38 VALUES LESS THAN ('2023-03-01') ,
 PARTITION p39 VALUES LESS THAN ('2023-04-01') ,
 PARTITION p40 VALUES LESS THAN ('2023-05-01') ,
 PARTITION p41 VALUES LESS THAN ('2023-06-01') ,
 PARTITION p42 VALUES LESS THAN ('2023-07-01') ,
 PARTITION p43 VALUES LESS THAN ('2023-08-01') ,
 PARTITION p44 VALUES LESS THAN ('2023-09-01') ,
 PARTITION p45 VALUES LESS THAN ('2023-10-01') ,
 PARTITION p46 VALUES LESS THAN ('2023-11-01') ,
 PARTITION p47 VALUES LESS THAN ('2023-12-01') ,
 PARTITION p48 VALUES LESS THAN ('2024-01-01') ,
 PARTITION p49 VALUES LESS THAN ('2024-02-01') ,
 PARTITION p50 VALUES LESS THAN ('2024-03-01') ,
 PARTITION p51 VALUES LESS THAN ('2024-04-01') ,
 PARTITION p52 VALUES LESS THAN ('2024-05-01') ,
 PARTITION p53 VALUES LESS THAN ('2024-06-01') ,
 PARTITION p54 VALUES LESS THAN ('2024-07-01') ,
 PARTITION p55 VALUES LESS THAN ('2024-08-01') ,
 PARTITION p56 VALUES LESS THAN ('2024-09-01') ,
 PARTITION p57 VALUES LESS THAN ('2024-10-01') ,
 PARTITION p58 VALUES LESS THAN ('2024-11-01') ,
 PARTITION p59 VALUES LESS THAN ('2024-12-01') ,
 PARTITION p60 VALUES LESS THAN (MAXVALUE) ) */;

--
-- Table structure for tb_refund_channel_order
--
CREATE TABLE  if not exists tb_refund_channel_order
(
 id bigint NOT NULL AUTO_INCREMENT,
 refund_channel_order_no varchar(64) NOT NULL COMMENT '退款渠道流水号',
 refund_order_no varchar(64) NOT NULL COMMENT '聚合退款流水号',
 org_pay_order_no varchar(64) NOT NULL COMMENT '原支付单流水号',
 org_pay_channel_order_no varchar(64) NOT NULL COMMENT '原支付渠道流水号',
 channel_code varchar(20) NOT NULL COMMENT '渠道编码',
 track_no varchar(64) DEFAULT 'NA' COMMENT '系统跟踪号',
 user_id varchar(64) DEFAULT NULL COMMENT '用户user_id',
 amount decimal(12, 0) NOT NULL COMMENT '退款支付金额',
 currency char(3) NOT NULL COMMENT '交易币种',
 country char(2) NOT NULL COMMENT '国家编号',
 status tinyint NOT NULL DEFAULT '0' COMMENT '渠道退款状态码,(0：退款申请，1：退款成功，2：退款失败)',
 refund_third_bank_txn_id varchar(128) DEFAULT NULL COMMENT '退款第三银行txnid',
 refund_third_channel_no varchar(128) DEFAULT NULL COMMENT '退款第三方支付渠道流水号',
 remark varchar(256) DEFAULT NULL COMMENT '退款备注',
 add_field1 varchar(32) DEFAULT NULL COMMENT '扩展字段1',
 add_field2 varchar(64) DEFAULT NULL COMMENT '扩展字段2',
 add_field3 varchar(128) DEFAULT NULL COMMENT '扩展字段3',
 add_field4 varchar(32) DEFAULT NULL COMMENT '扩展字段4',
 add_field5 varchar(64) DEFAULT NULL COMMENT '扩展字段5',
 add_field6 varchar(256) DEFAULT NULL COMMENT '扩展字段6',
 response_code varchar(64) DEFAULT NULL COMMENT '请求渠道返回code',
 response_msg varchar(256) DEFAULT NULL COMMENT '请求渠道返回msg',
 response_json mediumtext COMMENT '请求渠道返回json',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
 creator varchar(50) NOT NULL DEFAULT 'system',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ,
 modifier varchar(50) NOT NULL DEFAULT 'system',
 is_deleted char(1) NOT NULL DEFAULT 'N',
 PRIMARY KEY (id) ,
 UNIQUE KEY INDEX_7ea11cfbbf8347a0b21de3fade80299a (refund_channel_order_no),
 UNIQUE KEY INDEX_1c6e325ba42a4cfdadd798099be4c5b4 (refund_order_no)
) COMMENT='退款渠道流水表';

--
-- Table structure for tb_report_channel_limit
--
CREATE TABLE  if not exists tb_report_channel_limit
(
 id int NOT NULL AUTO_INCREMENT COMMENT '0-0-0-主键',
 channel_report_code varchar(64) NOT NULL COMMENT '4-2-1-渠道报备编码',
 factor_key varchar(32) NOT NULL COMMENT '4-1-1-类型',
 factor_value varchar(512) NOT NULL COMMENT '4-1-1-值',
 tag_code varchar(32) NOT NULL COMMENT '4-1-1-命中该限制的报备标签',
 remark mediumtext COMMENT '4-1-1-说明',
 creator varchar(32) NOT NULL DEFAULT 'system' COMMENT '4-1-1-创建人',
 modifier varchar(32) NOT NULL DEFAULT 'system' COMMENT '4-1-1-修改人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '4-1-1-创建时间',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '4-1-1-修改时间',
 is_deleted tinyint NOT NULL DEFAULT '0' COMMENT '4-1-1-逻辑删除(0:未删除,1:已删除)',
 PRIMARY KEY (id),
 KEY INDEX_51b70c83a8f740af91a6e17f3a086697 (channel_report_code,factor_key,is_deleted)
) COMMENT='报备渠道限制';

--
-- Table structure for tb_report_channel_material
--
CREATE TABLE  if not exists tb_report_channel_material
(
 id int NOT NULL AUTO_INCREMENT COMMENT '0-0-0-主键',
 channel_report_code varchar(64) NOT NULL COMMENT '4-2-1-渠道报备编码',
 code varchar(16) NOT NULL COMMENT '4-1-1-材料编码(与商户服务保持一致)',
 type varchar(16) NOT NULL COMMENT '4-1-1-材料类型(FILE:文件,TEXT:文本)',
 remark mediumtext COMMENT '4-2-1-描述',
 required tinyint NOT NULL DEFAULT '1' COMMENT '4-1-1-是否可选(0:可选,1:必填,2:条件必填)',
 required_condition text COMMENT '4-1-1-必填的条件',
 validate_condition text COMMENT '4-1-1-材料校验规则',
 url mediumtext COMMENT '4-5-2-样例文件(仅文件会使用)',
 creator varchar(32) NOT NULL DEFAULT 'system' COMMENT '4-1-1-创建人',
 modifier varchar(32) NOT NULL DEFAULT 'system' COMMENT '4-1-1-修改人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '4-1-1-创建时间',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '4-1-1-修改时间',
 is_deleted tinyint NOT NULL DEFAULT '0' COMMENT '4-1-1-逻辑删除(0:未删除,1:已删除)',
 PRIMARY KEY (id),
 KEY INDEX_b939861c9e534f35bea602f504ee400a (channel_report_code,code,is_deleted)
) COMMENT='报备材料';

--
-- Table structure for tb_report_contract
--
CREATE TABLE  if not exists tb_report_contract
(
 id int NOT NULL AUTO_INCREMENT COMMENT '0-0-0-主键',
 kyb_no varchar(64) NOT NULL COMMENT '3-1-1-KYB_NO',
 merchant_no varchar(32) NOT NULL COMMENT '3-1-1-商户号',
 cashier_product_code varchar(16) NOT NULL COMMENT '4-1-1-收银产品',
 shared_merchant_no varchar(32) DEFAULT NULL COMMENT '3-1-1-共享的商户',
 is_virtual tinyINT NOT NULL DEFAULT '0' COMMENT '4-1-1-是否是虚拟签约关系(0:否,1:是)',
 creator varchar(32) NOT NULL DEFAULT 'system' COMMENT '4-1-1-创建人',
 modifier varchar(32) NOT NULL DEFAULT 'system' COMMENT '4-1-1-修改人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '4-1-1-创建时间',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '4-1-1-修改时间',
 is_deleted tinyint NOT NULL DEFAULT '0' COMMENT '4-1-1-逻辑删除(0:未删除,1:已删除)',
 PRIMARY KEY (id),
 KEY INDEX_4ed09d9ec5954966bdb9447a0ce33156 (merchant_no,cashier_product_code,is_deleted),
 KEY INDEX_1c9c808f847143728ffc48d111b7f192 (kyb_no,cashier_product_code,is_deleted)
) COMMENT='商户签约关系';

--
-- Table structure for tb_report_order_audit_log
--
CREATE TABLE  if not exists tb_report_order_audit_log
(
 id int NOT NULL AUTO_INCREMENT COMMENT '0-0-0-主键',
 report_no varchar(64) NOT NULL COMMENT '4-1-1-报备申请单号',
 from_state tinyint DEFAULT NULL COMMENT '4-1-1-初始状态',
 to_state tinyint DEFAULT NULL COMMENT '4-1-1-结束状态',
 operate_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '4-1-1-操作时间',
 operator varchar(64) NOT NULL DEFAULT 'system' COMMENT '4-1-1-操作人',
 PRIMARY KEY (id),
 KEY INDEX_b02ed0e851f24b9a945e3d1068ebdd05 (report_no)
) ;

--
-- Table structure for tb_routing_group
--
CREATE TABLE  if not exists tb_routing_group
(
 id int NOT NULL AUTO_INCREMENT,
 routing_group_id varchar(64) NOT NULL COMMENT '路由组ID',
 routing_group_name varchar(128) NOT NULL COMMENT '路由组名称',
 remark varchar(512) DEFAULT NULL COMMENT '备注',
 creator varchar(50) NOT NULL COMMENT '创建人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间UTC',
 modifier varchar(50) NOT NULL COMMENT '修改人',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间UTC',
 is_deleted char(1) NOT NULL COMMENT '是否逻辑删除(N：未删除，Y：已删除)',
 PRIMARY KEY (id),
 UNIQUE KEY INDEX_82ee38fa31f249819f53aa1693604989 (routing_group_id)
) COMMENT='路由组表';

--
-- Table structure for tb_routing_group_mapping
--
CREATE TABLE  if not exists tb_routing_group_mapping
(
 id int NOT NULL AUTO_INCREMENT,
 routing_group_id varchar(64) NOT NULL COMMENT '路由组ID',
 merchant_id varchar(128) NOT NULL COMMENT 'SHAREit分配给商户的id',
 biz_product_code varchar(128) NOT NULL COMMENT '业务产品编号',
 remark varchar(512) DEFAULT NULL COMMENT '备注',
 creator varchar(50) NOT NULL COMMENT '创建者',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间UTC',
 modifier varchar(50) NOT NULL COMMENT '修改者',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间UTC',
 is_deleted char(1) NOT NULL COMMENT '是否逻辑删除(N：未删除，Y：已删除)',
 PRIMARY KEY (id),
 UNIQUE KEY INDEX_0e4f34e5ebf740028de46d06acdcf6a6 (merchant_id,routing_group_id,biz_product_code)
) COMMENT='路由组配置表';

--
-- Table structure for tb_routing_grouping_data
--
CREATE TABLE  if not exists tb_routing_grouping_data
(
 id int NOT NULL AUTO_INCREMENT,
 group_id varchar(64) DEFAULT NULL COMMENT '组ID',
 group_type varchar(64) NOT NULL COMMENT '数据组类型',
 group_name varchar(128) NOT NULL COMMENT '数据组名称',
 group_value varchar(10240) NOT NULL COMMENT '数据组对应的值，逗号隔开',
 remark varchar(512) DEFAULT NULL COMMENT '备注',
 creator varchar(50) NOT NULL COMMENT '创建者',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间UTC',
 modifier varchar(50) NOT NULL COMMENT '修改者',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间UTC',
 is_deleted char(1) NOT NULL COMMENT '是否逻辑删除(N：未删除，Y：已删除)',
 PRIMARY KEY (id),
 UNIQUE KEY INDEX_6cc3e340b6c3484db85b0011287736ef (group_id)
) COMMENT='分组数据表';

--
-- Table structure for tb_routing_rule_info
--
CREATE TABLE  if not exists tb_routing_rule_info
(
 id int NOT NULL AUTO_INCREMENT,
 routing_id varchar(64) NOT NULL COMMENT '路由ID',
 routing_name varchar(128) NOT NULL COMMENT '路由名称',
 routing_group_id varchar(64) NOT NULL COMMENT '路由组ID',
 channel_code varchar(20) NOT NULL COMMENT '渠道编号',
 pay_method_code varchar(2048) NOT NULL COMMENT '支持的一级支付方式，多选时，逗号隔开，* 表示不参与路由',
 pay_method_sub_code_group_id varchar(64) NOT NULL COMMENT '二级支付方式数据组ID',
 payment_type varchar(8) NOT NULL COMMENT '支付类型(00:入款,01:出款)',
 priority tinyint DEFAULT NULL COMMENT '优先级，数值越小优先级越高',
 country varchar(32) NOT NULL COMMENT '支持国家，单个，* 表示不参与路由',
 currency varchar(2048) NOT NULL COMMENT '支持币种，多选时，逗号隔开，* 表示不参与路由',
 card_bin_group_id varchar(64) NOT NULL COMMENT '支持卡bin数据组ID',
 card_type varchar(64) NOT NULL COMMENT '支持卡类型，多选时，逗号隔开，* 表示不参与路由',
 mid_group_id varchar(64) NOT NULL COMMENT 'MID数据组ID',
 mcc_group_id varchar(2048) NOT NULL COMMENT 'MCC数据组ID',
 amount_sum_limit varchar(64) NOT NULL COMMENT '日限额，路由规则级限额（累加）',
 amount_range varchar(2048) NOT NULL COMMENT '单笔交易的限额（区间），多选时，逗号隔开，* 表示不参与路由',
 amount_scale varchar(24) NOT NULL COMMENT '金额规模约束（如：1、10、100），* 表示不参与路由',
 terminal varchar(256) NOT NULL COMMENT '支持终端类型，多选时，逗号隔开，* 表示不参与路由',
 trade_time varchar(1024) NOT NULL COMMENT '允许交易的时间段UTC，单选，* 表示不参与路由',
 reserve_field varchar(64) DEFAULT NULL COMMENT '保留字段',
 effective_time varchar(512) NOT NULL COMMENT '生效时间UTC，如（2020-11-26 15:26:21）',
 is_available char(1) NOT NULL COMMENT '是否可用(N：不可用，Y：可用)',
 remark varchar(512) DEFAULT NULL COMMENT '备注',
 creator varchar(50) NOT NULL COMMENT '创建人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间UTC',
 modifier varchar(50) NOT NULL COMMENT '修改人',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间UTC',
 is_deleted char(1) NOT NULL COMMENT '是否逻辑删除(N：未删除，Y：已删除)',
 PRIMARY KEY (id),
 KEY INDEX_0ab757ed07e44cd6bd72581ff4d0b7e7 (routing_group_id)
) COMMENT='路由信息表';

--
-- Table structure for tb_sequence
--
CREATE TABLE  if not exists tb_sequence
(
 id int NOT NULL AUTO_INCREMENT COMMENT 'id',
 name varchar(128) NOT NULL COMMENT 'SEQUENCE 名称',
 value int NOT NULL,
 remark varchar(256) DEFAULT NULL COMMENT '备注',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 creator varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建者',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
 modifier varchar(50) NOT NULL DEFAULT 'system' COMMENT '修改者',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除标识(N/Y)',
 PRIMARY KEY (id) ,
 UNIQUE KEY INDEX_b65987dfa4ac46c69d8bd4f09a5a1f9b (name)
) ;

--
-- Table structure for tb_share_report_source_merchant_merchant_mapping
--
CREATE TABLE  if not exists tb_share_report_source_merchant_merchant_mapping
(
 id int NOT NULL AUTO_INCREMENT COMMENT '0-0-0-PK',
 channel_id varchar(32) NOT NULL DEFAULT '' COMMENT '4-2-1-渠道标识',
 entity varchar(64) DEFAULT '' COMMENT '4-2-1-签约主体,1-Cybershell,2-Funtech,3-PMMax, 5-DigiMax,6-Silot,7-PTSmart, 8-Ampay',
 source_merchant_id varchar(64) NOT NULL DEFAULT '' COMMENT '3-5-1-源商户号',
 source_sub_merchant_id varchar(64) NOT NULL COMMENT '3-1-1-源子商户号',
 merchant_id varchar(64) NOT NULL DEFAULT '' COMMENT '3-5-1-老商户号',
 sub_merchant_id varchar(64) NOT NULL COMMENT '3-1-1-子商户号',
 creator varchar(50) NOT NULL DEFAULT '' COMMENT '0-0-0-创建人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0-创建时间UTC',
 modifier varchar(50) NOT NULL DEFAULT '' COMMENT '0-0-0-修改人',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0-修改时间UTC',
 update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0-0-0-修改时间UTC',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '0-0-0-是否逻辑删除(N:未删除,Y:已删除)',
 PRIMARY KEY (id),
 UNIQUE KEY INDEX_bd6802d2cf1440a8a7116362aba6b576 (channel_id,entity,merchant_id)
) COMMENT='共享报备-源商户共享商户映射';

--
-- Table structure for tb_special_mapping_config
--
CREATE TABLE  if not exists tb_special_mapping_config
(
 id bigint NOT NULL AUTO_INCREMENT,
 country char(2) DEFAULT NULL COMMENT '国家',
 channel_code varchar(20) NOT NULL COMMENT '付款渠道编码',
 method_code varchar(16) DEFAULT NULL COMMENT '一级支付方式,配置*代表所有',
 method_sub_code varchar(32) DEFAULT NULL COMMENT '二级支付方式,配置*代表所有',
 need_preliminary_review char(1) NOT NULL DEFAULT 'N' COMMENT '是否需要初审',
 need_review char(1) NOT NULL DEFAULT 'N' COMMENT '是否需要复审',
 business_type varchar(32) DEFAULT NULL COMMENT '功能类型(PAYOUT_RETRY:出款重发,BOUNCE_BACK:退票)',
 status tinyint DEFAULT NULL COMMENT '状态认定',
 response_code varchar(64) DEFAULT NULL COMMENT '错误码认定',
 response_msg text COMMENT '错误描述认定,配置*代表所有',
 result_json tinytext COMMENT '处理结果',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
 is_deleted char(1) NOT NULL DEFAULT 'N' COMMENT '是否逻辑删除',
 PRIMARY KEY (id) ,
 KEY INDEX_2f58eff50a8741afa3546e1f5c2f422f (country,channel_code,method_code,method_sub_code,business_type)
) COMMENT='特殊状态处理表';

--
-- Table structure for tb_target_merchant
--
CREATE TABLE  if not exists tb_target_merchant
(
 id int NOT NULL AUTO_INCREMENT,
 channel_id varchar(32) NOT NULL COMMENT '渠道标识',
 contracting_subject varchar(64) DEFAULT NULL COMMENT '签约主体，1-Cybershell，2-Funtech，3-PMMax, 5-DigiMax，6-Silot，7-PTSmart, 8-Ampay',
 merchant_id varchar(64) NOT NULL COMMENT '商户号',
 sub_merchant_id varchar(64) NOT NULL DEFAULT '' COMMENT '3-1-1-子商户号',
 target_merchant_id varchar(255) NOT NULL COMMENT '3-1-1-目标商户号',
 target_merchant_mcc varchar(32) DEFAULT NULL COMMENT '目标商户号所属行业类别',
 target_merchant_name varchar(255) DEFAULT NULL COMMENT '目标商户号名称',
 target_merchant_address varchar(255) DEFAULT NULL COMMENT '目标商户号地址',
 target_merchant_city varchar(64) DEFAULT NULL COMMENT '目标商户号所在城市',
 target_merchant_country varchar(32) DEFAULT NULL COMMENT '目标商户号所在国家',
 target_merchant_post_code varchar(32) DEFAULT NULL COMMENT '目标商户号所在地邮编',
 target_merchant_phone varchar(64) DEFAULT NULL COMMENT '目标商户号电话',
 target_merchant_email varchar(255) DEFAULT NULL COMMENT '目标商户号邮箱',
 target_merchant_billing_descriptor varchar(255) DEFAULT NULL COMMENT '目标商户账单描述',
 target_merchant_secret_key varchar(2048) DEFAULT NULL COMMENT '目标商户号秘钥',
 target_merchant_info text COMMENT '2-4-1-目标商户信息',
 response_code varchar(64) DEFAULT NULL COMMENT '线上商户报备响应码',
 response_msg mediumtext COMMENT '线上商户报备响应信息',
 config_json varchar(512) DEFAULT NULL COMMENT '扩展信息，json格式',
 remark varchar(512) DEFAULT NULL COMMENT '备注',
 verify_status tinyint DEFAULT '0' COMMENT '验证状态：-1：无需验证，0：待验证，1：验证中，2：验证通过，3：验证未通过，4：验证失败',
 status char(1) NOT NULL COMMENT '状态(N：不可用，Y：可用，P：报备中，F：报备失败)',
 creator varchar(50) NOT NULL COMMENT '创建人',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间UTC',
 modifier varchar(50) NOT NULL COMMENT '修改人',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间UTC',
 is_deleted char(1) NOT NULL COMMENT '是否逻辑删除(N：未删除，Y：已删除)',
 PRIMARY KEY (id) ,
 KEY INDEX_6243a2a895b74953ab4fbafe9ed8b117 (channel_id,merchant_id)
) COMMENT='目标商户表';

--
-- Table structure for tb_target_merchant_verification
--
CREATE TABLE  if not exists tb_target_merchant_verification
(
 id bigint NOT NULL AUTO_INCREMENT,
 target_merchant_id bigint NOT NULL COMMENT '二级商户配置id',
 report_channel_id varchar(100) NOT NULL COMMENT '报备渠道',
 trade_no varchar(100) NOT NULL COMMENT '商户交易单号',
 merchant_no varchar(100) NOT NULL COMMENT '被套商户号',
 payment_type varchar(100) NOT NULL COMMENT '渠道类型 I:入款 O:出款 T:资金调拨 F:外汇 R:风控合规',
 country varchar(100) NOT NULL COMMENT '国家',
 payment_method_type varchar(100) NOT NULL COMMENT '支付方式类型',
 target_org varchar(100) NOT NULL COMMENT '目标机构',
 channel_code varchar(100) NOT NULL COMMENT '渠道编码',
 channel_method_code varchar(100) DEFAULT NULL COMMENT '渠道能力编码',
 channel_request_no varchar(100) DEFAULT NULL COMMENT '渠道请求单号',
 channel_commit_no varchar(100) DEFAULT NULL COMMENT '渠道提交号',
 status tinyint NOT NULL COMMENT '状态：0：待验证，1：验证中，2：验证完成',
 creator varchar(100) DEFAULT NULL COMMENT '验证人',
 create_time datetime DEFAULT NULL COMMENT '创建时间',
 update_time datetime DEFAULT NULL ,
 PRIMARY KEY (id),
 UNIQUE KEY INDEX_33ce753bfe7b4ed8be1e939eb7dac451 (trade_no) ,
 KEY INDEX_41bc141200ef4113bf96c56ed2678f14 (target_merchant_id)
) COMMENT='二级商户配置验证记录';

--
-- Table structure for tb_target_org_info
--
CREATE TABLE  if not exists tb_target_org_info
(
 org_code varchar(64) NOT NULL COMMENT '目标机构',
 business_key varchar(256) NOT NULL COMMENT '业务key:(根据实际业务拼接,用于查询对应的配置信息)',
 config_json text COMMENT '扩展配置',
 create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 modified_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (org_code, business_key)
) COMMENT='目标机构信息表';

--
-- Table structure for tb_validate_field
--
CREATE TABLE  if not exists tb_validate_field
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
 field_name varchar(64) NOT NULL COMMENT '字段名',
 display_name varchar(64) DEFAULT NULL COMMENT '字段显示名称',
 filed_length int DEFAULT NULL COMMENT '字段超长截取长度',
 product_code varchar(16) DEFAULT NULL COMMENT '产品码',
 country_code varchar(16) DEFAULT NULL COMMENT '国家码',
 channel_code varchar(64) DEFAULT NULL COMMENT '渠道编码',
 method_code varchar(64) DEFAULT NULL COMMENT '一级支付方式 或 目标机构 或 卡组',
 special_check_value varchar(64) DEFAULT NULL COMMENT '特殊校验参数JSON',
 rule_ids varchar(64) DEFAULT NULL COMMENT '校验规则id 对应 tb_payment_rule',
 status tinyint NOT NULL DEFAULT '1' COMMENT '状态 1：启用 0：禁用',
 create_time datetime DEFAULT CURRENT_TIMESTAMP,
 update_time datetime DEFAULT CURRENT_TIMESTAMP ,
 creator varchar(64) NOT NULL DEFAULT 'system' COMMENT '创建人',
 modifier varchar(64) NOT NULL DEFAULT 'system' COMMENT '修改人',
 source_field_name varchar(64) DEFAULT NULL,
 PRIMARY KEY (id) ,
 KEY INDEX_291fb0b7a4164f85944f80a1e9ecafd1 (channel_code,country_code)
) COMMENT='校验字段表';

--
-- Table structure for tb_validate_rule
--
CREATE TABLE  if not exists tb_validate_rule
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
 name varchar(64) DEFAULT NULL COMMENT '规则名称',
 rule text NOT NULL COMMENT '0-0-0-规则',
 type tinyint NOT NULL DEFAULT '1' COMMENT '规则类型： 1.正则匹配 2.正则提取 3.枚举 4.Mvel表达式 5.特殊逻辑',
 status tinyint DEFAULT '1' COMMENT '状态 1：启用 0：禁用',
 create_time datetime DEFAULT CURRENT_TIMESTAMP,
 update_time datetime DEFAULT CURRENT_TIMESTAMP ,
 param_names varchar(64) NOT NULL DEFAULT '' COMMENT '参数名称，用逗号隔开',
 creator varchar(64) NOT NULL DEFAULT 'system' COMMENT '创建人',
 modifier varchar(64) NOT NULL DEFAULT 'system' COMMENT '修改人',
 PRIMARY KEY (id) ,
 UNIQUE KEY INDEX_340494dcc17743b68cd4e17ea3c50df8 (name)
) COMMENT='校验规则表';

--
-- Table structure for tb_workflow_processing_record
--
CREATE TABLE  if not exists tb_workflow_processing_record
(
 id bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
 title varchar(128) DEFAULT NULL COMMENT '流程名称',
 business_key varchar(64) DEFAULT NULL COMMENT '工作流处理业务键',
 process_id varchar(128) DEFAULT NULL COMMENT '流程ID',
 process_status varchar(16) DEFAULT 'CREATING' COMMENT '处理状态：CREATING("创建中"),START_FAIL("发起失败"),START_ERROR("发起异常"),ING("审批中"),YES("同意"),NO("拒绝"),REVOKE("撤回"),STOP("流程终止");',
 callback text COMMENT 'WorkflowCallback',
 original_data mediumtext COMMENT '原始数据',
 new_data mediumtext COMMENT '提交新数据',
 msg varchar(256) DEFAULT NULL COMMENT '描述信息',
 status tinyINT NOT NULL DEFAULT '1' COMMENT '状态：0：处理完毕，1：处理中，2：临时保存',
 creator varchar(64) NOT NULL DEFAULT 'system' COMMENT '创建人',
 utc_create datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 modifier varchar(64) NOT NULL DEFAULT 'system' COMMENT '修改人',
 utc_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
 PRIMARY KEY (id),
 UNIQUE KEY INDEX_b6cc5063cef14b28acb514ecc434af53 (process_id),
 KEY INDEX_2cd809d2a64d4481a20982c9b1bd864f (business_key,title,status)
) COMMENT='工作流处理记录表';