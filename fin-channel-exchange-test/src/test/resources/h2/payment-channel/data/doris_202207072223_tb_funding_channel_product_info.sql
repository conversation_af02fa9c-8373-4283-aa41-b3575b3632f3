INSERT INTO tb_funding_channel_product_info (product_code,payment_method_type,payment_type,customer_type,status,create_time,modified_time) VALUES
	 ('CARDPAY201','CardPay','20','1',1,'2022-01-07 14:24:21','2022-01-07 14:24:21'),
	 ('NETBANKING201','NetBanking','20','1',1,'2022-01-07 14:24:21','2022-01-07 14:24:21'),
	 ('VIRTUALACCOUNT201','VirtualAccount','20','1',1,'2022-01-07 14:24:21','2022-01-07 14:24:21'),
	 ('BARCODE201','BarCode','20','1',1,'2022-01-07 14:24:21','2022-01-07 14:24:21'),
	 ('WALLET201','Wallet','20','1',1,'2022-01-07 14:24:21','2022-01-07 14:24:21'),
	 ('CARRIERBILLING201','CarrierBilling','20','1',1,'2022-01-07 14:24:21','2022-01-07 14:24:21'),
	 ('AUTODEBIT201','AutoDebit','20','1',1,'2022-01-07 14:24:21','2022-01-07 14:24:21'),
	 ('DIRECTDEBIT201','DirectDebit','20','1',1,'2022-01-07 14:24:21','2022-01-07 14:24:21'),
	 ('PREPAID201','PrePaid','20','1',1,'2022-01-07 14:24:21','2022-01-07 14:24:21'),
	 ('POINTSPAY201','PointsPay','20','1',1,'2022-01-07 14:24:21','2022-01-07 14:24:21');
INSERT INTO tb_funding_channel_product_info (product_code,payment_method_type,payment_type,customer_type,status,create_time,modified_time) VALUES
	 ('PAYLATER201','PayLater','20','1',1,'2022-01-07 14:24:21','2022-01-07 14:24:21'),
	 ('VIRTUALACCOUNT502','VirtualAccount','50','2',1,'2022-01-08 22:51:20','2022-01-08 22:51:20'),
	 ('CARDPAY101','CardPay','10','1',1,'2022-01-07 14:24:21','2022-01-07 14:24:21'),
	 ('NETBANKING101','NetBanking','10','1',1,'2022-01-07 14:24:21','2022-01-07 14:24:21'),
	 ('VIRTUALACCOUNT101','VirtualAccount','10','1',1,'2022-01-07 14:24:21','2022-01-07 14:24:21'),
	 ('BARCODE101','BarCode','10','1',1,'2022-01-07 14:24:21','2022-01-07 14:24:21'),
	 ('WALLET101','Wallet','10','1',1,'2022-01-07 14:24:21','2022-01-07 14:24:21'),
	 ('CARRIERBILLING101','CarrierBilling','10','1',1,'2022-01-07 14:24:21','2022-01-07 14:24:21'),
	 ('AUTODEBIT101','AutoDebit','10','1',1,'2022-01-07 14:24:21','2022-01-07 14:24:21'),
	 ('DIRECTDEBIT101','DirectDebit','10','1',1,'2022-01-07 14:24:21','2022-01-07 14:24:21');
INSERT INTO tb_funding_channel_product_info (product_code,payment_method_type,payment_type,customer_type,status,create_time,modified_time) VALUES
	 ('PREPAID101','PrePaid','10','1',1,'2022-01-07 14:24:21','2022-01-07 14:24:21'),
	 ('POINTSPAY101','PointsPay','10','1',1,'2022-01-07 14:24:21','2022-01-07 14:24:21'),
	 ('PAYLATER101','PayLater','10','1',1,'2022-01-07 14:24:21','2022-01-07 14:24:21'),
	 ('BANKTRANSFER101','BankTransfer','10','1',1,'2022-01-07 14:24:21','2022-01-07 14:24:21'),
	 ('BANKTRANSFER10','BankTransfer','10','*',1,'2022-01-07 14:24:21','2022-01-07 14:24:21'),
	 ('BANKTRANSFER201','BankTransfer','20','1',1,'2022-01-07 14:24:21','2022-01-07 14:24:21'),
	 ('BANKTRANSFER20','BankTransfer','20','*',1,'2022-01-07 14:24:21','2022-01-07 14:24:21'),
	 ('BANKTRANSFER502','BankTransfer','50','2',1,NULL,NULL),
	 ('NEW_WALLET201', 'WALLET', '20', '1', 1, '2024-01-19 06:49:52', '2024-01-19 06:49:53'),
	 ('NEW_CARD201', 'CARD', '20', '1', 1, '2024-01-05 08:30:03', '2024-01-05 08:30:04'),
	 ('NEW_BANKTRANSFER10','BANK_TRANSFER','10','*',1,'2022-01-07 14:24:21','2022-01-07 14:24:21'),
	 ('NEW_BANKTRANSFER20','BANK_TRANSFER','20','*',1,'2022-01-07 14:24:21','2022-01-07 14:24:21');
INSERT INTO tb_funding_channel_product_info (product_code, payment_method_type, payment_type, customer_type, status, create_time, modified_time) VALUES
    ('CARDPAY601', 'CardPay', '60', '1', 1, '2022-01-07 14:24:21', '2022-01-07 14:24:21');
INSERT INTO tb_funding_channel_product_info (product_code, payment_method_type, payment_type, customer_type, status, create_time, modified_time) VALUES
    ('NEW_CARD601', 'CARD', '60', '1', 1, '2022-01-07 14:24:21', '2022-01-07 14:24:21');