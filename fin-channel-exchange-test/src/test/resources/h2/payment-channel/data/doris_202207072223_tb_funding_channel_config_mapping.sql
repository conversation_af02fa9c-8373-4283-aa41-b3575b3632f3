INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_GPAY_P01','I_GPAY_P01_VIRTUALACCOUNT201_ZIRAATBANK_TR','10101','A18','ziraat','GPAY801',NULL,1,'2021-09-27 07:38:47','2022-01-07 15:00:52'),
	 ('I_DRAGONPAY_P01','I_DRAGONPAY_P01_VIRTUALACCOUNT201_BANKTRANSFER_PH','10101','A18','A18','DPAY102',NULL,1,'2021-09-27 07:38:48','2022-02-24 14:26:45'),
	 ('I_DRAGONPAY_P01','I_DRAGONPAY_P01_VIRTUALACCOUNT201_SMBILL_PH','10101','A13','SMR','DPAY102',NULL,1,'2021-09-27 07:38:49','2022-02-24 14:26:45'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_VIRTUALACCOUNT201_MB_VN','10101','1011','MBBANK-2','NGANLUONG801',NULL,1,'2021-09-27 07:38:49','2022-01-07 15:00:52'),
	 ('I_PAYTR_T01','I_PAYTR_T01_CARDPAY201_MASTERCARD_TR','10101','20','TROY','PAYTR101',NULL,1,'2021-09-27 07:38:50','2022-01-07 15:00:52'),
	 ('I_DANA_P01','I_DANA_P01_WALLET201_DANA_ID','10101','A7','A7','DANA801',NULL,1,'2021-09-27 07:38:50','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_01','I_DLOCAL_P01_01_CARDPAY201_AMEX_CO','10101','20','20','DLOCAL801',NULL,1,'2021-09-27 07:38:50','2022-03-14 05:50:43'),
	 ('I_DRAGONPAY_P01','I_DRAGONPAY_P01_WALLET201_GRABPAY_PH','10101','A39','A39','DPAY102',NULL,1,'2021-09-27 07:38:51','2022-02-24 14:26:45'),
	 ('I_ALIPAYHK_T01_02','I_ALIPAYHK_T01_02_AUTODEBIT201_GCASH_PH','10101','1009','********','ALIPAY102',NULL,1,'2021-09-27 07:38:52','2022-03-14 05:50:09'),
	 ('I_TAP_T01','I_TAP_T01_CARDPAY201_VISA_OM','10101','A34','A34','TAP101',NULL,1,'2021-09-27 07:38:53','2022-01-07 15:00:52');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_PAGSMILE_P01','I_PAGSMILE_P01_CARDPAY201_AMEX_BR','10101','20','20','PAGSMILE801',NULL,1,'2021-09-27 07:38:53','2022-01-07 15:00:52'),
	 ('I_FASPAY_P01','I_FASPAY_P01_VIRTUALACCOUNT201_MAYBANK_ID','10101','A18','MAYBANK','FASPAY801',NULL,1,'2021-09-27 07:38:54','2022-01-07 15:00:52'),
	 ('I_GPAY_P01','I_GPAY_P01_VIRTUALACCOUNT201_VAKIFBANK_TR','10101','A18','vakifbank','GPAY801',NULL,1,'2021-09-27 07:38:54','2022-01-07 15:00:52'),
	 ('I_DRAGONPAY_P01','I_DRAGONPAY_P01_VIRTUALACCOUNT201_BAYAD_PH','10101','A13','BAYD','DPAY102',NULL,1,'2021-09-27 07:38:55','2022-02-24 14:26:45'),
	 ('I_GASH_P01','I_GASH_P01_CARRIERBILLING201_CHT_TW','10101','CB','CHT','GASH801',NULL,1,'2021-09-27 07:38:55','2022-01-07 15:00:52'),
	 ('I_PAYCO_P01','I_PAYCO_P01_WALLET201_PAYCO_KR','10101','PAYCO','PAYCO','PAYCO801',NULL,1,'2021-09-27 07:38:56','2022-01-07 15:00:52'),
	 ('I_TPAY_T01','I_TPAY_T01_CARRIERBILLING201_ORANGE_EG','10101','A15','********','TPAY101',NULL,1,'2021-09-27 07:38:56','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_VIRTUALACCOUNT201_BBVA_PE','10101','03','BBVA','DLOCAL802',NULL,1,'2021-09-27 07:38:57','2022-03-14 05:50:56'),
	 ('I_DOKU_P01','I_DOKU_P01_WALLET201_DOKU_ID','10101','A1','A1','DOKU801',NULL,1,'2021-09-27 07:38:58','2022-01-07 15:00:52'),
	 ('I_MELON_P01','I_MELON_P01_CARRIERBILLING201_TELKOMSEL_ID','10101','A15','Telkomsel','MELON801',NULL,1,'2021-09-27 07:38:58','2022-01-07 15:00:52');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_RAZER_P01','I_RAZER_P01_CARRIERBILLING201_TRUEMOVETH_TH','10101','A15','TRUEMOVE','RAZER801',NULL,1,'2021-09-27 07:38:59','2022-01-07 15:00:52'),
	 ('I_MELON_P01','I_MELON_P01_CARRIERBILLING201_SMARTFREN_ID','10101','A15','Smartfren','MELON801',NULL,1,'2021-09-27 07:38:59','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_01','I_DLOCAL_P01_01_CARDPAY201_AMEX_BR','10101','20','20','DLOCAL801',NULL,1,'2021-09-27 07:39:00','2022-03-14 05:50:27'),
	 ('I_DRAGONPAY_P01','I_DRAGONPAY_P01_VIRTUALACCOUNT201_M_LHUILLIER_PH','10101','A13','MLH','DPAY102',NULL,1,'2021-09-27 07:39:01','2022-02-24 14:26:45'),
	 ('I_FASTPAY_P01','I_FASTPAY_P01_BARCODE201_FAMILYMART_TW','10101','1005','FAMILYMART','FASTPAY801',NULL,1,'2021-09-27 07:39:01','2022-01-07 15:00:52'),
	 ('I_TAP_T01','I_TAP_T01_CARDPAY201_MASTERCARD_AE','10101','A34','A34','TAP101',NULL,1,'2021-09-27 07:39:02','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_CARDPAY201_MASTERCARD_PY','10101','20','20','DLOCAL802',NULL,1,'2021-09-27 07:39:03','2022-03-14 05:51:05'),
	 ('I_GASH_P01','I_GASH_P01_CARRIERBILLING201_CHTFIXLINE_TW','10101','CB','CHTFIXLINE','GASH801',NULL,1,'2021-09-27 07:39:03','2022-01-07 15:00:52'),
	 ('I_CODA_T01','I_CODA_T01_WALLET201_LINEPAY_TH','10101','LINEPAY','LINEPAY','CODA101',NULL,1,'2021-09-27 07:39:03','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_01','I_DLOCAL_P01_01_CARDPAY201_VISA_MX','10101','20','20','DLOCAL801',NULL,1,'2021-09-27 07:39:05','2022-03-14 05:50:46');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_PAGSMILE_P01','I_PAGSMILE_P01_CARDPAY201_HIPERCARD_BR','10101','20','20','PAGSMILE801',NULL,1,'2021-09-27 07:39:05','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_VIRTUALACCOUNT201_EXB_VN','10101','1003','EXIMBANK-1','NGANLUONG801',NULL,1,'2021-09-27 07:39:06','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_01','I_DLOCAL_P01_01_CARDPAY201_MASTERCARD_ZA','10101','20','20','DLOCAL801',NULL,1,'2021-09-27 07:39:08','2022-03-14 05:51:10'),
	 ('I_XENDIT_P01_01','I_XENDIT_P01_01_PAYLATER201_BILLEASE_PH','10101','1014','********','XENDIT801',NULL,1,'2021-09-27 07:39:09','2022-03-14 05:50:17'),
	 ('I_SIMPAISA_P01','I_SIMPAISA_P01_WALLET201_JAZZCASH_PK','10101','1006','********','SIMPAISA101',NULL,1,'2021-09-27 07:39:10','2022-01-07 15:00:52'),
	 ('I_FAWRY_P01','I_FAWRY_P01_CARDPAY201_MASTERCARD_EG','10101','A34','A34','FAWRY801',NULL,1,'2021-09-27 07:39:10','2022-01-07 15:00:52'),
	 ('I_DRAGONPAY_P01','I_DRAGONPAY_P01_NETBANKING201_NETBANKING_PH','10101','03','03','DPAY102',NULL,1,'2021-09-27 07:39:10','2022-02-24 14:26:45'),
	 ('I_DLOCAL_P01_01','I_DLOCAL_P01_01_CARDPAY201_VISA_UY','10101','20','20','DLOCAL801',NULL,1,'2021-09-27 07:39:11','2022-03-14 05:50:57'),
	 ('I_FASPAY_P01','I_FASPAY_P01_NETBANKING201_MAYBANK_ID','10101','03','MAYBANK-1','FASPAY801',NULL,1,'2021-09-27 07:39:11','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_01','I_DLOCAL_P01_01_CARDPAY201_VISA_CO','10101','20','20','DLOCAL801',NULL,1,'2021-09-27 07:39:12','2022-03-14 05:50:42');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_PAGSMILE_P01','I_PAGSMILE_P01_CARDPAY201_ELO_BR','10101','20','20','PAGSMILE801',NULL,1,'2021-09-27 07:39:13','2022-01-07 15:00:52'),
	 ('I_FASPAY_P01','I_FASPAY_P01_VIRTUALACCOUNT201_DANAMON_ID','10101','A18','DANAMON','FASPAY801',NULL,1,'2021-09-27 07:39:13','2022-01-07 15:00:52'),
	 ('I_FASPAY_P01','I_FASPAY_P01_VIRTUALACCOUNT201_PERMATA_ID','10101','A18','PERMATA','FASPAY801',NULL,1,'2021-09-27 07:39:14','2022-01-07 15:00:52'),
	 ('I_GPAY_P01','I_GPAY_P01_VIRTUALACCOUNT201_ALTERNATIFBANK_TR','10101','A18','********','GPAY801',NULL,1,'2021-09-27 07:39:15','2022-01-07 15:00:52'),
	 ('I_GPAY_P01','I_GPAY_P01_CARRIERBILLING201_CARRIERBILLING_TR','10101','A15','A15','GPAY801',NULL,1,'2021-09-27 07:39:16','2022-01-07 15:00:52'),
	 ('I_TAP_T01','I_TAP_T01_CARDPAY201_MASTERCARD_KW','10101','A34','A34','TAP101',NULL,1,'2021-09-27 07:39:16','2022-01-07 15:00:52'),
	 ('I_CODA_T01','I_CODA_T01_VIRTUALACCOUNT201_INDOMARET_ID','10101','A12','A12','CODA101',NULL,1,'2021-09-27 07:39:16','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_NETBANKING201_NAB_VN','10101','1010','********','NGANLUONG801',NULL,1,'2021-09-27 07:39:17','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_03','I_DLOCAL_P01_03_VIRTUALACCOUNT201_BOLETO_FAST_BR','10101','A51','Boletofast','DLOCAL803',NULL,1,'2021-09-27 07:39:17','2022-03-14 05:50:33'),
	 ('I_ALIPAYHK_T01_03','I_ALIPAYHK_T01_03_WALLET201_ALIPAYHK_HK','10101','1006','ALIPAYHK','ALIPAY103',NULL,1,'2021-09-27 07:39:18','2022-03-14 05:50:21');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_RAZER_P01','I_RAZER_P01_NETBANKING201_BANGKOK_TH','10101','03','BANGKOK','RAZER801',NULL,1,'2021-09-27 07:39:19','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_04','I_DLOCAL_P01_04_WALLET201_UPI_IN_1','10103','CUPI','CUPI','DLOCAL804',NULL,1,'2021-09-27 07:39:19','2022-03-14 05:49:52'),
	 ('I_TAP_T01','I_TAP_T01_CARDPAY201_MASTERCARD_LB','10101','A34','A34','TAP101',NULL,1,'2021-09-27 07:39:20','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_VIRTUALACCOUNT201_VCB_VN','10101','1003','VIETCOMBANK-1','NGANLUONG801',NULL,1,'2021-09-27 07:39:21','2022-01-07 15:00:52'),
	 ('I_DRAGONPAY_P01','I_DRAGONPAY_P01_VIRTUALACCOUNT201_7-ELEVEN_PH','10101','A14','A14','DPAY102',NULL,1,'2021-09-27 07:39:21','2022-02-24 14:26:45'),
	 ('I_FASTPAY_P01','I_FASTPAY_P01_BARCODE201_HILIFE_TW','10101','1005','HILIFE','FASTPAY801',NULL,1,'2021-09-27 07:39:22','2022-01-07 15:00:52'),
	 ('I_GPAY_P01','I_GPAY_P01_PREPAID201_GPAY_TR','10101','1006','********','GPAY801',NULL,1,'2021-09-27 07:39:22','2022-01-07 15:00:52'),
	 ('I_CODA_T01','I_CODA_T01_WALLET201_7-ELEVEN_TH','10101','A14','A14','CODA101',NULL,1,'2021-09-27 07:39:23','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_VIRTUALACCOUNT201_TPB_VN','10101','1012','TPBANK-3','NGANLUONG801',NULL,1,'2021-09-27 07:39:24','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_VIRTUALACCOUNT201_TCB_VN_1','10101','1011','TECHCOMBANK-2','NGANLUONG801',NULL,1,'2021-09-27 07:39:25','2022-01-07 15:00:52');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_FASPAY_P01','I_FASPAY_P01_NETBANKING201_DANAMON_ID','10101','03','DANAMON-1','FASPAY801',NULL,1,'2021-09-27 07:39:26','2022-01-07 15:00:52'),
	 ('I_TPAY_T01','I_TPAY_T01_CARRIERBILLING201_WE_EG','10101','A15','********','TPAY101',NULL,1,'2021-09-27 07:39:26','2022-01-07 15:00:52'),
	 ('I_ALIPAYHK_T01_03','I_ALIPAYHK_T01_03_WALLET201_TNG_MY','10101','A43','A43','ALIPAY103',NULL,1,'2021-09-27 07:39:27','2022-03-14 05:50:18'),
	 ('I_REVPAY_P01','I_REVPAY_P01_WALLET201_AXIATABOOST_MY','10101','A40','A40','REVPAY801',NULL,1,'2021-09-27 07:39:28','2022-01-07 15:00:52'),
	 ('I_TPAY_P01','I_TPAY_P01_CARRIERBILLING201_DU_AE','10101','A15','********','TPAY801',NULL,1,'2021-09-27 07:39:28','2022-01-07 15:00:52'),
	 ('I_MCP_P01_01','I_MCP_P01_01_VIRTUALACCOUNT201_MUAMALAT_ID','10101','A18','MUAMALAT','MCP801',NULL,1,'2021-09-27 07:39:29','2022-03-14 05:50:06'),
	 ('I_CODA_T01','I_CODA_T01_VIRTUALACCOUNT201_OTC_PH','10101','A13','A13','CODA101',NULL,1,'2021-09-27 07:39:29','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_VIRTUALACCOUNT201_ACB_VN_1','10101','1011','ACB-2','NGANLUONG801',NULL,1,'2021-09-27 07:39:29','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_01','I_DLOCAL_P01_01_CARDPAY201_AMEX_UY','10101','20','20','DLOCAL801',NULL,1,'2021-09-27 07:39:30','2022-03-14 05:50:58'),
	 ('I_ALIPAYHK_P01','I_ALIPAYHK_P01_WALLET201_DANA_ID','10101','A7','A7','ALIPAY801',NULL,1,'2021-09-27 07:39:31','2022-01-07 15:00:52');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_CENTILI_P01_02','I_CENTILI_P01_02_CARRIERBILLING201_GLOBE_PH_1','10101','A15','Globe','CENTILI802',NULL,1,'2021-09-27 07:39:31','2022-03-14 05:50:12'),
	 ('I_DLOCAL_P01_01','I_DLOCAL_P01_01_CARDPAY201_MASTERCARD_CO','10101','20','20','DLOCAL801',NULL,1,'2021-09-27 07:39:31','2022-03-14 05:50:42'),
	 ('I_ALIPAYHK_P01','I_ALIPAYHK_P01_WALLET201_ALIPAYHK_HK','10101','1006','ALIPAYHK','ALIPAY801',NULL,1,'2021-09-27 07:39:32','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_NETBANKING201_PGB_VN','10101','1010','********','NGANLUONG801',NULL,1,'2021-09-27 07:39:33','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_01','I_DLOCAL_P01_01_CARDPAY201_MASTERCARD_PE','10101','20','20','DLOCAL801',NULL,1,'2021-09-27 07:39:34','2022-03-14 05:50:53'),
	 ('I_CENTILI_P01_02','I_CENTILI_P01_02_CARRIERBILLING201_SMARTSUN_PH_1','10101','A15','SmartSun','CENTILI802',NULL,1,'2021-09-27 07:39:34','2022-03-14 05:50:12'),
	 ('I_OMISE_P01','I_OMISE_P01_NETBANKING201_AYUDHYA_TH','10101','03','AYUDHYA','OMISE801',NULL,1,'2021-09-27 07:39:35','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_VIRTUALACCOUNT201_BB_BR','10101','03','BB','DLOCAL802',NULL,1,'2021-09-27 07:39:36','2022-03-14 05:50:30'),
	 ('I_CODA_T01','I_CODA_T01_CARRIERBILLING201_AIS_TH','10101','A15','AIS','CODA101',NULL,1,'2021-09-27 07:39:36','2022-01-07 15:00:52'),
	 ('I_PAYTR_T01','I_PAYTR_T01_VIRTUALACCOUNT201_AKBANK_TR','10101','A18','akbank','PAYTR101',NULL,1,'2021-09-27 07:39:37','2022-01-07 15:00:52');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_DRAGONPAY_P01','I_DRAGONPAY_P01_VIRTUALACCOUNT201_RDPAWNSHOP_PH','10101','A13','RDP','DPAY102',NULL,1,'2021-09-27 07:39:37','2022-02-24 14:26:45'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_VIRTUALACCOUNT201_ICB_VN_1','10101','1011','VIETINBANK-2','NGANLUONG801',NULL,1,'2021-09-27 07:39:37','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_01','I_DLOCAL_P01_01_CARDPAY201_AMEX_MX','10101','20','20','DLOCAL801',NULL,1,'2021-09-27 07:39:38','2022-03-14 05:50:47'),
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_VIRTUALACCOUNT201_BANKTRANSFER_AR','10101','03','03','DLOCAL802',NULL,1,'2021-09-27 07:39:38','2022-03-14 05:51:03'),
	 ('I_GASH_P01','I_GASH_P01_PREPAID201_GASH_TW','10101','PC','GASHPC','GASH801',NULL,1,'2021-09-27 07:39:39','2022-01-07 15:00:52'),
	 ('I_KCP_P01','I_KCP_P01_CARRIERBILLING201_CARRIERBILLING_KR','10101','1004','********','KCP101',NULL,1,'2021-09-27 07:39:39','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_VIRTUALACCOUNT201_SERVIPAG_CL','10101','A50','Servipag','DLOCAL802',NULL,1,'2021-09-27 07:39:40','2022-03-14 05:50:41'),
	 ('I_CODA_T01','I_CODA_T01_CARRIERBILLING201_CAT_TH','10101','A15','CAT','CODA101',NULL,1,'2021-09-27 07:39:41','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_VIRTUALACCOUNT201_INTERBANK_PE','10101','03','Interbank','DLOCAL802',NULL,1,'2021-09-27 07:39:42','2022-03-14 05:50:55'),
	 ('I_GASH_P01','I_GASH_P01_CARDPAY201_CARDPAYLOCAL_TW','10101','1001','********','GASH801',NULL,1,'2021-09-27 07:39:43','2022-01-07 15:00:52');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_CODA_T01','I_CODA_T01_VIRTUALACCOUNT201_ALFAMART_ID','10101','A11','A11','CODA101',NULL,1,'2021-09-27 07:39:44','2022-01-07 15:00:52'),
	 ('I_XENDIT_P01_01','I_XENDIT_P01_01_DIRECTDEBIT201_BPI_PH','10101','1013','********','XENDIT801',NULL,1,'2021-09-27 07:39:45','2022-03-14 05:50:15'),
	 ('I_XENDIT_T01','I_XENDIT_T01_VIRTUALACCOUNT201_BNI_ID','10101','A18','BNI','XENDIT02',NULL,1,'2021-09-27 07:39:45','2022-01-07 15:00:52'),
	 ('I_MCP_P01_01','I_MCP_P01_01_VIRTUALACCOUNT201_BNI_ID','10101','A18','BNI','MCP801',NULL,1,'2021-09-27 07:39:46','2022-03-14 05:50:05'),
	 ('I_TPAY_P01','I_TPAY_P01_CARRIERBILLING201_ETISALAT_AE','10101','A15','A15','TPAY801',NULL,1,'2021-09-27 07:39:47','2022-01-07 15:00:52'),
	 ('I_REVPAY_P01','I_REVPAY_P01_WALLET201_MAYBANK_MY','10101','A41','A41','REVPAY801',NULL,1,'2021-09-27 07:39:47','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_VIRTUALACCOUNT201_TCB_VN','10101','1003','TECHCOMBANK-1','NGANLUONG801',NULL,1,'2021-09-27 07:39:48','2022-01-07 15:00:52'),
	 ('I_PAGSMILE_P01','I_PAGSMILE_P01_CARDPAY201_MASTERCARD_BR','10101','20','20','PAGSMILE801',NULL,1,'2021-09-27 07:39:49','2022-01-07 15:00:52'),
	 ('I_TOSS_P01_03','I_TOSS_P01_03_WALLET201_TOSS_KR_1','10101','TOSS','TOSS','TOSS803',NULL,1,'2021-09-27 07:39:49','2022-03-14 05:50:23'),
	 ('I_CODA_T01','I_CODA_T01_CARRIERBILLING201_XL_ID','10101','A15','XL','CODA101',NULL,1,'2021-09-27 07:39:50','2022-01-07 15:00:52');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_NETBANKING201_TPB_VN','10101','1002','TPBANK','NGANLUONG801',NULL,1,'2021-09-27 07:39:51','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_01','I_DLOCAL_P01_01_CARDPAY201_VISA_AR','10101','20','20','DLOCAL801',NULL,1,'2021-09-27 07:39:51','2022-03-14 05:51:00'),
	 ('I_RAZER_P01','I_RAZER_P01_NETBANKING201_AYUDHYA_TH','10101','03','AYUDHYA','RAZER801',NULL,1,'2021-09-27 07:39:53','2022-01-07 15:00:52'),
	 ('I_MELON_P01','I_MELON_P01_CARRIERBILLING201_INDOSAT_ID','10101','A15','Indosat','MELON801',NULL,1,'2021-09-27 07:39:53','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_VIRTUALACCOUNT201_BALOTO_CO','10101','A50','Baloto','DLOCAL802',NULL,1,'2021-09-27 07:39:54','2022-03-14 05:50:45'),
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_VIRTUALACCOUNT201_ITAU_BR','10101','03','Itau','DLOCAL802',NULL,1,'2021-09-27 07:39:54','2022-03-14 05:50:29'),
	 ('I_DLOCAL_P01_01','I_DLOCAL_P01_01_CARDPAY201_AMEX_ZA','10101','20','20','DLOCAL801',NULL,1,'2021-09-27 07:39:54','2022-03-14 05:51:10'),
	 ('I_CODA_T01','I_CODA_T01_WALLET201_DOKU_ID','10101','A1','A1','CODA101',NULL,1,'2021-09-27 07:39:55','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_NETBANKING201_SHB_VN','10101','1010','********','NGANLUONG801',NULL,1,'2021-09-27 07:39:55','2022-01-07 15:00:52'),
	 ('I_XENDIT_T01','I_XENDIT_T01_VIRTUALACCOUNT201_PERMATA_ID','10101','A18','PERMATA','XENDIT02',NULL,1,'2021-09-27 07:39:56','2022-01-07 15:00:52');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_REDPAY_P01','I_REDPAY_P01_CARRIERBILLING201_INDOSAT_ID','10101','A15','Indosat','REDPAY801',NULL,1,'2021-09-27 07:39:56','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_NETBANKING201_VCB_VN_1','10101','1010','********','NGANLUONG801',NULL,1,'2021-09-27 07:39:57','2022-01-07 15:00:52'),
	 ('I_TOSS_P01_04','I_TOSS_P01_04_CARDPAY201_CARDPAY_KR','10101','1001','********','TOSS804',NULL,1,'2021-09-27 07:39:58','2022-03-14 05:50:24'),
	 ('I_PAGSMILE_P01_01','I_PAGSMILE_P01_01_VIRTUALACCOUNT201_LOTTERY_BR','10101','A52','A52','PAGSMILE801',NULL,1,'2021-09-27 07:39:59','2022-03-14 05:50:34'),
	 ('I_YOUMI_P01','I_YOUMI_P01_VIRTUALACCOUNT201_SBERBANK_RU','10101','BT','SBER','YOUMI801',NULL,1,'2021-09-27 07:40:00','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_VIRTUALACCOUNT201_BNB_BO','10101','03','BNB','DLOCAL802',NULL,1,'2021-09-27 07:40:00','2022-03-14 05:51:08'),
	 ('I_REDPAY_P01','I_REDPAY_P01_CARRIERBILLING201_TRIINDO_ID','10101','A15','Triindo','REDPAY801',NULL,1,'2021-09-27 07:40:01','2022-01-07 15:00:52'),
	 ('I_CODA_T01','I_CODA_T01_CARRIERBILLING201_DIGI_MY','10101','A15','DiGi','CODA101',NULL,1,'2021-09-27 07:40:01','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_NETBANKING201_STB_VN','10101','1010','********','NGANLUONG801',NULL,1,'2021-09-27 07:40:03','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_NETBANKING201_HDB_VN','10101','1010','********','NGANLUONG801',NULL,1,'2021-09-27 07:40:04','2022-01-07 15:00:52');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_VIRTUALACCOUNT201_STB_VN','10101','1003','SACOMBANK-1','NGANLUONG801',NULL,1,'2021-09-27 07:40:05','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_VIRTUALACCOUNT201_MB_VN_1','10101','1012','MBBANK-3','NGANLUONG801',NULL,1,'2021-09-27 07:40:06','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_VIRTUALACCOUNT201_CAIXA_BR','10101','03','Caixa','DLOCAL802',NULL,1,'2021-09-27 07:40:06','2022-03-14 05:50:31'),
	 ('I_GASH_P01','I_GASH_P01_CARRIERBILLING201_TSTAR_TW','10101','CB','TSTAR','GASH801',NULL,1,'2021-09-27 07:40:07','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_01','I_DLOCAL_P01_01_CARDPAY201_AMEX_EC','10101','20','20','DLOCAL801',NULL,1,'2021-09-27 07:40:07','2022-03-14 05:51:07'),
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_VIRTUALACCOUNT201_PAGOFACIL_AR','10101','A50','PagoFacil','DLOCAL802',NULL,1,'2021-09-27 07:40:07','2022-03-14 05:51:02'),
	 ('I_CODA_T01','I_CODA_T01_CARRIERBILLING201_INDOSAT_ID','10101','A15','Indosat','CODA101',NULL,1,'2021-09-27 07:40:08','2022-01-07 15:00:52'),
	 ('I_OMISE_P01','I_OMISE_P01_NETBANKING201_SCB_TH_TH','10101','03','SIAMCOMMERCIAL','OMISE801',NULL,1,'2021-09-27 07:40:08','2022-01-07 15:00:52'),
	 ('I_PAYTR_T01','I_PAYTR_T01_VIRTUALACCOUNT201_HALKBANK_TR','10101','A18','halkbank','PAYTR101',NULL,1,'2021-09-27 07:40:09','2022-01-07 15:00:52'),
	 ('I_TAP_T01','I_TAP_T01_CARDPAY201_VISA_KW','10101','A34','A34','TAP101',NULL,1,'2021-09-27 07:40:09','2022-01-07 15:00:52');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_ALIPAYHK_T01_03','I_ALIPAYHK_T01_03_WALLET201_KAKAOPAY_KR','10101','1006','KAKAOPAY','ALIPAY103',NULL,1,'2021-09-27 07:40:09','2022-03-14 05:50:22'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_NETBANKING201_VCB_VN','10101','1002','VIETCOMBANK','NGANLUONG801',NULL,1,'2021-09-27 07:40:10','2022-01-07 15:00:52'),
	 ('I_DOKU_P01','I_DOKU_P01_VIRTUALACCOUNT201_ALFAMART_ID','10101','A11','A11','DOKU801',NULL,1,'2021-09-27 07:40:10','2022-01-07 15:00:52'),
	 ('I_XENDIT_T01','I_XENDIT_T01_WALLET201_LINKAJA_ID','10101','A35','A35','XENDIT02',NULL,1,'2021-09-27 07:40:11','2022-01-07 15:00:52'),
	 ('I_SIMPAISA_P01','I_SIMPAISA_P01_WALLET201_EASYPAISA_PK','10101','1006','********','SIMPAISA101',NULL,1,'2021-09-27 07:40:12','2022-01-07 15:00:52'),
	 ('I_TPAY_P01','I_TPAY_P01_CARRIERBILLING201_ORANGE_EG','10101','A15','********','TPAY801',NULL,1,'2021-09-27 07:40:12','2022-01-07 15:00:52'),
	 ('I_ALIPAYHK_P01','I_ALIPAYHK_P01_WALLET201_TNG_MY','10101','A43','A43','ALIPAY801',NULL,1,'2021-09-27 07:40:13','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_NETBANKING201_ABB_VN','10101','1010','********','NGANLUONG801',NULL,1,'2021-09-27 07:40:13','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_NETBANKING201_AGB_VN','10101','1010','********','NGANLUONG801',NULL,1,'2021-09-27 07:40:13','2022-01-07 15:00:52'),
	 ('I_ESPAY_T01','I_ESPAY_T01_VIRTUALACCOUNT201_ALFAMART_ID','10101','A11','A11','ESPAY101',NULL,1,'2021-09-27 07:40:14','2022-01-07 15:00:52');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_NETBANKING201_NVB_VN','10101','1010','********','NGANLUONG801',NULL,1,'2021-09-27 07:40:14','2022-01-07 15:00:52'),
	 ('I_FAWRY_T01','I_FAWRY_T01_CARDPAY201_MASTERCARD_EG','10101','A34','A34','FAWRY101',NULL,1,'2021-09-27 07:40:15','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_VIRTUALACCOUNT201_WEBPAY_CL','10101','03','03','DLOCAL802',NULL,1,'2021-09-27 07:40:15','2022-03-14 05:50:40'),
	 ('I_XENDIT_T01','I_XENDIT_T01_WALLET201_DANA_ID','10101','A7','A7','XENDIT02',NULL,1,'2021-09-27 07:40:16','2022-01-07 15:00:52'),
	 ('I_KSHER_T01','I_KSHER_T01_WALLET201_TRUEMONEY_TH','10101','TRUEMONEY','TRUEMONEY','KSHER101',NULL,1,'2021-09-27 07:40:16','2022-01-07 15:00:52'),
	 ('I_MCP_P01_01','I_MCP_P01_01_VIRTUALACCOUNT201_PERMATA_ID','10101','A18','PERMATA','MCP801',NULL,1,'2021-09-27 07:40:17','2022-03-14 05:50:07'),
	 ('I_OMISE_P01','I_OMISE_P01_VIRTUALACCOUNT201_TESCOLOTUS_TH','10101','TESCOLOTUS','TESCOLOTUS','OMISE801',NULL,1,'2021-09-27 07:40:17','2022-01-07 15:00:52'),
	 ('I_TAP_T01','I_TAP_T01_CARDPAY201_MASTERCARD_SA','10101','A34','A34','TAP101',NULL,1,'2021-09-27 07:40:18','2022-01-07 15:00:52'),
	 ('I_CODA_T01','I_CODA_T01_CARRIERBILLING201_MAXIS_MY','10101','A15','Maxis','CODA101',NULL,1,'2021-09-27 07:40:19','2022-01-07 15:00:52'),
	 ('I_XENDIT_T01','I_XENDIT_T01_VIRTUALACCOUNT201_ALFAMART_ID','10101','A11','A11','XENDIT02',NULL,1,'2021-09-27 07:40:20','2022-01-07 15:00:52');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_PAGSMILE_P01_01','I_PAGSMILE_P01_01_WALLET201_MERCADOPAGO_BR','10101','1006','********','PAGSMILE801',NULL,1,'2021-09-27 07:40:20','2022-03-14 05:50:36'),
	 ('I_TAP_T01','I_TAP_T01_CARDPAY201_MADA_SA','10101','A45','A45','TAP101',NULL,1,'2021-09-27 07:40:21','2022-01-07 15:00:52'),
	 ('I_FASPAY_P01','I_FASPAY_P01_VIRTUALACCOUNT201_MANDIRI_ID','10101','A18','MANDIRI','FASPAY801',NULL,1,'2021-09-27 07:40:21','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_01','I_DLOCAL_P01_01_CARDPAY201_VISA_EC','10101','20','20','DLOCAL801',NULL,1,'2021-09-27 07:40:22','2022-03-14 05:51:06'),
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_VIRTUALACCOUNT201_SPEI_MX','10101','03','SPEI','DLOCAL802',NULL,1,'2021-09-27 07:40:23','2022-03-14 05:50:48'),
	 ('I_TAP_T01','I_TAP_T01_CARDPAY201_VISA_BH','10101','A34','A34','TAP101',NULL,1,'2021-09-27 07:40:23','2022-01-07 15:00:52'),
	 ('I_TAP_T01','I_TAP_T01_CARDPAY201_MASTERCARD_OM','10101','A34','A34','TAP101',NULL,1,'2021-09-27 07:40:24','2022-01-07 15:00:52'),
	 ('I_GASH_P01','I_GASH_P01_WALLET201_GASH_TW','10101','WA','GASHWA','GASH801',NULL,1,'2021-09-27 07:40:24','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_VIRTUALACCOUNT201_VCB_VN_1','10101','1011','VIETCOMBANK-2','NGANLUONG801',NULL,1,'2021-09-27 07:40:25','2022-01-07 15:00:52'),
	 ('I_XENDIT_T01','I_XENDIT_T01_VIRTUALACCOUNT201_BRI_ID','10101','A18','BRI','XENDIT02',NULL,1,'2021-09-27 07:40:25','2022-01-07 15:00:52');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_NETBANKING201_SGB_VN','10101','1010','********','NGANLUONG801',NULL,1,'2021-09-27 07:40:26','2022-01-07 15:00:52'),
	 ('I_CODA_T01','I_CODA_T01_CARRIERBILLING201_TELKOMSEL_ID','10101','A15','Telkomsel','CODA101',NULL,1,'2021-09-27 07:40:26','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_NETBANKING201_ICB_VN','10101','1002','VIETINBANK','NGANLUONG801',NULL,1,'2021-09-27 07:40:27','2022-01-07 15:00:52'),
	 ('I_MELON_P01','I_MELON_P01_CARRIERBILLING201_TRIINDO_ID','10101','A15','Triindo','MELON801',NULL,1,'2021-09-27 07:40:27','2022-01-07 15:00:52'),
	 ('I_GPAY_P01','I_GPAY_P01_CARDPAY201_MASTERCARD_TR','10101','20','TROY','GPAY801',NULL,1,'2021-09-27 07:40:28','2022-01-07 15:00:52'),
	 ('I_FASPAY_P01','I_FASPAY_P01_WALLET201_AKULAKU_ID','10101','AKULAKU','AKULAKU','FASPAY801',NULL,1,'2021-09-27 07:40:28','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_01','I_DLOCAL_P01_01_NETBANKING201_NETBANKING_IN','10103','03','03','DLOCAL801',NULL,1,'2021-09-27 07:40:29','2022-03-14 07:11:27'),
	 ('I_RAZER_P01','I_RAZER_P01_NETBANKING201_SCB_TH_TH','10101','03','SIAMCOMMERCIAL','RAZER801',NULL,1,'2021-09-27 07:40:30','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_VIRTUALACCOUNT201_PGB_VN','10101','1011','PGBANK-2','NGANLUONG801',NULL,1,'2021-09-27 07:40:30','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_VIRTUALACCOUNT201_BOLETO_BR','10101','A50','Boleto','DLOCAL802',NULL,1,'2021-09-27 07:40:31','2022-03-14 05:50:33');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_REDPAY_P01','I_REDPAY_P01_VIRTUALACCOUNT201_BCA_ID','10101','A18','BCA','REDPAY801',NULL,1,'2021-09-27 07:40:31','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_VIRTUALACCOUNT201_ABITAB_UY','10101','A50','Abitab','DLOCAL802',NULL,1,'2021-09-27 07:40:32','2022-03-14 05:51:00'),
	 ('I_XENDIT_T01','I_XENDIT_T01_WALLET201_OVO_ID','10101','A36','A36','XENDIT02',NULL,1,'2021-09-27 07:40:32','2022-01-07 15:00:52'),
	 ('I_CENTILI_P01_02','I_CENTILI_P01_02_CARRIERBILLING201_ZAIN_BH','10101','A15','Zain','CENTILI802',NULL,1,'2021-09-27 07:40:33','2022-03-14 05:49:48'),
	 ('I_TAP_T01','I_TAP_T01_CARDPAY201_VISA_JO','10101','A34','A34','TAP101',NULL,1,'2021-09-27 07:40:33','2022-01-07 15:00:52'),
	 ('I_CENTILI_P01_01','I_CENTILI_P01_01_CARRIERBILLING201_SMARTSUN_PH','10101','A15','SmartSun','CENTILI801',NULL,1,'2021-09-27 07:40:34','2022-03-14 05:50:11'),
	 ('I_CODA_T01','I_CODA_T01_WALLET201_TRUEMONEY_TH','10101','TRUEMONEY','TRUEMONEY','CODA101',NULL,1,'2021-09-27 07:40:35','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_VIRTUALACCOUNT201_SANTANDER_BR','10101','03','Santander','DLOCAL802',NULL,1,'2021-09-27 07:40:35','2022-03-14 05:50:32'),
	 ('I_ALIPAYHK_T01_02','I_ALIPAYHK_T01_02_AUTODEBIT201_TRUEMONEY_TH','10101','AD','TRUEMONEYAD','ALIPAY102',NULL,1,'2021-09-27 07:40:36','2022-07-04 10:12:23'),
	 ('I_GPAY_P01','I_GPAY_P01_CARDPAY201_TROY_TR','10101','20','TROY','GPAY801',NULL,1,'2021-09-27 07:40:36','2022-01-07 15:00:52');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_TAP_T01','I_TAP_T01_CARDPAY201_VISA_QA','10101','A34','A34','TAP101',NULL,1,'2021-09-27 07:40:37','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_NETBANKING201_VAB_VN','10101','1010','********','NGANLUONG801',NULL,1,'2021-09-27 07:40:38','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_NETBANKING201_BIDV_VN','10101','1002','BIDV','NGANLUONG801',NULL,1,'2021-09-27 07:40:39','2022-01-07 15:00:52'),
	 ('I_CODA_T01','I_CODA_T01_CARRIERBILLING201_ZAIN_KW','10101','A15','Zain','CODA101',NULL,1,'2021-09-27 07:40:39','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_VIRTUALACCOUNT201_DAB_VN','10101','1011','DONGABANK-2','NGANLUONG801',NULL,1,'2021-09-27 07:40:39','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_VIRTUALACCOUNT201_VIB_VN','10101','1003','VIB-1','NGANLUONG801',NULL,1,'2021-09-27 07:40:40','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_NETBANKING201_VIB_VN','10101','1010','********','NGANLUONG801',NULL,1,'2021-09-27 07:40:41','2022-01-07 15:00:52'),
	 ('I_KCP_P01','I_KCP_P01_VIRTUALACCOUNT201_BANKTRANSFER_KR','10101','1003','********','KCP101',NULL,1,'2021-09-27 07:40:42','2022-01-07 15:00:52'),
	 ('I_PAYTR_T01','I_PAYTR_T01_CARDPAY201_TROY_TR','10101','20','TROY','PAYTR101',NULL,1,'2021-09-27 07:40:42','2022-01-07 15:00:52'),
	 ('I_GPAY_P01','I_GPAY_P01_VIRTUALACCOUNT201_ISBANK_TR','10101','A18','isbank','GPAY801',NULL,1,'2021-09-27 07:40:43','2022-01-07 15:00:52');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_DLOCAL_P01_04','I_DLOCAL_P01_04_NETBANKING201_NETBANKING_IN_1','10103','03','03','DLOCAL805',NULL,1,'2021-09-27 07:40:43','2022-03-14 05:49:53'),
	 ('I_CENTILI_P01_01','I_CENTILI_P01_01_CARRIERBILLING201_GLOBE_PH','10101','A15','Globe','CENTILI801',NULL,1,'2021-09-27 07:40:43','2022-03-14 05:50:10'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_WALLET201_NGANLUONG_VN','10101','NGANLUONG','NGANLUONG','NGANLUONG801',NULL,1,'2021-09-27 07:40:44','2022-01-07 15:00:52'),
	 ('I_TOSS_P01_06','I_TOSS_P01_06_CARDPAY201_CARDPAY_KR_1','10101','1001','********','TOSS806',NULL,1,'2021-09-27 07:40:44','2022-03-14 05:50:25'),
	 ('I_GASH_P01','I_GASH_P01_CARDPAY201_CARDPAY_TW','10101','1001','********','GASH801',NULL,1,'2021-09-27 07:40:45','2022-01-07 15:00:52'),
	 ('I_FAWRY_T01','I_FAWRY_T01_VIRTUALACCOUNT201_FAWRY_EG','10101','A47','A47','FAWRY101',NULL,1,'2021-09-27 07:40:45','2022-01-07 15:00:52'),
	 ('I_TPAY_P01','I_TPAY_P01_CARRIERBILLING201_WE_EG','10101','A15','********','TPAY801',NULL,1,'2021-09-27 07:40:46','2022-01-07 15:00:52'),
	 ('I_CODA_T01','I_CODA_T01_VIRTUALACCOUNT201_VIRTUALACCOUNT_TH','10101','A50','A50','CODA101',NULL,1,'2021-09-27 07:40:48','2022-01-07 15:00:52'),
	 ('I_ESPAY_T01','I_ESPAY_T01_VIRTUALACCOUNT201_BNI_ID','10101','A18','BRI','ESPAY101',NULL,1,'2021-09-27 07:40:48','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_VIRTUALACCOUNT201_STB_VN_3','10101','1012','SACOMBANK-3','NGANLUONG801',NULL,1,'2021-09-27 07:40:49','2022-01-07 15:00:52');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_ESPAY_T01','I_ESPAY_T01_NETBANKING201_PERMATA_ID','10101','03','PERMATA-1','ESPAY101',NULL,1,'2021-09-27 07:40:49','2022-01-07 15:00:52'),
	 ('I_XENDIT_P01_01','I_XENDIT_P01_01_VIRTUALACCOUNT201_7-ELEVEN_PH','10101','A13','SEVENELEVEN','XENDIT801',NULL,1,'2021-09-27 07:40:50','2022-03-14 05:50:16'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_NETBANKING201_MB_VN','10101','1010','********','NGANLUONG801',NULL,1,'2021-09-27 07:40:50','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_VIRTUALACCOUNT201_BRADESCO_BR','10101','03','Bradesco','DLOCAL802',NULL,1,'2021-09-27 07:40:50','2022-03-14 05:50:30'),
	 ('I_OMISE_P01','I_OMISE_P01_POINTSPAY201_CITI_TH','10101','CITIREWARDS','CITIREWARDS','OMISE801',NULL,1,'2021-09-27 07:40:52','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_VIRTUALACCOUNT201_RAPIPAGO_AR','10101','A50','RapiPago','DLOCAL802',NULL,1,'2021-09-27 07:40:52','2022-03-14 05:51:02'),
	 ('I_OMISE_P01','I_OMISE_P01_WALLET201_PROMPTPAY_TH','10101','PROMPTPAY','PROMPTPAY','OMISE801',NULL,1,'2021-09-27 07:40:53','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_NETBANKING201_VPB_VN','10101','1010','********','NGANLUONG801',NULL,1,'2021-09-27 07:40:54','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_VIRTUALACCOUNT201_VIB_VN_1','10101','1012','VIB-3','NGANLUONG801',NULL,1,'2021-09-27 07:40:54','2022-01-07 15:00:52'),
	 ('I_RAZER_P01','I_RAZER_P01_NETBANKING201_KASIKORN_TH','10101','03','KASIKORN','RAZER801',NULL,1,'2021-09-27 07:40:54','2022-01-07 15:00:52');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_YOUMI_P01','I_YOUMI_P01_VIRTUALACCOUNT201_TINKOFFBANK_RU','10101','BT','TINKOFF','YOUMI801',NULL,1,'2021-09-27 07:40:55','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_NETBANKING201_TCB_VN_1','10101','1010','********','NGANLUONG801',NULL,1,'2021-09-27 07:40:55','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_VIRTUALACCOUNT201_MSB_VN','10101','1003','MARITIMEBANK-1','NGANLUONG801',NULL,1,'2021-09-27 07:40:56','2022-01-07 15:00:52'),
	 ('I_TAP_T01','I_TAP_T01_CARDPAY201_MASTERCARD_BH','10101','A34','A34','TAP101',NULL,1,'2021-09-27 07:40:57','2022-01-07 15:00:52'),
	 ('I_OMISE_P01','I_OMISE_P01_WALLET201_TRUEMONEY_TH','10101','TRUEMONEY','TRUEMONEY','OMISE801',NULL,1,'2021-09-27 07:40:57','2022-01-07 15:00:52'),
	 ('I_FASTPAY_P01','I_FASTPAY_P01_BARCODE201_OKMART_TW','10101','1005','OKMART','FASTPAY801',NULL,1,'2021-09-27 07:40:58','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_01','I_DLOCAL_P01_01_VIRTUALACCOUNT201_BANKTRANSFER_ZA','10101','A50','A50','DLOCAL801',NULL,1,'2021-09-27 07:40:58','2022-03-14 05:51:11'),
	 ('I_CODA_T01','I_CODA_T01_CARRIERBILLING201_GLOBE_PH','10101','A15','Globe','CODA101',NULL,1,'2021-09-27 07:40:58','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_VIRTUALACCOUNT201_PSE_CO','10101','03','03','DLOCAL802',NULL,1,'2021-09-27 07:40:59','2022-03-14 05:50:46'),
	 ('I_GPAY_P01','I_GPAY_P01_VIRTUALACCOUNT201_PTT_TR','10101','A18','ptt','GPAY801',NULL,1,'2021-09-27 07:40:59','2022-01-07 15:00:52');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_VIRTUALACCOUNT201_MSB_VN_3','10101','1012','MARITIMEBANK-3','NGANLUONG801',NULL,1,'2021-09-27 07:41:00','2022-01-07 15:00:52'),
	 ('I_TPAY_T01','I_TPAY_T01_CARRIERBILLING201_VODAFONE_EG','10101','A15','********','TPAY101',NULL,1,'2021-09-27 07:41:00','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_VIRTUALACCOUNT201_REDPAGOS_UY','10101','A50','RedPagos','DLOCAL802',NULL,1,'2021-09-27 07:41:01','2022-03-14 05:50:59'),
	 ('I_ESPAY_T01','I_ESPAY_T01_VIRTUALACCOUNT201_MANDIRI_ID','10101','A18','MANDIRI','ESPAY101',NULL,1,'2021-09-27 07:41:01','2022-01-07 15:00:52'),
	 ('I_FASPAY_P01','I_FASPAY_P01_VIRTUALACCOUNT201_SINARMAS_ID','10101','A18','SINARMAS','FASPAY801',NULL,1,'2021-09-27 07:41:01','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_VIRTUALACCOUNT201_TCB_VN_3','10101','1012','TECHCOMBANK-3','NGANLUONG801',NULL,1,'2021-09-27 07:41:02','2022-01-07 15:00:52'),
	 ('I_SKYPAY_P01','I_SKYPAY_P01_VIRTUALACCOUNT201_7-ELEVEN_PH','10101','A13','SEVENELEVEN','SKYPAY801',NULL,1,'2021-09-27 07:41:02','2022-01-07 15:00:52'),
	 ('I_XENDIT_T01','I_XENDIT_T01_WALLET201_SHOPEEPAY_ID','10101','SHOPEEPAY','SHOPEEPAY','XENDIT02',NULL,1,'2021-09-27 07:41:03','2022-01-07 15:00:52'),
	 ('I_PAYTR_T01','I_PAYTR_T01_VIRTUALACCOUNT201_PTT_TR','10101','A18','ptt','PAYTR101',NULL,1,'2021-09-27 07:41:04','2022-01-07 15:00:52'),
	 ('I_ALIPAYHK_P01','I_ALIPAYHK_P01_WALLET201_TRUEMONEY_TH','10101','TRUEMONEY','TRUEMONEY','ALIPAY801',NULL,1,'2021-09-27 07:41:05','2022-01-07 15:00:52');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_DRAGONPAY_P01','I_DRAGONPAY_P01_VIRTUALACCOUNT201_LBC_PH_PH','10101','A13','LBC','DPAY102',NULL,1,'2021-09-27 07:41:05','2022-02-24 14:26:45'),
	 ('I_DOKU_P01','I_DOKU_P01_VIRTUALACCOUNT201_INDOMARET_ID','10101','A12','A12','DOKU801',NULL,1,'2021-09-27 07:41:05','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_04','I_DLOCAL_P01_04_WALLET201_WALLET_IN_1','10103','CPTM','CPTM','DLOCAL804',NULL,1,'2021-09-27 07:41:06','2022-03-14 05:49:51'),
	 ('I_ALIPAYHK_T01_02','I_ALIPAYHK_T01_02_AUTODEBIT201_TNG_MY','10101','1009','********','ALIPAY102',NULL,1,'2021-09-27 07:41:07','2022-03-14 05:50:18'),
	 ('I_DLOCAL_P01_01','I_DLOCAL_P01_01_CARDPAY201_MASTERCARD_AR','10101','20','20','DLOCAL801',NULL,1,'2021-09-27 07:41:07','2022-03-14 05:51:01'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_NETBANKING201_ICB_VN_1','10101','1010','********','NGANLUONG801',NULL,1,'2021-09-27 07:41:08','2022-01-07 15:00:52'),
	 ('I_RAZER_P01','I_RAZER_P01_WALLET201_LINEPAY_TH','10101','LINEPAY','LINEPAY','RAZER801',NULL,1,'2021-09-27 07:41:08','2022-01-07 15:00:52'),
	 ('I_PAYTR_T01','I_PAYTR_T01_VIRTUALACCOUNT201_ZIRAATBANK_TR','10101','A18','ziraat','PAYTR101',NULL,1,'2021-09-27 07:41:09','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_NETBANKING201_OJB_VN','10101','1010','********','NGANLUONG801',NULL,1,'2021-09-27 07:41:10','2022-01-07 15:00:52'),
	 ('I_TPAY_P01','I_TPAY_P01_CARRIERBILLING201_VODAFONE_EG','10101','A15','********','TPAY801',NULL,1,'2021-09-27 07:41:11','2022-01-07 15:00:52');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_PAGSMILE_P01_01','I_PAGSMILE_P01_01_WALLET201_PICPAY_BR','10101','1006','********','PAGSMILE801',NULL,1,'2021-09-27 07:41:11','2022-03-14 05:50:38'),
	 ('I_ALIPAYHK_T01_03','I_ALIPAYHK_T01_03_WALLET201_TRUEMONEY_TH','10101','TRUEMONEY','TRUEMONEY','ALIPAY103',NULL,1,'2021-09-27 07:41:12','2022-03-14 05:50:20'),
	 ('I_TAP_T01','I_TAP_T01_CARDPAY201_VISA_LB','10101','A34','A34','TAP101',NULL,1,'2021-09-27 07:41:14','2022-01-07 15:00:52'),
	 ('I_MCP_P01_01','I_MCP_P01_01_VIRTUALACCOUNT201_CIMB_ID','10101','A18','CIMB','MCP801',NULL,1,'2021-09-27 07:41:14','2022-03-14 05:50:05'),
	 ('I_SKYPAY_P01','I_SKYPAY_P01_VIRTUALACCOUNT201_RDPAWNSHOP_PH','10101','A13','RDP','SKYPAY801',NULL,1,'2021-09-27 07:41:15','2022-01-07 15:00:52'),
	 ('I_ESPAY_T01','I_ESPAY_T01_VIRTUALACCOUNT201_PERMATA_ID','10101','A18','PERMATA','ESPAY101',NULL,1,'2021-09-27 07:41:16','2022-01-07 15:00:52'),
	 ('I_SKYPAY_P01','I_SKYPAY_P01_VIRTUALACCOUNT201_M_LHUILLIER_PH','10101','A13','MLH','SKYPAY801',NULL,1,'2021-09-27 07:41:17','2022-01-07 15:00:52'),
	 ('I_PAGSMILE_P01_01','I_PAGSMILE_P01_01_CARDPAY201_CARDPAY_BR','10101','20','20','PAGSMILE801',NULL,1,'2021-09-27 07:41:17','2022-03-14 05:50:34'),
	 ('I_REVPAY_P01','I_REVPAY_P01_WALLET201_TNG_MY','10101','A43','A43','REVPAY801',NULL,1,'2021-09-27 07:41:18','2022-01-07 15:00:52'),
	 ('I_GPAY_P01','I_GPAY_P01_CARDPAY201_VISA_TR','10101','20','TROY','GPAY801',NULL,1,'2021-09-27 07:41:18','2022-01-07 15:00:52');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_TAP_T01','I_TAP_T01_CARDPAY201_BENEFIT_BH','10101','A46','A46','TAP101',NULL,1,'2021-09-27 07:41:19','2022-01-07 15:00:52'),
	 ('I_TPAY_P01','I_TPAY_P01_CARRIERBILLING201_ZAIN_SA','10101','A15','Zain','TPAY801',NULL,1,'2021-09-27 07:41:19','2022-01-07 15:00:52'),
	 ('I_YOUMI_P01','I_YOUMI_P01_VIRTUALACCOUNT201_ALFABANK_RU','10101','BT','ALFA','YOUMI801',NULL,1,'2021-09-27 07:41:19','2022-01-07 15:00:52'),
	 ('I_GPAY_P01','I_GPAY_P01_WALLET201_BKMEXPRESS_TR','10101','1006','********','GPAY801',NULL,1,'2021-09-27 07:41:20','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_VIRTUALACCOUNT201_PAGOEXPRESS_PY','10101','A50','PagoExpress','DLOCAL802',NULL,1,'2021-09-27 07:41:21','2022-03-14 05:51:06'),
	 ('I_XENDIT_P01_01','I_XENDIT_P01_01_WALLET201_GCASH_PH','10101','A4','A4','XENDIT801',NULL,1,'2021-09-27 07:41:21','2022-03-14 05:50:14'),
	 ('I_PAGSMILE_P01_01','I_PAGSMILE_P01_01_WALLET201_AME_BR','10101','1006','********','PAGSMILE801',NULL,1,'2021-09-27 07:41:22','2022-03-14 05:50:37'),
	 ('I_MELON_P01','I_MELON_P01_CARRIERBILLING201_XL_ID','10101','A15','XL','MELON801',NULL,1,'2021-09-27 07:41:22','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_01','I_DLOCAL_P01_01_WALLET201_PIX_BR','10101','PIX','PIX','DLOCAL801',NULL,1,'2021-09-27 07:41:23','2022-03-14 05:50:28'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_VIRTUALACCOUNT201_VCB_VN_3','10101','1012','VIETCOMBANK-3','NGANLUONG801',NULL,1,'2021-09-27 07:41:23','2022-01-07 15:00:52');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_VIRTUALACCOUNT201_SANTANDER_MX','10101','03','Santander','DLOCAL802',NULL,1,'2021-09-27 07:41:24','2022-03-14 05:50:50'),
	 ('I_GASH_P01','I_GASH_P01_CARRIERBILLING201_FETNET_TW','10101','CB','fetnet','GASH801',NULL,1,'2021-09-27 07:41:24','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_VIRTUALACCOUNT201_ACB_VN','10101','1003','ACB-1','NGANLUONG801',NULL,1,'2021-09-27 07:41:25','2022-01-07 15:00:52'),
	 ('I_ESPAY_T01','I_ESPAY_T01_VIRTUALACCOUNT201_CIMB_ID','10101','A18','CIMB','ESPAY101',NULL,1,'2021-09-27 07:41:26','2022-01-07 15:00:52'),
	 ('I_GASH_P01','I_GASH_P01_CARRIERBILLING201_APTG_TW','10101','CB','APTG','GASH801',NULL,1,'2021-09-27 07:41:26','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_VIRTUALACCOUNT201_ICB_VN_3','10101','1012','VIETINBANK-3','NGANLUONG801',NULL,1,'2021-09-27 07:41:26','2022-01-07 15:00:52'),
	 ('I_PAYTR_T01','I_PAYTR_T01_VIRTUALACCOUNT201_FINANSBANK_TR','10101','A18','finansbank','PAYTR101',NULL,1,'2021-09-27 07:41:27','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_NETBANKING201_BAB_VN','10101','1010','********','NGANLUONG801',NULL,1,'2021-09-27 07:41:28','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_CARDPAY201_MASTERCARD_CL','10101','20','20','DLOCAL802',NULL,1,'2021-09-27 07:41:28','2022-03-14 05:50:39'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_VIRTUALACCOUNT201_PGB_VN_1','10101','1012','PGBANK-3','NGANLUONG801',NULL,1,'2021-09-27 07:41:28','2022-01-07 15:00:52');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_GPAY_P01','I_GPAY_P01_VIRTUALACCOUNT201_AKBANK_TR','10101','A18','akbank','GPAY801',NULL,1,'2021-09-27 07:41:29','2022-01-07 15:00:52'),
	 ('I_DRAGONPAY_P01','I_DRAGONPAY_P01_WALLET201_GCASH_PH','10101','A4','A4','DPAY101',NULL,1,'2021-09-27 07:41:30','2022-02-24 14:26:45'),
	 ('I_FAWRY_P01','I_FAWRY_P01_VIRTUALACCOUNT201_FAWRY_EG','10101','A47','A47','FAWRY801',NULL,1,'2021-09-27 07:41:30','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_NETBANKING201_ACB_VN','10101','1010','********','NGANLUONG801',NULL,1,'2021-09-27 07:41:31','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_VIRTUALACCOUNT201_EFECTY_CO','10101','A50','Efecty','DLOCAL802',NULL,1,'2021-09-27 07:41:31','2022-03-14 05:50:44'),
	 ('I_PAYTR_T01','I_PAYTR_T01_VIRTUALACCOUNT201_ISBANK_TR','10101','A18','isbank','PAYTR101',NULL,1,'2021-09-27 07:41:31','2022-01-07 15:00:52'),
	 ('I_GPAY_P01','I_GPAY_P01_PREPAID201_ININAL_TR','10101','********','********','GPAY801',NULL,1,'2021-09-27 07:41:32','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_01','I_DLOCAL_P01_01_WALLET201_WALLET_IN','10103','05','05','DLOCAL801',NULL,1,'2021-09-27 07:41:33','2022-03-14 05:49:50'),
	 ('I_XENDIT_T01','I_XENDIT_T01_VIRTUALACCOUNT201_INDOMARET_ID','10101','A12','A12','XENDIT02',NULL,1,'2021-09-27 07:41:33','2022-01-07 15:00:52'),
	 ('I_GASH_P01','I_GASH_P01_CARRIERBILLING201_TAIWANMOBILE_TW','10101','CB','TaiwanMobile','GASH801',NULL,1,'2021-09-27 07:41:34','2022-01-07 15:00:52');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_YOUMI_P01','I_YOUMI_P01_WALLET201_YOUMI_RU','10101','YOUMI','YOUMI','YOUMI801',NULL,1,'2021-09-27 07:41:34','2022-01-07 15:00:52'),
	 ('I_OMISE_P01','I_OMISE_P01_NETBANKING201_BANGKOK_TH','10101','03','BANGKOK','OMISE801',NULL,1,'2021-09-27 07:41:35','2022-01-07 15:00:52'),
	 ('I_CODA_T01','I_CODA_T01_WALLET201_DANA_ID','10101','A7','A7','CODA101',NULL,1,'2021-09-27 07:41:36','2022-01-07 15:00:52'),
	 ('I_MCP_P01_01','I_MCP_P01_01_VIRTUALACCOUNT201_MANDIRI_ID','10101','A18','MANDIRI','MCP801',NULL,1,'2021-09-27 07:41:36','2022-03-14 05:50:06'),
	 ('I_CODA_T01','I_CODA_T01_WALLET201_DOLFIN_TH','10101','DOLFIN','DOLFIN','CODA101',NULL,1,'2021-09-27 07:41:36','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_NETBANKING201_OCB_VN','10101','1010','********','NGANLUONG801',NULL,1,'2021-09-27 07:41:37','2022-01-07 15:00:52'),
	 ('I_GPAY_P01','I_GPAY_P01_WALLET201_GPAY_TR','10101','1006','********','GPAY801',NULL,1,'2021-09-27 07:41:37','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_01','I_DLOCAL_P01_01_CARDPAY201_AMEX_AR','10101','20','20','DLOCAL801',NULL,1,'2021-09-27 07:41:38','2022-03-14 05:51:01'),
	 ('I_MCP_P01_01','I_MCP_P01_01_VIRTUALACCOUNT201_BRI_ID','10101','A18','BRI','MCP801',NULL,1,'2021-09-27 07:41:38','2022-03-14 05:50:04'),
	 ('I_TOSS_P01_01','I_TOSS_P01_01_WALLET201_TOSS_KR','10101','TOSS','TOSS','TOSS801',NULL,1,'2021-09-27 07:41:39','2022-03-14 05:50:23');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_ALIPAYHK_T01_02','I_ALIPAYHK_T01_02_AUTODEBIT201_ALIPAYHK_HK','10101','1009','********','ALIPAY102',NULL,1,'2021-09-27 07:41:39','2022-03-14 05:50:21'),
	 ('I_REDPAY_P01','I_REDPAY_P01_WALLET201_GOPAY_ID','10101','A2','A2','REDPAY801',NULL,1,'2021-09-27 07:41:40','2022-01-07 15:00:52'),
	 ('I_KSHER_P01','I_KSHER_P01_WALLET201_PROMPTPAY_TH','10101','PROMPTPAY','PROMPTPAY','KSHER801',NULL,1,'2021-09-27 07:41:40','2022-01-07 15:00:52'),
	 ('I_RAZER_P01','I_RAZER_P01_CARRIERBILLING201_DTAC_TH','10101','A15','DTAC','RAZER801',NULL,1,'2021-09-27 07:41:41','2022-01-07 15:00:52'),
	 ('I_PAYTR_T01','I_PAYTR_T01_CARDPAY201_VISA_TR','10101','20','TROY','PAYTR101',NULL,1,'2021-09-27 07:41:41','2022-01-07 15:00:52'),
	 ('I_ALIPAYHK_T01_02','I_ALIPAYHK_T01_02_AUTODEBIT201_DANA_ID','10101','AD','DANAAD','ALIPAY102',NULL,1,'2021-09-27 07:41:41','2022-06-14 12:26:11'),
	 ('I_OMISE_P01','I_OMISE_P01_NETBANKING201_KRUNGTHAI_TH','10101','03','KRUNGTHAI','OMISE801',NULL,1,'2021-09-27 07:41:42','2022-01-07 15:00:52'),
	 ('I_FAWRY_T01','I_FAWRY_T01_CARDPAY201_VISA_EG','10101','A34','A34','FAWRY101',NULL,1,'2021-09-27 07:41:42','2022-01-07 15:00:52'),
	 ('I_REDPAY_P01','I_REDPAY_P01_CARRIERBILLING201_XL_ID','10101','A15','XL','REDPAY801',NULL,1,'2021-09-27 07:41:43','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_NETBANKING201_DAB_VN_1','10101','1010','********','NGANLUONG801',NULL,1,'2021-09-27 07:41:43','2022-01-07 15:00:52');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_RAZER_P01','I_RAZER_P01_WALLET201_RAZER_TH','10101','RAZERGOLD','RAZERGOLD','RAZER801',NULL,1,'2021-09-27 07:41:43','2022-01-07 15:00:52'),
	 ('I_SKYPAY_P01','I_SKYPAY_P01_VIRTUALACCOUNT201_TRUEMONEY_PH','10101','A13','TRUEMONEY','SKYPAY801',NULL,1,'2021-09-27 07:41:44','2022-01-07 15:00:52'),
	 ('I_TAP_T01','I_TAP_T01_CARDPAY201_VISA_EG','10101','A34','A34','TAP101',NULL,1,'2021-09-27 07:41:44','2022-01-07 15:00:52'),
	 ('I_PAGSMILE_P01_01','I_PAGSMILE_P01_01_WALLET201_PIX_BR','10101','PIX','PIX','PAGSMILE801',NULL,1,'2021-09-27 07:41:46','2022-03-14 05:50:36'),
	 ('I_FASPAY_P01','I_FASPAY_P01_WALLET201_LINKAJA_ID','10101','A35','A35','FASPAY801',NULL,1,'2021-09-27 07:41:46','2022-01-07 15:00:52'),
	 ('I_KSHER_P01','I_KSHER_P01_WALLET201_TRUEMONEY_TH','10101','TRUEMONEY','TRUEMONEY','KSHER801',NULL,1,'2021-09-27 07:41:46','2022-01-07 15:00:52'),
	 ('I_FASPAY_P01','I_FASPAY_P01_VIRTUALACCOUNT201_BNI_ID','10101','A18','BNI','FASPAY801',NULL,1,'2021-09-27 07:41:47','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_05','I_DLOCAL_P01_05_WALLET201_UPI_IN_2','10103','00','00','DLOCAL805',NULL,1,'2021-09-27 07:41:48','2022-03-14 05:49:55'),
	 ('I_PAYBY_P01','I_PAYBY_P01_CARDPAY201_MASTERCARD_AE','10101','A34','A34','PAYBY801',NULL,1,'2021-09-27 07:41:48','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_VIRTUALACCOUNT201_BIDV_VN_1','10101','1012','BIDV-3','NGANLUONG801',NULL,1,'2021-09-27 07:41:49','2022-01-07 15:00:52');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_DRAGONPAY_P01','I_DRAGONPAY_P01_VIRTUALACCOUNT201_RURALNET_PH','10101','A13','RLNT','DPAY102',NULL,1,'2021-09-27 07:41:49','2022-02-24 14:26:45'),
	 ('I_XENDIT_P01_01','I_XENDIT_P01_01_DIRECTDEBIT201_UNIONBANK_PH','10101','1013','********','XENDIT801',NULL,1,'2021-09-27 07:41:50','2022-03-14 05:50:16'),
	 ('I_TAP_T01','I_TAP_T01_CARDPAY201_MASTERCARD_EG','10101','A34','A34','TAP101',NULL,1,'2021-09-27 07:41:50','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_01','I_DLOCAL_P01_01_CARDPAY201_MASTERCARD_EC','10101','20','20','DLOCAL801',NULL,1,'2021-09-27 07:41:50','2022-03-14 05:51:07'),
	 ('I_DLOCAL_P01_01','I_DLOCAL_P01_01_CARDPAY201_VISA_ZA','10101','20','20','DLOCAL801',NULL,1,'2021-09-27 07:41:51','2022-03-14 05:51:09'),
	 ('I_DLOCAL_P01_01','I_DLOCAL_P01_01_CARDPAY201_MASTERCARD_MX','10101','20','20','DLOCAL801',NULL,1,'2021-09-27 07:41:52','2022-03-14 05:50:47'),
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_VIRTUALACCOUNT201_BBVA_MX','10101','03','BBVA','DLOCAL802',NULL,1,'2021-09-27 07:41:53','2022-03-14 05:50:49'),
	 ('I_CODA_T01','I_CODA_T01_CARRIERBILLING201_ETISALAT_AE','10101','A15','Etisalat','CODA101',NULL,1,'2021-09-27 07:41:53','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_VIRTUALACCOUNT201_BANORTE_MX','10101','03','Banorte','DLOCAL802',NULL,1,'2021-09-27 07:41:53','2022-03-14 05:50:49'),
	 ('I_DLOCAL_P01_05','I_DLOCAL_P01_05_WALLET201_WALLET_IN_2','10103','05','05','DLOCAL805',NULL,1,'2021-09-27 07:41:54','2022-03-14 05:49:54');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_NETBANKING201_SEA_VN','10101','1010','********','NGANLUONG801',NULL,1,'2021-09-27 07:41:55','2022-01-07 15:00:52'),
	 ('I_PAYTR_T01','I_PAYTR_T01_VIRTUALACCOUNT201_DENIZBANK_TR','10101','A18','denizbank','PAYTR101',NULL,1,'2021-09-27 07:41:57','2022-01-07 15:00:52'),
	 ('I_REVPAY_P01','I_REVPAY_P01_WALLET201_GRABPAY_MY','10101','A39','A39','REVPAY801',NULL,1,'2021-09-27 07:41:58','2022-01-07 15:00:52'),
	 ('I_PAYTR_T01','I_PAYTR_T01_VIRTUALACCOUNT201_VAKIFBANK_TR','10101','A18','vakifbank','PAYTR101',NULL,1,'2021-09-27 07:41:58','2022-01-07 15:00:52'),
	 ('I_PAYBY_P01','I_PAYBY_P01_CARDPAY201_VISA_AE','10101','A34','A34','PAYBY801',NULL,1,'2021-09-27 07:42:00','2022-01-07 15:00:52'),
	 ('I_ESPAY_T01','I_ESPAY_T01_VIRTUALACCOUNT201_MAYBANK_ID','10101','A18','MAYBANK','ESPAY101',NULL,1,'2021-09-27 07:42:00','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_01','I_DLOCAL_P01_01_WALLET201_UPI_IN','10103','00','00','DLOCAL801',NULL,1,'2021-09-27 07:42:01','2022-03-14 05:49:50'),
	 ('I_GPAY_P01','I_GPAY_P01_VIRTUALACCOUNT201_SEKERBANK_TR','10101','A18','********','GPAY801',NULL,1,'2021-09-27 07:42:02','2022-01-07 15:00:52'),
	 ('I_FAWRY_P01','I_FAWRY_P01_CARDPAY201_VISA_EG','10101','A34','A34','FAWRY801',NULL,1,'2021-09-27 07:42:02','2022-01-07 15:00:52'),
	 ('I_TAP_T01','I_TAP_T01_CARDPAY201_VISA_SA','10101','A34','A34','TAP101',NULL,1,'2021-09-27 07:42:02','2022-01-07 15:00:52');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_DLOCAL_P01_01','I_DLOCAL_P01_01_CARDPAY201_MASTERCARD_UY','10101','20','20','DLOCAL801',NULL,1,'2021-09-27 07:42:03','2022-03-14 05:50:58'),
	 ('I_GASH_P01','I_GASH_P01_CARRIERBILLING201_CHTADSL_TW','10101','CB','CHTADSL','GASH801',NULL,1,'2021-09-27 07:42:03','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_VIRTUALACCOUNT201_STB_VN_1','10101','1011','SACOMBANK-2','NGANLUONG801',NULL,1,'2021-09-27 07:42:04','2022-01-07 15:00:52'),
	 ('I_PAGSMILE_P01_01','I_PAGSMILE_P01_01_VIRTUALACCOUNT201_BOLETO_BR','10101','A50','Boleto','PAGSMILE801',NULL,1,'2021-09-27 07:42:05','2022-03-14 05:50:35'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_VIRTUALACCOUNT201_MSB_VN_1','10101','1011','MARITIMEBANK-2','NGANLUONG801',NULL,1,'2021-09-27 07:42:05','2022-01-07 15:00:52'),
	 ('I_KSHER_T01','I_KSHER_T01_WALLET201_LINEPAY_TH','10101','LINEPAY','LINEPAY','KSHER101',NULL,1,'2021-09-27 07:42:06','2022-01-07 15:00:52'),
	 ('I_KSHER_P01','I_KSHER_P01_WALLET201_LINEPAY_TH','10101','LINEPAY','LINEPAY','KSHER801',NULL,1,'2021-09-27 07:42:06','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_NETBANKING201_TPB_VN_1','10101','1010','********','NGANLUONG801',NULL,1,'2021-09-27 07:42:08','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_VIRTUALACCOUNT201_ICB_VN','10101','1003','VIETINBANK-1','NGANLUONG801',NULL,1,'2021-09-27 07:42:08','2022-01-07 15:00:52'),
	 ('I_ALIPAYHK_P01','I_ALIPAYHK_P01_WALLET201_GCASH_PH','10101','A4','A4','ALIPAY801',NULL,1,'2021-09-27 07:42:09','2022-01-07 15:00:52');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_RAZER_P01','I_RAZER_P01_CARRIERBILLING201_AIS_TH','10101','A15','AIS','RAZER801',NULL,1,'2021-09-27 07:42:10','2022-01-07 15:00:52'),
	 ('I_GPAY_P01','I_GPAY_P01_VIRTUALACCOUNT201_FINANSBANK_TR','10101','A18','finansbank','GPAY801',NULL,1,'2021-09-27 07:42:11','2022-01-07 15:00:52'),
	 ('I_CODA_T01','I_CODA_T01_CARRIERBILLING201_TRUEMOVETH_TH','10101','A15','TruemoveTH','CODA101',NULL,1,'2021-09-27 07:42:12','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_VIRTUALACCOUNT201_DAB_VN_1','10101','1012','DONGABANK-3','NGANLUONG801',NULL,1,'2021-09-27 07:42:13','2022-01-07 15:00:52'),
	 ('I_OMISE_P01','I_OMISE_P01_WALLET201_LINEPAY_TH','10101','LINEPay','LINEPay','OMISE801',NULL,1,'2021-09-27 07:42:14','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_VIRTUALACCOUNT201_AGB_VN','10101','1011','AGRIBANK-2','NGANLUONG801',NULL,1,'2021-09-27 07:42:14','2022-01-07 15:00:52'),
	 ('I_CODA_T01','I_CODA_T01_CARRIERBILLING201_ZAIN_BH','10101','A15','Zain','CODA101',NULL,1,'2021-09-27 07:42:15','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_VIRTUALACCOUNT201_OXXO_MX','10101','A50','OXXO','DLOCAL802',NULL,1,'2021-09-27 07:42:16','2022-03-14 05:50:48'),
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_VIRTUALACCOUNT201_PAGOEFECTIVO_EC','10101','A50','PagoEfectivo','DLOCAL802',NULL,1,'2021-09-27 07:42:16','2022-03-14 05:51:08'),
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_VIRTUALACCOUNT201_DAVIVIENDA_CO','10101','A50','Davivienda','DLOCAL802',NULL,1,'2021-09-27 07:42:16','2022-03-14 05:50:44');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_TAP_T01','I_TAP_T01_CARDPAY201_MASTERCARD_QA','10101','A34','A34','TAP101',NULL,1,'2021-09-27 07:42:17','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_NETBANKING201_GPB_VN','10101','1010','********','NGANLUONG801',NULL,1,'2021-09-27 07:42:18','2022-01-07 15:00:52'),
	 ('I_CODA_T01','I_CODA_T01_CARRIERBILLING201_SMARTSUN_PH','10101','A15','SmartSun','CODA101',NULL,1,'2021-09-27 07:42:18','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_01','I_DLOCAL_P01_01_CARDPAY201_MASTERCARD_BR','10101','20','20','DLOCAL801',NULL,1,'2021-09-27 07:42:19','2022-03-14 05:50:26'),
	 ('I_CODA_T01','I_CODA_T01_CARRIERBILLING201_DTAC_TH','10101','A15','DTAC','CODA101',NULL,1,'2021-09-27 07:42:19','2022-01-07 15:00:52'),
	 ('I_CODA_T01','I_CODA_T01_CARRIERBILLING201_UMOBILE_MY','10101','A15','Umobile','CODA101',NULL,1,'2021-09-27 07:42:20','2022-01-07 15:00:52'),
	 ('I_CODA_T01','I_CODA_T01_CARRIERBILLING201_TRIINDO_ID','10101','A15','Triindo','CODA101',NULL,1,'2021-09-27 07:42:21','2022-01-07 15:00:52'),
	 ('I_ESPAY_T01','I_ESPAY_T01_VIRTUALACCOUNT201_DANAMON_ID','10101','A18','DANAMON','ESPAY101',NULL,1,'2021-09-27 07:42:22','2022-01-07 15:00:52'),
	 ('I_CODA_T01','I_CODA_T01_WALLET201_GCASH_PH','10101','A4','A4','CODA101',NULL,1,'2021-09-27 07:42:23','2022-01-07 15:00:52'),
	 ('I_DRAGONPAY_P01','I_DRAGONPAY_P01_WALLET201_COINS_PH_PH','10101','A44','A44','DPAY101',NULL,1,'2021-09-27 07:42:23','2022-02-24 14:26:45');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_CARDPAY201_AMEX_CL','10101','20','20','DLOCAL802',NULL,1,'2021-09-27 07:42:24','2022-03-14 05:50:40'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_NETBANKING201_TCB_VN','10101','1002','TECHCOMBAK','NGANLUONG801',NULL,1,'2021-09-27 07:42:25','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_NETBANKING201_EXB_VN_1','10101','1010','********','NGANLUONG801',NULL,1,'2021-09-27 07:42:26','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_01','I_DLOCAL_P01_01_CARDPAY201_VISA_PE','10101','20','20','DLOCAL801',NULL,1,'2021-09-27 07:42:27','2022-03-14 05:50:52'),
	 ('I_XENDIT_P01_01','I_XENDIT_P01_01_WALLET201_PAYMAYA_PH','10101','1006','********','XENDIT801',NULL,1,'2021-09-27 07:42:27','2022-03-14 05:50:13'),
	 ('I_TAP_T01','I_TAP_T01_CARDPAY201_KNET_KW','10101','A44','A44','TAP101',NULL,1,'2021-09-27 07:42:28','2022-01-07 15:00:52'),
	 ('I_KCP_P01','I_KCP_P01_NETBANKING201_NETBANKING_KR','10101','1002','********','KCP101',NULL,1,'2021-09-27 07:42:29','2022-01-07 15:00:52'),
	 ('I_GPAY_P01','I_GPAY_P01_VIRTUALACCOUNT201_YAPIKREDI_TR','10101','A18','yapikredi','GPAY801',NULL,1,'2021-09-27 07:42:29','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_05','I_DLOCAL_P01_05_NETBANKING201_NETBANKING_IN_2','10103','03','03','DLOCAL805',NULL,1,'2021-09-27 07:42:29','2022-03-14 07:11:27'),
	 ('I_ESPAY_T01','I_ESPAY_T01_NETBANKING201_BRI_ID','10101','03','BRI-1','ESPAY101',NULL,1,'2021-09-27 07:42:30','2022-01-07 15:00:52');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_NETBANKING201_MSB_VN','10101','1010','********','NGANLUONG801',NULL,1,'2021-09-27 07:42:30','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_VIRTUALACCOUNT201_SHB_VN','10101','1011','SHB-2','NGANLUONG801',NULL,1,'2021-09-27 07:42:30','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_CARDPAY201_AMEX_PY','10101','20','20','DLOCAL802',NULL,1,'2021-09-27 07:42:31','2022-03-14 05:51:05'),
	 ('I_DRAGONPAY_P01','I_DRAGONPAY_P01_VIRTUALACCOUNT201_CEBUANA_PH','10101','A13','CEBL','DPAY102',NULL,1,'2021-09-27 07:42:31','2022-02-24 14:26:45'),
	 ('I_KSHER_T01','I_KSHER_T01_WALLET201_PROMPTPAY_TH','10101','PROMPTPAY','PROMPTPAY','KSHER101',NULL,1,'2021-09-27 07:42:32','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_CARDPAY201_VISA_PY','10101','20','20','DLOCAL802',NULL,1,'2021-09-27 07:42:33','2022-03-14 05:51:04'),
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_VIRTUALACCOUNT201_STP_MX','10101','03','STP','DLOCAL802',NULL,1,'2021-09-27 07:42:34','2022-03-14 05:50:50'),
	 ('I_XENDIT_T01','I_XENDIT_T01_VIRTUALACCOUNT201_MANDIRI_ID','10101','A18','MANDIRI','XENDIT02',NULL,1,'2021-09-27 07:42:35','2022-01-07 15:00:52'),
	 ('I_GPAY_P01','I_GPAY_P01_VIRTUALACCOUNT201_KUVEYTTURK_TR','10101','A18','********','GPAY801',NULL,1,'2021-09-27 07:42:36','2022-01-07 15:00:52'),
	 ('I_RAZER_P01','I_RAZER_P01_NETBANKING201_KRUNGTHAI_TH','10101','03','KRUNGTHAI','RAZER801',NULL,1,'2021-09-27 07:42:36','2022-01-07 15:00:52');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_ALIPAYHK_T01_03','I_ALIPAYHK_T01_03_WALLET201_DANA_ID','10101','A7','A7','ALIPAY103',NULL,1,'2021-09-27 07:42:37','2022-03-14 05:49:57'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_VIRTUALACCOUNT201_AGB_VN_1','10101','1012','AGRIBANK-3','NGANLUONG801',NULL,1,'2021-09-27 07:42:38','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_NETBANKING201_EXB_VN','10101','1002','EXIMBANK','NGANLUONG801',NULL,1,'2021-09-27 07:42:39','2022-01-07 15:00:52'),
	 ('I_TAP_T01','I_TAP_T01_CARDPAY201_VISA_AE','10101','A34','A34','TAP101',NULL,1,'2021-09-27 07:42:39','2022-01-07 15:00:52'),
	 ('I_ALIPAYHK_T01_03','I_ALIPAYHK_T01_03_WALLET201_GCASH_PH','10101','A4','A4','ALIPAY103',NULL,1,'2021-09-27 07:42:40','2022-03-14 05:50:10'),
	 ('I_ALIPAYHK_V4','I_ALIPAYHK_V4_WALLET201_GCASH_PH','10101','A4','A4','ALIPAY103',NULL,1,'2021-09-27 07:42:40','2022-03-14 05:50:10'),
	 ('I_FASPAY_P01','I_FASPAY_P01_VIRTUALACCOUNT201_ALFAMART_ID','10101','A11','A11','FASPAY801',NULL,1,'2021-09-27 07:42:40','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_VIRTUALACCOUNT201_BCP_PE','10101','03','BCP','DLOCAL802',NULL,1,'2021-09-27 07:42:40','2022-03-14 05:50:55'),
	 ('I_PAYTR_T01','I_PAYTR_T01_VIRTUALACCOUNT201_YAPIKREDI_TR','10101','A18','yapikredi','PAYTR101',NULL,1,'2021-09-27 07:42:41','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_NETBANKING201_LVB_VN','10101','1010','********','NGANLUONG801',NULL,1,'2021-09-27 07:42:42','2022-01-07 15:00:52'),
	 ('I_GPAY_P01','I_GPAY_P01_VIRTUALACCOUNT201_GARABTIBANK_TR','10101','A18','********','GPAY801',NULL,1,'2021-09-27 07:42:43','2022-01-07 15:00:52');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_CODA_T01','I_CODA_T01_WALLET201_GOPAY_ID','10101','A2','A2','CODA101',NULL,1,'2021-09-27 07:42:44','2022-01-07 15:00:52'),
	 ('I_ESPAY_T01','I_ESPAY_T01_VIRTUALACCOUNT201_BRI_ID','10101','A18','BNI','ESPAY101',NULL,1,'2021-09-27 07:42:44','2022-01-07 15:00:52'),
	 ('I_DRAGONPAY_P01','I_DRAGONPAY_P01_VIRTUALACCOUNT201_ECPAY_PH','10101','A13','ECPY','DPAY102',NULL,1,'2021-09-27 07:42:45','2022-02-24 14:26:45'),
	 ('I_DRAGONPAY_P01','I_DRAGONPAY_P01_VIRTUALACCOUNT201_ROBINSONS_PH','10101','A13','RDS','DPAY102',NULL,1,'2021-09-27 07:42:45','2022-02-24 14:26:45'),
	 ('I_RAZER_P01','I_RAZER_P01_WALLET201_TRUEMONEY_TH','10101','TRUEMONEY','TRUEMONEY','RAZER801',NULL,1,'2021-09-27 07:42:45','2022-01-07 15:00:52'),
	 ('I_FASTPAY_P01','I_FASTPAY_P01_BARCODE201_7-ELEVEN_TW','10101','1005','SEVENELEVEN','FASTPAY801',NULL,1,'2021-09-27 07:42:46','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_VIRTUALACCOUNT201_BIDV_VN','10101','1011','BIDV-2','NGANLUONG801',NULL,1,'2021-09-27 07:42:47','2022-01-07 15:00:52'),
	 ('I_REDPAY_P01','I_REDPAY_P01_CARRIERBILLING201_TELKOMSEL_ID','10101','A15','Telkomsel','REDPAY801',NULL,1,'2021-09-27 07:42:47','2022-01-07 15:00:52'),
	 ('I_ALIPAYHK_P01','I_ALIPAYHK_P01_WALLET201_KAKAOPAY_KR','10101','1006','KAKAOPAY','ALIPAY801',NULL,1,'2021-09-27 07:42:48','2022-01-07 15:00:52'),
	 ('I_GPAY_P01','I_GPAY_P01_VIRTUALACCOUNT201_DENIZBANK_TR','10101','A18','denizbank','GPAY801',NULL,1,'2021-09-27 07:42:49','2022-01-07 15:00:52');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_ALIPAYHK_T01_02','I_ALIPAYHK_T01_02_AUTODEBIT201_KAKAOPAY_KR','10101','1009','********','ALIPAY102',NULL,1,'2021-09-27 07:42:49','2022-03-14 05:50:22'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_NETBANKING201_DAB_VN','10101','1002','DONGABANK','NGANLUONG801',NULL,1,'2021-09-27 07:42:50','2022-01-07 15:00:52'),
	 ('I_CODA_T01','I_CODA_T01_WALLET201_LINKAJA_ID','10101','A35','A35','CODA101',NULL,1,'2021-09-27 07:42:50','2022-01-07 15:00:52'),
	 ('I_REDPAY_P01','I_REDPAY_P01_CARRIERBILLING201_SMARTFREN_ID','10101','A15','Smartfren','REDPAY801',NULL,1,'2021-09-27 07:42:51','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_CARDPAY201_VISA_CL','10101','20','20','DLOCAL802',NULL,1,'2021-09-27 07:42:51','2022-03-14 05:50:38'),
	 ('I_GASH_P01','I_GASH_P01_CARDPAY201_UNIONPAY_TW','10101','1001','********','GASH801',NULL,1,'2021-09-27 07:42:53','2022-01-07 15:00:52'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_NETBANKING201_BIDV_VN_1','10101','1010','********','NGANLUONG801',NULL,1,'2021-09-27 07:42:53','2022-01-07 15:00:52'),
	 ('I_CODA_T01','I_CODA_T01_CARRIERBILLING201_CELCOM_MY','10101','A15','Celcom','CODA101',NULL,1,'2021-09-27 07:42:54','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_01','I_DLOCAL_P01_01_CARDPAY201_VISA_BR','10101','20','20','DLOCAL801',NULL,1,'2021-09-27 07:42:55','2022-03-14 05:50:26'),
	 ('I_NGANLUONG_P01','I_NGANLUONG_P01_VIRTUALACCOUNT201_ACB_VN_3','10101','1012','ACB-3','NGANLUONG801',NULL,1,'2021-09-27 07:42:56','2022-01-07 15:00:52');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_DLOCAL_P01_02','I_DLOCAL_P01_02_VIRTUALACCOUNT201_PAGOEFECTIVO_PE','10101','A50','PagoEfectivo','DLOCAL802',NULL,1,'2021-09-27 07:42:57','2022-03-14 05:50:54'),
	 ('I_TAP_T01','I_TAP_T01_CARDPAY201_MASTERCARD_JO','10101','A34','A34','TAP101',NULL,1,'2021-09-27 07:42:57','2022-01-07 15:00:52'),
	 ('I_DLOCAL_P01_01','I_DLOCAL_P01_01_CARDPAY201_AMEX_PE','10101','20','20','DLOCAL801',NULL,1,'2021-09-27 07:42:58','2022-03-14 05:50:53'),
	 ('I_KCP_P01','I_KCP_P01_CARDPAY201_CARDPAY_KR','10101','1001','********','KCP101',NULL,1,'2021-09-27 07:42:59','2022-01-07 15:00:52'),
	 ('I_GPAY_P01','I_GPAY_P01_VIRTUALACCOUNT201_TURKIYEFINANSKATILIM_TR','10101','A18','********','GPAY801',NULL,1,'2021-09-27 07:42:59','2022-01-07 15:00:52'),
	 ('I_DRAGONPAY_P01','I_DRAGONPAY_P01_WALLET201_DRAGONPAY_PH','10101','A38','A38','DPAY102',NULL,1,'2021-09-27 07:43:00','2022-02-24 14:26:45'),
	 ('I_XENDIT_P01_01','I_XENDIT_P01_01_WALLET201_GRABPAY_PH','10101','A39','A39','XENDIT801',NULL,1,'2021-09-27 07:43:01','2022-03-14 05:50:14'),
	 ('I_TAP_T01','I_TAP_T01_VIRTUALACCOUNT201_FAWRY_EG','10101','A47','A47','TAP101',NULL,1,'2021-09-27 07:43:01','2022-01-07 15:00:52'),
	 ('I_GASH_P01','I_GASH_P01_VIRTUALACCOUNT201_YUSHANBANK_TW','10101','VA','YUSHANBANK','GASH801',NULL,1,'2021-10-11 05:52:40','2022-01-07 15:00:52'),
	 ('I_MCP_P01_02','I_MCP_P01_02_WALLET201_OVO_ID','10101','A36','A36','MCP802',NULL,1,'2021-10-14 07:45:34','2022-03-14 05:50:07');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_MIDTRANS_P01_01','I_MIDTRANS_P01_01_WALLET201_GOPAY_ID','10101','A2','A2','MIDTRANS801',NULL,1,'2021-10-28 09:21:21','2022-03-14 05:50:08'),
	 ('I_MIDTRANS_P01_02','I_MIDTRANS_P01_02_WALLET201_GOPAY_ID_1','10101','A2','A2','MIDTRANS802',NULL,1,'2021-10-28 09:21:21','2022-03-14 05:50:09'),
	 ('I_YOUMI_P01','I_YOUMI_P01_WALLET201_QIWI_RU','10101','QIWI','QIWI','YOUMI801',NULL,1,'2021-10-28 09:21:22','2022-01-07 15:00:52'),
	 ('I_BOA_P01','I_BOA_P01_VIRTUALACCOUNT201_BRADESCO_BR','10101','03','Bradesco','BOACOMPRA801',NULL,1,'2021-10-28 09:21:22','2022-01-07 15:00:52'),
	 ('I_BOA_P01','I_BOA_P01_WALLET201_PAYPAL_BR','10101','1006','PAYPAL','BOACOMPRA801',NULL,1,'2021-10-28 09:21:22','2022-01-07 15:00:52'),
	 ('I_PAGSMILE_P01_02','I_PAGSMILE_P01_02_VIRTUALACCOUNT201_SPEI_MX','10101','03','SPEI','PAGSMILE802',NULL,1,'2021-10-28 09:21:23','2022-03-14 05:50:51'),
	 ('I_BOA_P01','I_BOA_P01_WALLET201_PAGSEGURO_BR','10101','1006','PAGSEGURO','BOACOMPRA801',NULL,1,'2021-10-28 09:21:23','2022-02-21 13:00:17'),
	 ('I_BOA_P01','I_BOA_P01_VIRTUALACCOUNT201_SANTANDER_BR','10101','03','Santander','BOACOMPRA801',NULL,1,'2021-10-28 09:21:23','2022-01-07 15:00:52'),
	 ('I_BOA_P01','I_BOA_P01_WALLET201_PIX_BR','10101','PIX','PIX','BOACOMPRA801',NULL,1,'2021-10-28 09:21:24','2022-01-07 15:00:52'),
	 ('I_BOA_P01','I_BOA_P01_VIRTUALACCOUNT201_BB_BR','10101','03','BB','BOACOMPRA801',NULL,1,'2021-10-28 09:21:24','2022-01-07 15:00:52');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_SHOPEEPAYID_P01','I_SHOPEEPAYID_P01_WALLET201_SHOPEEPAY_ID','10101','SHOPEEPAY','SHOPEEPAY','SHOPEEPAY_ID801',NULL,1,'2021-10-28 09:21:24','2022-01-07 15:00:52'),
	 ('I_BOA_P01','I_BOA_P01_VIRTUALACCOUNT201_BOLETO_FAST_BR','10101','A51','Boletofast','BOACOMPRA801',NULL,1,'2021-10-28 09:21:24','2022-01-07 15:00:52'),
	 ('I_BOA_P01','I_BOA_P01_VIRTUALACCOUNT201_ITAU_BR','10101','03','Itau','BOACOMPRA801',NULL,1,'2021-10-28 09:21:25','2022-01-07 15:00:52'),
	 ('I_BOA_P01','I_BOA_P01_VIRTUALACCOUNT201_BOLETO_BR','10101','A50','Boleto','BOACOMPRA801',NULL,1,'2021-10-28 09:21:25','2022-01-07 15:00:52'),
	 ('I_BOA_P01','I_BOA_P01_CARDPAY201_HIPERCARD_BR','10101','20','20','BOACOMPRA801',NULL,1,'2021-10-28 09:21:25','2022-01-07 15:00:52'),
	 ('I_BOA_P01','I_BOA_P01_CARDPAY201_MASTERCARD_BR','10101','20','20','BOACOMPRA801',NULL,1,'2021-10-28 09:21:26','2022-01-07 15:00:52'),
	 ('I_BOA_P01','I_BOA_P01_CARDPAY201_ELO_BR','10101','20','20','BOACOMPRA801',NULL,1,'2021-10-28 09:21:26','2022-01-07 15:00:52'),
	 ('I_BOA_P01','I_BOA_P01_CARDPAY201_AMEX_BR','10101','20','20','BOACOMPRA801',NULL,1,'2021-10-28 09:21:26','2022-01-07 15:00:52'),
	 ('I_BOA_P01','I_BOA_P01_CARDPAY201_VISA_BR','10101','20','20','BOACOMPRA801',NULL,1,'2021-10-28 09:21:27','2022-01-07 15:00:52'),
	 ('I_WORLDPAY_P01','I_WORLDPAY_P01_CARDPAY201_VISA_*','10101','VISA','VISA','WORLDPAY801',NULL,1,'2021-12-10 02:11:15','2022-01-10 02:02:30');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_WORLDPAY_P01','I_WORLDPAY_P01_CARDPAY201_MASTERCARD_*','10101','MASTER','MASTER','WORLDPAY801',NULL,1,'2021-12-10 02:11:15','2022-01-10 02:02:37'),
	 ('I_CENTILI_P01_02','I_CENTILI_P01_02_CARRIERBILLING201_ZAIN_SA','10101','A15','Zain','CENTILI802',NULL,1,'2022-01-08 23:27:11','2022-03-14 05:49:49'),
	 ('I_ALIPAYHK_P01','I_ALIPAYHK_P01_WALLET201_LINEPAY_TH','10101','LINEPay','LINEPay','ALIPAY801',NULL,1,'2022-01-08 23:27:11','2022-01-08 23:27:11'),
	 ('I_FASTPAY_P01','I_FASTPAY_P01_VIRTUALACCOUNT201_FUBANGBANK_TW','10101','VA','FUBANGBANK','FASTPAY801',NULL,1,'2022-01-08 23:27:11','2022-01-08 23:27:11'),
	 ('I_YOUMI_P01','I_YOUMI_P01_CARDPAY201_VISA_RU','10101','CARD','CARD','YOUMI801',NULL,1,'2022-01-08 23:27:11','2022-01-08 23:27:11'),
	 ('I_YOUMI_P01','I_YOUMI_P01_CARDPAY201_MASTERCARD_RU','10101','CARD','CARD','YOUMI801',NULL,1,'2022-01-08 23:27:11','2022-02-24 05:32:41'),
	 ('I_YOUMI_P01','I_YOUMI_P01_CARDPAY201_AMEX_RU','10101','CARD','CARD','YOUMI801',NULL,1,'2022-01-08 23:27:11','2022-02-24 05:32:41'),
	 ('I_CENTILI_P01_02','I_CENTILI_P01_02_CARRIERBILLING201_DU_AE','10101','A15','********','CENTILI802',NULL,1,'2022-01-09 14:30:10','2022-03-14 05:49:47'),
	 ('I_CENTILI_P01_02','I_CENTILI_P01_02_CARRIERBILLING201_ETISALAT_AE','10101','A15','A15','CENTILI802',NULL,1,'2022-01-09 14:30:10','2022-03-14 05:49:48'),
	 ('I_PAYTR_T01','I_PAYTR_T01_VIRTUALACCOUNT201_TEB_TR','10101','A18','teb','PAYTR101',NULL,1,'2022-01-09 14:30:10','2022-01-09 14:30:10');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_GPAY_P01','I_GPAY_P01_VIRTUALACCOUNT201_TEB_TR','10101','A18','teb','GPAY801',NULL,1,'2022-01-09 14:30:10','2022-01-09 14:30:10'),
	 ('I_SHOPEEPAY_P01','I_SHOPEEPAY_P01_WALLET201_SHOPEEPAY_TH','10101','AIRPAY','AIRPAY','SHOPEEPAY801',NULL,1,'2022-01-09 14:30:10','2022-01-09 14:30:10'),
	 ('I_MCPAYMENT_P01','I_MCPAYMENT_P01_NETBANKING201_ENETS_SG','10101','ENETS','ENETS','MCPAYMENT801',NULL,1,'2022-01-09 14:30:10','2022-01-09 14:30:10'),
	 ('I_MCPAYMENT_P01','I_MCPAYMENT_P01_WALLET201_PAYNOW_SG','10101','PAYNOW','PAYNOW','MCPAYMENT801',NULL,1,'2022-01-09 14:30:10','2022-01-09 14:30:10'),
	 ('I_MCPAYMENT_P01','I_MCPAYMENT_P01_WALLET201_GRABPAY_SG','10101','GRABPAY','GRABPAY','MCPAYMENT801',NULL,1,'2022-01-09 14:30:10','2022-01-09 14:30:10'),
	 ('I_ALIPAYHK_P01','I_ALIPAYHK_P01_WALLET201_ALIPAY_CN','10101','EWALLET','ALIPAYCN','ALIPAY801',NULL,1,'2022-01-10 08:54:55','2022-03-25 05:33:51'),
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_VISA_AE','10101','A34','A34','APS811',NULL,1,'2022-01-10 08:57:07','2022-01-10 08:57:07'),
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_MASTERCARD_AE','10101','A34','A34','APS811',NULL,1,'2022-01-10 08:57:07','2022-01-10 08:57:07'),
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_AMEX_AE','10101','A34','A34','APS811',NULL,1,'2022-01-10 08:57:07','2022-01-10 08:57:07'),
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_VISA_BH','10101','A34','A34','APS811',NULL,1,'2022-01-10 08:57:07','2022-01-10 08:57:07');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_MASTERCARD_BH','10101','A34','A34','APS811',NULL,1,'2022-01-10 08:57:07','2022-01-10 08:57:07'),
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_AMEX_BH','10101','A34','A34','APS811',NULL,1,'2022-01-10 08:57:07','2022-01-10 08:57:07'),
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_VISA_EG','10101','A34','A34','APS811',NULL,1,'2022-01-10 08:57:07','2022-01-10 08:57:07'),
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_MASTERCARD_EG','10101','A34','A34','APS811',NULL,1,'2022-01-10 08:57:08','2022-01-10 08:57:08'),
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_AMEX_EG','10101','A34','A34','APS811',NULL,1,'2022-01-10 08:57:08','2022-01-10 08:57:08'),
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_MEEZA_EG','10101','20','20','APS811',NULL,1,'2022-01-10 08:57:08','2022-01-10 08:57:08'),
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_VISA_JO','10101','A34','A34','APS811',NULL,1,'2022-01-10 08:57:08','2022-01-10 08:57:08'),
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_MASTERCARD_JO','10101','A34','A34','APS811',NULL,1,'2022-01-10 08:57:08','2022-01-10 08:57:08'),
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_AMEX_JO','10101','A34','A34','APS811',NULL,1,'2022-01-10 08:57:08','2022-01-10 08:57:08'),
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_VISA_KW','10101','A34','A34','APS811',NULL,1,'2022-01-10 08:57:08','2022-01-10 08:57:08');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_MASTERCARD_KW','10101','A34','A34','APS811',NULL,1,'2022-01-10 08:57:08','2022-01-10 08:57:08'),
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_AMEX_KW','10101','A34','A34','APS811',NULL,1,'2022-01-10 08:57:09','2022-01-10 08:57:09'),
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_KNET_KW','10101','A44','A44','APS811',NULL,1,'2022-01-10 08:57:09','2022-01-10 08:57:09'),
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_VISA_LB','10101','A34','A34','APS811',NULL,1,'2022-01-10 08:57:09','2022-01-10 08:57:09'),
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_MASTERCARD_LB','10101','A34','A34','APS811',NULL,1,'2022-01-10 08:57:09','2022-01-10 08:57:09'),
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_AMEX_LB','10101','A34','A34','APS811',NULL,1,'2022-01-10 08:57:09','2022-01-10 08:57:09'),
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_VISA_OM','10101','A34','A34','APS811',NULL,1,'2022-01-10 08:57:09','2022-01-10 08:57:09'),
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_MASTERCARD_OM','10101','A34','A34','APS811',NULL,1,'2022-01-10 08:57:10','2022-01-10 08:57:10'),
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_AMEX_OM','10101','A34','A34','APS811',NULL,1,'2022-01-10 08:57:10','2022-01-10 08:57:10'),
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_VISA_QA','10101','A34','A34','APS811',NULL,1,'2022-01-10 08:57:10','2022-01-10 08:57:10');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_MASTERCARD_QA','10101','A34','A34','APS811',NULL,1,'2022-01-10 08:57:10','2022-01-10 08:57:10'),
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_AMEX_QA','10101','A34','A34','APS811',NULL,1,'2022-01-10 08:57:10','2022-01-10 08:57:10'),
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_NAPS_QA','10101','NAPS','NAPS','APS811',NULL,1,'2022-01-10 08:57:10','2022-01-10 08:57:10'),
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_VISA_SA','10101','A34','A34','APS811',NULL,1,'2022-01-10 08:57:10','2022-01-10 08:57:10'),
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_MASTERCARD_SA','10101','A34','A34','APS811',NULL,1,'2022-01-10 08:57:10','2022-01-10 08:57:10'),
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_AMEX_SA','10101','A34','A34','APS811',NULL,1,'2022-01-10 08:57:11','2022-01-10 08:57:11'),
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_MADA_SA','10101','A45','A45','APS811',NULL,1,'2022-01-10 08:57:11','2022-01-10 08:57:11'),
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_VISA_TR','10101','20','TROY','APS811',NULL,1,'2022-01-10 08:57:11','2022-03-11 02:56:05'),
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_MASTERCARD_TR','10101','20','TROY','APS811',NULL,1,'2022-01-10 08:57:11','2022-03-11 02:56:05'),
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_AMEX_TR','10101','20','TROY','APS811',NULL,1,'2022-01-10 08:57:11','2022-03-11 02:56:05');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_VISA_MA','10101','A34','A34','APS811',NULL,1,'2022-01-10 08:57:11','2022-01-10 08:57:11'),
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_MASTERCARD_MA','10101','A34','A34','APS811',NULL,1,'2022-01-10 08:57:11','2022-01-10 08:57:11'),
	 ('I_AMAZONPAY_NETWORK_S02','I_AMAZONPAY_NETWORK_S02_CARDPAY201_AMEX_MA','10101','A34','A34','APS811',NULL,1,'2022-01-10 08:57:11','2022-01-10 08:57:11'),
	 ('T_CITI_T01','T_CITI_T01_VIRTUALACCOUNT502_BANKTRANSFER_*','10101','BANKTRANSFER','BANKTRANSFER','CITI801',NULL,1,'2022-01-10 13:16:23','2022-01-10 13:16:23'),
	 ('T_DBS_P01','T_DBS_P01_VIRTUALACCOUNT502_BANKTRANSFER_*','10101','BANKTRANSFER','BANKTRANSFER','DBS801',NULL,1,'2022-01-10 13:16:24','2022-01-10 13:16:24'),
	 ('I_PAPARA_P01','I_PAPARA_P01_WALLET201_PAPARA_TR','10101','PAPARA','PAPARA','PAPARA801',NULL,1,'2022-01-25 07:10:31','2022-01-25 07:10:31'),
	 ('I_XENDIT_P01_02','I_XENDIT_P01_02_VIRTUALACCOUNT201_BRI_ID','10101','A18','BRI','XENDIT802',NULL,1,'2022-01-25 07:10:32','2022-03-14 05:49:58'),
	 ('I_XENDIT_P01_02','I_XENDIT_P01_02_VIRTUALACCOUNT201_MANDIRI_ID','10101','A18','MANDIRI','XENDIT802',NULL,1,'2022-01-25 07:10:32','2022-03-14 05:49:58'),
	 ('I_XENDIT_P01_02','I_XENDIT_P01_02_VIRTUALACCOUNT201_BNI_ID','10101','A18','BNI','XENDIT802',NULL,1,'2022-01-25 07:10:32','2022-03-14 05:49:59'),
	 ('I_XENDIT_P01_02','I_XENDIT_P01_02_VIRTUALACCOUNT201_PERMATA_ID','10101','A18','PERMATA','XENDIT802',NULL,1,'2022-01-25 07:10:32','2022-03-14 05:50:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_XENDIT_P01_02','I_XENDIT_P01_02_VIRTUALACCOUNT201_ALFAMART_ID','10101','A11','A11','XENDIT802',NULL,1,'2022-01-25 07:10:32','2022-03-14 05:50:01'),
	 ('I_XENDIT_P01_02','I_XENDIT_P01_02_VIRTUALACCOUNT201_INDOMARET_ID','10101','A12','A12','XENDIT802',NULL,1,'2022-01-25 07:10:32','2022-03-14 05:50:01'),
	 ('I_XENDIT_P01_02','I_XENDIT_P01_02_WALLET201_OVO_ID','10101','A36','A36','XENDIT802',NULL,1,'2022-01-25 07:10:32','2022-03-14 05:50:02'),
	 ('I_XENDIT_P01_02','I_XENDIT_P01_02_WALLET201_LINKAJA_ID','10101','A35','A35','XENDIT802',NULL,1,'2022-01-25 07:10:33','2022-03-14 05:50:03'),
	 ('I_XENDIT_P01_02','I_XENDIT_P01_02_WALLET201_DANA_ID','10101','A7','A7','XENDIT802',NULL,1,'2022-01-25 07:10:33','2022-03-14 05:50:03'),
	 ('I_IPAY88_P01','I_IPAY88_P01_WALLET201_PROMPTPAY_TH','10101','PROMPTPAY','PROMPTPAY','IPAY88_801',NULL,1,'2022-01-25 07:10:33','2022-01-25 07:10:33'),
	 ('I_IPAY88_P01','I_IPAY88_P01_WALLET201_LINEPAY_TH','10101','LINEPAY','LINEPAY','IPAY88_801',NULL,1,'2022-01-25 07:10:33','2022-01-25 07:10:33'),
	 ('I_IPAY88_P01','I_IPAY88_P01_NETBANKING201_KASIKORN_TH','10101','03','KASIKORN','IPAY88_801',NULL,1,'2022-01-25 07:10:33','2022-01-25 07:10:33'),
	 ('I_IPAY88_P01','I_IPAY88_P01_NETBANKING201_BANGKOK_TH','10101','03','BANGKOK','IPAY88_801',NULL,1,'2022-01-25 07:10:33','2022-01-25 07:10:33'),
	 ('I_IPAY88_P01','I_IPAY88_P01_NETBANKING201_AYUDHYA_TH','10101','03','AYUDHYA','IPAY88_801',NULL,1,'2022-01-25 07:10:33','2022-01-25 07:10:33');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_IPAY88_P01','I_IPAY88_P01_NETBANKING201_SCB_TH_TH','10101','03','SIAMCOMMERCIAL','IPAY88_801',NULL,1,'2022-01-25 07:10:33','2022-01-25 07:10:33'),
	 ('T_CITI_T01','T_CITI_T01_VIRTUALACCOUNT502_BANKTRANSFER_HK','10101','BANKTRANSFER','BANKTRANSFER','CITI801',NULL,1,'2022-01-10 13:16:23','2022-01-10 13:16:23'),
	 ('I_YOUMI_P01','I_YOUMI_P01_CARDPAY201_MIR_RU','10101','CARD','CARD','YOUMI801',NULL,1,'2022-01-08 23:27:11','2022-01-08 23:27:11'),
	 ('I_YOUMI_P01','I_YOUMI_P01_CARDPAY201_JCB_RU','10101','CARD','CARD','YOUMI801',NULL,1,'2022-01-08 23:27:11','2022-01-08 23:27:11'),
	 ('I_QIWI_P01','I_QIWI_P01_WALLET201_QIWI_RU','10101','QIWI','QIWI','QIWI801',NULL,1,'2022-02-24 14:26:45','2022-02-24 14:26:45'),
	 ('I_OY_P01','I_OY_P01_VIRTUALACCOUNT201_BRI_ID','10101','BankTransfer','BRI','OY801',NULL,1,'2022-02-24 14:26:46','2022-02-24 14:26:46'),
	 ('I_OY_P01','I_OY_P01_VIRTUALACCOUNT201_PERMATA_ID','10101','BankTransfer','Permata','OY801',NULL,1,'2022-02-24 14:26:47','2022-02-24 14:26:47'),
	 ('I_OY_P01','I_OY_P01_VIRTUALACCOUNT201_CIMB_ID','10101','BankTransfer','CIMB','OY801',NULL,1,'2022-02-24 14:26:47','2022-02-24 14:26:47'),
	 ('I_OY_P01','I_OY_P01_VIRTUALACCOUNT201_MANDIRI_ID','10101','BankTransfer','Mandiri','OY801',NULL,1,'2022-02-24 14:26:48','2022-02-24 14:26:48'),
	 ('I_OY_P01','I_OY_P01_VIRTUALACCOUNT201_BNI_ID','10101','BankTransfer','BNI','OY801',NULL,1,'2022-02-24 14:26:48','2022-02-24 14:26:48');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_OY_P01','I_OY_P01_VIRTUALACCOUNT201_BTPN_ID','10101','BankTransfer','BTPN','OY801',NULL,1,'2022-02-24 14:26:49','2022-02-24 14:26:49'),
	 ('I_OY_P01','I_OY_P01_WALLET201_LINKAJA_ID','10101','A35','A35','OY801',NULL,1,'2022-02-24 14:26:50','2022-02-24 14:26:50'),
	 ('I_2C2P_P02','I_2C2P_P02_WALLET201_LINEPAY_TH','10101','LINEPAY','LINEPAY','2C2P801',NULL,1,'2022-02-24 14:26:50','2022-02-24 14:26:50'),
	 ('I_2C2P_P02','I_2C2P_P02_WALLET201_TRUEMONEY_TH','10101','TRUEMONEY','TRUEMONEY','2C2P801',NULL,1,'2022-02-24 14:26:51','2022-02-24 14:26:51'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_SETTLEORG_AR','10201','93','93','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_MTB_P01','O_MTB_P01_BANKTRANSFER101_SETTLEORG_BD','10201','93','93','MTB211',NULL,1,'2022-03-08 12:39:11','2022-03-21 08:37:46'),
	 ('O_MTB_P01','O_MTB_P01_WALLET101_BKASH_BD','10201','63','63','MTB211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_CEF_BR','10201','93','104','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BOCOM_BBM_BR','10201','93','107','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_WESTERN_UNION_BR','10201','93','119','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_RODOBENS_BR','10201','93','120','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_AGIPLAN_BR','10201','93','121','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BERJ_BR','10201','93','122','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_WOORIBANK_BR','10201','93','124','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_PLURAL_BR','10201','93','125','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BR_PARTNERS_BR','10201','93','126','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_MSBANK_BR','10201','93','128','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_UBS_BR_BR','10201','93','129','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_ICBC_BR','10201','93','132','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_CONNACOOCU_BR','10201','93','136','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_INTESA_SANPAOLO_BR','10201','93','139','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BEXS_BR','10201','93','144','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_COMMERZBANK_BR','10201','93','163','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_OLE_BONSUCESSO_BR','10201','93','169','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_ITAU_BBA_BR','10201','93','184','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BTG_PACTUAL_BR','10201','93','208','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_ORIGINAl_BR','10201','93','212','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_ARBI_BR','10201','93','213','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_JOHN_DEERE_BR','10201','93','217','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BONSUCESSO_BR','10201','93','218','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_AGRICOLE_CREDIT_BR','10201','93','222','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_FIBRA_BR','10201','93','224','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_CIFRA_BR','10201','93','233','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BRADESCO_BR','10201','93','237','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_CLASSICO_BR','10201','93','241','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_MAXIMA_BR','10201','93','243','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_ABCB_BR','10201','93','246','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BIU_BR','10201','93','249','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BCV_BR','10201','93','250','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BEXS_CORRETORA_BR','10201','93','253','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_PARANA_BR','10201','93','254','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_MONEYCORP_BR','10201','93','259','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_NUBANK_BR','10201','93','260','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_FATOR_BR','10201','93','265','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_CEDULA_BR','10201','93','266','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_HSBC_BR','10201','93','269','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_PAGSEGURO_INTERNET_BR','10201','93','290','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_NACION_AR_BR','10201','93','300','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BPP_IP_BR','10201','93','301','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BMG_BR','10201','93','318','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_CCB_BR','10201','93','320','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_MERCADOPAGO_BR','10201','93','323','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BARI_IEF_BR','10201','93','330','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_DIGIO_BR','10201','93','335','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BANCOC6_BR','10201','93','336','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_UNIBANCO_BR','10201','93','341','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BANCO_XP_BR','10201','93','348','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_SOCIETE_GENERALE_BR','10201','93','366','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_MIZUHO_BR','10201','93','370','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_JPMORGAN_BR','10201','93','376','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_MERCANTIL_BR','10201','93','389','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_FINANCIAMENTOS_BR','10201','93','394','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_KIRTON_BR','10201','93','399','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_CAPITAL_BR','10201','93','412','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_SAFRA_BR','10201','93','422','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_MUFGB_BR','10201','93','456','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BSMB_BR','10201','93','464','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_CAIXA_BR','10201','93','473','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_CITI_NA_BR','10201','93','477','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_ITAU_BR','10201','93','479','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_DEUTSCHE_ALEMAO_BR','10201','93','487','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_JPMORGAN_NA_BR','10201','93','488','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_ING_NV_BR','10201','93','492','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_SUISSE_CREDIT_BR','10201','93','505','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_LUSO_BR','10201','93','600','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_INDUSTRIAL_BR','10201','93','604','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BANCOVR_BR','10201','93','610','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_PAULISTA_BR','10201','93','611','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_GUANABARA_BR','10201','93','612','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_OMNI_BR','10201','93','613','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_PANAMERICANO_BR','10201','93','623','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_FICSA_BR','10201','93','626','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_SMARTBANK_BR','10201','93','630','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_RENDIMENTO_BR','10201','93','633','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_TRIANGULO_BR','10201','93','634','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_SOFISA_BR','10201','93','637','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_PINE_BR','10201','93','643','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_UNIBANCO_HOLDING_BR','10201','93','652','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_INDUSVAL_BR','10201','93','653','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_AJ_RENNER_BR','10201','93','654','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_VOTORANTIM_BR','10201','93','655','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_DAYCOVAL_BR','10201','93','707','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_OURINVEST_BR','10201','93','712','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_NEON_BR','10201','93','735','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_CETELEM_BR','10201','93','739','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_RIBEIRAO_PRETO_BR','10201','93','741','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_SEMEAR_BR','10201','93','743','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_CITI_BR','10201','93','745','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_Modal_BR','10201','93','746','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_RABOBANK_INTER_BR','10201','93','747','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_SICREDI_BR','10201','93','748','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_SCOTIABANK_BR','10201','93','751','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BNP_PARIBAS_BR','10201','93','752','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_CONTINENTAL_BR','10201','93','753','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_SISTEMA_BR','10201','93','754','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_MERRILL_LYNCH_BR','10201','93','755','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BANCOOB_BR','10201','93','756','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_KEB_HANA_BR','10201','93','757','DLOCAL211',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01_02','O_DLOCAL_P01_02_WALLET101_PIX_BR','10201','71','71','DLOCAL212',NULL,1,'2022-03-08 12:39:11','2022-03-10 07:20:27'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_WALLET101_PIX_BR','10201','71','71','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CEF_BR','10201','93','104','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BOCOM_BBM_BR','10201','93','107','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_WESTERN_UNION_BR','10201','93','119','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_RODOBENS_BR','10201','93','120','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_AGIPLAN_BR','10201','93','121','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BERJ_BR','10201','93','122','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_WOORIBANK_BR','10201','93','124','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_PLURAL_BR','10201','93','125','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BR_PARTNERS_BR','10201','93','126','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_MSBANK_BR','10201','93','128','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_UBS_BR_BR','10201','93','129','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_ICBC_BR','10201','93','132','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_INTESA_SANPAOLO_BR','10201','93','139','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BEXS_BR','10201','93','144','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_GUITTA_BR','10201','93','146','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_FACTA_BR','10201','93','149','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_COMMERZBANK_BR','10201','93','163','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BRL_TRUST_BR','10201','93','173','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_PEFISA_BR','10201','93','174','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_GUIDE_BR','10201','93','177','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CM_CCTVM_BR','10201','93','180','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_SCMEPP_BR','10201','93','183','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_ITAU_BBA_BR','10201','93','184','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_ATIVA_BR','10201','93','188','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_HS_FINANCEIRA_BR','10201','93','189','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_SERVICOOP_BR','10201','93','190','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_NOVA_FUTURA_BR','10201','93','191','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_PARMETAL_BR','10201','93','194','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_FAIR_BR','10201','93','196','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_STONE_PAGAMENTOS_BR','10201','93','197','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BTG_PACTUAL_BR','10201','93','208','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_ORIGINAl_BR','10201','93','212','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_ARBI_BR','10201','93','213','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_JOHN_DEERE_BR','10201','93','217','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BONSUCESSO_BR','10201','93','218','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_AGRICOLE_CREDIT_BR','10201','93','222','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_FIBRA_BR','10201','93','224','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CIFRA_BR','10201','93','233','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BRADESCO_BR','10201','93','237','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CLASSICO_BR','10201','93','241','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_MAXIMA_BR','10201','93','243','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_ABCB_BR','10201','93','246','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BIU_BR','10201','93','249','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BCV_BR','10201','93','250','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BEXS_CORRETORA_BR','10201','93','253','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_PARANA_BR','10201','93','254','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_MONEYCORP_BR','10201','93','259','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_FATOR_BR','10201','93','265','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CEDULA_BR','10201','93','266','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_HSBC_BR','10201','93','269','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_SAGITUR _BR','10201','93','270','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_IB_CCTVM_BR','10201','93','271','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_AGK_CC_BR','10201','93','272','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_SAO_MIGUEL_BR','10201','93','273','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_MONEY_PLUS_BR','10201','93','274','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BCO_SENFF_BR','10201','93','276','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_GENIAL_BR','10201','93','278','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_PRIMAVERA_BR','10201','93','279','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_WILL_BR','10201','93','280','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_COOPAVEL_BR','10201','93','281','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_RB_BR','10201','93','283','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_FRENTE_BR','10201','93','285','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_OURO_BR','10201','93','286','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CAROL_BR','10201','93','288','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_DECYSEO_BR','10201','93','289','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_PAGSEGURO_INTERNET_BR','10201','93','290','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_LASTRO_RDV_BR','10201','93','293','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_VISION_BR','10201','93','296','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_VIPS_CC_BR','10201','93','298','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_SOROCRED_BR','10201','93','299','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_NACION_AR_BR','10201','93','300','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BPP_IP_BR','10201','93','301','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_PORTOPAR_BR','10201','93','306','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_TERRA_BR','10201','93','307','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CAMBIONET_BR','10201','93','309','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_VORTX_BR','10201','93','310','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_DOURADA_BR','10201','93','311','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_HSCM_BR','10201','93','312','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_AMAZONIA_CC_BR','10201','93','313','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_PI_DTVM_BR','10201','93','315','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BMG_BR','10201','93','318','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_OM_DTVM_BR','10201','93','319','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CCB_BR','10201','93','320','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CREFAZ_BR','10201','93','321','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_ABELARDO_BR','10201','93','322','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_MERCADOPAGO_BR','10201','93','323','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CARTOS_BR','10201','93','324','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_ORAMA_BR','10201','93','325','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_PARATI_BR','10201','93','326','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CECM_FABRIC_BR','10201','93','328','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_QI_SCD_BR','10201','93','329','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BARI_IEF_BR','10201','93','330','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_FRAM_BR','10201','93','331','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_ACESSO_SP_BR','10201','93','332','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_DIGIO_BR','10201','93','335','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BANCOC6_BR','10201','93','336','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_UNIBANCO_BR','10201','93','341','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BANCO_XP_BR','10201','93','348','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_AL5_BR','10201','93','349','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CREHNOR_BR','10201','93','350','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_TORO_BR','10201','93','352','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_NECTON_BR','10201','93','354','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_OTIMO_BR','10201','93','355','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_MIDWAY_BR','10201','93','358','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_ZEMA_BR','10201','93','359','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_TRINUS_BR','10201','93','360','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CIELO_BR','10201','93','362','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_SINGULARE_BR','10201','93','363','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_GERENCIANET_BR','10201','93','364','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_SIMPAUL_BR','10201','93','365','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_SOCIETE_GENERALE_BR','10201','93','366','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_VITREO_BR','10201','93','367','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_MIZUHO_BR','10201','93','370','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_WARREN_BR','10201','93','371','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_UP_P_BR','10201','93','373','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_REALIZE_BR','10201','93','374','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_JPMORGAN_BR','10201','93','376','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BBC_LEASING_BR','10201','93','378','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CECM_COO_BR','10201','93','379','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_PICPAY_BR','10201','93','380','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_MERCEDES_BENZ_BR','10201','93','381','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_FIDUCIA_BR','10201','93','382','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_JUNO_BR','10201','93','383','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_GLOBAL_SCM_BR','10201','93','384','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_NU_FINANCEIRA_BR','10201','93','386','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_TOYOTA_BR','10201','93','387','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_MERCANTIL_BR','10201','93','389','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BCO_GM_BR','10201','93','390','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_IBIAM_BR','10201','93','391','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_VOLKSWAGEN_BR','10201','93','393','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_FINANCIAMENTOS_BR','10201','93','394','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_FD_GOLD_BR','10201','93','395','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_PAGAMENTOS_BR','10201','93','396','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_LISTO_BR','10201','93','397','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_IDEAL_BR','10201','93','398','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_KIRTON_BR','10201','93','399','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_POUP_SER_BR','10201','93','400','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_IUGU_BR','10201','93','401','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_COBUCCIO_BR','10201','93','402','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CORA_BR','10201','93','403','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_SUMUP_BR','10201','93','404','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_ACCREDITO_BR','10201','93','406','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_INDIGO_BR','10201','93','407','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BONUSPAGO_BR','10201','93','408','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_PLANNER_SCM_BR','10201','93','410','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_VIA_CERTA_BR','10201','93','411','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CAPITAL_BR','10201','93','412','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_LAMARA_BR','10201','93','416','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_NUMBRS_BR','10201','93','419','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_SAFRA_BR','10201','93','422','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CRED_UFES_BR','10201','93','427','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CRED_SYSTEM_BR','10201','93','428','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_PLANNER_TRUSTEE_BR','10201','93','438','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_MUFGB_BR','10201','93','456','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BSMB_BR','10201','93','464','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CAIXA_BR','10201','93','473','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CITI_NA_BR','10201','93','477','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_ITAU_BR','10201','93','479','PAGSMILE201',NULL,1,'2022-03-08 12:39:11','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_DEUTSCHE_ALEMAO_BR','10201','93','487','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_JPMORGAN_NA_BR','10201','93','488','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_ING_NV_BR','10201','93','492','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_SUISSE_CREDIT_BR','10201','93','505','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_LUSO_BR','10201','93','600','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_INDUSTRIAL_BR','10201','93','604','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BANCOVR_BR','10201','93','610','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_PAULISTA_BR','10201','93','611','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_GUANABARA_BR','10201','93','612','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_OMNI_BR','10201','93','613','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_PANAMERICANO_BR','10201','93','623','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_FICSA_BR','10201','93','626','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_SMARTBANK_BR','10201','93','630','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_RENDIMENTO_BR','10201','93','633','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_TRIANGULO_BR','10201','93','634','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_SOFISA_BR','10201','93','637','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_PINE_BR','10201','93','643','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_UNIBANCO_HOLDING_BR','10201','93','652','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_INDUSVAL_BR','10201','93','653','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_AJ_RENNER_BR','10201','93','654','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_VOTORANTIM_BR','10201','93','655','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_DAYCOVAL_BR','10201','93','707','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_OURINVEST_BR','10201','93','712','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CETELEM_BR','10201','93','739','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_RIBEIRAO_PRETO_BR','10201','93','741','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_SEMEAR_BR','10201','93','743','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CITI_BR','10201','93','745','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_Modal_BR','10201','93','746','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_RABOBANK_INTER_BR','10201','93','747','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_SICREDI_BR','10201','93','748','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_SCOTIABANK_BR','10201','93','751','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BNP_PARIBAS_BR','10201','93','752','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CONTINENTAL_BR','10201','93','753','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_SISTEMA_BR','10201','93','754','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_MERRILL_LYNCH_BR','10201','93','755','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BANCOOB_BR','10201','93','756','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_KEB_HANA_BR','10201','93','757','PAGSMILE201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BB_BR','10201','93','001','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_AMAZONIA_BR','10201','93','003','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_NORDESTE_BR','10201','93','004','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BNDES_BR','10201','93','007','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CCRCOO_BR','10201','93','010','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CSHGCOR_BR','10201','93','011','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_INBURSA_BR','10201','93','012','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_STATE_STREET _BR','10201','93','014','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_UBS_CTVM_BR','10201','93','015','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CCM_DESP_BR','10201','93','016','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BNY_MELLON_BR','10201','93','017','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_TRICURY_BR','10201','93','018','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BANESTES_BR','10201','93','021','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BANDEPE_BR','10201','93','024','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_ALFA_BR','10201','93','025','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_ITAU_CONSIGNADO_BR','10201','93','029','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_SANTANDER_BR','10201','93','033','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BRADESCO_BBI_BR','10201','93','036','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BANPARA_BR','10201','93','037','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CARGILL_BR','10201','93','040','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BANRISUL_BR','10201','93','041','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BANESE_BR','10201','93','047','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CONFIDENCE_CC_BR','10201','93','060','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_HIPERCARD_BR','10201','93','062','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BRADESCARD_BR','10201','93','063','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_GS_BR','10201','93','064','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_ANDBANK_BR','10201','93','065','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_MORGAN_STANLEY_BR','10201','93','066','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CREFISA_BR','10201','93','069','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BRASILIA_BR','10201','93','070','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_J_SAFRA_BR','10201','93','074','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_ABN_AMRO_BR','10201','93','075','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_KDB_BR','10201','93','076','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_INTER_BR','10201','93','077','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_HAITONG_BR','10201','93','078','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_ORIGINAL_AGR_BR','10201','93','079','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BT_CC_BR','10201','93','080','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BANCOSEGURO_BR','10201','93','081','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_TOPAZIO_BR','10201','93','082','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BOC_BR','10201','93','083','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_UNICRED_NORTE_BR','10201','93','084','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CECRED_BR','10201','93','085','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_RANDON_BR','10201','93','088','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CREDISAN_CC_BR','10201','93','089','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BRK_CFI_BR','10201','93','092','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_POLOCRED_SCMEPP_BR','10201','93','093','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_FINAXIS_BR','10201','93','094','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_TRAVELEX_BR','10201','93','095','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BANCOB3_BR','10201','93','096','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CREDISIS_BR','10201','93','097','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CREDIALIANCA_BR','10201','93','098','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_UNIPRIME_BR','10201','93','099','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_PLANNER_CV_BR','10201','93','100','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_RENASCENCA_BR','10201','93','101','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_XP_INVESTIMENTOS_BR','10201','93','102','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CEF_BR','10201','93','104','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_LECCA_BR','10201','93','105','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BOCOM_BBM_BR','10201','93','107','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_PORTOCRED_BR','10201','93','108','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_OLIVEIRA_TRUST_BR','10201','93','111','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_MAGLIANO_BR','10201','93','113','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CECOOP_BR','10201','93','114','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_ADVANCED_BR','10201','93','117','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_WESTERN_UNION_BR','10201','93','119','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_RODOBENS_BR','10201','93','120','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_AGIPLAN_BR','10201','93','121','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BERJ_BR','10201','93','122','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_WOORIBANK_BR','10201','93','124','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_PLURAL_BR','10201','93','125','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BR_PARTNERS_BR','10201','93','126','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CODEPE_BR','10201','93','127','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_MSBANK_BR','10201','93','128','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_UBS_BR_BR','10201','93','129','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CARUANA_BR','10201','93','130','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_TULLETT_PREBON_BR','10201','93','131','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_ICBC_BR','10201','93','132','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CRESOL_BR','10201','93','133','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BGC_LIQUIDEZ_BR','10201','93','134','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CONNACOOCU_BR','10201','93','136','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_GET_MONEY_BR','10201','93','138','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_INTESA_SANPAOLO_BR','10201','93','139','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_EASYNVEST_BR','10201','93','140','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BROKER_BR','10201','93','142','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_TREVISO_BR','10201','93','143','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BEXS_BR','10201','93','144','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_LEVYCAM_BR','10201','93','145','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_GUITTA_BR','10201','93','146','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_FACTA_BR','10201','93','149','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_ICAP_BR','10201','93','157','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CASA_CREDITO_BR','10201','93','159','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_COMMERZBANK_BR','10201','93','163','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BRL_TRUST_BR','10201','93','173','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_PEFISA_BR','10201','93','174','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_GUIDE_BR','10201','93','177','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CM_CCTVM_BR','10201','93','180','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_SCMEPP_BR','10201','93','183','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_ITAU_BBA_BR','10201','93','184','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_ATIVA_BR','10201','93','188','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_HS_FINANCEIRA_BR','10201','93','189','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_SERVICOOP_BR','10201','93','190','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_NOVA_FUTURA_BR','10201','93','191','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_PARMETAL_BR','10201','93','194','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_VALOR_SOCIEDADE_BR','10201','93','195','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_FAIR_BR','10201','93','196','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_STONE_PAGAMENTOS_BR','10201','93','197','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BTG_PACTUAL_BR','10201','93','208','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_ORIGINAl_BR','10201','93','212','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_ARBI_BR','10201','93','213','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_JOHN_DEERE_BR','10201','93','217','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BONSUCESSO_BR','10201','93','218','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_AGRICOLE_CREDIT_BR','10201','93','222','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_FIBRA_BR','10201','93','224','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CIFRA_BR','10201','93','233','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BRADESCO_BR','10201','93','237','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CLASSICO_BR','10201','93','241','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_MAXIMA_BR','10201','93','243','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_ABCB_BR','10201','93','246','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BIU_BR','10201','93','249','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BCV_BR','10201','93','250','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BEXS_CORRETORA_BR','10201','93','253','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_PARANA_BR','10201','93','254','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_MONEYCORP_BR','10201','93','259','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_NUBANK_BR','10201','93','260','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_FATOR_BR','10201','93','265','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CEDULA_BR','10201','93','266','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BARI_BR','10201','93','268','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_HSBC_BR','10201','93','269','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_SAGITUR _BR','10201','93','270','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_IB_CCTVM_BR','10201','93','271','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_AGK_CC_BR','10201','93','272','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_SAO_MIGUEL_BR','10201','93','273','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_MONEY_PLUS_BR','10201','93','274','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BCO_SENFF_BR','10201','93','276','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_GENIAL_BR','10201','93','278','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_PRIMAVERA_BR','10201','93','279','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_WILL_BR','10201','93','280','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_COOPAVEL_BR','10201','93','281','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_RB_BR','10201','93','283','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_FRENTE_BR','10201','93','285','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_OURO_BR','10201','93','286','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CAROL_BR','10201','93','288','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_DECYSEO_BR','10201','93','289','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_PAGSEGURO_INTERNET_BR','10201','93','290','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_PAGSEGURO_BR','10201','93','292','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_LASTRO_RDV_BR','10201','93','293','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_VISION_BR','10201','93','296','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_VIPS_CC_BR','10201','93','298','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_SOROCRED_BR','10201','93','299','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_NACION_AR_BR','10201','93','300','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BPP_IP_BR','10201','93','301','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_PORTOPAR_BR','10201','93','306','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_TERRA_BR','10201','93','307','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CAMBIONET_BR','10201','93','309','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_VORTX_BR','10201','93','310','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_DOURADA_BR','10201','93','311','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_HSCM_BR','10201','93','312','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_AMAZONIA_CC_BR','10201','93','313','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_PI_DTVM_BR','10201','93','315','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BMG_BR','10201','93','318','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_OM_DTVM_BR','10201','93','319','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CCB_BR','10201','93','320','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CREFAZ_BR','10201','93','321','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_ABELARDO_BR','10201','93','322','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_MERCADOPAGO_BR','10201','93','323','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CARTOS_BR','10201','93','324','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_ORAMA_BR','10201','93','325','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_PARATI_BR','10201','93','326','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CECM_FABRIC_BR','10201','93','328','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_QI_SCD_BR','10201','93','329','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BARI_IEF_BR','10201','93','330','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_FRAM_BR','10201','93','331','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_ACESSO_SP_BR','10201','93','332','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_DIGIO_BR','10201','93','335','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BANCOC6_BR','10201','93','336','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_SPEAME_BR','10201','93','340','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_UNIBANCO_BR','10201','93','341','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CREDITAS_BR','10201','93','342','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_FFA_SCMEPP_BR','10201','93','343','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BANCO_XP_BR','10201','93','348','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_AL5_BR','10201','93','349','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CREHNOR_BR','10201','93','350','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_TORO_BR','10201','93','352','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_NECTON_BR','10201','93','354','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_OTIMO_BR','10201','93','355','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_MIDWAY_BR','10201','93','358','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_ZEMA_BR','10201','93','359','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_TRINUS_BR','10201','93','360','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CIELO_BR','10201','93','362','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_SINGULARE_BR','10201','93','363','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_GERENCIANET_BR','10201','93','364','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_SIMPAUL_BR','10201','93','365','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_SOCIETE_GENERALE_BR','10201','93','366','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_VITREO_BR','10201','93','367','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BCO_CSF_BR','10201','93','368','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_MIZUHO_BR','10201','93','370','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_WARREN_BR','10201','93','371','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_UP_P_BR','10201','93','373','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_REALIZE_BR','10201','93','374','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_JPMORGAN_BR','10201','93','376','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BMS_SCD_BR','10201','93','377','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BBC_LEASING_BR','10201','93','378','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CECM_COO_BR','10201','93','379','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_PICPAY_BR','10201','93','380','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_MERCEDES_BENZ_BR','10201','93','381','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_FIDUCIA_BR','10201','93','382','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_JUNO_BR','10201','93','383','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_GLOBAL_SCM_BR','10201','93','384','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_NU_FINANCEIRA_BR','10201','93','386','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_TOYOTA_BR','10201','93','387','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_MERCANTIL_BR','10201','93','389','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BCO_GM_BR','10201','93','390','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_IBIAM_BR','10201','93','391','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_VOLKSWAGEN_BR','10201','93','393','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_FINANCIAMENTOS_BR','10201','93','394','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_FD_GOLD_BR','10201','93','395','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_PAGAMENTOS_BR','10201','93','396','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_LISTO_BR','10201','93','397','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_IDEAL_BR','10201','93','398','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_KIRTON_BR','10201','93','399','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_POUP_SER_BR','10201','93','400','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_IUGU_BR','10201','93','401','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_COBUCCIO_BR','10201','93','402','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CORA_BR','10201','93','403','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_SUMUP_BR','10201','93','404','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_ACCREDITO_BR','10201','93','406','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_INDIGO_BR','10201','93','407','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BONUSPAGO_BR','10201','93','408','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_PLANNER_SCM_BR','10201','93','410','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_VIA_CERTA_BR','10201','93','411','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CAPITAL_BR','10201','93','412','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BCOBV_BR','10201','93','413','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_WORK_BR','10201','93','414','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_LAMARA_BR','10201','93','416','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_ZIPDIN_BR','10201','93','418','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_NUMBRS_BR','10201','93','419','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_SAFRA_BR','10201','93','421','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_LAR_CREDI_BR','10201','93','421','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_SAFRA_BR_1','10201','93','422','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_COLUNA_BR','10201','93','423','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_SOCINAL_BR','10201','93','425','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BIORC_BR','10201','93','426','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CRED_UFES_BR','10201','93','427','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CRED_SYSTEM_BR','10201','93','428','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CREDIARE_BR','10201','93','429','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CREDISEARA_BR','10201','93','430','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BR_CAPITAL_BR','10201','93','433','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_DELCRED_BR','10201','93','435','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_PLANNER_TRUSTEE_BR','10201','93','438','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_ID_CORRETORA_BR','10201','93','439','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CREDIBRF_BR','10201','93','440','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_MAGNETIS_BR','10201','93','442','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CREDIHOME_BR','10201','93','443','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_TRINUS_SCD_BR','10201','93','444','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_PLANTAE_BR','10201','93','445','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_MIRAE_ASSET_BR','10201','93','447','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_HEMERA_BR','10201','93','448','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_DMCARD_BR','10201','93','449','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_FITBANK_BR','10201','93','450','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CREDIFIT_BR','10201','93','452','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_MERITO_BR','10201','93','454','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_MUFGB_BR','10201','93','456','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_UY3_BR','10201','93','457','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_HEDGE_BR','10201','93','458','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_SERV_SP_BR','10201','93','459','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_ASAAS_IP_BR','10201','93','461','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_STARK_BR','10201','93','462','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BSMB_BR','10201','93','464','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CAPITAL_CONSIG_BR','10201','93','465','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_MASTER_BR','10201','93','467','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CAIXA_BR','10201','93','473','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CITI_NA_BR','10201','93','477','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_ITAU_BR','10201','93','479','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_DEUTSCHE_ALEMAO_BR','10201','93','487','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_JPMORGAN_NA_BR','10201','93','488','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_ING_NV_BR','10201','93','492','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_LPBA_BR','10201','93','495','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_SUISSE_CREDIT_BR','10201','93','505','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_SENSO_BR','10201','93','545','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_LUSO_BR','10201','93','600','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_INDUSTRIAL_BR','10201','93','604','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BANCOVR_BR','10201','93','610','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_PAULISTA_BR','10201','93','611','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_GUANABARA_BR','10201','93','612','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_OMNI_BR','10201','93','613','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_PANAMERICANO_BR','10201','93','623','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_FICSA_BR','10201','93','626','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_SMARTBANK_BR','10201','93','630','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_RENDIMENTO_BR','10201','93','633','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_TRIANGULO_BR','10201','93','634','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_SOFISA_BR','10201','93','637','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_PINE_BR','10201','93','643','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_UNIBANCO_HOLDING_BR','10201','93','652','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_INDUSVAL_BR','10201','93','653','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_AJ_RENNER_BR','10201','93','654','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_VOTORANTIM_BR','10201','93','655','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_DAYCOVAL_BR','10201','93','707','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_OURINVEST_BR','10201','93','712','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BCORNX_BR','10201','93','720','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CETELEM_BR','10201','93','739','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_RIBEIRAO_PRETO_BR','10201','93','741','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_SEMEAR_BR','10201','93','743','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CITI_BR','10201','93','745','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_Modal_BR','10201','93','746','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_RABOBANK_INTER_BR','10201','93','747','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_SICREDI_BR','10201','93','748','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_SCOTIABANK_BR','10201','93','751','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BNP_PARIBAS_BR','10201','93','752','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_CONTINENTAL_BR','10201','93','753','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_SISTEMA_BR','10201','93','754','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_MERRILL_LYNCH_BR','10201','93','755','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_BANCOOB_BR','10201','93','756','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_BANKTRANSFER101_KEB_HANA_BR','10201','93','757','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BEXS_P01','O_BEXS_P01_WALLET101_PIX_BR','10201','71','71','BEXS211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_BOA_P01','O_BOA_P01_WALLET101_PAGBANK_BR','10201','72','72','BOA211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BICE_CL','10201','93','28','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_CONSORCIO_CL','10201','93','55','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_CREDITO_INV_CL','10201','93','16','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BANCOCHILE_CL','10201','93','1','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_DESARROLLO_CL','10201','93','507','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_ESTADO_CL','10201','93','12','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_FALABELLA_CL','10201','93','51','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_INTERNACIONAL_CL','10201','93','9','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_RIPLEY_CL','10201','93','53','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_SANTANDER_S_CL','10201','93','37','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_SECURITY_CL','10201','93','49','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BBVA_CL','10201','93','504','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_DEUTSCHE_CL','10201','93','52','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_HSBC_CL','10201','93','31','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_ITAU_CORPBANCA_CL','10201','93','39','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_SCOTIABANK_CL','10201','93','14','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_COOPEUCH_CL','10201','93','672','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_ABNA_CO','10201','93','ABNA','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BCMA_CO','10201','93','BCMA','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_CCAI_CO','10201','93','CCAI','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BAVI_CO','10201','93','BAVI','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BCSC_CO','10201','93','BCSC','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_COLP_CO','10201','93','COLP','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_COMP_CO','10201','93','COMP','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_CAFE_CO','10201','93','CAFE','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BBOG_CO','10201','93','BBOG','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_OCCI_CO','10201','93','OCCI','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_FALA_CO','10201','93','FALA','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_FINA_CO','10201','93','FINA','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_MULT_CO','10201','93','MULT','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BMM_CO','10201','93','BMM','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_PICH_CO','10201','93','PICH','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BPOP_CO','10201','93','BPOP','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_PRCB_CO','10201','93','PRCB','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_SANT_CO','10201','93','SANT','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_SERF_CO','10201','93','SERF','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BSUD_CO','10201','93','BSUD','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BWSA_CO','10201','93','BWSA','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BLDX_CO','10201','93','BLDX','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_COLO_CO','10201','93','COLO','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BCMEVA_CO','10201','93','BCMEVA','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BBVA_CO','10201','93','BBVA','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_CLTE_CO','10201','93','CLTE','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_CONFIAR_CO','10201','93','CONFIAR','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_CPCT_CO','10201','93','CPCT','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_FIAC_CO','10201','93','FIAC','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_COTRAFA_CO','10201','93','COTRAFA','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_DVPL_CO','10201','93','DVPL','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_JURS_CO','10201','93','JURS','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_HSBC_CO','10201','93','HSBC','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_ITAU_CO','10201','93','ITAU','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_NEQUI_CO','10201','93','NEQUI','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_FAWRY_P01','O_FAWRY_P01_VIRTUALACCOUNT101_FAWRY_EG','10201','73','73','FAWRY201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DANA_P01','O_DANA_P01_WALLET101_DANA_ID','10201','97','97','DANA211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_JABAR_ID','10201','93','JABAR','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_DKI_ID','10201','93','DKI','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_DAERAH_ISTIMEWA_ID','10201','93','DAERAH_ISTIMEWA','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_JAWA_TENGAH_ID','10201','93','JAWA_TENGAH','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_JAWA_TIMUR_ID','10201','93','JAWA_TIMUR','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_JAMBI_ID','10201','93','JAMBI','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_SUMUT_ID','10201','93','SUMUT','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_SUMATERA_BARAT_UUS_ID','10201','93','SUMATERA_BARAT_UUS','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_RIAU_DAN_KEPRI_ID','10201','93','RIAU_DAN_KEPRI','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_SUMSEL_DAN_BABEL_ID','10201','93','SUMSEL_DAN_BABEL','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_LAMPUNG_ID','10201','93','LAMPUNG','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_KALIMANTAN_SELATAN_ID','10201','93','KALIMANTAN_SELATAN','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_KALIMANTAN_BARAT_ID','10201','93','KALIMANTAN_BARAT','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_KALIMANTAN_TIMUR_ID','10201','93','KALIMANTAN_TIMUR','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_KALIMANTAN_TENGAH_ID','10201','93','KALIMANTAN_TENGAH','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_SULSELBAR_ID','10201','93','SULSELBAR','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_SULUT_ID','10201','93','SULUT','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_NUSA_TENGGARA_BARAT_ID','10201','93','NUSA_TENGGARA_BARAT','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_BALI_ID','10201','93','BALI','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_NUSA_TENGGARA_TIMUR_ID','10201','93','NUSA_TENGGARA_TIMUR','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_MALUKU_ID','10201','93','MALUKU','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_PAPUA_ID','10201','93','PAPUA','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_BENGKULU_ID','10201','93','BENGKULU','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_SULAWESI_ID','10201','93','SULAWESI','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_SULTRA_ID','10201','93','SULTRA','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_NUSANTARA_PARAHYANGAN_ID','10201','93','NUSANTARA_PARAHYANGAN','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_INDIA_ID','10201','93','INDIA','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_MUAMALAT_ID','10201','93','MUAMALAT','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_MESTIKA_DHARMA_ID','10201','93','MESTIKA_DHARMA','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_SINARMAS_ID','10201','93','SINARMAS','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_MASPION_ID','10201','93','MASPION','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_GANESHA_ID','10201','93','GANESHA','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_CCB_ID','10201','93','CCB','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_ICBC_ID','10201','93','ICBC','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_QNB_INDONESIA_ID','10201','93','QNB_INDONESIA','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_BTN_ID','10201','93','BTN','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_TABUNGAN_PENSIUNAN_NASIONAL_ID','10201','93','TABUNGAN_PENSIUNAN_NASIONAL','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_VICTORIA_SYR_ID','10201','93','VICTORIA_SYR','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_BRI_SYR_ID','10201','93','BRI_SYR','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_BJB_SYR_ID','10201','93','BJB_SYR','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_MEGA_ID','10201','93','MEGA','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_BNI_SYR_ID','10201','93','BNI_SYR','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_BUKOPIN_ID','10201','93','BUKOPIN','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_MANDIRI_SYR_ID','10201','93','MANDIRI_SYR','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_JASA_JAKARTA_ID','10201','93','JASA_JAKARTA','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_HANA_ID','10201','93','HANA','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_BUMIPUTERA_ID','10201','93','BUMIPUTERA','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_AGRONIAGA_ID','10201','93','AGRONIAGA','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_SBI_INDONESIA_ID','10201','93','SBI_INDONESIA','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_ROYAL_ID','10201','93','ROYAL','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_NATIONALNOBU_ID','10201','93','NATIONALNOBU','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_MEGA_SYR_ID','10201','93','MEGA_SYR','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_INA_PERDANA_ID','10201','93','INA_PERDANA','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_PANIN_SYR_ID','10201','93','PANIN_SYR','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_BUKOPIN_SYR_ID','10201','93','BUKOPIN_SYR','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_SAHABAT_SAMPOERNA_ID','10201','93','SAHABAT_SAMPOERNA','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_KESEJAHTERAAN_ID','10201','93','KESEJAHTERAAN','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_ARTOS_ID','10201','93','ARTOS','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_BTN_UUS_ID','10201','93','BTN_UUS','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_MAYORA_ID','10201','93','MAYORA','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_INDEX_SELINDO_ID','10201','93','INDEX_SELINDO','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_BANTEN_ID','10201','93','BANTEN','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_VICTORIA_ID','10201','93','VICTORIA','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_LSB_ID','10201','93','LSB','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_KS_ID','10201','93','KS','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_BPR_EKA_ID','10201','93','BPR_EKA','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_AGRIS_ID','10201','93','AGRIS','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_CHINATRUST_ID','10201','93','CHINATRUST','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_COMMONWEALTH_ID','10201','93','COMMONWEALTH','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_BRI_ID','10201','93','BRI','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_MANDIRI_ID','10201','93','MANDIRI','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_BNI_ID','10201','93','BNI','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_DANAMON_ID','10201','93','DANAMON','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_PERMATA_ID','10201','93','PERMATA','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_BCA_ID','10201','93','BCA','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_MAYBANK_ID','10201','93','MAYBANK','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_PANIN_ID','10201','93','PANIN','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_CIMB_ID','10201','93','CIMB','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_CIMB_UUS_ID','10201','93','CIMB_UUS','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_UOB_ID','10201','93','UOB','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_CITI_ID','10201','93','CITIBANK','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_MULTICOR_ID','10201','93','MULTICOR','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_ARTHA_ID','10201','93','ARTHA','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_TOKYO_ID','10201','93','TOKYO','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_DBS_ID','10201','93','DBS','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_SCB_ID','10201','93','STANDARD_CHARTERED','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_CAPITAL_ID','10201','93','CAPITAL','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_ANZ_ID','10201','93','ANZ','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_WOORI_ID','10201','93','WOORI','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_BOC_ID','10201','93','BOC','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_BUMI_ARTA_ID','10201','93','BUMI_ARTA','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_HSBC_ID','10201','93','HSBC','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_ANTARDAERAH_ID','10201','93','ANTARDAERAH','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_RABOBANK_ID','10201','93','RABOBANK','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_JTRUST_ID','10201','93','JTRUST','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_MAYAPADA_ID','10201','93','MAYAPADA','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_BANKTRANSFER101_ACEH_ID','10201','93','ACEH','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DOKU_P01_02','O_DOKU_P01_02_BANKTRANSFER101_BRI_ID','10201','93','BRI','DOKU212',NULL,1,'2022-03-08 12:39:12','2022-03-10 07:17:25'),
	 ('O_DOKU_P01_02','O_DOKU_P01_02_BANKTRANSFER101_MANDIRI_ID','10201','93','MANDIRI','DOKU212',NULL,1,'2022-03-08 12:39:12','2022-03-10 07:17:25'),
	 ('O_DOKU_P01_02','O_DOKU_P01_02_BANKTRANSFER101_BNI_ID','10201','93','BNI','DOKU212',NULL,1,'2022-03-08 12:39:12','2022-03-10 07:17:26'),
	 ('O_DOKU_P01_02','O_DOKU_P01_02_BANKTRANSFER101_BCA_ID','10201','93','BCA','DOKU212',NULL,1,'2022-03-08 12:39:12','2022-03-10 07:17:26'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_BRI_ID','10201','93','BRI','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_MANDIRI_ID','10201','93','MANDIRI','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_BNI_ID','10201','93','BNI','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_DANAMON_ID','10201','93','DANAMON','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_PERMATA_ID','10201','93','PERMATA','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_BCA_ID','10201','93','BCA','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_MAYBANK_ID','10201','93','MAYBANK','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_PANIN_ID','10201','93','PANIN','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_CIMB_ID','10201','93','CIMB','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_CIMB_UUS_ID','10201','93','CIMB_UUS','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_UOB_ID','10201','93','UOB','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_OCBC_ID','10201','93','OCBC','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_CITI_ID','10201','93','CITIBANK','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_MULTICOR_ID','10201','93','MULTICOR','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_ARTHA_ID','10201','93','ARTHA','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_TOKYO_ID','10201','93','TOKYO','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_DBS_ID','10201','93','DBS','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_SCB_ID','10201','93','STANDARD_CHARTERED','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_CAPITAL_ID','10201','93','CAPITAL','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_ANZ_ID','10201','93','ANZ','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_BOC_ID','10201','93','BOC','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_BUMI_ARTA_ID','10201','93','BUMI_ARTA','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_HSBC_ID','10201','93','HSBC','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_ANTARDAERAH_ID','10201','93','ANTARDAERAH','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_RABOBANK_ID','10201','93','RABOBANK','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_JTRUST_ID','10201','93','JTRUST','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_MAYAPADA_ID','10201','93','MAYAPADA','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_JABAR_ID','10201','93','JABAR','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_DKI_ID','10201','93','DKI','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_DAERAH_ISTIMEWA_ID','10201','93','DAERAH_ISTIMEWA','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_JAWA_TENGAH_ID','10201','93','JAWA_TENGAH','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_JAWA_TIMUR_ID','10201','93','JAWA_TIMUR','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_JAMBI_ID','10201','93','JAMBI','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_ACEH_ID','10201','93','ACEH','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_ACEH_UUS_ID','10201','93','ACEH_UUS','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_SUMUT_ID','10201','93','SUMUT','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_SUMATERA_BARAT_UUS_ID','10201','93','SUMATERA_BARAT_UUS','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_RIAU_DAN_KEPRI_ID','10201','93','RIAU_DAN_KEPRI','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_SUMSEL_DAN_BABEL_ID','10201','93','SUMSEL_DAN_BABEL','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_LAMPUNG_ID','10201','93','LAMPUNG','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_KALIMANTAN_SELATAN_ID','10201','93','KALIMANTAN_SELATAN','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_KALIMANTAN_BARAT_ID','10201','93','KALIMANTAN_BARAT','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_KALIMANTAN_TIMUR_ID','10201','93','KALIMANTAN_TIMUR','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_KALIMANTAN_TENGAH_ID','10201','93','KALIMANTAN_TENGAH','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_SULSELBAR_ID','10201','93','SULSELBAR','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_SULUT_ID','10201','93','SULUT','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_NUSA_TENGGARA_BARAT_ID','10201','93','NUSA_TENGGARA_BARAT','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_BALI_ID','10201','93','BALI','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_NUSA_TENGGARA_TIMUR_ID','10201','93','NUSA_TENGGARA_TIMUR','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_MALUKU_ID','10201','93','MALUKU','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_PAPUA_ID','10201','93','PAPUA','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_SULAWESI_ID','10201','93','SULAWESI','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_SULTRA_ID','10201','93','SULTRA','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_BANTEN_ID','10201','93','BANTEN','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_NUSANTARA_PARAHYANGAN_ID','10201','93','NUSANTARA_PARAHYANGAN','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_INDIA_ID','10201','93','INDIA','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_MUAMALAT_ID','10201','93','MUAMALAT','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_MESTIKA_DHARMA_ID','10201','93','MESTIKA_DHARMA','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_SHINHAN_ID','10201','93','SHINHAN','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_SINARMAS_ID','10201','93','SINARMAS','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_MASPION_ID','10201','93','MASPION','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_GANESHA_ID','10201','93','GANESHA','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_ICBC_ID','10201','93','ICBC','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_QNB_INDONESIA_ID','10201','93','QNB_INDONESIA','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_BTN_ID','10201','93','BTN','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_WOORI_ID','10201','93','WOORI','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_TABUNGAN_PENSIUNAN_NASIONAL_ID','10201','93','TABUNGAN_PENSIUNAN_NASIONAL','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_VICTORIA_SYR_ID','10201','93','VICTORIA_SYR','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_BJB_SYR_ID','10201','93','BJB_SYR','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_MEGA_ID','10201','93','MEGA','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_BRI_SYR_ID','10201','93','BRI_SYR','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_BNI_SYR_ID','10201','93','BNI_SYR','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_MANDIRI_SYR_ID','10201','93','MANDIRI_SYR','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_BSI_ID','10201','93','BSI','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_JASA_JAKARTA_ID','10201','93','JASA_JAKARTA','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_HANA_ID','10201','93','HANA','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_MNC_INTERNASIONAL_ID','10201','93','MNC_INTERNASIONAL','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_YUDHA_BHAKTI_ID','10201','93','YUDHA_BHAKTI','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_AGRONIAGA_ID','10201','93','AGRONIAGA','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_SBI_INDONESIA_ID','10201','93','SBI_INDONESIA','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_ROYAL_ID','10201','93','ROYAL','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_NATIONALNOBU_ID','10201','93','NATIONALNOBU','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_MEGA_SYR_ID','10201','93','MEGA_SYR','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_INA_PERDANA_ID','10201','93','INA_PERDANA','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_PANIN_SYR_ID','10201','93','PANIN_SYR','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_PERMATA_UUS_ID','10201','93','PERMATA_UUS','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_BUKOPIN_SYR_ID','10201','93','BUKOPIN_SYR','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_SAHABAT_SAMPOERNA_ID','10201','93','SAHABAT_SAMPOERNA','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_DINAR_INDONESIA_ID','10201','93','DINAR_INDONESIA','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_KESEJAHTERAAN_ID','10201','93','KESEJAHTERAAN','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_BCA_SYR_ID','10201','93','BCA_SYR','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_ARTOS_ID','10201','93','ARTOS','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_BTN_UUS_ID','10201','93','BTN_UUS','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_MASB_ID','10201','93','MASB','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_MAYORA_ID','10201','93','MAYORA','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_INDEX_SELINDO_ID','10201','93','INDEX_SELINDO','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_CENTRATAMA_ID','10201','93','CENTRATAMA','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_MANDIRI_TASPEN_ID','10201','93','MANDIRI_TASPEN','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_VICTORIA_ID','10201','93','VICTORIA','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_HARDA_INTERNASIONAL_ID','10201','93','HARDA_INTERNASIONAL','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_KS_ID','10201','93','KS','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_AGRIS_ID','10201','93','AGRIS','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_CHINATRUST_ID','10201','93','CHINATRUST','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_COMMONWEALTH_ID','10201','93','COMMONWEALTH','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_BANKTRANSFER101_BUKOPIN_ID','10201','93','BUKOPIN','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DOKU_P01','O_DOKU_P01_WALLET101_DOKU_ID','10201','99','99','DOKU211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_OVO_P01','O_OVO_P01_WALLET101_OVO_ID','10201','94','OVO','OVO201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_SHOPEEPAY_P01','O_SHOPEEPAY_P01_WALLET101_SHOPEEPAY_ID','10201','87','87','SHOPEE211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_OY_P01','O_OY_P01_WALLET101_LINKAJA_ID','10201','96','96','OY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_SETTLEORG_IN','10206','93','93','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:54:03'),
	 ('O_DLOCAL_P01_02','O_DLOCAL_P01_02_BANKTRANSFER101_SETTLEORG_IN','10206','93','93','DLOCAL212',NULL,1,'2022-03-08 12:39:12','2022-03-10 07:20:27'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_SETTLEORG_MA','10201','93','93','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_SETTLEORG_MX','10201','93','93','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_TNG_P01','O_TNG_P01_WALLET101_TNG_MY','10201','86','86','TNG201',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_SETTLEORG_PE','10201','93','93','DLOCAL211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_WALLET101_GCASH_PH','10201','80','80','SKYPAY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_SKYPAY_P01','O_SKYPAY_P01_WALLET101_PAYMAYA_PH','10201','82','82','SKYPAY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_ALB_PH','10201','93','ALB','SKYPAY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_AUB_PH','10201','93','AUB','SKYPAY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_BDO_PH','10201','93','BDO','SKYPAY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_BMB_PH','10201','93','BMB','SKYPAY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_BOC_PH','10201','93','BOC','SKYPAY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_BPI_PH','10201','93','BPI','SKYPAY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_CBC_PH','10201','93','CBC','SKYPAY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_CBS_PH','10201','93','CBS','SKYPAY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_CTBC_PH','10201','93','CTBC','SKYPAY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_CLB_PH','10201','93','CLB','SKYPAY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_DBI_PH','10201','93','DBI','SKYPAY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_DBP_PH','10201','93','DBP','SKYPAY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_EQB_PH','10201','93','EQB','SKYPAY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_EWB_PH','10201','93','EWB','SKYPAY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_EWR_PH','10201','93','EWR','SKYPAY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_IBI_PH','10201','93','IBI','SKYPAY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_ING_PH','10201','93','ING','SKYPAY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_LBP_PH','10201','93','LBP','SKYPAY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_MET_PH','10201','93','MET','SKYPAY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_MPI_PH','10201','93','MPI','SKYPAY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_MSB_PH','10201','93','MSB','SKYPAY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_ONB_PH','10201','93','ONB','SKYPAY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_OPI_PH','10201','93','OPI','SKYPAY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_PAR_PH','10201','93','PAR','SKYPAY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_PBB_PH','10201','93','PBB','SKYPAY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_PBC_PH','10201','93','PBC','SKYPAY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_PNB_PH','10201','93','PNB','SKYPAY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_PNS_PH','10201','93','PNS','SKYPAY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_PSB_PH','10201','93','PSB','SKYPAY211',NULL,1,'2022-03-08 12:39:12','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_PTC_PH','10201','93','PTC','SKYPAY211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_PVB_PH','10201','93','PVB','SKYPAY211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_QRB_PH','10201','93','QRB','SKYPAY211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_ROB_PH','10201','93','ROB','SKYPAY211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_RCBC_PH','10201','93','RCBC','SKYPAY211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_SBA_PH','10201','93','SBA','SKYPAY211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_SEC_PH','10201','93','SEC','SKYPAY211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_SSB_PH','10201','93','SSB','SKYPAY211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_TYB_PH','10201','93','TYB','SKYPAY211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_UBP_PH','10201','93','UBP','SKYPAY211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_UCP_PH','10201','93','UCP','SKYPAY211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_USB_PH','10201','93','USB','SKYPAY211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SKYPAY_P01','O_SKYPAY_P01_BANKTRANSFER101_WDB_PH','10201','93','WDB','SKYPAY211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_WALLET101_GCASH_PH','10201','80','80','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_WALLET101_COINS_PH_PH','10201','81','81','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_WALLET101_PAYMAYA_PH','10201','82','82','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_WALLET101_GRABPAY_PH','10201','84','84','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_ALB_PH','10201','93','ALB','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_AHB_PH','10201','93','AHB','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_AUB_PH','10201','93','AUB','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_BDI_PH','10201','93','BDI','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_BDO_PH','10201','93','BDO','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_BMB_PH','10201','93','BMB','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_BOC_PH','10201','93','BOC','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_BPI_PH','10201','93','BPI','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_BRB_PH','10201','93','BRB','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_CBC_PH','10201','93','CBC','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_CBS_PH','10201','93','CBS','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_CLB_PH','10201','93','CLB','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_CMG_PH','10201','93','CMG','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_CRD_PH','10201','93','CRD','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_CTBC_PH','10201','93','CTBC','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_CTS_PH','10201','93','CTS','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_DBI_PH','10201','93','DBI','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_DBP_PH','10201','93','DBP','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_DCB_PH','10201','93','DCB','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_DCP_PH','10201','93','DCP','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_EBI_PH','10201','93','EBI','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_EQB_PH','10201','93','EQB','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_ERB_PH','10201','93','ERB','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_EWB_PH','10201','93','EWB','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_EWR_PH','10201','93','EWR','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_HBP_PH','10201','93','HBP','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_HSB_PH','10201','93','HSB','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_ING_PH','10201','93','ING','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_IBI_PH','10201','93','IBI','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_KEB_PH','10201','93','KEB','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_LBP_PH','10201','93','LBP','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_LDB_PH','10201','93','LDB','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_MET_PH','10201','93','MET','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_MPI_PH','10201','93','MPI','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_MSB_PH','10201','93','MSB','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_NLK_PH','10201','93','NLK','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_OFB_PH','10201','93','OFB','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_OPI_PH','10201','93','OPI','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_ONB_PH','10201','93','ONB','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_PAR_PH','10201','93','PAR','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_PBB_PH','10201','93','PBB','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_PBC_PH','10201','93','PBC','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_PBK_PH','10201','93','PBK','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_PNB_PH','10201','93','PNB','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_PNS_PH','10201','93','PNS','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_PRB_PH','10201','93','PRB','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_PRS_PH','10201','93','PRS','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_PSB_PH','10201','93','PSB','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_PTC_PH','10201','93','PTC','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_PVB_PH','10201','93','PVB','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_QCB_PH','10201','93','QCB','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_QRB_PH','10201','93','QRB','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_RBG_PH','10201','93','RBG','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_RCBC_PH','10201','93','RCBC','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_ROB_PH','10201','93','ROB','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_SBA_PH','10201','93','SBA','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_SCB_PH','10201','93','SCB','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-18 12:20:32'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_SEC_PH','10201','93','SEC','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_SSB_PH','10201','93','SSB','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_TRB_PH','10201','93','TRB','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_UBP_PH','10201','93','UBP','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_USB_PH','10201','93','USB','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_UCP_PH','10201','93','UCP','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_WDB_PH','10201','93','WDB','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_XENDIT_P01','O_XENDIT_P01_BANKTRANSFER101_TYB_PH','10201','93','TYB','XENDIT201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_WALLET101_JAZZCASH_PK','10201','61','61','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_WALLET101_EASYPAISA_PK','10201','62','62','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_ADVANS_PK','10201','93','ADVANS','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_AKHUWAT_PK','10201','93','AKHUWAT','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_ALBARAKA_PK','10201','93','ALBARAKA','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_ALFALAH_PK','10201','93','ALFALAH','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_ALHABIB_PK','10201','93','ALHABIB','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_ALLIED_PK','10201','93','ALLIED','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_APNA_PK','10201','93','APNA','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_ASAP_PK','10201','93','ASAP','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_ASKARI_PK','10201','93','ASKARI','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_BIPL_PK','10201','93','BIPL','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_BOC_PK','10201','93','BOC','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_CITI_PK','10201','93','CITI','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_DBAG_PK','10201','93','DBAG','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_DIB_PK','10201','93','DIB','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_FINCA_PK','10201','93','FINCA','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_FMFB_PK','10201','93','FMFB','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_FWBL_PK','10201','93','FWBL','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_HBL_PK','10201','93','HBL','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_HMB_PK','10201','93','HMB','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_HMETRO_PK','10201','93','HMETRO','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_ICBC_PK','10201','93','ICBC','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_IDBI_PK','10201','93','IDBI','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_ISLAMI_PK','10201','93','ISLAMI','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_JSBL_PK','10201','93','JSBL','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_KASB_PK','10201','93','KASB','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_KHUSHHALI_PK','10201','93','KHUSHHALI','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_KHYBER_PK','10201','93','KHYBER','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_MBL_PK','10201','93','MBL','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_MCB_PK','10201','93','MCB','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_MCBI_PK','10201','93','MCBI','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_MMBL_PK','10201','93','MMBL','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_MUFG_PK','10201','93','MUFG','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_NBP_PK','10201','93','NBP','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_NIB_PK','10201','93','NIB','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_NRSP_PK','10201','93','NRSP','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_OMAN_PK','10201','93','OMAN','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_PAKOMAN_PK','10201','93','PAKOMAN','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_PAYSAL_PK','10201','93','PAYSAL','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_PPCBL_PK','10201','93','PPCBL','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_PUNJAB_PK','10201','93','PUNJAB','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_SAMBA_PK','10201','93','SAMBA','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_SBP_PK','10201','93','SBP','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_SCB_PK','10201','93','SCBP','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_SILK_PK','10201','93','SILK','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_SME_PK','10201','93','SME','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_SMFB_PK','10201','93','SMFB','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_SONERI_PK','10201','93','SONERI','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_SUMMIT_PK','10201','93','SUMMIT','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_TELENORBANK_PK','10201','93','TELENOR','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_TMFB_PK','10201','93','TMFB','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_UBL_PK','10201','93','UBL','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_UFJ_PK','10201','93','UFJ','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_UMB_PK','10201','93','UMB','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SIMPAISA_P01','O_SIMPAISA_P01_BANKTRANSFER101_ZTBL_PK','10201','93','ZTBL','SIMPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_EASYPAISA_P01','O_EASYPAISA_P01_WALLET101_EASYPAISA_PK','10201','62','62','EASYPAISA201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_YOUMI_P01','O_YOUMI_P01_WALLET101_YOUMI_RU','10201','74','74','YOUMI211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_QIWI_P01','O_QIWI_P01_WALLET101_QIWI_RU','10201','QIWI','QIWI','QIWI211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_YOUMI_P01','O_YOUMI_P01_BANKTRANSFER101_CARDORG_RU','10201','10','10','YOUMI211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_YOUMI_P01','O_YOUMI_P01_BANKTRANSFER101_SETTLEORG_RU','10201','93','93','YOUMI211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_QIWI_P01','O_QIWI_P01_BANKTRANSFER101_CARDORG_RU','10201','10','10','QIWI211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_TRUEMONEY_P01','O_TRUEMONEY_P01_WALLET101_TRUEMONEY_TH','10201','85','85','TRUEMONEY201',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_PAPARA_P01','O_PAPARA_P01_WALLET101_PAPARA_TR','10201','88','88','PAPARA211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_PAYPOETER_P01','O_PAYPOETER_P01_BANKTRANSFER101_SETTLEORG_TR','10201','93','93','PAYPORTER211',NULL,1,'2022-03-08 12:39:13','2022-03-14 05:34:05'),
	 ('O_PAYPOETER_P01','O_PAYPOETER_P01_BANKTRANSFER101_CARDORG_TR','10201','10','10','PAYPORTER211',NULL,1,'2022-03-08 12:39:13','2022-03-14 05:34:06'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_AGB_VN','10201','83','AGB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_BIDV_VN','10201','83','BIDV','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_EXB_VN','10201','83','EXB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_MSB_VN','10201','83','MSB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_STB_VN','10201','83','STB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_NCB_VN','10201','83','NCB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_PGB_VN','10201','83','PGB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_ICB_VN','10201','83','ICB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_TPB_VN','10201','83','TPB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_VIB_VN','10201','83','VIB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_VCB_VN','10201','83','VCB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_DAB_VN','10201','83','DAB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_ACB_VN','10201','83','ACB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_VPB_VN','10201','83','VPB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_BVB_VN','10201','83','BVB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_KLB_VN','10201','83','KLB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_SCB_TH_VN','10201','83','SCB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_ABB_VN','10201','83','ABB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_OCB_VN','10201','83','OCB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_BAB_VN','10201','93','BAB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_BIDV_VN_1','10201','93','BIDV','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_EXB_VN_1','10201','93','EXB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_MSB_VN_1','10201','93','MSB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_STB_VN_1','10201','93','STB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_SGB_VN','10201','93','SGB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_NCB_VN_1','10201','93','NCB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_PGB_VN_1','10201','93','PGB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_GPB_VN','10201','93','GPB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_ICB_VN_1','10201','93','ICB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_TCB_VN','10201','93','TCB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_TPB_VN_1','10201','93','TPB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_VAB_VN','10201','93','VAB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_VIB_VN_1','10201','93','VIB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_VCB_VN_1','10201','93','VCB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_DAB_VN_1','10201','93','DAB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_MB_VN','10201','93','MB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_ACB_VN_1','10201','93','ACB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_HDB_VN','10201','93','HDB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_VPB_VN_1','10201','93','VPB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_OJB_VN','10201','93','OJB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_SHB_VN','10201','93','SHB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_NAB_VN','10201','93','NAB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_BVB_VN_1','10201','93','BVB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_KLB_VN_1','10201','93','KLB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_PVCOMBANK_VN','10201','93','PVCOMBANK','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_SCB_VN','10201','93','SCB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_SHNB_VN','10201','93','SHNB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_ABB_VN_1','10201','93','ABB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_GAB_VN','10201','93','GAB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_NGANLUONG_P01','O_NGANLUONG_P01_BANKTRANSFER101_OCB_VN_1','10201','93','OCB','NGANLUONG211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SOCHITEL_P01','O_SOCHITEL_P01_CARRIERBILLING101_AIRTELTIGO_GH','10201','11','AIRTELTIGO','SOCHITEL211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SOCHITEL_P01','O_SOCHITEL_P01_CARRIERBILLING101_GLO_GH','10201','11','GLO','SOCHITEL211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_SOCHITEL_P01','O_SOCHITEL_P01_CARRIERBILLING101_MTN_GH','10201','11','MTN','SOCHITEL211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SOCHITEL_P01','O_SOCHITEL_P01_CARRIERBILLING101_VODAFONE_GH','10201','11','VODAFONE','SOCHITEL211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SOCHITEL_P01','O_SOCHITEL_P01_CARRIERBILLING101_AIRTEL_KE','10201','11','AIRTEL','SOCHITEL211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SOCHITEL_P01','O_SOCHITEL_P01_CARRIERBILLING101_SAFARICOM_KE','10201','11','SAFARICOM','SOCHITEL211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SOCHITEL_P01','O_SOCHITEL_P01_CARRIERBILLING101_TELKOM_KE','10201','11','TELKOM','SOCHITEL211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SOCHITEL_P01','O_SOCHITEL_P01_CARRIERBILLING101_MCEL_MZ','10201','11','MCEL','SOCHITEL211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SOCHITEL_P01','O_SOCHITEL_P01_CARRIERBILLING101_MOVITEL_MZ','10201','11','MOVITEL','SOCHITEL211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SOCHITEL_P01','O_SOCHITEL_P01_CARRIERBILLING101_VODACOM_MZ','10201','11','VODACOM','SOCHITEL211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SOCHITEL_P01','O_SOCHITEL_P01_CARRIERBILLING101_9MOBILE_NG','10201','11','9MOBILE','SOCHITEL211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SOCHITEL_P01','O_SOCHITEL_P01_CARRIERBILLING101_AIRTEL_NG','10201','11','AIRTEL','SOCHITEL211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_SOCHITEL_P01','O_SOCHITEL_P01_CARRIERBILLING101_GLO_NG','10201','11','GLO','SOCHITEL211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SOCHITEL_P01','O_SOCHITEL_P01_CARRIERBILLING101_NTEL_NG','10201','11','NTEL','SOCHITEL211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SOCHITEL_P01','O_SOCHITEL_P01_CARRIERBILLING101_MTN_NG','10201','11','MTN','SOCHITEL211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SOCHITEL_P01','O_SOCHITEL_P01_CARRIERBILLING101_AIRTEL_TZ','10201','11','AIRTEL','SOCHITEL211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SOCHITEL_P01','O_SOCHITEL_P01_CARRIERBILLING101_HALOTEL_TZ','10201','11','HALOTEL','SOCHITEL211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SOCHITEL_P01','O_SOCHITEL_P01_CARRIERBILLING101_TIGO_TZ','10201','11','TIGO','SOCHITEL211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SOCHITEL_P01','O_SOCHITEL_P01_CARRIERBILLING101_VODACOM_TZ','10201','11','VODACOM','SOCHITEL211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SOCHITEL_P01','O_SOCHITEL_P01_CARRIERBILLING101_ZANTEL_TZ','10201','11','ZANTEL','SOCHITEL211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SOCHITEL_P01','O_SOCHITEL_P01_CARRIERBILLING101_AIRTEL_UG','10201','11','AIRTEL','SOCHITEL211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SOCHITEL_P01','O_SOCHITEL_P01_CARRIERBILLING101_MTN_UG','10201','11','MTN','SOCHITEL211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_SOCHITEL_P01','O_SOCHITEL_P01_CARRIERBILLING101_TELECOM MOBILE_UG','10201','11','TELECOM MOBILE','SOCHITEL211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SOCHITEL_P01','O_SOCHITEL_P01_CARRIERBILLING101_CDMA_MA','10201','11','CDMA','SOCHITEL211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SOCHITEL_P01','O_SOCHITEL_P01_CARRIERBILLING101_INWI_MA','10201','11','INWI','SOCHITEL211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SOCHITEL_P01','O_SOCHITEL_P01_CARRIERBILLING101_MAROC_TELECOM_MA','10201','11','MAROC_TEL','SOCHITEL211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SOCHITEL_P01','O_SOCHITEL_P01_CARRIERBILLING101_ORANGE_MA','10201','11','ORANGE','SOCHITEL211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_AVANTEL_CO','10201','11','AVANTEL','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CLARO_CO','10201','11','CLARO','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_ETB_CO','10201','11','ETB','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_EXITO_CO','10201','11','EXITO','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_FLASH_MOBILE_CO','10201','11','FLASH_MOBILE','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_MOVISTAR_CO','10201','11','MOVISTAR','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_TIGO_CO','10201','11','TIGO','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_VIRGIN_CO','10201','11','VIRGIN_MOBILE','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_ATT_MX','10201','11','ATT','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_UNEFON_MX','10201','11','UNEFON','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_AIRTELTIGO_GH','10201','11','AIRTELTIGO','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_GLO_GH','10201','11','GLO','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_MTN_GH','10201','11','MTN','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_VODAFONE_GH','10201','11','VODAFONE','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_AIRTEL_KE','10201','11','AIRTEL','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_SAFARICOM_KE','10201','11','SAFARICOM','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_TELKOM_KE','10201','11','TELKOM','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_NCELL_NP','10201','11','NCELL','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_NTC_NP','10201','11','NTC','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_9MOBILE_NG','10201','11','9MOBILE','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_AIRTEL_NG','10201','11','AIRTEL','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_GLO_NG','10201','11','GLO','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_MTN_NG','10201','11','MTN','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_JAZZ_PK','10201','11','JAZZ','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_TELENOR_PK','10201','11','TELENOR','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_UFONE_PK','10201','11','UFONE','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_WARID_PK','10201','11','WARID','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_ZONG_PK','10201','11','ZONG','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_DITO_PH','10201','11','DITO','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_SMART_PH','10201','11','SMART','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_SMARTBRO_PH','10201','11','SMARTBRO','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_TNT_PH','10201','11','TNT','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_KB_IMPULS_BEELINE_RU','10201','11','KB_IMPULS_BEELINE','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_MEGAFON_RU','10201','11','MEGAFON','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_MOTIV_RU','10201','11','MOTIV','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_MTS_RU','10201','11','MTS','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_ROSTELECOM_MRF_URAL_RU','10201','11','ROSTELECOM_MRF_URAL','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_TELE2_RU','10201','11','TELE2','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_M1_SG','10201','11','M1','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_STARHUB_SG','10201','11','STARHUB','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_TRUE_MOVE_H_TH','10201','11','TRUE_MOVE_H','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_OOREDOO_TN','10201','11','OOREDOO','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_ALGAR_TELECOM_BR','10201','11','ALGAR_TELECOM','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CLARO_BR','10201','11','CLARO','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_NEXTEL_BR','10201','11','NEXTEL','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_OI_BR','10201','11','OI','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_TIM_BR','10201','11','TIM','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_VIVO_BR','10201','11','VIVO','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_DJEZZY_DZ','10201','11','DJEZZY','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_MOBILIS_DZ','10201','11','MOBILIS','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_OOREDOO_DZ','10201','11','OOREDOO','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_AXIS_ID','10201','11','AXIS','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_INDOSAT_ID','10201','11','INDOSAT','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_NET1_ID','10201','11','NET1','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_SMARTFREN_ID','10201','11','SMARTFREN','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_TELKOMSEL_ID','10201','11','TELKOMSEL','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_THREE_TELECOM_ID','10201','11','THREE_TELECOM','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_XL_ID','10201','11','XL','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_AIRTEL_IN','10201','11','AIRTEL','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_BSNL_IN','10201','11','BSNL','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_MTNL_IN','10201','11','MTNL','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_RELIANCE_JIO_BUNDLES_IN','10201','11','RELIANCE_JIO_BUNDLES','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_VI_IN','10201','11','VI','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_INWI_MA','10201','11','INWI','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_MAROC_TEL_MA','10201','11','MAROC_TEL','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_ORANGE_MA','10201','11','ORANGE','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_MOVISTAR_MX','10201','11','MOVISTAR','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_TELCEL_MX','10201','11','TELCEL','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_VIRGIN_MX','10201','11','VIRGIN','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_ASTRO_NJOI_PIN_MY','10201','11','ASTRO_NJOI_PIN','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CELCOM_MY','10201','11','CELCOM','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_DIGI_MY','10201','11','DIGI','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_MAXIS_MY','10201','11','MAXIS','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_TUNETALK_MY','10201','11','TUNETALK','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_UMOBILE_MY','10201','11','UMOBILE','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_SMARTCELL_NP','10201','11','SMARTCELL','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_GLOBE_TELECOM_PH','10201','11','GLOBE_TELECOM','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_TOUCH_MOBILE_PH','10201','11','TOUCH_MOBILE','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_FRIENDI_PIN_SAUDI_SA','10201','11','FRIENDI_PIN_SAUDI','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_LEBARA_PIN_SAUDI_SA','10201','11','LEBARA_PIN_SAUDI','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_MOBILY_SAUDI_SA','10201','11','MOBILY_SAUDI','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_STC_SAUDI_SA','10201','11','STC_SAUDI','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_VIRGIN_PIN_SAUDI_SA','10201','11','VIRGIN_PIN_SAUDI','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_ZAIN_SAUDI_SA','10201','11','ZAIN_SAUDI','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_SINGTEL_SG','10201','11','SINGTEL','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_168_TH','10201','11','168','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_AIS_TH','10201','11','AIS','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_DTAC_TH','10201','11','DTAC','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_MY_TH','10201','11','MY','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_PENGUIN_TH','10201','11','PENGUIN','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_TOT_TH','10201','11','TOT','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_ORANGE_TN','10201','11','ORANGE','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_TUNISIE_TELECOM_TN','10201','11','TUNISIE_TELECOM','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_TURK_TELECOM_TR','10201','11','TURK_TELECOM','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_TURKCELL_TR','10201','11','TURKCELL','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_VODAFONE_TR','10201','11','VODAFONE','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_VODAFONE_CYPRUS_TR','10201','11','VODAFONE_CYPRUS','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_GMOBILE_VN','10201','11','GMOBILE','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_MOBIFONE_VN','10201','11','MOBIFONE','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_VIETNA_MOBILE_VN','10201','11','VIETNA_MOBILE','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_VIETTEL_MOBILE_VN','10201','11','VIETTEL_MOBILE','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_VINAPHONE_VN','10201','11','VINAPHONE','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_FIVE_AE','10201','11','FIVE','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_VIRGIN_AE','10201','11','VIRGIN_MOBILE','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_BATELCO_PIN_BH','10201','11','BATELCO_PIN','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_LUCKY_BH','10201','11','LUCKY','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CLARO_CL','10201','11','CLARO','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_ETISALAT_EG','10201','11','ETISALAT','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_ORANGE_EG','10201','11','ORANGE','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_KT_KR','10201','11','KT','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_LGU_KR','10201','11','LGU','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_SK_KR','10201','11','SK','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_OMANTEL_PIN_OM','10201','11','OMANTEL_PIN','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_RED_BULL_PIN_OM','10201','11','RED_BULL_PIN','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_RENNA_PIN_OM','10201','11','RENNA_PIN','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CLARO_PE','10201','11','CLARO','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_MOVISTAR_PE','10201','11','MOVISTAR','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CLARO_PY','10201','11','CLARO','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_TIGO_PY','10201','11','TIGO','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_VOX_PY','10201','11','VOX','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_OOREDOO_QA','10201','11','OOREDOO','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_VODAFONE_PIN_QA','10201','11','VODAFONE_PIN','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_ACCESS_WIRELESS_PIN_US','10201','11','ACCESS_WIRELESS_PIN','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_AIRLINK_MOBILE_PIN_US','10201','11','AIRLINK_MOBILE_PIN','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_AIRVOICE_PIN_US','10201','11','AIRVOICE_PIN','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_BOOST_MOBILE_RETAIL_US','10201','11','BOOST_MOBILE_RETAIL','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CALLNROAM_US','10201','11','CALLNROAM','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CRICKET_RETAIL_US','10201','11','CRICKET_RETAIL','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_FREEUP_MOBILE_US','10201','11','FREEUP_MOBILE','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_GOOD2GO_PIN_US','10201','11','GOOD2GO_PIN','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_GOSMART_US','10201','11','GOSMART','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_H2O_US','10201','11','H2O','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_LIBERTY_MOBILE_US','10201','11','LIBERTY_MOBILE','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_LIFE_WIRELESS_PIN_US','10201','11','LIFE_WIRELESS_PIN','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_METROPCS_RETAIL_US','10201','11','METROPCS_RETAIL','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_MOVIDA_PIN_US','10201','11','MOVIDA_PIN','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_NET10_PARENT_US','10201','11','NET10_PARENT','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_PAGEPLUS_PIN_US','10201','11','PAGEPLUS_PIN','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_REACHOUT_PIN_US','10201','11','REACHOUT_PIN','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_RED_POCKET_US','10201','11','RED_POCKET','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_SAFELINK_WIRELESS_PIN_US','10201','11','SAFELINK_WIRELESS_PIN','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_SIMPLEMOBILE_US','10201','11','SIMPLEMOBILE','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_TELCEL_AMERICA_PIN_US','10201','11','TELCEL_AMERICA_PIN','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_TOTAL_WIRELESS_US','10201','11','TOTAL_WIRELESS','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_TRACFONE_US','10201','11','TRACFONE','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_ULTRA_MOBILE_US','10201','11','ULTRA_MOBILE','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CLARO_UY','10201','11','CLARO','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_MOVISTAR_UY','10201','11','MOVISTAR','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_LYCA_MOBILE_ZA','10201','11','LYCA_MOBILE','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_VIRGIN_PIN_ZA','10201','11','VIRGIN_PIN','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CLARO_AR','10201','11','CLARO','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_MOVISTAR_AR','10201','11','MOVISTAR','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_PERSONAL_AR','10201','11','PERSONAL','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_TUENTI_AR','10201','11','TUENTI','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_STC_BH','10201','11','STC','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_ZAIN_BH','10201','11','ZAIN','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_ENTEL_BO','10201','11','ENTEL','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_TIGO_BO','10201','11','TIGO','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_VIVA_BO','10201','11','VIVA','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_ENTEL_CL','10201','11','ENTEL','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_MOVISTAR_CL','10201','11','MOVISTAR','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_VIRGIN_CL','10201','11','VIRGIN_MOBILE','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_VTR_CL','10201','11','VTR','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_WOM_CL','10201','11','WOM','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_VODAFONE_EG','10201','11','VODAFONE','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_OOREDOO_KW','10201','11','OOREDOO','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_STC_KW','10201','11','STC','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_ZAIN_KW','10201','11','ZAIN','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_BITEL_PE','10201','11','BITEL','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_ENTEL_PE','10201','11','ENTEL','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CELLC_ZA','10201','11','CELLC','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_MTN_ZA','10201','11','MTN','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_TELKOM_ZA','10201','11','TELKOM','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_VODACOM_ZA','10201','11','VODACOM','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_DU_AE','10201','11','DU','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_ETISALAT_AE','10201','11','ETISALAT','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_ATT_US','10201','11','ATT','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_LYCA_MOBILE_US','10201','11','LYCA_MOBILE','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_SINPIN_PINLESS_US','10201','11','SINPIN_PINLESS','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_TMOBILE_US','10201','11','TMOBILE','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_VERIZON_US','10201','11','VERIZON','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_ANTEL_UY','10201','11','ANTEL','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SOCHITEL_P01','O_SOCHITEL_P01_CARRIERBILLING101_CARRIERBILLING_KE','10201','11','11','SOCHITEL211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SOCHITEL_P01','O_SOCHITEL_P01_CARRIERBILLING101_CARRIERBILLING_MZ','10201','11','11','SOCHITEL211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SOCHITEL_P01','O_SOCHITEL_P01_CARRIERBILLING101_CARRIERBILLING_UG','10201','11','11','SOCHITEL211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_SOCHITEL_P01','O_SOCHITEL_P01_CARRIERBILLING101_CARRIERBILLING_GH','10201','11','11','SOCHITEL211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SOCHITEL_P01','O_SOCHITEL_P01_CARRIERBILLING101_CARRIERBILLING_MA','10201','11','11','SOCHITEL211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SOCHITEL_P01','O_SOCHITEL_P01_CARRIERBILLING101_CARRIERBILLING_NG','10201','11','11','SOCHITEL211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_SOCHITEL_P01','O_SOCHITEL_P01_CARRIERBILLING101_CARRIERBILLING_TZ','10201','11','11','SOCHITEL211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_AE','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_AR','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_BH','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_BO','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_BR','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_CL','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_CO','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_DZ','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_EG','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_GH','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_ID','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_IN','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_KE','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_KR','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_KW','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_MA','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_MX','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_MY','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_NG','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_NP','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_OM','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_PE','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_PH','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_PK','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_PY','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_QA','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_RU','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_SA','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_SG','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_TH','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_TN','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_TR','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_US','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_UY','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_VN','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_ZA','10201','11','11','DTONE211',NULL,1,'2022-03-08 12:39:13','2022-03-09 10:49:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_GOPAY_P01','O_GOPAY_P01_WALLET101_GOPAY_ID','10201','95','GOPAY','GOPAY211',NULL,1,'2022-03-11 14:09:21','2022-03-22 03:57:58'),
	 ('O_YOUMI_P01','O_YOUMI_P01_CARRIERBILLING101_CARRIERBILLING_RU','10201','11','11','YOUMI211',NULL,1,'2022-03-15 08:27:10','2022-03-21 08:42:00'),
	 ('I_9PAY_P01','I_9PAY_P01_CARDPAY201_CARDPAY_VN','10101','1001','********','9PAY801',NULL,1,'2022-03-16 10:08:29','2022-03-16 10:08:29'),
	 ('I_9PAY_P01','I_9PAY_P01_NETBANKING201_ATMCARD_VN','10101','1010','1010','9PAY801',NULL,1,'2022-03-16 10:08:30','2022-04-08 07:50:31'),
	 ('I_9PAY_P01','I_9PAY_P01_WALLET201_9PAY_VN','10101','9PAY','9PAY','9PAY801',NULL,1,'2022-03-16 10:08:31','2022-03-17 09:15:52'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_SETTLEORG_EG','10201','93','93','DLOCAL211',NULL,1,'2022-03-16 14:37:56','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BB_BR','10201','93','001','PAGSMILE201',NULL,1,'2022-03-16 14:37:56','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_AMAZONIA_BR','10201','93','003','PAGSMILE201',NULL,1,'2022-03-16 14:37:57','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_NORDESTE_BR','10201','93','004','PAGSMILE201',NULL,1,'2022-03-16 14:37:57','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BNDES_BR','10201','93','007','PAGSMILE201',NULL,1,'2022-03-16 14:37:57','2022-03-21 08:42:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CCRCOO_BR','10201','93','010','PAGSMILE201',NULL,1,'2022-03-16 14:37:58','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CSHGCOR_BR','10201','93','011','PAGSMILE201',NULL,1,'2022-03-16 14:37:58','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_INBURSA_BR','10201','93','012','PAGSMILE201',NULL,1,'2022-03-16 14:37:58','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_STATE_STREET_BR','10201','93','014','PAGSMILE201',NULL,1,'2022-03-16 14:37:59','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_UBS_CTVM_BR','10201','93','015','PAGSMILE201',NULL,1,'2022-03-16 14:37:59','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CCM_DESP_BR','10201','93','016','PAGSMILE201',NULL,1,'2022-03-16 14:38:00','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BNY_MELLON_BR','10201','93','017','PAGSMILE201',NULL,1,'2022-03-16 14:38:00','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_TRICURY_BR','10201','93','018','PAGSMILE201',NULL,1,'2022-03-16 14:38:00','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BANESTES_BR','10201','93','021','PAGSMILE201',NULL,1,'2022-03-16 14:38:01','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BANDEPE_BR','10201','93','024','PAGSMILE201',NULL,1,'2022-03-16 14:38:01','2022-03-21 08:42:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_ALFA_BR','10201','93','025','PAGSMILE201',NULL,1,'2022-03-16 14:38:01','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_ITAU_CONSIGNADO_BR','10201','93','029','PAGSMILE201',NULL,1,'2022-03-16 14:38:02','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_SANTANDER_BR','10201','93','033','PAGSMILE201',NULL,1,'2022-03-16 14:38:02','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BRADESCO_BBI_BR','10201','93','036','PAGSMILE201',NULL,1,'2022-03-16 14:38:03','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BANPARA_BR','10201','93','037','PAGSMILE201',NULL,1,'2022-03-16 14:38:03','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CARGILL_BR','10201','93','040','PAGSMILE201',NULL,1,'2022-03-16 14:38:03','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BANRISUL_BR','10201','93','041','PAGSMILE201',NULL,1,'2022-03-16 14:38:04','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BANESE_BR','10201','93','047','PAGSMILE201',NULL,1,'2022-03-16 14:38:04','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CONFIDENCE_CC_BR','10201','93','060','PAGSMILE201',NULL,1,'2022-03-16 14:38:04','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_HIPERCARD_BR','10201','93','062','PAGSMILE201',NULL,1,'2022-03-16 14:38:05','2022-03-21 08:42:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BRADESCARD_BR','10201','93','063','PAGSMILE201',NULL,1,'2022-03-16 14:38:05','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_GS_BR','10201','93','064','PAGSMILE201',NULL,1,'2022-03-16 14:38:05','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_ANDBANK_BR','10201','93','065','PAGSMILE201',NULL,1,'2022-03-16 14:38:06','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_MORGAN_STANLEY_BR','10201','93','066','PAGSMILE201',NULL,1,'2022-03-16 14:38:06','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CREFISA_BR','10201','93','069','PAGSMILE201',NULL,1,'2022-03-16 14:38:06','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BRASILIA_BR','10201','93','070','PAGSMILE201',NULL,1,'2022-03-16 14:38:07','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_J_SAFRA_BR','10201','93','074','PAGSMILE201',NULL,1,'2022-03-16 14:38:07','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_ABN_AMRO_BR','10201','93','075','PAGSMILE201',NULL,1,'2022-03-16 14:38:07','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_KDB_BR','10201','93','076','PAGSMILE201',NULL,1,'2022-03-16 14:38:08','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_INTER_BR','10201','93','077','PAGSMILE201',NULL,1,'2022-03-16 14:38:08','2022-03-21 08:42:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_HAITONG_BR','10201','93','078','PAGSMILE201',NULL,1,'2022-03-16 14:38:08','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_ORIGINAL_AGR_BR','10201','93','079','PAGSMILE201',NULL,1,'2022-03-16 14:38:09','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BT_CC_BR','10201','93','080','PAGSMILE201',NULL,1,'2022-03-16 14:38:09','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BANCOSEGURO_BR','10201','93','081','PAGSMILE201',NULL,1,'2022-03-16 14:38:10','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_TOPAZIO_BR','10201','93','082','PAGSMILE201',NULL,1,'2022-03-16 14:38:10','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BOC_BR','10201','93','083','PAGSMILE201',NULL,1,'2022-03-16 14:38:10','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_UNICRED_NORTE_BR','10201','93','084','PAGSMILE201',NULL,1,'2022-03-16 14:38:11','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CECRED_BR','10201','93','085','PAGSMILE201',NULL,1,'2022-03-16 14:38:11','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_RANDON_BR','10201','93','088','PAGSMILE201',NULL,1,'2022-03-16 14:38:11','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CREDISAN_CC_BR','10201','93','089','PAGSMILE201',NULL,1,'2022-03-16 14:38:12','2022-03-21 08:42:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CCCM_BR','10201','93','091','PAGSMILE201',NULL,1,'2022-03-16 14:38:12','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BRK_CFI_BR','10201','93','092','PAGSMILE201',NULL,1,'2022-03-16 14:38:12','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_POLOCRED_SCMEPP_BR','10201','93','093','PAGSMILE201',NULL,1,'2022-03-16 14:38:13','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_FINAXIS_BR','10201','93','094','PAGSMILE201',NULL,1,'2022-03-16 14:38:13','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_TRAVELEX_BR','10201','93','095','PAGSMILE201',NULL,1,'2022-03-16 14:38:13','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BANCOB3_BR','10201','93','096','PAGSMILE201',NULL,1,'2022-03-16 14:38:14','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CREDISIS_BR','10201','93','097','PAGSMILE201',NULL,1,'2022-03-16 14:38:14','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CREDIALIANCA_BR','10201','93','098','PAGSMILE201',NULL,1,'2022-03-16 14:38:15','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_UNIPRIME_BR','10201','93','099','PAGSMILE201',NULL,1,'2022-03-16 14:38:15','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_PLANNER_CV_BR','10201','93','100','PAGSMILE201',NULL,1,'2022-03-16 14:38:15','2022-03-21 08:42:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_RENASCENCA_BR','10201','93','101','PAGSMILE201',NULL,1,'2022-03-16 14:38:16','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_XP_INVESTIMENTOS_BR','10201','93','102','PAGSMILE201',NULL,1,'2022-03-16 14:38:17','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_LECCA_BR','10201','93','105','PAGSMILE201',NULL,1,'2022-03-16 14:38:17','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_PORTOCRED_BR','10201','93','108','PAGSMILE201',NULL,1,'2022-03-16 14:38:18','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_OLIVEIRA_TRUST_BR','10201','93','111','PAGSMILE201',NULL,1,'2022-03-16 14:38:19','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_MAGLIANO_BR','10201','93','113','PAGSMILE201',NULL,1,'2022-03-16 14:38:19','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CECOOP_BR','10201','93','114','PAGSMILE201',NULL,1,'2022-03-16 14:38:20','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_ADVANCED_BR','10201','93','117','PAGSMILE201',NULL,1,'2022-03-16 14:38:21','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CODEPE_BR','10201','93','127','PAGSMILE201',NULL,1,'2022-03-16 14:38:21','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CARUANA_BR','10201','93','130','PAGSMILE201',NULL,1,'2022-03-16 14:38:22','2022-03-21 08:42:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_TULLETT_PREBON_BR','10201','93','131','PAGSMILE201',NULL,1,'2022-03-16 14:38:22','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CRESOL_BR','10201','93','133','PAGSMILE201',NULL,1,'2022-03-16 14:38:23','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BGC_LIQUIDEZ_BR','10201','93','134','PAGSMILE201',NULL,1,'2022-03-16 14:38:23','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CONNACOOCU_BR','10201','93','136','PAGSMILE201',NULL,1,'2022-03-16 14:38:23','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_GET_MONEY_BR','10201','93','138','PAGSMILE201',NULL,1,'2022-03-16 14:38:24','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_EASYNVEST_BR','10201','93','140','PAGSMILE201',NULL,1,'2022-03-16 14:38:24','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BROKER_BR','10201','93','142','PAGSMILE201',NULL,1,'2022-03-16 14:38:24','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_TREVISO_BR','10201','93','143','PAGSMILE201',NULL,1,'2022-03-16 14:38:25','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_LEVYCAM_BR','10201','93','145','PAGSMILE201',NULL,1,'2022-03-16 14:38:25','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_ICAP_BR','10201','93','157','PAGSMILE201',NULL,1,'2022-03-16 14:38:25','2022-03-21 08:42:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CASA_CREDITO_BR','10201','93','159','PAGSMILE201',NULL,1,'2022-03-16 14:38:26','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_NUBANK_BR','10201','93','260','PAGSMILE201',NULL,1,'2022-03-16 14:38:26','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BARI_BR','10201','93','268','PAGSMILE201',NULL,1,'2022-03-16 14:38:26','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_PAGSEGURO_BR','10201','93','292','PAGSMILE201',NULL,1,'2022-03-16 14:38:27','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_SPEAME_BR','10201','93','340','PAGSMILE201',NULL,1,'2022-03-16 14:38:27','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_CREDITAS_BR','10201','93','342','PAGSMILE201',NULL,1,'2022-03-16 14:38:27','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_FFA_SCMEPP_BR','10201','93','343','PAGSMILE201',NULL,1,'2022-03-16 14:38:28','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BCO_CSF_BR','10201','93','368','PAGSMILE201',NULL,1,'2022-03-16 14:38:28','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BMS_SCD_BR','10201','93','377','PAGSMILE201',NULL,1,'2022-03-16 14:38:29','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_WORK_BR','10201','93','414','PAGSMILE201',NULL,1,'2022-03-16 14:38:29','2022-03-21 08:42:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_SENSO_BR','10201','93','545','PAGSMILE201',NULL,1,'2022-03-16 14:38:29','2022-03-21 08:42:00'),
	 ('O_PAGSMILE_P01','O_PAGSMILE_P01_BANKTRANSFER101_BCORNX_BR','10201','93','720','PAGSMILE201',NULL,1,'2022-03-16 14:38:30','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BB_BR','10201','93','001','DLOCAL211',NULL,1,'2022-03-16 14:38:30','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_AMAZONIA_BR','10201','93','003','DLOCAL211',NULL,1,'2022-03-16 14:38:30','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_NORDESTE_BR','10201','93','004','DLOCAL211',NULL,1,'2022-03-16 14:38:31','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BNDES_BR','10201','93','007','DLOCAL211',NULL,1,'2022-03-16 14:38:31','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_CCRCOO_BR','10201','93','010','DLOCAL211',NULL,1,'2022-03-16 14:38:31','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_CSHGCOR_BR','10201','93','011','DLOCAL211',NULL,1,'2022-03-16 14:38:32','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_INBURSA_BR','10201','93','012','DLOCAL211',NULL,1,'2022-03-16 14:38:32','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_STATE_STREET_BR','10201','93','014','DLOCAL211',NULL,1,'2022-03-16 14:38:32','2022-03-21 08:42:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_UBS_CTVM_BR','10201','93','015','DLOCAL211',NULL,1,'2022-03-16 14:38:33','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BNY_MELLON_BR','10201','93','017','DLOCAL211',NULL,1,'2022-03-16 14:38:33','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_TRICURY_BR','10201','93','018','DLOCAL211',NULL,1,'2022-03-16 14:38:33','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BANESTES_BR','10201','93','021','DLOCAL211',NULL,1,'2022-03-16 14:38:34','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BANDEPE_BR','10201','93','024','DLOCAL211',NULL,1,'2022-03-16 14:38:34','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_ALFA_BR','10201','93','025','DLOCAL211',NULL,1,'2022-03-16 14:38:34','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_ITAU_CONSIGNADO_BR','10201','93','029','DLOCAL211',NULL,1,'2022-03-16 14:38:35','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_SANTANDER_BR','10201','93','033','DLOCAL211',NULL,1,'2022-03-16 14:38:35','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BRADESCO_BBI_BR','10201','93','036','DLOCAL211',NULL,1,'2022-03-16 14:38:35','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BANPARA_BR','10201','93','037','DLOCAL211',NULL,1,'2022-03-16 14:38:36','2022-03-21 08:42:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_CARGILL_BR','10201','93','040','DLOCAL211',NULL,1,'2022-03-16 14:38:36','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BANRISUL_BR','10201','93','041','DLOCAL211',NULL,1,'2022-03-16 14:38:36','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BANESE_BR','10201','93','047','DLOCAL211',NULL,1,'2022-03-16 14:38:37','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_HIPERCARD_BR','10201','93','062','DLOCAL211',NULL,1,'2022-03-16 14:38:37','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BRADESCARD_BR','10201','93','063','DLOCAL211',NULL,1,'2022-03-16 14:38:38','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_GS_BR','10201','93','064','DLOCAL211',NULL,1,'2022-03-16 14:38:38','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_ANDBANK_BR','10201','93','065','DLOCAL211',NULL,1,'2022-03-16 14:38:39','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_MORGAN_STANLEY_BR','10201','93','066','DLOCAL211',NULL,1,'2022-03-16 14:38:39','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_CREFISA_BR','10201','93','069','DLOCAL211',NULL,1,'2022-03-16 14:38:39','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BRASILIA_BR','10201','93','070','DLOCAL211',NULL,1,'2022-03-16 14:38:40','2022-03-21 08:42:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_J_SAFRA_BR','10201','93','074','DLOCAL211',NULL,1,'2022-03-16 14:38:40','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_ABN_AMRO_BR','10201','93','075','DLOCAL211',NULL,1,'2022-03-16 14:38:40','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_KDB_BR','10201','93','076','DLOCAL211',NULL,1,'2022-03-16 14:38:41','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_INTER_BR','10201','93','077','DLOCAL211',NULL,1,'2022-03-16 14:38:41','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_HAITONG_BR','10201','93','078','DLOCAL211',NULL,1,'2022-03-16 14:38:41','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_ORIGINAL_AGR_BR','10201','93','079','DLOCAL211',NULL,1,'2022-03-16 14:38:42','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BANCOSEGURO_BR','10201','93','081','DLOCAL211',NULL,1,'2022-03-16 14:38:42','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_TOPAZIO_BR','10201','93','082','DLOCAL211',NULL,1,'2022-03-16 14:38:42','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BOC_BR','10201','93','083','DLOCAL211',NULL,1,'2022-03-16 14:38:43','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_UNICRED_NORTE_BR','10201','93','084','DLOCAL211',NULL,1,'2022-03-16 14:38:43','2022-03-21 08:42:00');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_CECRED_BR','10201','93','085','DLOCAL211',NULL,1,'2022-03-16 14:38:43','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_FINAXIS_BR','10201','93','094','DLOCAL211',NULL,1,'2022-03-16 14:38:44','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_TRAVELEX_BR','10201','93','095','DLOCAL211',NULL,1,'2022-03-16 14:38:44','2022-03-21 08:42:00'),
	 ('O_DLOCAL_P01','O_DLOCAL_P01_BANKTRANSFER101_BANCOB3_BR','10201','93','096','DLOCAL211',NULL,1,'2022-03-16 14:38:44','2022-03-21 08:42:00'),
	 ('I_SHOPEEPAYID_P05','I_SHOPEEPAYID_P05_WALLET201_QRIS_ID','10101','QRIS','QRIS','SHOPEEPAY_ID701',NULL,1,'2022-03-17 07:46:37','2022-03-17 07:46:37'),
	 ('I_BBL_P02','I_BBL_P02_NETBANKING201_BBL_TH','10101','PROMPTPAY','PROMPTPAY','BBL801',NULL,1,'2022-03-10 08:09:36','2022-03-10 08:22:37'),
	 ('I_BBL_P02','I_BBL_P02_WALLET201_PROMPTPAY_TH','10101','PROMPTPAY','PROMPTPAY','BBL801',NULL,1,'2022-03-18 06:58:15','2022-03-18 06:58:15'),
	 ('I_BEXS_P01','I_BEXS_P01_VIRTUALACCOUNT201_BOLETO_BR','10101','Boleto','Boleto','BEXS801',NULL,1,'2022-03-25 12:21:41','2022-03-25 12:21:41'),
	 ('I_BEXS_P01','I_BEXS_P01_WALLET201_PIX_BR','10101','PIX','PIX','BEXS801',NULL,1,'2022-03-25 12:21:42','2022-03-25 12:21:42'),
	 ('I_BEXS_P01','I_BEXS_P01_VIRTUALACCOUNT201_BANKTRANSFER_BR','10101','03','03','BEXS801',NULL,1,'2022-03-25 12:21:43','2022-03-25 12:21:43');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_CCAVENUE_S02','I_CCAVENUE_S02_CARDPAY201_VISA_AE','10101','A34','A34','CCAVENUE801',NULL,1,'2022-03-29 02:44:33','2022-03-29 02:44:33'),
	 ('I_CCAVENUE_S02','I_CCAVENUE_S02_CARDPAY201_MASTERCARD_AE','10101','A34','A34','CCAVENUE801',NULL,1,'2022-03-29 02:44:34','2022-03-29 02:44:34'),
	 ('I_CCAVENUE_S02','I_CCAVENUE_S02_CARDPAY201_AMEX_AE','10101','A34','A34','CCAVENUE801',NULL,1,'2022-03-29 02:44:35','2022-03-29 02:44:35'),
	 ('I_CCAVENUE_S02','I_CCAVENUE_S02_CARDPAY201_VISA_BH','10101','A34','A34','CCAVENUE801',NULL,1,'2022-03-29 02:44:36','2022-03-29 02:44:36'),
	 ('I_CCAVENUE_S02','I_CCAVENUE_S02_CARDPAY201_MASTERCARD_BH','10101','A34','A34','CCAVENUE801',NULL,1,'2022-03-29 02:44:37','2022-03-29 02:44:37'),
	 ('I_CCAVENUE_S02','I_CCAVENUE_S02_CARDPAY201_AMEX_BH','10101','A34','A34','CCAVENUE801',NULL,1,'2022-03-29 02:44:38','2022-03-29 02:44:38'),
	 ('I_CCAVENUE_S02','I_CCAVENUE_S02_CARDPAY201_VISA_EG','10101','A34','A34','CCAVENUE801',NULL,1,'2022-03-29 02:44:39','2022-03-29 02:44:39'),
	 ('I_CCAVENUE_S02','I_CCAVENUE_S02_CARDPAY201_MASTERCARD_EG','10101','A34','A34','CCAVENUE801',NULL,1,'2022-03-29 02:44:40','2022-03-29 02:44:40'),
	 ('I_CCAVENUE_S02','I_CCAVENUE_S02_CARDPAY201_AMEX_EG','10101','A34','A34','CCAVENUE801',NULL,1,'2022-03-29 02:44:41','2022-03-29 02:44:41'),
	 ('I_CCAVENUE_S02','I_CCAVENUE_S02_CARDPAY201_VISA_JO','10101','A34','A34','CCAVENUE801',NULL,1,'2022-03-29 02:44:42','2022-03-29 02:44:42');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_CCAVENUE_S02','I_CCAVENUE_S02_CARDPAY201_MASTERCARD_JO','10101','A34','A34','CCAVENUE801',NULL,1,'2022-03-29 02:44:43','2022-03-29 02:44:43'),
	 ('I_CCAVENUE_S02','I_CCAVENUE_S02_CARDPAY201_AMEX_JO','10101','A34','A34','CCAVENUE801',NULL,1,'2022-03-29 02:44:44','2022-03-29 02:44:44'),
	 ('I_CCAVENUE_S02','I_CCAVENUE_S02_CARDPAY201_VISA_KW','10101','A34','A34','CCAVENUE801',NULL,1,'2022-03-29 02:44:45','2022-03-29 02:44:45'),
	 ('I_CCAVENUE_S02','I_CCAVENUE_S02_CARDPAY201_MASTERCARD_KW','10101','A34','A34','CCAVENUE801',NULL,1,'2022-03-29 02:44:46','2022-03-29 02:44:46'),
	 ('I_CCAVENUE_S02','I_CCAVENUE_S02_CARDPAY201_AMEX_KW','10101','A34','A34','CCAVENUE801',NULL,1,'2022-03-29 02:44:47','2022-03-29 02:44:47'),
	 ('I_CCAVENUE_S02','I_CCAVENUE_S02_CARDPAY201_VISA_LB','10101','A34','A34','CCAVENUE801',NULL,1,'2022-03-29 02:44:48','2022-03-29 02:44:48'),
	 ('I_CCAVENUE_S02','I_CCAVENUE_S02_CARDPAY201_MASTERCARD_LB','10101','A34','A34','CCAVENUE801',NULL,1,'2022-03-29 02:44:49','2022-03-29 02:44:49'),
	 ('I_CCAVENUE_S02','I_CCAVENUE_S02_CARDPAY201_AMEX_LB','10101','A34','A34','CCAVENUE801',NULL,1,'2022-03-29 02:44:50','2022-03-29 02:44:50'),
	 ('I_CCAVENUE_S02','I_CCAVENUE_S02_CARDPAY201_VISA_OM','10101','A34','A34','CCAVENUE801',NULL,1,'2022-03-29 02:44:51','2022-03-29 02:44:51'),
	 ('I_CCAVENUE_S02','I_CCAVENUE_S02_CARDPAY201_MASTERCARD_OM','10101','A34','A34','CCAVENUE801',NULL,1,'2022-03-29 02:44:52','2022-03-29 02:44:52');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_CCAVENUE_S02','I_CCAVENUE_S02_CARDPAY201_AMEX_OM','10101','A34','A34','CCAVENUE801',NULL,1,'2022-03-29 02:44:53','2022-03-29 02:44:53'),
	 ('I_CCAVENUE_S02','I_CCAVENUE_S02_CARDPAY201_VISA_QA','10101','A34','A34','CCAVENUE801',NULL,1,'2022-03-29 02:44:54','2022-03-29 02:44:54'),
	 ('I_CCAVENUE_S02','I_CCAVENUE_S02_CARDPAY201_MASTERCARD_QA','10101','A34','A34','CCAVENUE801',NULL,1,'2022-03-29 02:44:55','2022-03-29 02:44:55'),
	 ('I_CCAVENUE_S02','I_CCAVENUE_S02_CARDPAY201_AMEX_QA','10101','A34','A34','CCAVENUE801',NULL,1,'2022-03-29 02:44:56','2022-03-29 02:44:56'),
	 ('I_CCAVENUE_S02','I_CCAVENUE_S02_CARDPAY201_VISA_SA','10101','A34','A34','CCAVENUE801',NULL,1,'2022-03-29 02:44:57','2022-03-29 02:44:57'),
	 ('I_CCAVENUE_S02','I_CCAVENUE_S02_CARDPAY201_MASTERCARD_SA','10101','A34','A34','CCAVENUE801',NULL,1,'2022-03-29 02:44:58','2022-03-29 02:44:58'),
	 ('I_CCAVENUE_S02','I_CCAVENUE_S02_CARDPAY201_AMEX_SA','10101','A34','A34','CCAVENUE801',NULL,1,'2022-03-29 02:44:59','2022-03-29 02:44:59'),
	 ('I_CCAVENUE_S02','I_CCAVENUE_S02_CARDPAY201_MADA_SA','10101','A45','A45','CCAVENUE801',NULL,1,'2022-03-29 02:45:00','2022-03-29 02:45:00'),
	 ('I_CCAVENUE_S02','I_CCAVENUE_S02_CARDPAY201_VISA_TR','10101','A34','A34','CCAVENUE801',NULL,1,'2022-03-29 02:45:01','2022-03-29 02:45:01'),
	 ('I_CCAVENUE_S02','I_CCAVENUE_S02_CARDPAY201_MASTERCARD_TR','10101','A34','A34','CCAVENUE801',NULL,1,'2022-03-29 02:45:02','2022-03-29 02:45:02');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_CCAVENUE_S02','I_CCAVENUE_S02_CARDPAY201_AMEX_TR','10101','A34','A34','CCAVENUE801',NULL,1,'2022-03-29 02:45:04','2022-03-29 02:45:04'),
	 ('I_CCAVENUE_S02','I_CCAVENUE_S02_CARDPAY201_VISA_MA','10101','A34','A34','CCAVENUE801',NULL,1,'2022-03-29 02:45:05','2022-03-29 02:45:05'),
	 ('I_CCAVENUE_S02','I_CCAVENUE_S02_CARDPAY201_MASTERCARD_MA','10101','A34','A34','CCAVENUE801',NULL,1,'2022-03-29 02:45:06','2022-03-29 02:45:06'),
	 ('I_CCAVENUE_S02','I_CCAVENUE_S02_CARDPAY201_AMEX_MA','10101','A34','A34','CCAVENUE801',NULL,1,'2022-03-29 02:45:06','2022-03-29 02:45:06'),
	 ('I_TENPAY_P01_01','I_TENPAY_P01_01_WALLET201_WECHATPAY_CN','10101','EWALLET','WECHAT','WECHAT801',NULL,1,'2022-03-30 10:21:24','2022-03-30 10:21:24'),
	 ('I_TENPAY_P01_03','I_TENPAY_P01_03_WALLET201_WECHATPAY_CN','10101','EWALLET','WECHAT','WECHAT803',NULL,1,'2022-03-30 10:21:24','2022-03-30 10:21:24'),
	 ('I_TENPAY_P01_02','I_TENPAY_P01_02_WALLET201_WECHATPAYHK_HK','10101','WECHAT','WECHAT','WECHAT802',NULL,1,'2022-03-30 10:21:25','2022-03-30 10:21:25'),
	 ('I_TENPAY_P01_04','I_TENPAY_P01_04_WALLET201_WECHATPAYHK_HK','10101','WECHAT','WECHAT','WECHAT804',NULL,1,'2022-03-30 10:21:25','2022-03-30 10:21:25'),
	 ('I_MCP_P05_30','I_MCP_P05_30_CARDPAY201_CARDPAY_ID','10101','A34','A34','MCP830',NULL,1,'2022-03-31 10:56:37','2022-05-27 07:19:23'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_ABB_VN','10101','83','ABB','9PAY211',NULL,1,'2022-04-12 10:23:21','2022-04-12 10:23:21');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_AGB_VN','10101','83','AGB','9PAY211',NULL,1,'2022-04-12 10:23:21','2022-04-12 10:23:21'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_BVB_VN','10101','83','BVB','9PAY211',NULL,1,'2022-04-12 10:23:21','2022-04-12 10:23:21'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_BIDV_VN','10101','83','BIDV','9PAY211',NULL,1,'2022-04-12 10:23:22','2022-04-12 10:23:22'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_CIMB_VN','10101','83','CIMB','9PAY211',NULL,1,'2022-04-12 10:23:22','2022-04-12 10:23:22'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_DAB_VN','10101','83','DAB','9PAY211',NULL,1,'2022-04-12 10:23:22','2022-04-12 10:23:22'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_EXB_VN','10101','83','EXB','9PAY211',NULL,1,'2022-04-12 10:23:23','2022-04-12 10:23:23'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_GPB_VN','10101','83','GPB','9PAY211',NULL,1,'2022-04-12 10:23:23','2022-04-12 10:23:23'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_HDB_VN','10101','83','HDB','9PAY211',NULL,1,'2022-04-12 10:23:23','2022-04-12 10:23:23'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_HONGLEONG_VN','10101','83','HLB','9PAY211',NULL,1,'2022-04-12 10:23:24','2022-04-12 10:23:24'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_IVB_VN','10101','83','IVB','9PAY211',NULL,1,'2022-04-12 10:23:24','2022-04-12 10:23:24');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_KLB_VN','10101','83','KLB','9PAY211',NULL,1,'2022-04-12 10:23:25','2022-04-12 10:23:25'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_LIENVIETPOSTBANK_VN','10101','83','LIENVIETPOSTBANK','9PAY211',NULL,1,'2022-04-12 10:23:25','2022-04-12 10:23:25'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_MB_VN','10101','83','MB','9PAY211',NULL,1,'2022-04-12 10:23:25','2022-04-12 10:23:25'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_MSB_VN','10101','83','MSB','9PAY211',NULL,1,'2022-04-12 10:23:26','2022-04-12 10:23:26'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_NAB_VN','10101','83','NAB','9PAY211',NULL,1,'2022-04-12 10:23:26','2022-04-12 10:23:26'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_NCB_VN','10101','83','NCB','9PAY211',NULL,1,'2022-04-12 10:23:26','2022-04-12 10:23:26'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_OCB_VN','10101','83','OCB','9PAY211',NULL,1,'2022-04-12 10:23:27','2022-04-12 10:23:27'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_OJB_VN','10101','83','OJB','9PAY211',NULL,1,'2022-04-12 10:23:27','2022-04-12 10:23:27'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_PGB_VN','10101','83','PGB','9PAY211',NULL,1,'2022-04-12 10:23:27','2022-04-12 10:23:27'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_PUBLICBANK_VN','10101','83','PUBLICBANK','9PAY211',NULL,1,'2022-04-12 10:23:28','2022-04-12 10:23:28');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_PVCOMBANK_VN','10101','83','PVCOMBANK','9PAY211',NULL,1,'2022-04-12 10:23:28','2022-04-12 10:23:28'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_STB_VN','10101','83','STB','9PAY211',NULL,1,'2022-04-12 10:23:28','2022-04-12 10:23:28'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_SGJSC_VN','10101','83','SCB','9PAY211',NULL,1,'2022-04-12 10:23:29','2022-04-12 10:23:29'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_SEA_VN','10101','83','SEA','9PAY211',NULL,1,'2022-04-12 10:23:29','2022-04-12 10:23:29'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_SHNB_VN','10101','83','SHNB','9PAY211',NULL,1,'2022-04-12 10:23:29','2022-04-12 10:23:29'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_TCB_VN','10101','83','TCB','9PAY211',NULL,1,'2022-04-12 10:23:30','2022-04-12 10:23:30'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_TPB_VN','10101','83','TPB','9PAY211',NULL,1,'2022-04-12 10:23:30','2022-04-12 10:23:30'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_UOB_VN','10101','83','UOB','9PAY211',NULL,1,'2022-04-12 10:23:30','2022-04-12 10:23:30'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_VIB_VN','10101','83','VIB','9PAY211',NULL,1,'2022-04-12 10:23:31','2022-04-12 10:23:31'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_VAB_VN','10101','83','VAB','9PAY211',NULL,1,'2022-04-12 10:23:31','2022-04-12 10:23:31');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_VIETBANK_VN','10101','83','VIETBANK','9PAY211',NULL,1,'2022-04-12 10:23:31','2022-04-12 10:23:31'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_VCB_VN','10101','83','VCB','9PAY211',NULL,1,'2022-04-12 10:23:32','2022-04-12 10:23:32'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_ICB_VN','10101','83','ICB','9PAY211',NULL,1,'2022-04-12 10:23:32','2022-04-12 10:23:32'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_VPB_VN','10101','83','VPB','9PAY211',NULL,1,'2022-04-12 10:23:32','2022-04-12 10:23:32'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_VRB_VN','10101','83','VRB','9PAY211',NULL,1,'2022-04-12 10:23:33','2022-04-12 10:23:33'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_ABB_VN_1','10101','93','ABB','9PAY211',NULL,1,'2022-04-12 10:23:33','2022-04-12 10:23:33'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_AGB_VN_1','10101','93','AGB','9PAY211',NULL,1,'2022-04-12 10:23:34','2022-04-12 10:23:34'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_BVB_VN_1','10101','93','BVB','9PAY211',NULL,1,'2022-04-12 10:23:34','2022-04-12 10:23:34'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_BIDV_VN_1','10101','93','BIDV','9PAY211',NULL,1,'2022-04-12 10:23:34','2022-04-12 10:23:34'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_CIMB_VN_1','10101','93','CIMB','9PAY211',NULL,1,'2022-04-12 10:23:35','2022-04-12 10:23:35');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_DAB_VN_1','10101','93','DAB','9PAY211',NULL,1,'2022-04-12 10:23:35','2022-04-12 10:23:35'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_EXB_VN_1','10101','93','EXB','9PAY211',NULL,1,'2022-04-12 10:23:35','2022-04-12 10:23:35'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_GPB_VN_1','10101','93','GPB','9PAY211',NULL,1,'2022-04-12 10:23:36','2022-04-12 10:23:36'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_HDB_VN_1','10101','93','HDB','9PAY211',NULL,1,'2022-04-12 10:23:36','2022-04-12 10:23:36'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_HONGLEONG_VN_1','10101','93','HLB','9PAY211',NULL,1,'2022-04-12 10:23:37','2022-04-12 10:23:37'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_IVB_VN_1','10101','93','IVB','9PAY211',NULL,1,'2022-04-12 10:23:37','2022-04-12 10:23:37'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_KLB_VN_1','10101','93','KLB','9PAY211',NULL,1,'2022-04-12 10:23:37','2022-04-12 10:23:37'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_LIENVIETPOSTBANK_VN_1','10101','93','LIENVIETPOSTBANK','9PAY211',NULL,1,'2022-04-12 10:23:38','2022-04-12 10:23:38'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_MB_VN_1','10101','93','MB','9PAY211',NULL,1,'2022-04-12 10:23:38','2022-04-12 10:23:38'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_MSB_VN_1','10101','93','MSB','9PAY211',NULL,1,'2022-04-12 10:23:39','2022-04-12 10:23:39');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_NAB_VN_1','10101','93','NAB','9PAY211',NULL,1,'2022-04-12 10:23:39','2022-04-12 10:23:39'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_NCB_VN_1','10101','93','NCB','9PAY211',NULL,1,'2022-04-12 10:23:39','2022-04-12 10:23:39'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_OCB_VN_1','10101','93','OCB','9PAY211',NULL,1,'2022-04-12 10:23:40','2022-04-12 10:23:40'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_OJB_VN_1','10101','93','OJB','9PAY211',NULL,1,'2022-04-12 10:23:40','2022-04-12 10:23:40'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_PGB_VN_1','10101','93','PGB','9PAY211',NULL,1,'2022-04-12 10:23:40','2022-04-12 10:23:40'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_PUBLICBANK_VN_1','10101','93','PUBLICBANK','9PAY211',NULL,1,'2022-04-12 10:23:41','2022-04-12 10:23:41'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_PVCOMBANK_VN_1','10101','93','PVCOMBANK','9PAY211',NULL,1,'2022-04-12 10:23:41','2022-04-12 10:23:41'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_STB_VN_1','10101','93','STB','9PAY211',NULL,1,'2022-04-12 10:23:41','2022-04-12 10:23:41'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_SGJSC_VN_1','10101','93','SCB','9PAY211',NULL,1,'2022-04-12 10:23:42','2022-04-12 10:23:42'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_SEA_VN_1','10101','93','SEA','9PAY211',NULL,1,'2022-04-12 10:23:42','2022-04-12 10:23:42');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_SHNB_VN_1','10101','93','SHNB','9PAY211',NULL,1,'2022-04-12 10:23:43','2022-04-12 10:23:43'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_TCB_VN_1','10101','93','TCB','9PAY211',NULL,1,'2022-04-12 10:23:43','2022-04-12 10:23:43'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_TPB_VN_1','10101','93','TPB','9PAY211',NULL,1,'2022-04-12 10:23:44','2022-04-12 10:23:44'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_UOB_VN_1','10101','93','UOB','9PAY211',NULL,1,'2022-04-12 10:23:44','2022-04-12 10:23:44'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_VIB_VN_1','10101','93','VIB','9PAY211',NULL,1,'2022-04-12 10:23:44','2022-04-12 10:23:44'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_VAB_VN_1','10101','93','VAB','9PAY211',NULL,1,'2022-04-12 10:23:45','2022-04-12 10:23:45'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_VIETBANK_VN_1','10101','93','VIETBANK','9PAY211',NULL,1,'2022-04-12 10:23:45','2022-04-12 10:23:45'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_VCB_VN_1','10101','93','VCB','9PAY211',NULL,1,'2022-04-12 10:23:45','2022-04-12 10:23:45'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_ICB_VN_1','10101','93','ICB','9PAY211',NULL,1,'2022-04-12 10:23:46','2022-04-12 10:23:46'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_VPB_VN_1','10101','93','VPB','9PAY211',NULL,1,'2022-04-12 10:23:46','2022-04-12 10:23:46');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_VRB_VN_1','10101','93','VRB','9PAY211',NULL,1,'2022-04-12 10:23:46','2022-04-12 10:23:46'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_ACB_VN','10101','93','ACB','9PAY211',NULL,1,'2022-04-12 10:23:47','2022-04-12 10:23:47'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_BAB_VN','10101','93','BAB','9PAY211',NULL,1,'2022-04-12 10:23:47','2022-04-12 10:23:47'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_GDB_VN','10101','93','GDB','9PAY211',NULL,1,'2022-04-12 10:23:47','2022-04-12 10:23:47'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_SGB_VN','10101','93','SGB','9PAY211',NULL,1,'2022-04-12 10:23:48','2022-04-12 10:23:48'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_SHB_VN','10101','93','SHB','9PAY211',NULL,1,'2022-04-12 10:23:48','2022-04-12 10:23:48'),
	 ('O_9PAY_P01','O_9PAY_P01_BANKTRANSFER101_WOORIBANKVN_VN','10101','93','WOORIBANK','9PAY211',NULL,1,'2022-04-12 10:23:49','2022-04-12 10:23:49'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_KDBKR_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:23:49','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_IBK_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:23:49','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_KB_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:23:49','2022-04-25 06:05:08');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_KEBHB_OLD_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:23:50','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_KB_OLD_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:23:50','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_SUHYUP_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:23:50','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_NONGHYUP_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:23:51','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_NACF_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:23:51','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_WOORIBANKKR_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:23:52','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_SCB_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:23:52','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_CITI_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:23:53','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_DGB_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:23:53','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_BNK_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:23:53','2022-04-25 06:05:08');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_KWANGJU_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:23:54','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_JEJU_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:23:54','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_JEONBUK_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:23:54','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_KYONGNAM_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:23:55','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_MG_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:23:55','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_SHINHYUP_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:23:55','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_KFSB_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:23:56','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_HSBC_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:23:56','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_DEUTSCHE_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:23:56','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_NFCF_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:23:57','2022-04-25 06:05:08');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_EPOST_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:23:57','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_KEBHANA_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:23:58','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_SHNB_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:23:58','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_KBANK_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:23:59','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_KAKAOBANK_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:23:59','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_KB_SECURITIES_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:23:59','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_MIRAE_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:24:00','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_SAMSUNG_SECURITIES_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:24:00','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_KIS_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:24:00','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_NHIS_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:24:01','2022-04-25 06:05:08');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_KYOBO_SECURITIES_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:24:01','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_HIIS_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:24:01','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_HM_SECURITIES_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:24:01','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_KIWOOM_SECURITIES_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:24:02','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_EBESTIS_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:24:02','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_SK_SECURITIES_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:24:03','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_DAISHIN_SECURITIES_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:24:03','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_HANHWAIS_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:24:03','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_SHINHAN_INVESTMENT_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:24:04','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_DBFI_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:24:04','2022-04-25 06:05:08');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_EUGENEIS_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:24:04','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_MERITZ_SECURITIES_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:24:05','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_WSB_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:24:05','2022-04-25 06:05:08'),
	 ('O_GME_P01','O_GME_P01_BANKTRANSFER101_GME_KR','10101','93','93','GME211',NULL,1,'2022-04-12 10:24:05','2022-04-25 06:05:08'),
	 ('O_PAYBY_P01','O_PAYBY_P01_BANKTRANSFER101_SETTLEORG_AE','10101','93','93','GME211',NULL,1,'2022-04-12 10:24:06','2022-04-12 10:24:06'),
	 ('O_PAYBY_P01','O_PAYBY_P01_BANKTRANSFER101_PAYBY_AE','10101','PAYBY','PAYBY','GME211',NULL,1,'2022-04-12 10:24:07','2022-04-12 10:24:07'),
	 ('O_LAZADAPH_P01','O_LAZADAPH_P01_WALLET101_LAZADAPH_PH','10101','LAZADA','LAZADA','LAZADA211',NULL,1,'2022-04-12 10:24:07','2022-04-12 10:24:07'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_AXIS_ID_1','10101','CBDATA','AXIS','DTONE211',NULL,1,'2022-04-12 10:24:08','2022-04-12 10:24:08'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_INDOSAT_ID_1','10101','CBDATA','INDOSAT','DTONE211',NULL,1,'2022-04-12 10:24:08','2022-04-12 10:24:08'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_TELKOMSEL_ID_1','10101','CBDATA','TELKOMSEL','DTONE211',NULL,1,'2022-04-12 10:24:08','2022-04-12 10:24:08');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_THREE_TELECOM_ID_1','10101','CBDATA','THREE_TELECOM','DTONE211',NULL,1,'2022-04-12 10:24:09','2022-04-12 10:24:09'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_XL_ID_1','10101','CBDATA','XL','DTONE211',NULL,1,'2022-04-12 10:24:09','2022-04-12 10:24:09'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_ID_1','10101','CBDATA','CarrierBillingData','DTONE211',NULL,1,'2022-04-12 10:24:09','2022-04-12 10:24:09'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CELCOM_MY_1','10101','CBDATA','CELCOM','DTONE211',NULL,1,'2022-04-12 10:24:10','2022-04-12 10:24:10'),
	 ('O_DTONE_P01','O_DTONE_P01_CARRIERBILLING101_CARRIERBILLING_MY_1','10101','CBDATA','CarrierBillingData','DTONE211',NULL,1,'2022-04-12 10:24:10','2022-04-12 10:24:10'),
	 ('F_PMHK_EIKONUK','*','10101','EIKON','EIKON','EIKON801',NULL,1,'2022-04-20 12:06:46','2022-04-20 12:06:46'),
	 ('I_WORLDPAY_P01','I_WORLDPAY_P01_CARDPAY201_VISA_TW','10101','VISA','VISA','WORLDPAY801',NULL,1,'2021-12-10 02:11:15','2022-01-10 02:02:30'),
	 ('I_WORLDPAY_P01','I_WORLDPAY_P01_CARDPAY201_MASTERCARD_TW','10101','MASTER','MASTER','WORLDPAY801',NULL,1,'2021-12-10 02:11:15','2022-01-10 02:02:37'),
	 ('I_WORLDPAY_P01','I_WORLDPAY_P01_CARDPAY201_VISA_JP','10101','VISA','VISA','WORLDPAY801',NULL,1,'2021-12-10 02:11:15','2022-01-10 02:02:30'),
	 ('I_WORLDPAY_P01','I_WORLDPAY_P01_CARDPAY201_MASTERCARD_JP','10101','MASTER','MASTER','WORLDPAY801',NULL,1,'2021-12-10 02:11:15','2022-01-10 02:02:37');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_AFFINBANK_P01','I_AFFINBANK_P01_NETBANKING201_AFFIN_MY','10101','FPX','FPX_AFFIN','FPX801',NULL,1,'2022-04-29 13:54:38','2022-04-29 13:54:38'),
	 ('I_AFFINBANK_P01','I_AFFINBANK_P01_NETBANKING201_AGRONET_MY','10101','FPX','FPX_AGRONET','FPX801',NULL,1,'2022-05-06 03:32:00','2022-05-06 03:32:00'),
	 ('I_AFFINBANK_P01','I_AFFINBANK_P01_NETBANKING201_AMBANK_MY','10101','FPX','FPX_AMBANK','FPX801',NULL,1,'2022-05-06 03:32:01','2022-05-06 03:32:01'),
	 ('I_AFFINBANK_P01','I_AFFINBANK_P01_NETBANKING201_ISLAM_MY','10101','FPX','FPX_ISIAM','FPX801',NULL,1,'2022-05-06 03:32:01','2022-05-06 03:32:01'),
	 ('I_AFFINBANK_P01','I_AFFINBANK_P01_NETBANKING201_MUAMALAT_MY','10101','FPX','FPX_MUAMALAT','FPX801',NULL,1,'2022-05-06 03:32:01','2022-05-06 03:32:01'),
	 ('I_AFFINBANK_P01','I_AFFINBANK_P01_NETBANKING201_RAKYAT_MY','10101','FPX','FPX_RAKYAT','FPX801',NULL,1,'2022-05-06 03:32:02','2022-05-06 03:32:02'),
	 ('I_AFFINBANK_P01','I_AFFINBANK_P01_NETBANKING201_BSN_MY','10101','FPX','FPX_BSN','FPX801',NULL,1,'2022-05-06 03:32:02','2022-05-06 03:32:02'),
	 ('I_AFFINBANK_P01','I_AFFINBANK_P01_NETBANKING201_CIMB_CLICKS_MY','10101','FPX','FPX_CIMB','FPX801',NULL,1,'2022-05-06 03:32:03','2022-05-06 03:32:03'),
	 ('I_AFFINBANK_P01','I_AFFINBANK_P01_NETBANKING201_HONGLEONG_MY','10101','FPX','FPX_HONGLEONG','FPX801',NULL,1,'2022-05-06 03:32:03','2022-05-06 03:32:03'),
	 ('I_AFFINBANK_P01','I_AFFINBANK_P01_NETBANKING201_HSBC_MY','10101','FPX','FPX_HSBC','FPX801',NULL,1,'2022-05-06 03:32:03','2022-05-06 03:32:03');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_AFFINBANK_P01','I_AFFINBANK_P01_NETBANKING201_KFH_MY','10101','FPX','FPX_KFH','FPX801',NULL,1,'2022-05-06 03:32:04','2022-05-06 03:32:04'),
	 ('I_AFFINBANK_P01','I_AFFINBANK_P01_NETBANKING201_MAYBANK2E_MY','10101','FPX','FPX_MAYBANK2E','FPX801',NULL,1,'2022-05-06 03:32:04','2022-05-06 03:32:04'),
	 ('I_AFFINBANK_P01','I_AFFINBANK_P01_NETBANKING201_MAYBANK2U_MY','10101','FPX','FPX_MAYBANK2U','FPX801',NULL,1,'2022-05-06 03:32:04','2022-05-06 03:32:04'),
	 ('I_AFFINBANK_P01','I_AFFINBANK_P01_NETBANKING201_OCBC_MY','10101','FPX','FPX_OCBC','FPX801',NULL,1,'2022-05-06 03:32:05','2022-05-06 03:32:05'),
	 ('I_AFFINBANK_P01','I_AFFINBANK_P01_NETBANKING201_PUBLIC_MY','10101','FPX','FPX_PUBLIC','FPX801',NULL,1,'2022-05-06 03:32:05','2022-05-06 03:32:05'),
	 ('I_AFFINBANK_P01','I_AFFINBANK_P01_NETBANKING201_RHB_MY','10101','FPX','FPX_RHB','FPX801',NULL,1,'2022-05-06 03:32:06','2022-05-06 03:32:06'),
	 ('I_AFFINBANK_P01','I_AFFINBANK_P01_NETBANKING201_SCB_MY','10101','FPX','FPX_SCB','FPX801',NULL,1,'2022-05-06 03:32:07','2022-05-06 03:32:07'),
	 ('I_AFFINBANK_P01','I_AFFINBANK_P01_NETBANKING201_UOBM_MY','10101','FPX','FPX_UOB','FPX801',NULL,1,'2022-05-06 03:32:07','2022-05-06 03:32:07'),
	 ('I_AFFINBANK_P01','I_AFFINBANK_P01_NETBANKING201_ALLIANCE_MY','10101','FPX','FPX_ALLIANCE','FPX801',NULL,1,'2022-05-06 03:32:08','2022-05-06 03:32:08'),
	 ('I_AFFINBANK_P01','I_AFFINBANK_P01_NETBANKING201_BOC_MY','10101','FPX','FPX_BOC','FPX801',NULL,1,'2022-05-06 03:32:08','2022-05-06 03:32:08');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_AFFINBANK_P01','I_AFFINBANK_P01_NETBANKING201_SBI_BANK_A_MY','10101','FPX','SBI_Bank_A','FPX801',NULL,1,'2022-05-07 09:48:32','2022-05-07 09:48:32'),
	 ('I_AFFINBANK_P01','I_AFFINBANK_P01_NETBANKING201_SBI_BANK_B_MY','10101','FPX','SBI_Bank_B','FPX801',NULL,1,'2022-05-07 09:48:33','2022-05-07 09:48:33'),
	 ('I_AFFINBANK_P01','I_AFFINBANK_P01_NETBANKING201_SBI_BANK_C_MY','10101','FPX','SBI_Bank_C','FPX801',NULL,1,'2022-05-07 09:48:33','2022-05-07 09:48:33'),
	 ('I_9PAY_P01','I_9PAY_P01_VIRTUALACCOUNT201_BIDV_VN','10101','1003','BIDV-4','9PAY802',NULL,1,'2022-05-10 04:23:58','2022-05-11 06:33:59'),
	 ('I_9PAY_P01','I_9PAY_P01_VIRTUALACCOUNT201_VPB_VN','10101','1003','VPB-4','9PAY802',NULL,1,'2022-05-10 04:23:59','2022-05-11 06:34:01'),
	 ('I_9PAY_P01','I_9PAY_P01_VIRTUALACCOUNT201_SHB_VN','10101','1003','SHB-4','9PAY802',NULL,1,'2022-05-10 04:23:59','2022-05-11 06:47:35'),
	 ('I_MCP_P05_06','I_MCP_P05_06_WALLET201_OVO_ID','10101','A36','A36','MCP816',NULL,1,'2022-05-10 16:06:07','2022-05-10 16:06:07'),
	 ('I_MCP_P05_07','I_MCP_P05_07_WALLET201_OVO_ID','10101','A36','A36','MCP817',NULL,1,'2022-05-10 16:06:08','2022-05-10 16:06:08'),
	 ('I_MCP_P05_03','I_MCP_P05_03_WALLET201_OVO_ID','10101','A36','A36','MCP813',NULL,1,'2022-05-10 16:06:09','2022-05-10 16:06:09'),
	 ('I_MCP_P05_04','I_MCP_P05_04_WALLET201_OVO_ID','10101','A36','A36','MCP814',NULL,1,'2022-05-10 16:06:09','2022-05-10 16:06:09');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_MCP_P05_05','I_MCP_P05_05_WALLET201_OVO_ID','10101','A36','A36','MCP815',NULL,1,'2022-05-10 16:06:10','2022-05-10 16:06:10'),
	 ('I_MCP_P05_08','I_MCP_P05_08_WALLET201_OVO_ID','10101','A36','A36','MCP818',NULL,1,'2022-05-10 16:06:11','2022-05-10 16:06:11'),
	 ('I_MCP_P05_09','I_MCP_P05_09_WALLET201_OVO_ID','10101','A36','A36','MCP819',NULL,1,'2022-05-10 16:06:11','2022-05-10 16:06:11'),
	 ('I_WORLDPAY_P01','I_WORLDPAY_P01_CARDPAY201_VISA_SG','10101','VISA','VISA','WORLDPAY801',NULL,1,'2022-05-13 07:07:15','2022-05-13 07:07:15'),
	 ('I_WORLDPAY_P01','I_WORLDPAY_P01_CARDPAY201_MASTERCARD_SG','10101','MASTER','MASTER','WORLDPAY801',NULL,1,'2022-05-13 07:07:16','2022-05-13 07:07:16'),
	 ('I_WORLDPAY_P01','I_WORLDPAY_P01_CARDPAY201_VISA_MY','10101','VISA','VISA','WORLDPAY801',NULL,1,'2022-05-13 07:07:17','2022-05-13 07:07:17'),
	 ('I_WORLDPAY_P01','I_WORLDPAY_P01_CARDPAY201_MASTERCARD_MY','10101','MASTER','MASTER','WORLDPAY801',NULL,1,'2022-05-13 07:07:18','2022-05-13 07:07:18'),
	 ('I_WORLDPAY_P01','I_WORLDPAY_P01_CARDPAY201_VISA_TH','10101','VISA','VISA','WORLDPAY801',NULL,1,'2022-05-13 07:07:18','2022-05-13 07:07:18'),
	 ('I_WORLDPAY_P01','I_WORLDPAY_P01_CARDPAY201_MASTERCARD_TH','10101','MASTER','MASTER','WORLDPAY801',NULL,1,'2022-05-13 07:07:19','2022-05-13 07:07:19'),
	 ('I_WORLDPAY_P01','I_WORLDPAY_P01_CARDPAY201_VISA_IN','10101','VISA','VISA','WORLDPAY801',NULL,1,'2022-05-13 07:07:20','2022-05-13 07:07:20');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_WORLDPAY_P01','I_WORLDPAY_P01_CARDPAY201_MASTERCARD_IN','10101','MASTER','MASTER','WORLDPAY801',NULL,1,'2022-05-13 07:07:21','2022-05-13 07:07:21'),
	 ('I_WORLDPAY_P01','I_WORLDPAY_P01_CARDPAY201_VISA_KR','10101','VISA','VISA','WORLDPAY801',NULL,1,'2022-05-13 07:07:21','2022-05-13 07:07:21'),
	 ('I_WORLDPAY_P01','I_WORLDPAY_P01_CARDPAY201_MASTERCARD_KR','10101','MASTER','MASTER','WORLDPAY801',NULL,1,'2022-05-13 07:07:22','2022-05-13 07:07:22'),
	 ('I_WORLDPAY_P01','I_WORLDPAY_P01_CARDPAY201_VISA_HK','10101','VISA','VISA','WORLDPAY801',NULL,1,'2022-05-13 07:07:23','2022-05-13 07:07:23'),
	 ('I_WORLDPAY_P01','I_WORLDPAY_P01_CARDPAY201_MASTERCARD_HK','10101','MASTER','MASTER','WORLDPAY801',NULL,1,'2022-05-13 07:07:24','2022-05-13 07:07:24'),
	 ('I_WORLDPAY_P01','I_WORLDPAY_P01_CARDPAY201_VISA_PH','10101','VISA','VISA','WORLDPAY801',NULL,1,'2022-05-13 07:07:24','2022-05-13 07:07:24'),
	 ('I_WORLDPAY_P01','I_WORLDPAY_P01_CARDPAY201_MASTERCARD_PH','10101','MASTER','MASTER','WORLDPAY801',NULL,1,'2022-05-13 07:07:25','2022-05-13 07:07:25'),
	 ('O_PMHK01_MUTHOOTUS_01','O_PMHK01_MUTHOOTUS_01_BANKTRANSFER101_SETTLEORG_IN','10206','93','93','MUTHOOT211',NULL,1,'2022-05-17 10:01:19','2022-05-17 10:01:19'),
	 ('O_PMHK01_MUTHOOTUS_01','O_PMHK01_MUTHOOTUS_01_WALLET101_UPI_IN','10201','UPI','UPI','MUTHOOT211',NULL,1,'2022-05-17 10:01:19','2022-05-24 07:22:34'),
	 ('O_QIWI_P01_02','O_QIWI_P01_02_WALLET101_QIWI_RU','10201','QIWI','QIWI','QIWI212',NULL,1,'2022-05-17 10:01:20','2022-05-17 10:01:20');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_QIWI_P01_02','O_QIWI_P01_02_BANKTRANSFER101_CARDORG_RU','10201','10','10','QIWI212',NULL,1,'2022-05-17 10:01:20','2022-05-17 10:01:20'),
	 ('I_MCP_P05_01','I_MCP_P05_01_VIRTUALACCOUNT201_BCA_ID','10101','A18','BCA','MCP811',NULL,1,'2022-05-24 03:59:12','2022-05-24 03:59:12'),
	 ('O_PMHK01_CITIHK_01','O_PMHK01_CITIHK_01_BANKTRANSFER101_SETTLEORG_SG','10201','93','93','CITI211',NULL,1,'2022-05-26 12:32:03','2022-05-26 12:32:03'),
	 ('I_BBL_P02','I_BBL_P02_NETBANKING201_BANGKOK_TH','10101','03','BANGKOK','BBL801',NULL,1,'2022-05-27 07:04:12','2022-05-27 07:04:12'),
	 ('F_PMHK01_BARXGB_01','F_PMHK01_BARXGB_01','10101','BARX','BARX','BARX801',NULL,1,'2022-05-27 10:56:16','2022-05-27 10:56:16'),
	 ('I_WORLDPAY_P01','I_WORLDPAY_P01_CARDPAY201_VISA_US','10101','VISA','VISA','WORLDPAY801',NULL,1,'2022-06-06 03:29:13','2022-06-06 03:29:13'),
	 ('I_WORLDPAY_P01','I_WORLDPAY_P01_CARDPAY201_MASTERCARD_US','10101','MASTER','MASTER','WORLDPAY801',NULL,1,'2022-06-06 03:32:41','2022-06-06 03:32:41'),
	 ('I_WORLDPAY_P01','I_WORLDPAY_P01_CARDPAY201_VISA_CA','10101','VISA','VISA','WORLDPAY801',NULL,1,'2022-06-06 03:32:42','2022-06-06 03:32:42'),
	 ('I_WORLDPAY_P01','I_WORLDPAY_P01_CARDPAY201_MASTERCARD_CA','10101','MASTER','MASTER','WORLDPAY801',NULL,1,'2022-06-06 03:32:43','2022-06-06 03:32:43'),
	 ('I_WORLDPAY_P01','I_WORLDPAY_P01_CARDPAY201_VISA_AU','10101','VISA','VISA','WORLDPAY801',NULL,1,'2022-06-06 03:32:45','2022-06-06 03:32:45');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_WORLDPAY_P01','I_WORLDPAY_P01_CARDPAY201_MASTERCARD_AU','10101','MASTER','MASTER','WORLDPAY801',NULL,1,'2022-06-06 03:32:46','2022-06-06 03:32:46'),
	 ('I_WORLDPAY_P01','I_WORLDPAY_P01_CARDPAY201_VISA_GB','10101','VISA','VISA','WORLDPAY801',NULL,1,'2022-06-06 03:32:48','2022-06-06 03:32:48'),
	 ('I_WORLDPAY_P01','I_WORLDPAY_P01_CARDPAY201_MASTERCARD_GB','10101','MASTER','MASTER','WORLDPAY801',NULL,1,'2022-06-06 03:32:49','2022-06-06 03:32:49'),
	 ('I_WORLDPAY_P01','I_WORLDPAY_P01_CARDPAY201_VISA_FR','10101','VISA','VISA','WORLDPAY801',NULL,1,'2022-06-06 03:32:50','2022-06-06 03:32:50'),
	 ('I_WORLDPAY_P01','I_WORLDPAY_P01_CARDPAY201_MASTERCARD_FR','10101','MASTER','MASTER','WORLDPAY801',NULL,1,'2022-06-06 03:32:51','2022-06-06 03:32:51'),
	 ('I_WORLDPAY_P01','I_WORLDPAY_P01_CARDPAY201_VISA_DE','10101','VISA','VISA','WORLDPAY801',NULL,1,'2022-06-06 03:32:52','2022-06-06 03:32:52'),
	 ('I_WORLDPAY_P01','I_WORLDPAY_P01_CARDPAY201_MASTERCARD_DE','10101','MASTER','MASTER','WORLDPAY801',NULL,1,'2022-06-06 03:32:53','2022-06-06 03:32:53'),
	 ('I_WORLDPAY_P01','I_WORLDPAY_P01_CARDPAY201_VISA_BE','10101','VISA','VISA','WORLDPAY801',NULL,1,'2022-06-06 03:32:54','2022-06-06 03:32:54'),
	 ('I_WORLDPAY_P01','I_WORLDPAY_P01_CARDPAY201_MASTERCARD_BE','10101','MASTER','MASTER','WORLDPAY801',NULL,1,'2022-06-06 03:32:55','2022-06-06 03:32:55'),
	 ('I_MCP_P01_03','I_MCP_P01_03_VIRTUALACCOUNT201_ALFAMART_ID','10101','A11','A11','MCP806',NULL,1,'2022-06-07 09:55:32','2022-06-07 09:55:32');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_MCP_P01_03','I_MCP_P01_03_VIRTUALACCOUNT201_POS_INDONESIA_ID','10101','POS INDONESIA','POS_INDONESIA','MCP806',NULL,1,'2022-06-07 09:55:33','2022-06-07 09:55:33'),
	 ('I_MCP_P01_03','I_MCP_P01_03_VIRTUALACCOUNT201_PEGADAIAN_ID','10101','PEGADAIAN','PEGADAIAN','MCP806',NULL,1,'2022-06-07 09:55:34','2022-06-07 09:55:34'),
	 ('I_CCAVENUE_S02','I_CCAVENUE_S02_WALLET201_PAYIT_AE','10101','PAYIT','PAYIT','CCAVENUE801',NULL,1,'2022-06-09 07:59:34','2022-06-16 05:45:15'),
	 ('I_ALIPAYHK_P01','I_ALIPAYHK_P01_WALLET201_AXIATABOOST_MY','10101','A40','A40','ALIPAY801',NULL,1,'2022-06-09 14:39:39','2022-06-09 14:39:39'),
	 ('I_ALIPAYHK_P01_02','I_ALIPAYHK_P01_02_AUTODEBIT201_DANA_ID','10101','AD','AD_DANA','ALIPAY802',NULL,1,'2022-06-17 06:06:52','2022-06-17 06:06:52'),
	 ('I_FLUTTERWAVE_S02','I_FLUTTERWAVE_S02_WALLET201_MTN_GH','10101','A15','MTN','FLW801',NULL,1,'2022-06-24 06:13:58','2022-06-24 06:13:58'),
	 ('I_FLUTTERWAVE_S02','I_FLUTTERWAVE_S02_WALLET201_VODAFONE_GH','10101','A15','VODAFONE','FLW801',NULL,1,'2022-06-24 06:13:58','2022-06-24 06:13:58'),
	 ('I_FLUTTERWAVE_S02','I_FLUTTERWAVE_S02_WALLET201_AIRTELTIGO_GH','10101','A15','AIRTEL','FLW801',NULL,1,'2022-06-24 06:13:58','2022-06-24 06:13:58'),
	 ('I_FLUTTERWAVE_S02','I_FLUTTERWAVE_S02_VIRTUALACCOUNT201_BANKTRANSFER_NG','10101','A18','A18','FLW801',NULL,1,'2022-06-24 06:13:58','2022-06-24 06:13:58'),
	 ('I_PMHK01_PPRO_01','I_PMHK01_PPRO_01_CARDPAY201_BANCONTACT_BE','10101','CARD','BANCONTACT','PPRO801',NULL,1,'2022-06-27 09:49:51','2022-06-27 09:49:51');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_PMHK01_PPRO_01','I_PMHK01_PPRO_01_NETBANKING201_GIROPAY_DE','10101','03','GIROPAY','PPRO801',NULL,1,'2022-06-27 09:49:54','2022-06-27 09:49:54'),
	 ('I_PMHK01_PPRO_01','I_PMHK01_PPRO_01_NETBANKING201_SOFORT_BE','10101','03','SOFORT','PPRO801',NULL,1,'2022-06-27 09:49:56','2022-06-27 09:49:56'),
	 ('I_PMHK01_PPRO_01','I_PMHK01_PPRO_01_NETBANKING201_SOFORT_DE','10101','03','SOFORT','PPRO801',NULL,1,'2022-06-27 09:49:57','2022-06-27 09:49:57'),
	 ('I_PMHK01_PPRO_01','I_PMHK01_PPRO_01_NETBANKING201_SOFORT_ES','10101','03','SOFORT','PPRO801',NULL,1,'2022-06-27 09:49:59','2022-06-27 09:49:59'),
	 ('I_PMHK01_PPRO_01','I_PMHK01_PPRO_01_NETBANKING201_SOFORT_IT','10101','03','SOFORT','PPRO801',NULL,1,'2022-06-27 09:50:01','2022-06-27 09:50:01'),
	 ('I_PMHK01_PPRO_01','I_PMHK01_PPRO_01_NETBANKING201_TRUSTLY_DE','10101','03','TRUSTLY','PPRO801',NULL,1,'2022-06-27 09:50:03','2022-06-27 09:50:03'),
	 ('I_PMHK01_PPRO_01','I_PMHK01_PPRO_01_NETBANKING201_TRUSTLY_ES','10101','03','TRUSTLY','PPRO801',NULL,1,'2022-06-27 09:50:05','2022-06-27 09:50:05'),
	 ('I_WORLDPAY_P01_02','I_WORLDPAY_P01_02_CARDPAY201_VISA_TW','10101','VISA','VISA','WORLDPAY802',NULL,1,'2022-06-28 05:49:37','2022-06-28 05:49:37'),
	 ('I_WORLDPAY_P01_02','I_WORLDPAY_P01_02_CARDPAY201_MASTERCARD_TW','10101','MASTER','MASTER','WORLDPAY802',NULL,1,'2022-06-28 05:49:40','2022-06-28 05:49:40'),
	 ('I_WORLDPAY_P01_02','I_WORLDPAY_P01_02_CARDPAY201_VISA_SG','10101','VISA','VISA','WORLDPAY802',NULL,1,'2022-06-28 05:49:43','2022-06-28 05:49:43');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_WORLDPAY_P01_02','I_WORLDPAY_P01_02_CARDPAY201_MASTERCARD_SG','10101','MASTER','MASTER','WORLDPAY802',NULL,1,'2022-06-28 05:49:47','2022-06-28 05:49:47'),
	 ('I_WORLDPAY_P01_02','I_WORLDPAY_P01_02_CARDPAY201_VISA_MY','10101','VISA','VISA','WORLDPAY802',NULL,1,'2022-06-28 05:49:50','2022-06-28 05:49:50'),
	 ('I_WORLDPAY_P01_02','I_WORLDPAY_P01_02_CARDPAY201_MASTERCARD_MY','10101','MASTER','MASTER','WORLDPAY802',NULL,1,'2022-06-28 05:49:53','2022-06-28 05:49:53'),
	 ('I_WORLDPAY_P01_02','I_WORLDPAY_P01_02_CARDPAY201_VISA_TH','10101','VISA','VISA','WORLDPAY802',NULL,1,'2022-06-28 05:49:56','2022-06-28 05:49:56'),
	 ('I_WORLDPAY_P01_02','I_WORLDPAY_P01_02_CARDPAY201_MASTERCARD_TH','10101','MASTER','MASTER','WORLDPAY802',NULL,1,'2022-06-28 05:49:58','2022-06-28 05:49:58'),
	 ('I_WORLDPAY_P01_02','I_WORLDPAY_P01_02_CARDPAY201_VISA_IN','10101','VISA','VISA','WORLDPAY802',NULL,1,'2022-06-28 05:50:01','2022-06-28 05:50:01'),
	 ('I_WORLDPAY_P01_02','I_WORLDPAY_P01_02_CARDPAY201_MASTERCARD_IN','10101','MASTER','MASTER','WORLDPAY802',NULL,1,'2022-06-28 05:50:04','2022-06-28 05:50:04'),
	 ('I_WORLDPAY_P01_02','I_WORLDPAY_P01_02_CARDPAY201_VISA_KR','10101','VISA','VISA','WORLDPAY802',NULL,1,'2022-06-28 05:50:07','2022-06-28 05:50:07'),
	 ('I_WORLDPAY_P01_02','I_WORLDPAY_P01_02_CARDPAY201_MASTERCARD_KR','10101','MASTER','MASTER','WORLDPAY802',NULL,1,'2022-06-28 05:50:10','2022-06-28 05:50:10'),
	 ('I_WORLDPAY_P01_02','I_WORLDPAY_P01_02_CARDPAY201_VISA_HK','10101','VISA','VISA','WORLDPAY802',NULL,1,'2022-06-28 05:50:14','2022-06-28 05:50:14');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_WORLDPAY_P01_02','I_WORLDPAY_P01_02_CARDPAY201_MASTERCARD_HK','10101','MASTER','MASTER','WORLDPAY802',NULL,1,'2022-06-28 05:50:17','2022-06-28 05:50:17'),
	 ('I_WORLDPAY_P01_02','I_WORLDPAY_P01_02_CARDPAY201_VISA_PH','10101','VISA','VISA','WORLDPAY802',NULL,1,'2022-06-28 05:50:20','2022-06-28 05:50:20'),
	 ('I_WORLDPAY_P01_02','I_WORLDPAY_P01_02_CARDPAY201_MASTERCARD_PH','10101','MASTER','MASTER','WORLDPAY802',NULL,1,'2022-06-28 05:50:23','2022-06-28 05:50:23'),
	 ('I_WORLDPAY_P01_02','I_WORLDPAY_P01_02_CARDPAY201_VISA_US','10101','VISA','VISA','WORLDPAY802',NULL,1,'2022-06-28 05:50:27','2022-06-28 05:50:27'),
	 ('I_WORLDPAY_P01_02','I_WORLDPAY_P01_02_CARDPAY201_MASTERCARD_US','10101','MASTER','MASTER','WORLDPAY802',NULL,1,'2022-06-28 05:50:30','2022-06-28 05:50:30'),
	 ('I_WORLDPAY_P01_02','I_WORLDPAY_P01_02_CARDPAY201_VISA_CA','10101','VISA','VISA','WORLDPAY802',NULL,1,'2022-06-28 05:50:33','2022-06-28 05:50:33'),
	 ('I_WORLDPAY_P01_02','I_WORLDPAY_P01_02_CARDPAY201_MASTERCARD_CA','10101','MASTER','MASTER','WORLDPAY802',NULL,1,'2022-06-28 05:50:35','2022-06-28 05:50:35'),
	 ('I_WORLDPAY_P01_02','I_WORLDPAY_P01_02_CARDPAY201_VISA_AU','10101','VISA','VISA','WORLDPAY802',NULL,1,'2022-06-28 05:50:38','2022-06-28 05:50:38'),
	 ('I_WORLDPAY_P01_02','I_WORLDPAY_P01_02_CARDPAY201_MASTERCARD_AU','10101','MASTER','MASTER','WORLDPAY802',NULL,1,'2022-06-28 05:50:41','2022-06-28 05:50:41'),
	 ('I_WORLDPAY_P01_02','I_WORLDPAY_P01_02_CARDPAY201_VISA_GB','10101','VISA','VISA','WORLDPAY802',NULL,1,'2022-06-28 05:50:46','2022-06-28 05:50:46');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_WORLDPAY_P01_02','I_WORLDPAY_P01_02_CARDPAY201_MASTERCARD_GB','10101','MASTER','MASTER','WORLDPAY802',NULL,1,'2022-06-28 05:50:50','2022-06-28 05:50:50'),
	 ('I_WORLDPAY_P01_02','I_WORLDPAY_P01_02_CARDPAY201_VISA_FR','10101','VISA','VISA','WORLDPAY802',NULL,1,'2022-06-28 05:50:54','2022-06-28 05:50:54'),
	 ('I_WORLDPAY_P01_02','I_WORLDPAY_P01_02_CARDPAY201_MASTERCARD_FR','10101','MASTER','MASTER','WORLDPAY802',NULL,1,'2022-06-28 05:50:58','2022-06-28 05:50:58'),
	 ('I_WORLDPAY_P01_02','I_WORLDPAY_P01_02_CARDPAY201_VISA_DE','10101','VISA','VISA','WORLDPAY802',NULL,1,'2022-06-28 05:51:01','2022-06-28 05:51:01'),
	 ('I_WORLDPAY_P01_02','I_WORLDPAY_P01_02_CARDPAY201_MASTERCARD_DE','10101','MASTER','MASTER','WORLDPAY802',NULL,1,'2022-06-28 05:51:05','2022-06-28 05:51:05'),
	 ('I_WORLDPAY_P01_02','I_WORLDPAY_P01_02_CARDPAY201_VISA_BE','10101','VISA','VISA','WORLDPAY802',NULL,1,'2022-06-28 05:51:08','2022-06-28 05:51:08'),
	 ('I_WORLDPAY_P01_02','I_WORLDPAY_P01_02_CARDPAY201_MASTERCARD_BE','10101','MASTER','MASTER','WORLDPAY802',NULL,1,'2022-06-28 05:51:11','2022-06-28 05:51:11'),
	 ('I_WORLDPAY_P01_02','I_WORLDPAY_P01_02_CARDPAY201_VISA_JP','10101','VISA','VISA','WORLDPAY802',NULL,1,'2022-06-28 05:51:16','2022-06-28 05:51:16'),
	 ('I_WORLDPAY_P01_02','I_WORLDPAY_P01_02_CARDPAY201_MASTERCARD_JP','10101','MASTER','MASTER','WORLDPAY802',NULL,1,'2022-06-28 05:51:20','2022-06-28 05:51:20'),
	 ('I_PMHK01_BUYUCOIN_01','I_PMHK01_BUYUCOIN_01_WALLET201_UPI_IN','10101','CUPI','CUPI','BUYUCONIN801',NULL,1,'2022-07-07 06:30:53','2022-07-07 06:30:53');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('I_PMHK01_BUYUCOIN_01','I_PMHK01_BUYUCOIN_01_WALLET201_UPI_IN_1','10101','CUPI','CUPI','BUYUCONIN801',NULL,1,'2022-07-07 06:30:55','2022-07-07 06:30:55'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_AHLI_UNITED_EG','10201','10','AUB','PAYMOB211',NULL,1,'2022-07-07 06:31:29','2022-07-07 06:31:29'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_CITI_NA_EG','10201','10','CITI','PAYMOB211',NULL,1,'2022-07-07 12:06:19','2022-07-07 12:06:19'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_MID_EG','10201','10','MIDB','PAYMOB211',NULL,1,'2022-07-07 12:06:22','2022-07-07 12:06:22'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_BANQUE_DU_CAIRE_EG','10201','10','BDC','PAYMOB211',NULL,1,'2022-07-07 12:06:26','2022-07-07 12:06:26'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_HSBC_EG','10201','10','HSBC','PAYMOB211',NULL,1,'2022-07-07 12:06:29','2022-07-07 12:06:29'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_AGRICOLE_CREDIT_EGYPT_EG','10201','10','CAE','PAYMOB211',NULL,1,'2022-07-07 12:06:32','2022-07-07 12:06:32'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_GULF_EG','10201','10','EGB','PAYMOB211',NULL,1,'2022-07-07 12:06:35','2022-07-07 12:06:35'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_UNITED_EGYPT_EG','10201','10','UB','PAYMOB211',NULL,1,'2022-07-07 12:06:38','2022-07-07 12:06:38'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_ALAHLI_QNB_EG','10201','10','QNB','PAYMOB211',NULL,1,'2022-07-07 12:06:41','2022-07-07 12:06:41');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_ARAB_PLC_EG','10201','10','ARAB','PAYMOB211',NULL,1,'2022-07-07 12:06:45','2022-07-07 12:06:45'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_EMIRATES_NATIONAL_DUBAI_EG','10201','10','ENBD','PAYMOB211',NULL,1,'2022-07-07 12:06:48','2022-07-07 12:06:48'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_AL_AHLI_KUWAIT_EG','10201','10','ABK','PAYMOB211',NULL,1,'2022-07-07 12:06:51','2022-07-07 12:06:51'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_NATIONAL_KUWAIT_EG','10201','10','NBK','PAYMOB211',NULL,1,'2022-07-07 12:06:55','2022-07-07 12:06:55'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_ARAB_CORPORATION_EG','10201','10','ABC','PAYMOB211',NULL,1,'2022-07-07 12:06:58','2022-07-07 12:06:58'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_FIRST_ABU_DHABI_EG','10201','10','FAB','PAYMOB211',NULL,1,'2022-07-07 12:07:01','2022-07-07 12:07:01'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_ABU_DHABI_ISLAMIC_EG','10201','10','ADIB','PAYMOB211',NULL,1,'2022-07-07 12:07:04','2022-07-07 12:07:04'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_COMMERCIAL_INTERNATIONAL_EG','10201','10','CIB','PAYMOB211',NULL,1,'2022-07-07 12:07:07','2022-07-07 12:07:07'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_HOUSING_DEVELOPMENT_EG','10201','10','HDB','PAYMOB211',NULL,1,'2022-07-07 12:07:11','2022-07-07 12:07:11'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_BANQUE_MISR_EG','10201','10','MISR','PAYMOB211',NULL,1,'2022-07-07 12:07:14','2022-07-07 12:07:14');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_ARAB_AFRICAN_INTERNATIONAL_EG','10201','10','AAIB','PAYMOB211',NULL,1,'2022-07-07 12:07:17','2022-07-07 12:07:17'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_ARAB_LAND_EG','10201','10','EALB','PAYMOB211',NULL,1,'2022-07-07 12:07:20','2022-07-07 12:07:20'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_EXPORT_DEVELOPMENT_EG','10201','10','EDBE','PAYMOB211',NULL,1,'2022-07-07 12:07:24','2022-07-07 12:07:24'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_FAISAL_ISLAMIC_EG','10201','10','FAIB','PAYMOB211',NULL,1,'2022-07-07 12:07:27','2022-07-07 12:07:27'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_BLOM_EG','10201','10','BLOM','PAYMOB211',NULL,1,'2022-07-07 12:07:30','2022-07-07 12:07:30'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_ABU_DHABI_COMMERCIAL_EG','10201','10','ADCB','PAYMOB211',NULL,1,'2022-07-07 12:07:33','2022-07-07 12:07:33'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_ALEX_BANK_EG','10201','10','BOA','PAYMOB211',NULL,1,'2022-07-07 12:07:37','2022-07-07 12:07:37'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_SOCIETE_ARABE_INTERNATIONALE_EG','10201','10','SAIB','PAYMOB211',NULL,1,'2022-07-07 12:07:40','2022-07-07 12:07:40'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_NATIONAL_EGYPT_EG','10201','10','NBE','PAYMOB211',NULL,1,'2022-07-07 12:07:43','2022-07-07 12:07:43'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_AL_BARAKA_EG','10201','10','ABRK','PAYMOB211',NULL,1,'2022-07-07 12:07:46','2022-07-07 12:07:46');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_POST_EG','10201','10','POST','PAYMOB211',NULL,1,'2022-07-07 12:07:49','2022-07-07 12:07:49'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_NASSER_SOCIAL_EG','10201','10','NSB','PAYMOB211',NULL,1,'2022-07-07 12:07:53','2022-07-07 12:07:53'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_INDUSTRIAL_DEVELOPMENT_EG','10201','10','IDB','PAYMOB211',NULL,1,'2022-07-07 12:07:56','2022-07-07 12:07:56'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_SUEZ_CANAL_EG','10201','10','SCB','PAYMOB211',NULL,1,'2022-07-07 12:07:59','2022-07-07 12:07:59'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_MASHREQ_EG','10201','10','MASH','PAYMOB211',NULL,1,'2022-07-07 12:08:02','2022-07-07 12:08:02'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_ARAB_INVESTMENT_EG','10201','10','AIB','PAYMOB211',NULL,1,'2022-07-07 12:08:05','2022-07-07 12:08:05'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_AUDI_EG','10201','10','AUDI','PAYMOB211',NULL,1,'2022-07-07 12:08:08','2022-07-07 12:08:08'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_GASC_EG','10201','10','GASC','PAYMOB211',NULL,1,'2022-07-07 12:08:12','2022-07-07 12:08:12'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_ARAB_INTERNATIONAL_EG','10201','10','ARIB','PAYMOB211',NULL,1,'2022-07-07 12:08:15','2022-07-07 12:08:15'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_AGRICULTURAL_EGYPT_EG','10201','10','PDAC','PAYMOB211',NULL,1,'2022-07-07 12:08:18','2022-07-07 12:08:18');
INSERT INTO tb_funding_channel_config_mapping (channel_code,channel_method_code,old_product_code,old_method_code,old_method_sub_code,old_channel_code,config_json,status,create_time,modified_time) VALUES
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_NATIONAL_GREECE_EG','10201','10','NBG','PAYMOB211',NULL,1,'2022-07-07 12:08:21','2022-07-07 12:08:21'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_CENTRAL_EGYPT_EG','10201','10','CBE','PAYMOB211',NULL,1,'2022-07-07 12:08:24','2022-07-07 12:08:24'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_BANKTRANSFER101_ATTIJARIWAFA_EG','10201','10','BBE','PAYMOB211',NULL,1,'2022-07-07 12:08:27','2022-07-07 12:08:27'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_WALLET101_VODAFONE_EG','10201','VODAFONE','VODAFONE','PAYMOB211',NULL,1,'2022-07-07 12:08:31','2022-07-07 12:08:31'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_WALLET101_ETISALAT_EG','10201','ETISALAT','ETISALAT','PAYMOB211',NULL,1,'2022-07-07 12:08:33','2022-07-07 12:08:33'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_WALLET101_ORANGE_EG','10201','ORANGE','ORANGE','PAYMOB211',NULL,1,'2022-07-07 12:08:35','2022-07-07 12:08:35'),
	 ('O_PMHK01_PAYMOBEG01_01','O_PMHK01_PAYMOBEG01_01_CASH101_AMAN_EG','10201','AMAN','AMAN','PAYMOB211',NULL,1,'2022-07-07 12:08:37','2022-07-07 12:08:37');
