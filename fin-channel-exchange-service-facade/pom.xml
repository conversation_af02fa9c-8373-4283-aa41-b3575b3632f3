<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.payermax.channel</groupId>
        <artifactId>channel-exchange</artifactId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>channel-exchange-facade</artifactId>
    <version>${channel-exchange-facade.version}</version>
    <name>channel-exchange-facade</name>
    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.payermax.common</groupId>
            <artifactId>common-dto</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.infra</groupId>
            <artifactId>ionia-log-digest-core</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-web</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-log4j2</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.payermax.common</groupId>
                    <artifactId>common-lang</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
    <distributionManagement>
        <repository>
            <id>Releases</id>
            <name>User Porject Release</name>
            <url>http://pay-nexus.shareitpay.in/repository/maven-releases/</url>
            <uniqueVersion>true</uniqueVersion>
        </repository>
        <snapshotRepository>
            <id>Snapshots</id>
            <name>User Porject Snapshot</name>
            <url>http://pay-nexus.shareitpay.in/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
    <build>
        <plugins>
            <plugin>
                <groupId>com.github.shalousun</groupId>
                <artifactId>smart-doc-maven-plugin</artifactId>
                <version>2.3.2</version>
                <configuration>
                    <!--指定生成文档的使用的配置文件,配置文件放在自己的项目中-->
                    <configFile>./src/main/resources/smart-doc.json</configFile>
                    <!--指定项目名称-->
                    <projectName>channel-exchange</projectName>
                    <excludes>
                        <!--1.0.7版本开始你还可以用正则匹配排除,如：poi.* -->
                        <exclude>com.alibaba:fastjson</exclude>
                    </excludes>
                    <includes>
                        <!--格式为：groupId:artifactId;参考如下-->
                        <include>com.alibaba:fastjson</include>
                    </includes>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>html</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.4</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.payermax.infra</groupId>
                <artifactId>check-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>3.1.0</version>
                <dependencies>
                    <dependency>
                        <groupId>custom-rule</groupId>
                        <artifactId>custom-rule-sample</artifactId>
                        <version>1.0.0-SNAPSHOT</version>
                    </dependency>
                </dependencies>
                <executions>
                    <execution>
                        <id>enforce-banned-dependencies</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <myCustomRule implementation="com.payermax.infra.custom.rule.sample.SnapshotInterceptorRule">
                                    <!--只有当打release包时规则生效-->
                                    <onlyWhenRelease>false</onlyWhenRelease>
                                    <!--告警级别-->
                                    <level>ERROR</level>
                                    <!--这些包排除掉，因为历史原因，允许为snapshot-->
                                    <excludes>
                                        <!--历史版本代码，无人维护，代码在fintech-parent sharding分支 -->
                                        <exclude>com.ushareit.fintech.parent:fintech-components-shardingphysical-core:jar:[1.0-SNAPSHOT]</exclude>
                                        <!--未部署release分支，无人维护 -->
                                        <exclude>com.ushareit.fintech.parent:fintech-components-security:jar:[1.0-SNAPSHOT]</exclude>
                                        <!--未部署release分支，无人维护 -->
                                        <exclude>com.payermax.common:common-exception-handler:jar:[1.0.0-SNAPSHOT]</exclude>
                                        <!--未部署release分支，无人维护 -->
                                        <exclude>com.ushareit.fintech.parent:fintech-components-jasypt-dubbo:jar:[1.0-SNAPSHOT]</exclude>
                                        <!--未部署release分支，无人维护 -->
                                        <exclude>com.ushareit.fintech.parent:fintech-components-jasypt-kms:jar:[1.0-SNAPSHOT]</exclude>
                                        <!--未部署release分支，无人维护 -->
                                        <exclude>com.ushareit.fintech.parent:fintech-components-jasypt:jar:[1.0-SNAPSHOT]</exclude>
                                    </excludes>
                                    <message>依赖列表中包含SNAPSHOT版本禁止部署，请升级到RELEASE版本再部署：</message>
                                </myCustomRule>
                                <bannedDependencies>
                                    <level>ERROR</level>
                                    <excludes>
                                        <!-- 摘要日志缺陷版本 -->
                                        <exclude>com.payermax.common:fintech-components-log:[1.4.5-20220719-RELEASE]</exclude>
                                        <exclude>com.payermax.common:fintech-components-log:[1.4.2-20220621-RELEASE]</exclude>
                                        <exclude>com.payermax.common:fintech-components-log:[1.4.1-20220620-RELEASE]</exclude>
                                        <exclude>com.payermax.common:fintech-components-log:[1.4.0-20220620-RELEASE]</exclude>
                                        <exclude>com.payermax.common:fintech-components-log:[1.3.9-20220617-RELEASE]</exclude>
                                        <exclude>com.payermax.common:fintech-components-log:[1.3.8-20220617-RELEASE]</exclude>
                                        <exclude>com.payermax.common:fintech-components-log:[1.3.6-20220617-RELEASE]</exclude>
                                        <exclude>com.payermax.common:fintech-components-log:[1.3.3-20220617-RELEASE]</exclude>
                                        <exclude>com.payermax.common:fintech-components-log:[1.3.3-20220613-RELEASE]</exclude>
                                        <exclude>com.payermax.common:fintech-components-log:[1.3.2-20220613-RELEASE]</exclude>
                                        <exclude>com.payermax.common:fintech-components-log:[1.3.1-20220530-RELEASE]</exclude>
                                    </excludes>
                                    <message>存在有缺陷的摘要日志版本,请升级至最新版本</message>
                                </bannedDependencies>
                                <bannedDependencies>
                                    <level>ERROR</level>
                                    <excludes>
                                        <exclude>com.alibaba:fastjson</exclude>
                                    </excludes>
                                    <includes>
                                        <!-- fastjson缺陷版本,仅支持1.2.83以上的版本 -->
                                        <include>com.alibaba:fastjson:1.2.83</include>
                                    </includes>
                                    <message>请将fastjson版本升级到1.2.83版本及以上</message>
                                </bannedDependencies>
                                <bannedDependencies>
                                    <level>ERROR</level>
                                    <excludes>
                                        <exclude>com.payermax.common:fintech-components-mq-kafka-change-server-apollo</exclude>
                                        <exclude>com.payermax.common:fintech-components-mq-kafka-change-server-nacos</exclude>
                                    </excludes>
                                    <message>Kafka集群迁移工作已完成,请移除对应的自动切换组件</message>
                                </bannedDependencies>
                                <bannedDependencies>
                                    <level>ERROR</level>
                                    <excludes>
                                        <exclude>org.jboss.resteasy:*:[3.0.19.Final]</exclude>
                                    </excludes>
                                    <message>resteasy该版本有内存泄露缺陷,请升级到3.0.20.Final及以上版本</message>
                                </bannedDependencies>
                                <bannedDependencies>
                                    <level>ERROR</level>
                                    <excludes>
                                        <exclude>com.payermax.common:fintech-components-multilevel-cache-aspectj:[1.0.2-20220615-RELEASE]</exclude>
                                    </excludes>
                                    <message>存在有缺陷的多级缓存版本,请升级至最新版本</message>
                                </bannedDependencies>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
