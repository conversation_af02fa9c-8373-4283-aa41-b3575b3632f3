package com.payermax.fin.exchange.service.response;

import lombok.Data;

import java.io.Serializable;

@Data
public class ChannelMerchantResponse implements Serializable {

    private static final long serialVersionUID = 5442809303902096231L;

    private String code;

    private String instCode;

    private String channelCode;

    private String entity;

    private String mid;

    private String name;

    private String accountJson;

    private Byte status;

    private String bizHandleInstCode;

    private String fundsSettleInstCode;

    private Byte relatedMerchantType;

    private String relatedMerchantDetail;

    private String channelType;

    private Byte assignType;

    private String channelId;

}
