package com.payermax.fin.exchange.service.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/14 16:12
 */
@Data
public class AvailabilityFilterResponse implements Serializable {

    private static final long serialVersionUID = -784481336692166245L;

    /**
     * 支付方式可用性详情
     */
    private List<PaymentMethod> paymentMethods;
    /**
     * 收银产品不可用原因
     */
    private List<DisablePaymentMethod> disablePaymentMethods;

    @Data
    public static class PaymentMethod implements Serializable {

        private static final long serialVersionUID = 931005119084219083L;
        /**
         * 收银产品编码
         */
        private String cashierProductNo;
        /**
         * 币种
         */
        private String currency;

    }

    @Data
    public static class DisablePaymentMethod implements Serializable {

        private static final long serialVersionUID = -6260765682674802991L;

        /**
         * 收银产品编码
         */
        private String cashierProductNo;
        /**
         * 币种
         */
        private String currency;
        /**
         * 不可用原因明细集合
         */
        private List<DisableReasonDetail> reasonDetails;
    }

    @Data
    public static class DisableReasonDetail implements Serializable {

        private static final long serialVersionUID = -8040535679524112430L;
        /**
         * 支付方式Code
         */
        private String channelMethodCode;
        /**
         * 渠道商户Mid
         */
        private String channelMerchantCode;
        /**
         * 错误码
         */
        private String errorCode;
        /**
         * 错误描述
         */
        private String errorMsg;
    }

}
