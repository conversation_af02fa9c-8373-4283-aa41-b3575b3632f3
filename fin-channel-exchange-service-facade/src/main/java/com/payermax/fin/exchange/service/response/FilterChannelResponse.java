package com.payermax.fin.exchange.service.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/10/14 16:23
 */
@Data
public class FilterChannelResponse implements Serializable {
    private static final long serialVersionUID = -8184382515028590083L;

    /**
     * 支付方式可用性详情
     */
    private List<PaymentMethod> paymentMethods;

    @Data
    public static class PaymentMethod implements Serializable {

        private static final long serialVersionUID = 931005119084219083L;
        /**
         * 此条支付方式唯一标识，同请求入参，用于和请求对应
         */
        private String paymentMethodNo;
        /**
         * 可用渠道列表
         */
        private Set<String> availableChannelCodes;

    }
}
