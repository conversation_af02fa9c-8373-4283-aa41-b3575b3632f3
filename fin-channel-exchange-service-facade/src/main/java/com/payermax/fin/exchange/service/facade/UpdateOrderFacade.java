package com.payermax.fin.exchange.service.facade;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.fin.exchange.service.request.CloseOrderRequest;
import com.payermax.fin.exchange.service.request.UpdateCommitOrderRequest;
import com.payermax.fin.exchange.service.request.UpdateRequestOrderRequest;

import java.util.List;

/**
 * 更新订单服务
 *
 * <AUTHOR>
 * @date 2022/4/19 17:53
 * @dubbo
 */
public interface UpdateOrderFacade {

    /**
     * 更新提交单信息
     *
     * @param request
     * @return
     */
    Result<Long> updateCommitOrder(List<UpdateCommitOrderRequest> request);

    /**
     * 更新请求单信息
     *
     * @param request
     * @return
     */
    Result<Long> updateRequestOrder(List<UpdateRequestOrderRequest> request);

    /**
     * 关单通知
     *
     * @param request
     * @return
     */
    Result<Void> closeOrderNotify(CloseOrderRequest request);

}
