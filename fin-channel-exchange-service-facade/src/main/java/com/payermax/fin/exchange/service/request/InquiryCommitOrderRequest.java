package com.payermax.fin.exchange.service.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 查询提交单请求
 *
 * <AUTHOR>
 * @date 2022/2/15 22:22
 */
@Data
public class InquiryCommitOrderRequest implements BaseRequest {

    private static final long serialVersionUID = -6591287407406682539L;

    /**
     * 渠道提交单号
     */
    private String channelPayCommitNo;

    /**
     * 支付类型
     */
    private String paymentType;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 渠道支付方式编码
     */
    private String channelMethodCode;

    /**
     * 渠道支付方式编码集合
     */
    private List<String> channelMethodCodes;

    /**
     * 国家二字码
     */
    private String country;

    /**
     * 查询开始时间戳
     */
    private Long startTimeMillis;

    /**
     * 查询结束时间戳
     */
    private Long endTimeMillis;

    /**
     * 状态集合
     */
    private List<Integer> statusList;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 是否有批次号
     */
    private Boolean hasBatchNo;

    /**
     * 请求类型
     */
    private String requestType;

    /**
     * 请求大类
     */
    private String mainRequestType;

    /**
     * 订单主体
     */
    private String channelEntity;

    /**
     * 渠道商户编码
     */
    private String channelMerchantCode;

    /**
     * 渠道商户编码集合
     */
    private List<String> channelMerchantCodes;

}
