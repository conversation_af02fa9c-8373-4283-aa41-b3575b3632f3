package com.payermax.fin.exchange.service.request;

import lombok.Data;

/**
 * <AUTHOR>
 * @date Create on 2021/9/2 20:29
 */
@Data
public class InquiryOrderInfoRequest implements BaseRequest {

    private static final long serialVersionUID = 134817620753322318L;

    /**
     * 渠道提交单号，和渠道申请单号 两者必填其一
     */
    private String channelPayCommitNo;

    /**
     * 渠道申请单号，和渠道提交单号 两者必填其一
     */
    private String channelPayRequestNo;

    /**
     * 支付类型,可用值:10-出,20-入,30-兑,40-退,50-转
     */
    private String paymentType;

}
