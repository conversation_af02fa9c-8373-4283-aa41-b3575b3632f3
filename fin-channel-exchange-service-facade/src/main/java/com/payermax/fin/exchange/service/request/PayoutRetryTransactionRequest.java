package com.payermax.fin.exchange.service.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2021/10/22 14:52
 */
@Data
public class PayoutRetryTransactionRequest implements BaseRequest {

    private static final long serialVersionUID = -5140831013737774741L;

    /**
     * 渠道提交单
     *
     * @mock P23456789
     */
    @NotBlank(message = "[channelPayCommitNo] is mandatory")
    private String channelPayCommitNo;

    /**
     * 是否人工操作
     */
    private String isManually;

}
