package com.payermax.fin.exchange.service.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 即期报价查询请求
 *
 * <AUTHOR>
 * @date 2022/4/8 10:22
 */
@Data
public class InquirySpotRateRequest implements BaseRequest {


    private static final long serialVersionUID = -8290680744364360505L;
    /**
     * 渠道编码
     */
    @NotBlank(message = "[channelCode] is mandatory")
    private String channelCode;

    /**
     * 请求单号
     */
    @NotBlank(message = "[requestNo] is mandatory")
    private String requestNo;

    /**
     * 币种对集合
     */
    @NotEmpty(message = "[currencyPairs] is mandatory")
    private List<CurrencyPair> currencyPairs;

    /**
     * 汇率价格类型集合
     */
    @NotEmpty(message = "[priceTypes] is mandatory")
    private List<String> priceTypes;

    /**
     * 查询汇率时间类型(NOW/HISTORY)
     */
    @NotBlank(message = "[queryTimeType] is mandatory")
    private String queryTimeType;

    /**
     * 报价机构
     */
    private String institutionCode;

    /**
     * 查询汇率时间
     */
    private Long queryTime;


    @Data
    public static class CurrencyPair implements Serializable {

        private static final long serialVersionUID = 7176985684156046634L;
        /**
         * 基准货币
         */
        private String baseCurrency;
        /**
         * 标价货币
         */
        private String quoteCurrency;
        /**
         * 货币对标识
         */
        private String currencyPairFlag;
    }

}
