package com.payermax.fin.exchange.service.response;

import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/9/28 21:31
 */
@Getter
@Setter
public class PayoutResponse extends TransactionBaseResponse {

    private static final long serialVersionUID = 8680523171351575539L;

    /**
     * 扩展返回数据,用于返回不同渠道的特定属性
     */
    private Map<String, Object> extendResult;

}
