package com.payermax.fin.exchange.service.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 申请资金账号request类
 *
 * <AUTHOR>
 * @date 2022/10/5 17:01
 */
@Data
public class ApplyFundsAccountRequest implements BaseRequest {

    private static final long serialVersionUID = 8573144555148628480L;

    /**
     * 请求单号
     */
    @NotBlank(message = "[requestNo] is mandatory")
    @Size(max = 64, message = "[requestNo] maximum 64 length")
    private String requestNo;

    /**
     * 商户号*
     */
    @NotBlank(message = "[merchantNo] is mandatory")
    private String merchantNo;

    /**
     * 签约主体*
     */
    private String entity;

    /**
     * 国家*
     */
    @NotBlank(message = "[country] is mandatory")
    @Size(min = 2, max = 2, message = "[country] maximum 2 length")
    @Pattern(regexp = "^[A-Za-z]+$", message = "[country] value is invalid")
    private String country;

    /**
     * 币种*
     */
    @NotBlank(message = "[currency] is mandatory")
    @Size(min = 3, max = 3, message = "[currency] maximum 3 length")
    @Pattern(regexp = "^[A-Za-z]+$", message = "[currency] value is invalid")
    private String currency;

    /**
     * 用途*
     */
    @NotBlank(message = "[useType] is mandatory")
    private String useType;

    /**
     * 自定义账号名称*
     */
    private String customAccountName;

}
