package com.payermax.fin.exchange.service.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 补偿查询请求
 *
 * <AUTHOR>
 * @date 2022/1/22 1:40
 */
@Data
public class CompensateInquiryRequest implements BaseRequest {

    private static final long serialVersionUID = 8393354556655070870L;

    /**
     * 渠道请求单
     */
    private String channelPayRequestNo;

    /**
     * 渠道提交单
     */
    @NotBlank(message = "[channelPayCommitNo] is mandatory")
    private String channelPayCommitNo;

    /**
     * 支付类型
     */
    @NotBlank(message = "[paymentType] is mandatory")
    private String paymentType;

    /**
     * |出款补偿查询出款使用：是否触发重试逻辑. 默认不触发
     */
    private Boolean isRetryTransaction = Boolean.FALSE;

}
