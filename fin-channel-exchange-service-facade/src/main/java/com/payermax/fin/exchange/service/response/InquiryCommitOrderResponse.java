package com.payermax.fin.exchange.service.response;

import com.payermax.fin.exchange.service.enums.OrderStatus4OutEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 查询渠道提交单响应
 *
 * <AUTHOR>
 * @date 2022/2/15 22:26
 */
@Data
public class InquiryCommitOrderResponse implements Serializable {

    private static final long serialVersionUID = -2066337056755521227L;

    /**
     * 支付请求单号
     */
    private String payRequestNo;

    /**
     * 支付引擎-支付单号
     */
    private String bizOrderNo;

    /**
     * 渠道请求单号
     */
    private String channelPayRequestNo;

    /**
     * 渠道提交单号
     */
    private String channelPayCommitNo;

    /**
     * 渠道商户编码
     */
    private String channelMerchantCode;

    /**
     * 国家二字码
     */
    private String country;

    /**
     * 币种
     */
    private String currency;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 渠道签约主体,可用值:3-PMMax,2-Funtech,1-SHAREit
     */
    private String channelEntity;

    /**
     * 三方机构单号
     */
    private String thirdOrgOrderNo;

    /**
     * 四方机构单号
     */
    private String fourthOrgOrderNo;

    /**
     * 状态,可用值:PENDING-支付中,SUCCESS-成功,FAILED-失败
     */
    private OrderStatus4OutEnum status;

    /**
     * 请求报文
     */
    private String requestBody;

    private String addField1;

    private String addField2;

    private String addField3;

    private String addField4;

    private String addField5;

    private String addField6;

    private String addField7;
}
