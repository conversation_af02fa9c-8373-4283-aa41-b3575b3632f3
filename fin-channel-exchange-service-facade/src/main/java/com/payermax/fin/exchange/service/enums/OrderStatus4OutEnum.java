package com.payermax.fin.exchange.service.enums;

/**
 * <AUTHOR>
 * @date Create on 2021/8/23 15:00
 */
public enum OrderStatus4OutEnum {

    /**
     * 对外展示交易状态的取值
     */
    SUCCESS,
    FAILED,
    PENDING,
    BOUNCEBACK;

    /**
     * 状态映射
     *
     * @param value
     * @return
     */
    public static OrderStatus4OutEnum mappingStatus4Out(String value) {
        if (SUCCESS.name().equals(value)) {
            return SUCCESS;
        }
        if (FAILED.name().equals(value)) {
            return FAILED;
        }
        if (BOUNCEBACK.name().equals(value)) {
            return BOUNCEBACK;
        }
        return PENDING;
    }
}
