package com.payermax.fin.exchange.service.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @ClassName PayoutAbnormalRetryTransactionRequest
 * @Description
 * <AUTHOR>
 * @Date 2022/9/14 16:21
 */
@Data
public class PayoutAbnormalRetryTransactionRequest implements BaseRequest{
    /**
     * 渠道请求单列表
     *
     */
    @NotEmpty(message = "[channelPayRequestNoList] is mandatory")
    private List<String> channelPayRequestNoList;

}
