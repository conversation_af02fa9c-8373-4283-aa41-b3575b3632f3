package com.payermax.fin.exchange.service.response;


import com.payermax.common.lang.util.money.Money;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 外汇交易响应
 *
 * <AUTHOR>
 * @date 2022/7/31 11:22
 */
@Data
public class ForexTradeResponse implements Serializable {

    private static final long serialVersionUID = 1846126426689561514L;
    /**
     * 请求单号
     */
    private String transactionOrderNo;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 渠道提交单号
     */
    private String channelPayCommitNo;

    /**
     * 状态(SUCCESS/FAIL/INITIATE/PENDING)
     */
    private String status;

    /**
     * 响应映射code
     */
    private String mappingCode;

    /**
     * 响应映射msg
     */
    private String mappingMsg;

    /**
     * 客户号
     */
    private String customerNo;

    /**
     * CITI交易单号
     */
    private String channelTransactionId;

    /**
     * 原始状态
     */
    private String ordStatus;

    /**
     * 货币对
     */
    private String currencyPairFlag;

    /**
     * 期间
     */
    private String duration;

    /**
     * 起息日
     */
    private Long valueDate;

    /**
     * 交易货币
     */
    private String transactionCurrency;

    /**
     * 交易方向
     */
    private String transactionSide;

    /**
     * 交易金额
     */
    private Money transactionAmount;

    /**
     * 机构汇率版本号
     */
    private String rateSheetRef;

    /**
     * 目标货币
     */
    private String counterCcy;

    /**
     * 目标金额
     */
    private BigDecimal contAmtValue;

    /**
     * 交易汇率
     */
    private BigDecimal lastSpotRate;

    /**
     * 加点
     */
    private BigDecimal lastForwardPoints;

    /**
     * 加点后汇率
     */
    private BigDecimal outrightRate;

    /**
     * 错误码
     */
    private String rejectCode;

    /**
     * 错误原因
     */
    private String rejectReason;

    /**
     * 交易类型
     */
    private String transactionType;

    /**
     * 交易时间（yyyy-MM-dd HH:mm:ss）
     */
    private Long transactionTime;

    /**
     * 交易日
     */
    private Long tradeDate;
}
