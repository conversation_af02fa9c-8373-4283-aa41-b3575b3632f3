package com.payermax.fin.exchange.service.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 远期报价查询请求
 *
 * <AUTHOR>
 * @date 2022/5/19 10:22
 */
@Data
public class InquiryForwardRateRequest implements BaseRequest {

    private static final long serialVersionUID = 897197649629436995L;

    /**
     * 渠道编码
     */
    @NotBlank(message = "[channelCode] is mandatory")
    private String channelCode;

    /**
     * 请求单号
     */
    @NotBlank(message = "[requestNo] is mandatory")
    private String requestNo;

    /**
     * 报价机构
     */
    private String institutionCode;

    /**
     * 汇率产品（NDF/DF）
     */
    private String rateProduct;

    /**
     * 客户号
     */
    private String customerNo;

    /**
     * 币种对集合
     */
    private List<CurrencyPair> currencyPairs;

    /**
     * 汇率价格类型集合
     */
    private List<String> priceTypes;

    /**
     * 查询汇率时间类型(NOW/HISTORY)
     */
    @NotBlank(message = "[queryTimeType] is mandatory")
    private String queryTimeType;

    /**
     * 查询汇率时间
     */
    private Long queryTime;


    @Data
    public static class CurrencyPair implements Serializable {

        private static final long serialVersionUID = -5470968683629184024L;
        /**
         * 基准货币
         */
        private String baseCurrency;
        /**
         * 标价货币
         */
        private String quoteCurrency;
        /**
         * 期间
         */
        private String duration;

        /**
         * 货币对
         */
        private String currencyPairFlag;

        /**
         * 汇率周期（1M/24H）
         */
        private String ratePeriod;

        /**
         * 外汇交易币种
         */
        private String transactionCurrency;

        /**
         * 外汇交易
         */
        private BigDecimal transactionAmount;

    }

}
