package com.payermax.fin.exchange.service.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/09/20 17:59
 */
@Data
public class PendingTranDataCorrectionRequest implements BaseRequest {

    private static final long serialVersionUID = -46586765434644775L;

    /**
     * 渠道提交单
     *
     * @mock P123456789
     */
    @Size(min = 1, message = "[channelCommitOrderNos] is mandatory")
    private Set<String> channelCommitOrderNos;

    /**
     * 渠道编码
     * */
    @NotBlank(message = "[channelCode] is mandatory")
    private String channelCode;

    /**
     * 目标渠道编码
     */
    private String targetChannelCode;

    /**
     * 支付类型
     * */
    @NotBlank(message = "[paymentType] is mandatory")
    private String paymentType;

    /**
     * 操作人
     * */
    @NotBlank(message = "[operator] is mandatory")
    private String operator;

}
