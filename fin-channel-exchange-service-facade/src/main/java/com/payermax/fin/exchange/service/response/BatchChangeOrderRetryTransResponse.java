package com.payermax.fin.exchange.service.response;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/09/20 11:57
 */
@Data
public class BatchChangeOrderRetryTransResponse implements Serializable {

    private static final long serialVersionUID = -3932943254313878456L;

    private String channelPayRequestNo;

    private String channelPayCommitNo;

    private Boolean retry;

    private String message;

    private BigDecimal amount;

    private String currency;

    private Date retryTime;
}
