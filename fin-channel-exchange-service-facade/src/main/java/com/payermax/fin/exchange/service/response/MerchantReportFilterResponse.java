package com.payermax.fin.exchange.service.response;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/11/19 16:12
 */
@Data
public class MerchantReportFilterResponse implements Serializable {

    private static final long serialVersionUID = -784481336692166245L;

    /**
     * 支付方式可用性详情
     */
    private List<PaymentMethod> paymentMethods;
    /**
     * 收银产品不可用原因
     */
    private List<DisablePaymentMethod> disablePaymentMethods;

    @Data
    public static class PaymentMethod implements Serializable {

        private static final long serialVersionUID = 931005119084219083L;
        /**
         * 收银产品编码
         */
        private String cashierProductNo;
        /**
         * 币种
         */
        private String currency;
        /**
         * 渠道集合
         */
        private Set<ChannelDetail> channelDetails;

    }

    @Data
    public static class DisablePaymentMethod implements Serializable {

        private static final long serialVersionUID = -6260765682674802991L;

        /**
         * 收银产品编码
         */
        private String cashierProductNo;
        /**
         * 币种
         */
        private String currency;
        /**
         * 不可用原因明细集合
         */
        private Set<DisableChannelDetail> channelDetails;
    }

    @Data
    @EqualsAndHashCode
    public static class ChannelDetail implements Serializable {

        private static final long serialVersionUID = -8040535679524112430L;
        /**
         * 支付方式Code
         */
        private String channelId;
        /**
         * 渠道商户Mid
         */
        private String entity;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class DisableChannelDetail extends ChannelDetail implements Serializable {

        private static final long serialVersionUID = -8040535679524112430L;

        /**
         * 错误码
         */
        @EqualsAndHashCode.Exclude
        private Set<String> errorCodes;
    }

}
