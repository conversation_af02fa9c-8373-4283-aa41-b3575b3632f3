package com.payermax.fin.exchange.service.response;

import lombok.Data;

import java.io.Serializable;

/**
 * 申请DDC响应
 *
 * <AUTHOR>
 * @date 2023/2/15 14:14
 */
@Data
public class ApplyDDCResponse implements Serializable {

    private static final long serialVersionUID = -2077037343665697796L;

    /**
     * 渠道标识*
     */
    private String channelCode;

    /**
     * DDC机构
     */
    private String ddcOrg;

    /**
     * ddc地址*
     */
    private String ddcUrl;

    /**
     * ddcSessionId*
     */
    private String ddcSessionId;

}
