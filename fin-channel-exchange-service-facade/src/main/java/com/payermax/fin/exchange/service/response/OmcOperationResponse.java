package com.payermax.fin.exchange.service.response;

import com.payermax.fin.exchange.service.enums.OrderStatus4OutEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/10/22 10:51
 */
@Data
public class OmcOperationResponse implements Serializable {

    private static final long serialVersionUID = -3177339757785950270L;

    /**
     * 渠道请求单号
     */
    private String channelPayRequestNo;

    /**
     * 渠道提交单号
     */
    private String channelPayCommitNo;

    /**
     * 请求状态,可用值:PENDING-支付中,SUCCESS-成功,FAILED-失败
     */
    private OrderStatus4OutEnum status;

    /**
     * 是否支持重试
     *
     * @ignore
     */
    private Boolean support2Retry = Boolean.FALSE;

    /**
     * 渠道响应码
     */
    private String channelResponseCode;

    /**
     * 渠道响应信息
     */
    private String channelResponseMsg;

}
