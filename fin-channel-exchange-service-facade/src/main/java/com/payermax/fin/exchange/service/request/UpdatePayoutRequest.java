package com.payermax.fin.exchange.service.request;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/10/18 19:15
 */

@Getter
@Setter
public class UpdatePayoutRequest implements BaseRequest {

    private static final long serialVersionUID = 7136608826355950553L;

    private String productCode;

    private String payoutChannelOrderNo;

    private String channelCode;

    private String trackNo;

    private String methodCode;

    private String methodSubCode;

    private String outUserId;

    private Long amount;

    private String currency;

    private String country;

    private Integer expirySeconds;

    private String remark;

    private String note;

    private Byte status;

    private String payThirdChannelNo;

    private String payThirdBankTxnId;

    private String eventCode;

    private String responseCode;

    private String mappedCode;

    private String responseMsg;

    private String responseJson;

    private String payeeType;

    private String payeeAccount;

    private String payeeBankName;

    private String payeeBankCode;

    private String payeeAccountName;

    private String addField1;

    private String addField2;

    private String addField3;

    private String addField4;

    private String addField5;

    private String addField6;

    private String creator;

    private Date createTime;

    private String modifier;

    private Date modifiedTime;

    private String isDeleted;

    private String extraJson;

}
