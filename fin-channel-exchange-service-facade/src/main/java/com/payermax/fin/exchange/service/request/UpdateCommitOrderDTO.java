package com.payermax.fin.exchange.service.request;

import lombok.Data;

import java.io.Serializable;

/**
 * 对账推送过来的数据（包含ARN RRN数据）
 */
@Data
public class UpdateCommitOrderDTO implements Serializable{
    private static final long serialVersionUID = -7129147042937568583L;
    private String channelPayCommitNo;
    private String paymentType;
    private BizNoInfoDTO bizNoInfo;

    @Data
    public class BizNoInfoDTO implements Serializable {
        private static final long serialVersionUID = 7435008213977724741L;
        private String arn;
        private String rrn;
    }
}
