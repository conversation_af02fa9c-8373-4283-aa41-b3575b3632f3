package com.payermax.fin.exchange.service.context;

import com.payermax.infra.ionia.log.digest.core.annotation.Sensitive;
import com.payermax.infra.ionia.log.digest.core.common.SensitiveStrategy;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 出款请求上下文
 *
 * <AUTHOR>
 * @date 2021/9/28 20:46
 */
@Getter
@Setter
public class PayoutRequestContext implements Serializable {

    private static final long serialVersionUID = -6308381472431922496L;

    /**
     * 业务源
     */
    private String bizSource;

    /**
     * 支付方式信息
     */
    @Valid
    @NotNull(message = "[paymentMethod] is mandatory")
    private PaymentMethod paymentMethod;

    /**
     * 新支付方式信息
     */
    @Valid
    private NewPaymentMethod newPaymentMethod;

    /**
     * 订单信息
     */
    @Valid
    @NotNull(message = "[orderInfo] is mandatory")
    private OrderInfo orderInfo;

    /**
     * 采集参数集合
     */
    @Sensitive(strategy = SensitiveStrategy.MAP)
    private Map<String, String> params;

    @Data
    public static class PaymentMethod implements Serializable {

        private static final long serialVersionUID = -8942104535432017763L;

        /**
         * 售卖收银产品编码
         */
        private String cashierProductNo;

        /**
         * 收银产品版本
         */
        private String cashierProductVersion;

        /**
         * 支付类型,可用值:10-出
         *
         * @mock 10
         */
        @NotBlank(message = "[paymentType] is mandatory")
        private String paymentType = "10";

        /**
         * 国家二字码
         *
         * @mock ID
         */
        @NotBlank(message = "[country] is mandatory")
        @Size(min = 2, max = 2, message = "[country] maximum 2 length")
        @Pattern(regexp = "^[A-Za-z]+$", message = "[country] value is invalid")
        private String country;

        /**
         * 客户类型,可用值:1-2C,2-2B
         *
         * @mock 1
         */
        private String customerType;

        /**
         * 支付方式扩展属性集合
         */
        private List<ExtendProperty> extendProperties;

        /**
         * 是否匹配签约主体 Y:是 N:否
         */
        private String isMatchEntity;

        @Data
        public static class ExtendProperty implements Serializable {

            private static final long serialVersionUID = 2199777879131812044L;

            /**
             * 扩展属性key
             */
            private String key;
            /**
             * 扩展属性value
             */
            private String value;
            /**
             * 逻辑操作符
             */
            private String logicKey;

        }
    }

    @Data
    public static class NewPaymentMethod implements Serializable {

        private static final long serialVersionUID = -1419328264633136583L;

        /**
         * 售卖收银产品编码
         */
        @NotBlank(message = "[cashierProductNo] is mandatory")
        private String cashierProductNo;

        /**
         * 支付方式类型
         */
        @NotBlank(message = "[paymentMethodType] is mandatory")
        private String paymentMethodType;

        /**
         * 目标机构
         */
        @NotBlank(message = "[targetOrg] is mandatory")
        private String targetOrg;
    }

    @Data
    public static class OrderInfo implements Serializable {

        private static final long serialVersionUID = 1061911462996465539L;

        /**
         * 交易步骤*
         */
        private String transStep;

        /**
         * 内部商户号,最大长度64
         *
         * @mock SP1234567
         */
        @Size(max = 64, message = "[merchantNo] maximum 64 length")
        private String merchantNo;

        /**
         * 子商户号（外部商户的子商户）
         */
        private String subMerchantNo;

        /**
         * 二级商户信息
         */
        private SubMerchantInfo subMerchant;

        /**
         * 客户ID
         */
        private String customId;

        /**
         * 商户类型*
         */
        private String merchantType;

        /**
         * 商户用户标识,最大长度64
         *
         * @mock SP1234567_837629
         */
        @Size(max = 64, message = "[outUserId] maximum 64 length")
        private String outUserId;

        /**
         * 用户会员ID
         */
        @Size(max = 64, message = "[userMemberId] maximum 64 length")
        private String userMemberId;

        /**
         * MCC信息
         */
        private String mcc;

        /**
         * 商户三级MCC
         */
        private String subMcc;

        /**
         * 交易备注,最大长度512
         */
        @Size(max = 512, message = "[remark] maximum 512 length")
        private String remark;

    }

    /**
     * 内部二级商户信息
     */
    @Data
    public static class SubMerchantInfo implements Serializable {

        /**
         * 二级商户号
         */
        private String subMerchantNo;
        /**
         * 二级商户MCC
         */
        private String mcc;
        /**
         * 二级商户子MCC
         */
        private String subMcc;

    }
}
