package com.payermax.fin.exchange.service.request;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024-12-11 17:29:43
 * @description 清理缓存请求
 */
@Data
public class ClearCacheRequest implements BaseRequest {


    private static final long serialVersionUID = -5510887882634874637L;

    /**
     * 移除的缓存
     * 参数格式：{
     * "regions":[{"region":"channel1","businessTypes":[{"businessType":"info","businessKeys":["DANA","KEY1"]},{"businessType":"config","businessKeys":["OMS","KEY2"]}]},
     * {"region":"channel2","businessTypes":[{"businessType":"info","businessKeys":["KEY3","KEY4"]},{"businessType":"config","businessKeys":["KEY5","KEY6"]}]}]
     * }
     */
    private List<CacheRegionRequest> regions;

    @Data
    public static class CacheRegionRequest {
        private String region;
        private List<CacheBusinessRequest> businessTypes;
    }

    @Data
    public static class CacheBusinessRequest {
        private String businessType;
        private List<String> businessKeys;
    }
}
