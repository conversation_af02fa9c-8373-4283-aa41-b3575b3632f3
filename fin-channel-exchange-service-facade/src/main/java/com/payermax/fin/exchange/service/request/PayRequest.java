package com.payermax.fin.exchange.service.request;

import com.payermax.fin.exchange.service.context.PayRequestContext;
import com.ushareit.fintech.common.model.dto.Money;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @date Create on 2021/7/27 11:27
 */
@Data
public class PayRequest extends TransactionBaseRequest {

    private static final long serialVersionUID = 7645149764614142449L;

    /**
     * 支付请求单号，最大长度64
     */
    @NotBlank(message = "[payRequestNo] is mandatory")
    @Size(max = 64, message = "[payRequestNo] maximum 64 length")
    private String payRequestNo;

    /**
     * 支付引擎-支付单号,最大长度64
     *
     * @mock P123456789
     */
    @NotBlank(message = "[payOrderNo] is mandatory")
    @Size(max = 64, message = "[payOrderNo] maximum 64 length")
    private String payOrderNo;

    /**
     * 交易单号
     **/
    private String tradeOrderNo;

    /**
     * 业务身份
     *
     * @mock p201
     */
    @NotBlank(message = "[bizIdentify] is mandatory")
    private String bizIdentify;

    /**
     * 服务主体（商户签约主体）
     */
    private String serviceEntity;

    /**
     * 服务模式
     */
    private String serviceMode;

    /**
     * 业务产品编码
     */
    @NotBlank(message = "[productCode] is mandatory")
    private String productCode;

    /**
     * 支付方式类型,可用值:NetBanking,VirtualAccount,BarCode,Wallet,CardPay,CarrierBilling,PrePaid,PointsPay,PayLater,AutoDebit,DirectDebit
     *
     * @mock CarrierBilling
     */
    @NotBlank(message = "[paymentMethodType] is mandatory")
    private String paymentMethodType;

    /**
     * 目标机构,最大长度64
     *
     * @mock ********
     */
    @NotBlank(message = "[targetOrg] is mandatory")
    @Size(max = 64, message = "[targetOrg] maximum 64 length")
    private String targetOrg;

    /**
     * 交易金额,单位:元
     */
    @NotNull(message = "[amount] is mandatory")
    private Money amount;

    /**
     * 请求上下文对象:若为空,则通过上下文服务获取
     */
    @Valid
    private PayRequestContext requestContext;

    /**
     * 请求上下文JSON对象
     *
     * @ignore
     */
    private String requestContextJson;

}
