package com.payermax.fin.exchange.service.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2023/5/13 14:52
 */
@Data
public class RetryTransactionRequest implements BaseRequest {

    private static final long serialVersionUID = -5140831013523774741L;

    /**
     * 渠道请求单
     *
     * @mock P23456789
     */
    @NotBlank(message = "[channelPayRequestNo] is mandatory")
    private String channelPayRequestNo;

    /**
     * 渠道提交单
     *
     * @mock P23456789
     */
    private String channelPayCommitNo;
    
    /**
     * 支付类型
     *
     * @mock 40
     */
    @NotBlank(message = "[paymentType] is mandatory")
    private String paymentType;
}
