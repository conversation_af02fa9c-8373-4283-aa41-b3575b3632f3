package com.payermax.fin.exchange.service.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 查询外汇交易请求
 *
 * <AUTHOR>
 * @date 2022/7/31 10:22
 */
@Data
public class InquiryForexTradeRequest implements BaseRequest {

    private static final long serialVersionUID = -5728446384543209088L;
    /**
     * 渠道编码
     */
    @NotBlank(message = "[channelCode] is mandatory")
    private String channelCode;

    /**
     * 请求单号
     */
    @NotBlank(message = "[transactionOrderNo] is mandatory")
    private String transactionOrderNo;

    /**
     * 客户号
     */
    private String customerNo;

    /**
     * 交易日期
     */
    private Long tradeDate;

    /**
     * 机构汇率版本号
     */
    private String rateSheetRef;


}
