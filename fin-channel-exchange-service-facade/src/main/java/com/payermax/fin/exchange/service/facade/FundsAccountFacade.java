package com.payermax.fin.exchange.service.facade;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.fin.exchange.service.request.ApplyFundsAccountRequest;
import com.payermax.fin.exchange.service.response.ApplyFundsAccountResponse;

/**
 * 资金账号Facade
 *
 * <AUTHOR>
 * @date 2022/10/5 17:01
 */
public interface FundsAccountFacade {


    /**Ø
     * 申请资金账号*
     *
     * @param request
     * @return
     */
    Result<ApplyFundsAccountResponse> applyFundsAccount(ApplyFundsAccountRequest request);

}
