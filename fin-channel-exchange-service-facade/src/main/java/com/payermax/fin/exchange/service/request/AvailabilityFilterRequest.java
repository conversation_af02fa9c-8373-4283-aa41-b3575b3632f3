package com.payermax.fin.exchange.service.request;

import com.ushareit.fintech.common.model.dto.Money;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/14 16:10
 */
@Data
public class AvailabilityFilterRequest implements BaseRequest {

    private static final long serialVersionUID = 220224574607396200L;

    /**
     * 筛选业务类型：FilterBizTypeEnum，空值相当于FilterBizTypeEnum.TRADE
     */
    private String filterBizType;

    /**
     * 支付方式集合
     */
    @Valid
    @NotEmpty(message = "[cashierProducts] is mandatory")
    private List<CashierProduct> cashierProducts;

    /**
     * 订单信息
     */
    @Valid
    @NotNull(message = "[orderInfo] is mandatory")
    private OrderInfo orderInfo;

    @Data
    public static class OrderInfo implements Serializable {

        private static final long serialVersionUID = 7303820597697058874L;

        /**
         * 上游业务订单号
         */
        private String bizOrderNo;

        /**
         * 交易步骤*
         */
        private String transStep;

        /**
         * 内部商户号
         *
         * @mock SP1234567
         */
        @NotBlank(message = "[merchantNo] is mandatory")
        private String merchantNo;

        /**
         * 子商户号（外部商户的子商户）
         */
        private String subMerchantNo;

        /**
         * 二级商户信息
         */
        private SubMerchantInfo subMerchant;

        /**
         * 客户ID
         */
        private String customId;

        /**
         * 商户类型*
         */
        private String merchantType;

        /**
         * 服务主体（商户签约主体）
         */
        private String serviceEntity;

        /**
         * 服务模式
         */
        private String serviceMode;

        /**
         * 集成模式
         */
        private String integrate;

        /**
         * 商户MCC
         */
        private String mcc;

        /**
         * 商户三级MCC
         */
        private String subMcc;

        /**
         * 商户用户标识,最大长度64
         *
         * @mock SP1234567_837629
         */
        @Size(max = 64, message = "[outUserId] maximum 64 length")
        private String outUserId;

        /**
         * 用户会员ID
         */
        @Size(max = 64, message = "[userMemberId] maximum 64 length")
        private String userMemberId;

    }

    @Data
    public static class CashierProduct implements Serializable {

        private static final long serialVersionUID = -8767644301987352862L;

        /**
         * 支付方式记录的唯一标识，金融交换不使用，仅原路返回给调用方，标识支付方式和渠道支付方式实例的关系
         */
        private String paymentMethodId;

        /**
         * 售卖收银产品编码
         */
        @NotBlank(message = "[cashierProductNo] is mandatory")
        private String cashierProductNo;

        /**
         * 售卖收银产品版本
         */
        @NotBlank(message = "[cashierProductVersion] is mandatory")
        private String cashierProductVersion;

        /**
         * 产品编码
         */
        private String productCode;

        /**
         * 支付方式类型,可用值:NetBanking,VirtualAccount,BarCode,Wallet,CardPay,CarrierBilling,PrePaid,PointsPay,PayLater,AutoDebit,DirectDebit
         *
         * @mock CarrierBilling
         */
        @NotBlank(message = "[paymentMethodType] is mandatory")
        private String paymentMethodType;

        /**
         * 支付类型,可用值:10-出款,20-入款,40-退款
         *
         * @mock 20
         */
        @NotBlank(message = "[paymentType] is mandatory")
        private String paymentType;

        /**
         * 国家二字码
         *
         * @mock ID
         */
        @NotBlank(message = "[country] is mandatory")
        @Size(min = 2, max = 2, message = "[country] maximum 2 length")
        @Pattern(regexp = "^[A-Za-z]+$", message = "[country] value is invalid")
        private String country;

        /**
         * 交易金额,单位:元 （组件支付业务场景下金额值可能为0）
         */
        @NotNull(message = "[amount] is mandatory")
        private Money amount;

        /**
         * 是否忽略交易金额 为true时，忽略对amount.value的校验，组件支付业务场景下使用*
         */
        private Boolean isIgnoreAmountValue;

        /**
         * 目标机构
         *
         * @mock 10000111
         */
        private String targetOrg;

        /**
         * 卡组
         */
        private String cardOrg;

        /**
         * 业务身份
         *
         * @mock p201
         */
        @NotBlank(message = "[bizIdentify] is mandatory")
        private String bizIdentify;

        /**
         * 客户类型,可用值:1-2C,2-2B
         *
         * @mock 1
         */
        private String customerType;

        /**
         * 是否匹配签约主体 Y:是 N:否
         */
        private String isMatchEntity;

        /**
         * 支付方式扩展属性集合
         */
        private List<ExtendProperty> extendProperties;

        @Data
        public static class ExtendProperty implements Serializable {

            private static final long serialVersionUID = -5953494831864959756L;

            /**
             * 扩展属性key
             */
            private String key;
            /**
             * 扩展属性value
             */
            private String value;
            /**
             * 逻辑操作符
             */
            private String logicKey;

        }
    }

}
