package com.payermax.fin.exchange.service.request;

import com.ushareit.fintech.common.model.dto.Money;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 退款请求
 *
 * <AUTHOR>
 * @date 2021/8/23 13:50
 */
@Data
public class RefundRequest extends TransactionBaseRequest {

    private static final long serialVersionUID = -5880741004640642034L;

    /**
     * 退款支付请求单号，最大长度64
     */
    private String payRequestNo;

    /**
     * 支付引擎-支付单号,最大长度64
     *
     * @mock P123456789
     */
    @NotBlank(message = "[payOrderNo] is mandatory")
    @Size(max = 64, message = "[payOrderNo] maximum 64 length")
    private String payOrderNo;

    /**
     * 支付引擎-原支付单号,最大长度64
     *
     * @mock P123456789
     */
    @NotBlank(message = "[oriPayOrderNo] is mandatory")
    @Size(max = 64, message = "[oriPayOrderNo] maximum 64 length")
    private String oriPayOrderNo;

    /**
     * 退款金额,单位:元
     */
    @NotNull(message = "[amount] is mandatory")
    private Money amount;

    /**
     * 拒付预警类型：RDR、Ethoca等
     */
    private String cbWarningType;

    /**
     * 退款备注,最大长度512
     */
    @Size(max = 512, message = "[remark] maximum 512 length")
    private String remark;

}
