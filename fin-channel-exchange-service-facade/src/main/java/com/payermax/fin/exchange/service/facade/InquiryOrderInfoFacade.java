package com.payermax.fin.exchange.service.facade;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.fin.exchange.service.request.InquiryCommitOrderRequest;
import com.payermax.fin.exchange.service.request.InquiryNewTargetMerchantRequest;
import com.payermax.fin.exchange.service.request.InquiryOrderInfoRequest;
import com.payermax.fin.exchange.service.response.InquiryCommitOrderResponse;
import com.payermax.fin.exchange.service.response.InquiryNewTargetMerchantResponse;
import com.payermax.fin.exchange.service.response.InquiryOrderInfoResponse;

import java.util.List;

/**
 * 渠道单关联信息查询服务
 */
public interface InquiryOrderInfoFacade {

    Result<InquiryOrderInfoResponse> inquiryOrderInfo(InquiryOrderInfoRequest inquiryOrderInfoRequest);

    Result<List<InquiryCommitOrderResponse>> inquiryCommitOrder(InquiryCommitOrderRequest inquiryCommitOrderRequest);

    Result<InquiryNewTargetMerchantResponse> inquiryTargetMerchant(InquiryNewTargetMerchantRequest inquiryNewTargetMerchantRequest);

}
