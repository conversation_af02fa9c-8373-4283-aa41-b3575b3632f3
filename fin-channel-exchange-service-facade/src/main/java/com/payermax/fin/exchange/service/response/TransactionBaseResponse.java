package com.payermax.fin.exchange.service.response;

import com.payermax.fin.exchange.service.enums.OrderStatus4OutEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * 交易基础响应
 *
 * <AUTHOR>
 * @date 2021/12/3 16:01
 */
@Data
public abstract class TransactionBaseResponse implements Serializable {

    /**
     * 资产交换-支付单号
     */
    private String payOrderNo;

    /**
     * 渠道请求单号
     */
    private String channelPayRequestNo;

    /**
     * 渠道提交单号
     */
    private String channelPayCommitNo;

    /**
     * 国家二字码
     */
    private String country;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 标准渠道编码*
     */
    private String standardChannelCode;

    /**
     * 渠道签约主体
     */
    private String channelEntity;

    /**
     * 三方机构单号
     */
    private String thirdOrgOrderNo;

    /**
     * 四方机构单号
     */
    private String fourthOrgOrderNo;

    /**
     * 支付状态,可用值:PENDING-支付中,SUCCESS-成功,FAIL-失败
     */
    private OrderStatus4OutEnum status;

    /**
     * 响应映射code
     */
    private String mappingCode;

    /**
     * 响应映射msg
     */
    private String mappingMsg;

    /**
     * 支付完成时间
     */
    private Long completeTime;

    /**
     * 外部渠道完成时间
     */
    private Long channelCompleteTime;

    /**
     * 老系统一级支付方式
     */
    private String oldMethodCode;

    /**
     * 老系统二级支付方式
     */
    private String oldMethodSubCode;

    /**
     * 结算币种
     */
    private String settleCurrency;

    /**
     * 差错标识
     */
    private String correctionLogo;

    /**
     * 支付方式编码
     */
    private String channelMethodCode;

    /**
     * 渠道商户编码
     */
    private String channelMerchantCode;

    /**
     * 渠道业务类型
     * PREFUND：预存资金
     */
    private String channelBusinessType;

    /**
     * 卡组
     */
    private String cardOrg;

    /**
     * 我方头寸账号ID*
     */
    private String fundsAccountId;

    /**
     * 渠道响应码
     */
    private String channelRespCode;

    /**
     * 渠道响应消息
     */
    private String channelRespMsg;

}
