package com.payermax.fin.exchange.service.facade;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.fin.exchange.service.request.AccelerateCompensationRequest;
import com.payermax.fin.exchange.service.request.CompensateStatusRequest;

/**
 * <AUTHOR>
 * @date 2023/4/28 16:36
 */
public interface CompensateFacade {

    /**
     * 加速渠道补偿
     *
     * @param request
     * @return
     */
    Result<Boolean> accelerateCompensation(AccelerateCompensationRequest request);

    /**
     * 补偿订单状态
     *
     * @param request
     * @return
     */
    Result<String> compensateOrderStatus(CompensateStatusRequest request);
}
