package com.payermax.fin.exchange.service.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * 通用网关转发查询请求
 *
 * <AUTHOR>
 * @date 2022/5/25 10:22
 */
@Data
public class CommonGateRequest implements BaseRequest {

    private static final long serialVersionUID = -8248030265680708870L;

    /**
     * 渠道编码
     */
    @NotBlank(message = "[channelCode] is mandatory")
    private String channelCode;

    /**
     * 请求单号
     */
    @NotBlank(message = "[requestNo] is mandatory")
    private String requestNo;

    /**
     * 请求类型
     */
    @NotBlank(message = "[requestType] is mandatory")
    private String requestType;

    /**
     * 请求大类
     */
    private String mainRequestType;

    /**
     * 支付方式
     */
    @NotBlank(message = "[paymentType] is mandatory")
    private String paymentType;

    /**
     * 扩展参数
     */
    private Map<String, Object> data;

}
