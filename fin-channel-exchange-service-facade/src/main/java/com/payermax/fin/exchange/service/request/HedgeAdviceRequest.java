package com.payermax.fin.exchange.service.request;

import com.payermax.common.lang.util.money.Money;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * HA交易请求
 *
 * <AUTHOR>
 * @date 2022/5/30 10:22
 */
@Data
public class HedgeAdviceRequest implements BaseRequest {

    private static final long serialVersionUID = 5512387067165937903L;
    /**
     * 渠道编码
     */
    @NotBlank(message = "[channelCode] is mandatory")
    private String channelCode;

    /**
     * 请求单号
     */
    @NotBlank(message = "[transactionOrderNo] is mandatory")
    private String transactionOrderNo;

    /**
     * 客户号
     */
    @NotBlank(message = "[customerNo] is mandatory")
    private String customerNo;

    /**
     * 外汇交易产品
     */
    private String transactionCcyType;

    /**
     * 交易类型
     */
    @NotBlank(message = "[transactionType] is mandatory")
    private String transactionType;

    /**
     * 基准货币
     */
    private String baseCurrency;

    /**
     * 交易货币
     */
    @NotBlank(message = "[transactionCurrency] is mandatory")
    private String transactionCurrency;

    /**
     * 锁价后基准货币金额
     */
    private Money baseAmount;

    /**
     * 交易金额
     */
    @NotNull(message = "[transactionAmount] is mandatory")
    private Money transactionAmount;

    /**
     * 交易时间
     */
    @NotNull(message = "[tradeTimestamp] is mandatory")
    private Long tradeTimestamp;

    /**
     * 支付渠道换汇时间
     */
    @NotNull(message = "[clientAdviceTimestamp] is mandatory")
    private Long clientAdviceTimestamp;

    /**
     * 期望汇率
     */
    private BigDecimal expectRate;

    /**
     * 机构汇率版本号
     */
    private String rateSheetRef;

    /**
     * 支付渠道
     */
    @NotBlank(message = "[paymentProvider] is mandatory")
    private String paymentProvider;

    /**
     * 外汇支付方式类型
     */
    @NotBlank(message = "[ratePaymentType] is mandatory")
    private String ratePaymentType;

}
