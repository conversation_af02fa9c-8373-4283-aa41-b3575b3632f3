package com.payermax.fin.exchange.service.response;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/8/4 11:57
 */
@Data
public class PayoutBatchRetryTransactionResponse implements Serializable {

    private static final long serialVersionUID = -3932943649813878456L;

    private String channelPayCommitNo;

    private Boolean retry;

    private String message;

    private BigDecimal amount;

    private String currency;

    private String channelCode;
}
