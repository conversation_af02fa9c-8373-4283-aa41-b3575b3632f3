package com.payermax.fin.exchange.service.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 预筛选资金账户响应
 *
 * <AUTHOR>
 * @date 2023/1/30 14:45
 */
@Data
public class PreFilterAccountResponse implements Serializable {

    private static final long serialVersionUID = -5682159853154384549L;

    /**
     * 支付方式集合
     */
    private List<PaymentMethod> paymentMethods;

    @Data
    public static class PaymentMethod implements Serializable {

        private static final long serialVersionUID = 6243071863052586098L;

        /**
         * 支付方式编号,来源于入参中的数据"
         */
        private String paymentMethodNo;

        /**
         * 支付方式类型
         */
        private String paymentMethodType;

        /**
         * 目标机构
         */
        private String targetOrg;

        /**
         * 渠道签约主体,可用值:3-PMMax,2-Funtech,1-SHAREit
         */
        private String channelEntity;

        /**
         * 国家
         */
        private String country;

        /**
         * 我方资金账户ID
         */
        private String fundsAccountId;

        /**
         * 支付流程
         */
        private String paymentFlow;

        /**
         * 支付方式扩展属性集合
         */
        private List<ExtendProperty> extendProperties;

        @Data
        public static class ExtendProperty implements Serializable {

            private static final long serialVersionUID = -3169516461547414808L;

            /**
             * 扩展属性key
             */
            private String key;
            /**
             * 扩展属性value
             */
            private String value;
            /**
             * 逻辑操作符
             */
            private String logicKey;

        }

    }

}
