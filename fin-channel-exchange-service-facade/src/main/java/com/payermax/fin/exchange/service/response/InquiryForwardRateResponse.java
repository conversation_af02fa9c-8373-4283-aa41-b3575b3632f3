package com.payermax.fin.exchange.service.response;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 远期查询响应
 *
 * <AUTHOR>
 * @date 2022/5/19 11:22
 */
@Data
public class InquiryForwardRateResponse implements Serializable {

    private static final long serialVersionUID = 2522034078926089064L;
    /**
     * 请求单号
     */
    private String requestNo;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 渠道提交单号
     */
    private String channelPayCommitNo;

    /**
     * 状态(SUCCESS/FAIL)
     */
    private String status;

    /**
     * 响应映射code
     */
    private String mappingCode;

    /**
     * 响应映射msg
     */
    private String mappingMsg;

    /**
     * 币种对集合
     */
    private List<CurrencyPair> currencyPairs;

    @Data
    public static class CurrencyPair implements Serializable {

        private static final long serialVersionUID = 2806190632719440722L;
        /**
         * 基准货币
         */
        private String baseCurrency;
        /**
         * 标价货币
         */
        private String quoteCurrency;
        /**
         * 买入价
         */
        private BigDecimal bidRate;
        /**
         * 卖入价
         */
        private BigDecimal offerRate;
        /**
         * 中间价
         */
        private BigDecimal midRate;
        /**
         * 期间
         */
        private String duration;
        /**
         * 汇率生成时间
         */
        private Long generateTime;
        /**
         * 汇率生效时间
         */
        private Long effectiveTime;
        /**
         * 汇率失效时间
         */
        private Long expirationTime;
        /**
         * 汇率版本号
         */
        private String rateVersion;

        /**
         * 是否支持NDF的确认（1:支持；0:不支持）
         */
        private String guranteed;

        /**
         * 货币对
         */
        private String currencyPairFlag;

        /**
         * 汇率周期（1M/24H）
         */
        private String ratePeriod;

        /**
         * 扩展参数
         */
        private Map<String, Object> extraInfo;
    }

}
