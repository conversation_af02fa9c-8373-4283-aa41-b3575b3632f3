package com.payermax.fin.exchange.service.request;

import com.ushareit.fintech.common.model.dto.Money;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 统一控制接口请求类
 *
 * <AUTHOR>
 * @date 2021/7/30 15:10
 */
@Data
public class UnifyControlRequest extends TransactionBaseRequest implements BaseRequest {

    private static final long serialVersionUID = -6241372165683751280L;

    /**
     * 请求类型,可用值:otpVerify-OTP验证,otpApplyRetry-OTP重发,queryPayElement-查询支付信息,queryExtPayInfo
     *
     * @mock otpVerify
     */
    private String requestType;

    /**
     * 渠道请求单号,最大长度64
     *
     * @mock P123456789
     */
    @NotBlank(message = "[channelPayRequestNo] is mandatory")
    @Size(max = 64, message = "[channelPayRequestNo] maximum 64 length")
    private String channelPayRequestNo;

    /**
     * 关联渠道请求单号,最大长度64
     *
     * @mock P123456789
     */
    @Size(max = 64, message = "[quoteChannelPayRequestNo] maximum 64 length")
    private String quoteChannelPayRequestNo;

    /**
     * 关联渠道提交单号,最大长度64
     *
     * @mock P123456789
     */
    @Size(max = 64, message = "[quoteChannelPayCommitNo] maximum 64 length")
    private String quoteChannelPayCommitNo;

    /**
     * 支付方式信息(若渠道第一步为控制类,则此项必需)
     */
    @Valid
    private PaymentMethod paymentMethod;

    /**
     * 订单信息(若渠道第一步为控制类,则此项必需)
     */
    @Valid
    private OrderInfo orderInfo;

    /**
     * 采集参数集合
     */
    private Map<String, String> params;

    @Data
    public static class PaymentMethod implements Serializable {

        private static final long serialVersionUID = 6592227393488532259L;

        /**
         * 售卖收银产品编码
         */
        private String cashierProductNo;

        /**
         * 收银产品版本
         */
        private String cashierProductVersion;

        /**
         * 银行列表
         */
        private String bankCode;

        /**
         * 支付方式类型,可用值:NetBanking,VirtualAccount,BarCode,Wallet,CardPay,CarrierBilling,PrePaid,PointsPay,PayLater,AutoDebit,DirectDebit
         *
         * @mock CarrierBilling
         */
        @NotBlank(message = "[paymentMethodType] is mandatory")
        private String paymentMethodType;

        /**
         * 支付类型,可用值:20-入款
         *
         * @mock 20
         */
        @NotBlank(message = "[paymentType] is mandatory")
        private String paymentType;

        /**
         * 国家二字码
         *
         * @mock ID
         */
        @NotBlank(message = "[country] is mandatory")
        @Size(min = 2, max = 2, message = "[country] maximum 2 length")
        @Pattern(regexp = "^[A-Za-z]+$", message = "[country] value is invalid")
        private String country;

        /**
         * 目标机构
         *
         * @mock ********
         */
        @NotBlank(message = "[targetOrg] is mandatory")
        private String targetOrg;

        /**
         * 业务身份
         *
         * @mock p201
         */
        @NotBlank(message = "[bizIdentify] is mandatory")
        private String bizIdentify;

        /**
         * 客户类型,可用值:1-2C,2-2B
         *
         * @mock 1
         */
        private String customerType;

        /**
         * 支付流程,可用值:DIRECT-联机,REDIRECT-跳转,OTP-OTP,REDIRECT_OTP-跳转+OTP,DIRECT_COLLECT-联机+后采集
         *
         * @mock REDIRECT_OTP
         */
        private String paymentFlow;

        /**
         * 支付方式扩展属性集合
         */
        private List<ExtendProperty> extendProperties;

        @Data
        public static class ExtendProperty implements Serializable {

            private static final long serialVersionUID = 4466675988777988469L;

            /**
             * 扩展属性key
             */
            private String key;
            /**
             * 扩展属性value
             */
            private String value;
            /**
             * 逻辑操作符
             */
            private String logicKey;

        }
    }

    @Data
    public static class OrderInfo implements Serializable {

        private static final long serialVersionUID = 8868666812282239020L;

        /**
         * 内部商户号,最大长度64
         *
         * @mock SP1234567
         */
        @NotBlank(message = "[merchantNo] is mandatory")
        @Size(max = 64, message = "[merchantNo] maximum 64 length")
        private String merchantNo;

        /**
         * 二级商户信息
         */
        private SubMerchantInfo subMerchant;

        /**
         * 商户类型
         */
        private String merchantType;

        /**
         * 服务主体（商户签约主体）
         */
        private String serviceEntity;

        /**
         * 服务模式
         */
        private String serviceMode;

        /**
         * 集成模式
         */
        private String integrate;

        /**
         * 商户用户标识,最大长度64
         *
         * @mock SP1234567_837629
         */
        @NotBlank(message = "[outUserId] is mandatory")
        @Size(max = 64, message = "[outUserId] maximum 64 length")
        private String outUserId;

        /**
         * 交易金额,单位:元
         */
        @NotNull(message = "[amount] is mandatory")
        private Money amount;

        /**
         * 消费信息,最大长度512
         */
        @Size(max = 512, message = "[purchaseInfo] maximum 512 length")
        private String purchaseInfo;

        /**
         * 交易备注,最大长度512
         */
        @Size(max = 512, message = "[remark] maximum 512 length")
        private String remark;

        /**
         * 用户使用语言
         */
        private String userLanguage;

        /**
         * 环境信息
         */
        private EnvInfo envInfo;

        @Data
        public static class EnvInfo implements Serializable {

            private static final long serialVersionUID = -5963635166112270588L;

            /**
             * 客户端IP
             *
             * @mock 127.0.0.1
             */
            private String clientIp;

            /**
             * 设备终端类型,可用值:WEB-PC浏览器,WAP-移动端WAP,APP-移动端APP
             *
             * @mock WEB
             */
            private String terminalType;

            /**
             * 设备操作系统类型,可用值:IOS-苹果IOS,ANDROID-安卓
             *
             * @mock IOS
             */
            private String osType;

        }

    }

}
