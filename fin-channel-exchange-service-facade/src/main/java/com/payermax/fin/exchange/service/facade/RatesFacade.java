package com.payermax.fin.exchange.service.facade;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.fin.exchange.service.request.*;
import com.payermax.fin.exchange.service.response.*;

/**
 * 外汇服务
 *
 * <AUTHOR>
 * @date 2022/4/8 21:01
 * @dubbo
 */
public interface RatesFacade {

    /**
     * 即期报价查询
     *
     * @param request
     * @return
     */
    Result<InquirySpotRateResponse> inquirySpotRate(InquirySpotRateRequest request);

    /**
     * 远期报价查询
     *
     * @param request
     * @return
     */
    Result<InquiryForwardRateResponse> inquiryForwardRate(InquiryForwardRateRequest request);

    /**
     * HA交易接口
     *
     * @param request
     * @return
     */
    Result<HedgeAdviceResponse> hedgeAdvice(HedgeAdviceRequest request);

    /**
     * TA交易接口
     *
     * @param request
     * @return
     */
    Result<TradeAdviceResponse> tradeAdvice(TradeAdviceRequest request);

    /**
     * HA交易补偿接口
     *
     * @param request
     * @return
     */
    Result<HedgeAdviceResponse> hedgeCompensate(OrderCompensateRequest request);

    /**
     * TA交易补偿接口
     *
     * @param request
     * @return
     */
    Result<TradeAdviceResponse> tradeCompensate(OrderCompensateRequest request);

    /**
     * 外汇交易接口
     */
    Result<ForexTradeResponse> forexTrade(ForexTradeRequest request);

    /**
     * 外汇交易查询接口
     */
    Result<ForexTradeResponse> inquiryForexTrade(InquiryForexTradeRequest request);

    /**
     * 外汇交易补偿接口
     */
    Result<ForexTradeResponse> forexTradeCompensate(OrderCompensateRequest request);
}
