package com.payermax.fin.exchange.service.request;

import com.ushareit.fintech.common.model.dto.Money;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * Capture请求
 *
 * <AUTHOR>
 * @date 2024/11/19 19:57
 */
@Data
public class CaptureRequest  extends TransactionBaseRequest{

    private static final long serialVersionUID = -5880741004640642123L;

    /**
     * 订单中心-capture请求单号，最大长度64
     */
    private String payRequestNo;

    /**
     * 资产交换-capture请求单号,最大长度64
     *
     * @mock P123456789
     */
    @NotBlank(message = "[payOrderNo] is mandatory")
    @Size(max = 64, message = "[payOrderNo] maximum 64 length")
    private String payOrderNo;

    /**
     * 资产交换-原auth请求单号,最大长度64
     *
     * @mock P123456789
     */
    @NotBlank(message = "[oriPayOrderNo] is mandatory")
    @Size(max = 64, message = "[oriPayOrderNo] maximum 64 length")
    private String oriPayOrderNo;

    /**
     * capture金额,单位:元
     */
    @NotNull(message = "[amount] is mandatory")
    private Money amount;

    /**
     * 备注,最大长度512
     */
    @Size(max = 512, message = "[remark] maximum 512 length")
    private String remark;
}
