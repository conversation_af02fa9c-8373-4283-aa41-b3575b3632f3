package com.payermax.fin.exchange.service.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 补偿3ds通知
 * <AUTHOR>
 * @time 2025-05-12 15:28:54
 * @description TODO
 */
@Data
public class CompensateThreeDSNotifyRequest implements BaseRequest {

    private static final long serialVersionUID = 2414547531125219873L;

    /**
     * 渠道提交单
     */
    @NotBlank(message = "[channelPayCommitNo] is mandatory")
    private String channelPayCommitNo;

    /**
     * 支付类型
     */
    @NotBlank(message = "[paymentType] is mandatory")
    private String paymentType;

}
