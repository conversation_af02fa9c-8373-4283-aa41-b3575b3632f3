package com.payermax.fin.exchange.service.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 订单交易补偿
 *
 * <AUTHOR>
 * @date 2022/5/30 16:22
 */
@Data
public class OrderCompensateRequest implements BaseRequest {

    private static final long serialVersionUID = 4984973978978133920L;

    /**
     * 请求单号
     */
    @NotBlank(message = "[requestNo] is mandatory")
    private String requestNo;

}
