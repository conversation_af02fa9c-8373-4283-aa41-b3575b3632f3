package com.payermax.fin.exchange.service.facade;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.fin.exchange.service.request.InquiryTransferRequest;
import com.payermax.fin.exchange.service.request.TransferRequest;
import com.payermax.fin.exchange.service.response.InquiryTransferResponse;
import com.payermax.fin.exchange.service.response.TransferResponse;

/**
 * 转账服务
 *
 * <AUTHOR>
 * @date 2022/2/16 15:20
 */
public interface TransferFacade {

    /**
     * 转账申请
     *
     * @param request
     * @return
     */
    Result<TransferResponse> transfer(TransferRequest request);

    /**
     * 查询出款单信息
     *
     * @param request
     * @return
     */
    Result<InquiryTransferResponse> inquiry(InquiryTransferRequest request);


}
