package com.payermax.fin.exchange.service.response;

import lombok.Data;

import java.io.Serializable;

/**
 * 申请资金账号response类
 *
 * <AUTHOR>
 * @date 2022/10/5 17:51
 */
@Data
public class ApplyFundsAccountResponse implements Serializable {

    private static final long serialVersionUID = -8546711671706843239L;

    /**
     * 机构标识*
     */
    private String instCode;

    /**
     * 机构账号标识*
     */
    private String accountId;

    /**
     * 机构账号号码*
     */
    private String accountNo;

    /**
     * 机构子账号标识*
     */
    private String subAccountId;

    /**
     * 机构子账号号码*
     */
    private String subAccountNo;

    /**
     * 机构子账号名称*
     */
    private String subAccountName;

    /**
     * 机构子账号状态 初始态、申请中、待激活、激活中、已激活、已停用*
     */
    private String subAccountStatus;

}
