package com.payermax.fin.exchange.service.enums;

/**
 * 结果类型枚举
 *
 * <AUTHOR>
 * @date 2021/8/5 10:02
 */
public enum ResultTypeEnum {

    API("1", "联机类"),
    REDIRECT("2", "跳转类"),
    RENDER("3", "渲染展示类"),
    COLLECT("4", "采集交互类"),
    RENDER_COLLECT("5", "展示并交互类"),
    THREE_DS("6", "3DS交互类-内嵌"),
    THREE_DS_REDIRECT("7", "3DS交互类-调整");

    private String type;

    private String desc;

    ResultTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
