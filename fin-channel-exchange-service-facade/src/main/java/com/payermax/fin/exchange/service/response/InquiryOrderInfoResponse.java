package com.payermax.fin.exchange.service.response;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date Create on 2021/9/2 20:33
 */
@Data
public class InquiryOrderInfoResponse implements Serializable {

    private static final long serialVersionUID = -5608760250474983220L;

    /**
     * 资金渠道请求订单信息
     */
    private ChannelRequestOrderDTO channelRequestOrderDTO;

    /**
     * 资金渠道提交点信息
     */
    private ChannelCommitOrderDTO channelCommitOrderDTO;

    /**
     * 资金渠道能力配置信息
     */
    private FundingChannelConfigDTO fundingChannelConfigDTO;

    /**
     * 资金渠道信息
     */
    private FundingChannelInfoDTO fundingChannelInfoDTO;

    /**
     * 资金渠道能力配置和老系统配置（一级支付方式、二级支付方式）映射关系
     */
    private FundingChannelConfigMappingDTO fundingChannelConfigMappingDTO;


    @Data
    public static class ChannelRequestOrderDTO implements Serializable {

        private static final long serialVersionUID = -3650022514143051764L;

        private Long id;

        private String payRequestNo;

        private String sourceBizOrderNo;

        private String bizOrderNo;

        private String channelPayRequestNo;

        private String quoteChannelPayRequestNo;

        private String mainRequestType;

        private String requestType;

        private String productCode;

        private String country;

        private String currency;

        private String paymentMethodType;

        private String paymentType;

        private String targetOrg;

        private String cardOrg;

        private String merchantNo;

        private String channelEntity;

        private String userId;

        private BigDecimal amount;

        private String remark;

        private String purchaseInfo;

        private String requestBody;

        private String mappingCode;

        private String mappingMsg;

        private Integer status;

        private String eventType;

        private Date completeTime;

        private Date createTime;

        private Date modifiedTime;

        @Override
        public String toString() {
            return JSON.toJSONString(this);
        }
    }

    @Data
    public static class ChannelCommitOrderDTO implements Serializable {

        private static final long serialVersionUID = -5752766753518293818L;

        private Long id;

        private String payRequestNo;

        private String bizOrderNo;

        private String channelPayRequestNo;

        private String channelPayCommitNo;

        private String quoteChannelPayCommitNo;

        private String channelCode;

        private String channelMerchantCode;

        private String channelMethodCode;

        private String mainRequestType;

        private String requestType;

        private String paymentType;

        private String channelEntity;

        private String requestBody;

        private String thirdOrgOrderNo;

        private String fourthOrgOrderNo;

        private String eventType;

        private Date expireTime;

        private Integer status;

        private String mappingCode;

        private String mappingMsg;

        private String responseCode;

        private String responseMsg;

        private String batchNo;

        private Date completeTime;

        private Date createTime;

        private Date modifiedTime;

        private String addField1;

        private String addField2;

        private String addField3;

        private String addField4;

        private String addField5;

        private String addField6;

        private String addField7;

        private String envMark;

        private String extJson;

        @Override
        public String toString() {
            return JSON.toJSONString(this);
        }
    }

    @Data
    public static class FundingChannelConfigDTO implements Serializable {

        private static final long serialVersionUID = 7305701489049774010L;

        /**
         * 主键ID
         */
        private Long id;

        /**
         * 渠道编码
         */
        private String channelCode;

        /**
         * 渠道配置标识
         */
        private String channelMethodCode;

        /**
         * 国家
         */
        private String country;

        /**
         * 币种
         */
        private String currency;

        /**
         * 目标机构
         */
        private String targetOrg;

        /**
         * 支付工具
         */
        private String paymentTool;

        /**
         * 客户类型 toB、toC
         */
        private String customerType;

        /**
         * 业务处理机构
         */
        private String bizHandleOrg;

        /**
         * 支付流程
         */
        private String paymentFlow;

        /**
         * 关单时间
         */
        private Integer expireTime;

        /**
         * 是否支持退款 1:支持，0:不支持
         */
        private Byte isSupportRefund;

        /**
         * 是否支持撤销 1:支持，0:不支持
         */
        private Byte isSupportCancel;

        /**
         * 扩展配置
         */
        private String configJson;

        /**
         * 状态 1：可用 0：不可用
         */
        private Byte status;

        /**
         * 渠道附加税唯一标识
         */

        private String additionalTaxCode;

        @Override
        public String toString() {
            return JSON.toJSONString(this);
        }
    }

    @Data
    public static class FundingChannelInfoDTO implements Serializable {

        private static final long serialVersionUID = -6539411041214479130L;

        /**
         * 主键ID
         */
        private Long id;

        /**
         * 渠道编码
         */
        private String channelCode;

        /**
         * 机构code
         */
        private String orgCode;

        /**
         * 签约主体
         */
        private String channelEntity;

        /**
         * 机构产品code
         */
        private String orgProductCode;

        /**
         * 支付方式类型
         */
        private String paymentMethodType;

        /**
         * 支付类型 1:入款 2:出款
         */
        private String paymentType;

        /**
         * 技术集成机构
         */
        private String technicalOrg;

        /**
         * 资金结算机构
         */
        private String fundsSettleOrg;

        /**
         * 是否需要报备商户 1：是 0：否
         */
        private Byte isNeedReportMerchant;

        /**
         * 回调是否验签 1：是 0：否
         */
        private Byte checkCallbackSign;

        /**
         * 交易码映射配置组
         */
        private String transCodeMapGroupId;

        /**
         * 响应码映射配置组
         */
        private String responseCodeMapGroupId;

        /**
         * 扩展配置
         */
        private String configJson;

        /**
         * 状态 1：可用 0：不可用
         */
        private Byte status;

        @Override
        public String toString() {
            return JSON.toJSONString(this);
        }
    }

    @Data
    public static class FundingChannelConfigMappingDTO implements Serializable {

        private static final long serialVersionUID = 1653959810077581164L;

        /**
         * 资金渠道编码
         */
        private String channelCode;

        /**
         * 资金渠道配置编码
         */
        private String channelMethodCode;

        private String oldProductCode;

        private String oldMethodCode;

        private String oldMethodSubCode;

        private String oldChannelCode;

        private Byte status;

        private String resultMapping;

        private String paramsMapping;

        private String configJson;

        @Override
        public String toString() {
            return JSON.toJSONString(this);
        }
    }

}
