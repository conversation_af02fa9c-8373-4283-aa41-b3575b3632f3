package com.payermax.fin.exchange.service.facade;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.fin.exchange.service.request.*;
import com.payermax.fin.exchange.service.response.*;

/**
 * 预筛选服务
 *
 * <AUTHOR>
 * @date 2021/7/15 21:09
 * @dubbo
 */
public interface PreFilterFacade {

    /**
     * 预筛选
     *
     * @param request
     * @return
     */
    Result<com.payermax.fin.exchange.service.response.PreFilterResponse> filter(PreFilterRequest request);

    /**
     * 筛选账号
     *
     * @param request
     * @return
     */
    Result<PreFilterAccountResponse> filterAccount(PreFilterAccountRequest request);

    /**
     * 收银产品预筛选渠道实例
     *
     * @param request
     * @return
     */
    Result<ProductPreFilterResponse> cashierProductFilter(ProductPreFilterRequest request);

    /**
     * 可用性筛选
     *
     * @param request
     * @return
     */
    Result<AvailabilityFilterResponse> availabilityFilter(AvailabilityFilterRequest request);

    /**
     * 新版可用性筛选
     **/
    Result<NewAvailabilityFilterResponse> newAvailabilityFilter(AvailabilityFilterRequest request);

    /**
     * 报备调用筛选
     **/
    Result<MerchantReportFilterResponse> merchantReportFilter(MerchantReportFilterRequest request);

    /**
     * 筛选支付方式下可用渠道（提供给沟通中心使用）
     *
     * @param request
     * @return
     */
    Result<FilterChannelResponse> filterChannel(FilterChannelRequest request);

    /**
     * 预筛选测试
     *
     * @param request
     * @return
     */
    Result<FilterChannelResponse> filterChannelTest(PreFilterTestRequest request);
}
