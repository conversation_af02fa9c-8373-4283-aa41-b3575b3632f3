package com.payermax.fin.exchange.service.facade;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.fin.exchange.service.request.*;
import com.payermax.fin.exchange.service.response.*;

/**
 * 支付服务
 *
 * <AUTHOR>
 * @date 2021/7/15 21:01
 * @dubbo
 */
public interface PaymentFacade {

    /**
     * 支付申请
     *
     * @param request
     * @return
     */
    Result<PayResponse> pay(PayRequest request);

    /**
     * Capture申请
     *
     * @param request
     * @return
     */
    Result<CaptureResponse> capture(CaptureRequest request);

    /**
     * 支付流程统一控制
     *
     * @param request
     * @return
     */
    Result<UnifyControlResponse> unifyControl(UnifyControlRequest request);

    /**
     * 查询支付单
     *
     * @param request
     * @return
     */
    Result<InquiryPaymentResponse> inquiryPayment(InquiryPaymentRequest request);

    /**
     * 查询capture订单
     *
     * @param request
     * @return
     */
    Result<InquiryCaptureResponse> inquiryCapture(InquiryCaptureRequest request);

    /**
     * 差错订单处理 补入款交易
     *
     * @param request
     * @return
     */
    Result<PayResponse> handlePayCorrectionOrder(PayCorrectionOrderRequest request);

    /**
     * 申请DDC*
     * *
     * @param request
     * @return
     */
    Result<ApplyDDCResponse> applyDDC(ApplyDDCRequest request);

    /**
     * 申请支付token
     *
     * @param request
     * @return
     */
    Result<ApplyPaymentTokenResponse> applyPaymentToken(ApplyPaymentTokenRequest request);

    /**
     * token解绑
     * @param request
     * @return
     */
    Result<TokenUnbindingResponse> tokenUnbinding(TokenUnbindingRequest request);

    /**
     * 主动关单
     **/
    Result<CloseResponse> closeApply(CloseRequest request);
}
