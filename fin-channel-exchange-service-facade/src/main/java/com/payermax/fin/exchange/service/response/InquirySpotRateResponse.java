package com.payermax.fin.exchange.service.response;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 即期报价查询响应
 *
 * <AUTHOR>
 * @date 2022/4/8 11:22
 */
@Data
public class InquirySpotRateResponse implements Serializable {


    private static final long serialVersionUID = 2102586682065506054L;
    /**
     * 请求单号
     */
    private String requestNo;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 渠道提交单号
     */
    private String channelPayCommitNo;

    /**
     * 状态(SUCCESS/FAIL)
     */
    private String status;

    /**
     * 响应映射code
     */
    private String mappingCode;

    /**
     * 响应映射msg
     */
    private String mappingMsg;

    /**
     * 币种对集合
     */
    private List<CurrencyPair> currencyPairs;

    @Data
    public static class CurrencyPair implements Serializable{

        private static final long serialVersionUID = 3483486712716143927L;
        /**
         * 基准货币
         */
        private String baseCurrency;
        /**
         * 标价货币
         */
        private String quoteCurrency;
        /**
         * 中间价
         */
        private BigDecimal midRate;
        /**
         * 卖入价
         */
        private BigDecimal offerRate;
        /**
         * 买入价
         */
        private BigDecimal bidRate;
        /**
         * 汇率生成时间
         */
        private Long generateTime;
        /**
         * 汇率生效时间
         */
        private Long effectTime;
        /**
         * 汇率失效时间
         */
        private Long expiryTime;
        /**
         * 机构编码
         */
        private String institutionCode;
    }


}
