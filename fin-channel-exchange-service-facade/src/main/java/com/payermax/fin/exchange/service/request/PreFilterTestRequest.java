package com.payermax.fin.exchange.service.request;

import lombok.Data;
import com.ushareit.fintech.common.model.dto.Money;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2025-01-06 19:07:51
 * @description TODO
 */
@Data
public class PreFilterTestRequest implements BaseRequest {

    private static final long serialVersionUID = -2060480771665888486L;

    /**
     * 指定过滤规则Name,值为过滤规则的BeanName
     */
    private List<String> filterRuleNameList;

    /**
     * 支付方式集合
     */
    private List<PaymentMethod> paymentMethods;

    /**
     * 订单信息
     */
    private OrderInfo orderInfo;

    @Data
    public static class OrderInfo implements Serializable {

        private static final long serialVersionUID = 7303820597697058875L;

        /**
         * 交易步骤*
         */
        private String transStep;

        /**
         * 内部商户号
         *
         * @mock SP1234567
         */
        private String merchantNo;

        /**
         * 子商户号（外部商户的子商户）
         */
        private String subMerchantNo;

        /**
         * 客户ID
         */
        private String customId;

        /**
         * 商户类型*
         */
        private String merchantType;

        /**
         * 服务主体（商户签约主体）
         */
        private String serviceEntity;

        /**
         * 服务模式
         */
        private String serviceMode;

        /**
         * 集成模式
         */
        private String integrate;

        /**
         * 商户是否开通拒付预警 Y：是 N：否
         */
        private String isOpenCbWarning;

        /**
         * 商户MCC
         */
        private String mcc;

        /**
         * 商户三级MCC
         */
        private String subMcc;

        /**
         * 商户用户标识,最大长度64
         *
         * @mock SP1234567_837629
         */
        @Size(max = 64, message = "[outUserId] maximum 64 length")
        private String outUserId;

        /**
         * 用户会员ID
         */
        @Size(max = 64, message = "[userMemberId] maximum 64 length")
        private String userMemberId;
    }

    @Data
    public static class PaymentMethod implements Serializable {

        private static final long serialVersionUID = -8767644301987352863L;

        /**
         * 售卖收银产品编码
         */
        private String cashierProductNo;

        /**
         * 售卖收银产品版本
         */
        private String cashierProductVersion;

        /**
         * 支付方式编号
         */
        private String paymentMethodNo;

        /**
         * 产品编码
         */
        private String productCode;

        /**
         * 支付方式类型,可用值:NetBanking,VirtualAccount,BarCode,Wallet,CardPay,CarrierBilling,PrePaid,PointsPay,PayLater,AutoDebit,DirectDebit
         *
         * @mock CarrierBilling
         */
        private String paymentMethodType;

        /**
         * 支付类型,可用值:10-出款,20-入款,40-退款
         *
         * @mock 20
         */
        private String paymentType;

        /**
         * 国家二字码
         *
         * @mock ID
         */
        private String country;

        /**
         * 交易金额,单位:元（组件支付业务场景下金额值可能为0）
         */
        private Money amount;

        /**
         * 是否忽略交易金额 为true时，忽略对amount.value的校验，组件支付业务场景下使用*
         */
        private Boolean isIgnoreAmountValue;

        /**
         * 目标机构
         *
         * @mock ********
         */
        private String targetOrg;

        /**
         * 业务身份
         *
         * @mock p201
         */
        private String bizIdentify;

        /**
         * 客户类型,可用值:1-2C,2-2B
         *
         * @mock 1
         */
        private String customerType;

        /**
         * 采集参数集合
         */
        private List<String> params;

        /**
         * 支付方式扩展属性集合
         */
        private List<ExtendProperty> extendProperties;

        /**
         * 是否匹配签约主体 Y:是 N:否
         */
        private String isMatchEntity;

        @Data
        public static class ExtendProperty implements Serializable {

            private static final long serialVersionUID = -5953494831864959757L;

            /**
             * 扩展属性key
             */
            private String key;
            /**
             * 扩展属性value
             */
            private String value;
            /**
             * 逻辑操作符
             */
            private String logicKey;

        }
    }
}
