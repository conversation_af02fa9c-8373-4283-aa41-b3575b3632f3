package com.payermax.fin.exchange.service.response;

import com.ushareit.fintech.common.model.dto.Money;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date Create on 2021/7/19 13:36
 */
@Data
public class PreFilterResponse implements Serializable {

    private static final long serialVersionUID = -7764667931730388906L;

    /**
     * 支付方式集合
     */
    private List<PaymentMethod> paymentMethods;

    /**
     * 不可用支付方式集合*
     */
    private List<DisablePaymentMethod> disablePaymentMethods;

    @Data
    public static class PaymentMethod implements Serializable {

        private static final long serialVersionUID = 6243071863052586098L;

        /**
         * 支付方式编号,来源于入参中的数据"
         */
        private String paymentMethodNo;

        /**
         * 金额,来源于入参中的数据
         */
        private Money amount;

        /**
         * 支付方式类型
         */
        private String paymentMethodType;

        /**
         * 目标机构
         */
        private String targetOrg;

        /**
         * 渠道签约主体,可用值:3-PMMax,2-Funtech,1-SHAREit
         */
        private String channelEntity;

        /**
         * 国家
         */
        private String country;

        /**
         * 支付流程
         */
        private String paymentFlow;

        /**
         * 是否支持退款,可用值:1-支持,0-不支持
         */
        private Byte isSupportRefund;

        /**
         * 渠道附加税标识
         */
        private String additionalTaxCode;

        /**
         * 渠道税费配置信息
         */
        private ChannelAdditionalTax channelAdditionalTax;

        /**
         * 渠道成本系数
         */
        private Integer fundingCost;

        /**
         * 渠道稳定性,默认为:10
         */
        private Integer channelStability = 10;

        /**
         * 下一步接口类型,可用值:1-支付类,2-控制类
         */
        private Integer nextApiType;

        /**
         * 采集参数集合
         */
        private List<String> params;

        /**
         * 扩展参数集合
         */
        private List<String> extraParams;

        /**
         * 扩展返回数据,用于返回不同支付渠道的特定属性
         */
        private Map<String, Object> extendResult;

        /**
         * 支付方式扩展属性集合
         */
        private List<ExtendProperty> extendProperties;

        @Data
        public static class ExtendProperty implements Serializable {

            private static final long serialVersionUID = -3169516461547414808L;

            /**
             * 扩展属性key
             */
            private String key;
            /**
             * 扩展属性value
             */
            private String value;
            /**
             * 逻辑操作符
             */
            private String logicKey;

        }

        @Data
        public static class ChannelAdditionalTax implements Serializable {

            private static final long serialVersionUID = -255731730834772621L;

            /**
             * 税种,可用值:1-GST,2-VAT(Transaction),3-GMF,4-VAT(Fee),5-IOF,6-ITF,7-WHT1,8-WHT2
             */
            private String taxType;

            /**
             * 税费计费规则,JSON格式
             */
            private String taxRate;

        }

    }

    @Data
    public static class DisablePaymentMethod implements Serializable {

        /**
         * 支付方式类型*
         */
        private String paymentMethodType;
        /**
         * 目标机构*
         */
        private String targetOrg;
        /**
         * 卡组*
         */
        private String cardOrg;
        /**
         * 不可用原因*
         * {"I_DANA_P01_WALLET201_DANA_ID": "AMOUNT_LIMIT"}*
         */
        private Map<String, String> reasonMap;
        /**
         * 不可用原因明细
         */
        private List<DisableReasonDetail> reasonDetails;
    }

    @Data
    public static class DisableReasonDetail implements Serializable {
        /**
         * 支付方式Code
         */
        private String channelMethodCode;
        /**
         * 渠道商户Mid
         */
        private String channelMerchantCode;
        /**
         * 错误码
         */
        private String errorCode;
        /**
         * 错误描述
         */
        private String errorMsg;
    }
}
