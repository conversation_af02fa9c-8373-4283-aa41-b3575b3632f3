package com.payermax.fin.exchange.service.request;

import com.payermax.common.lang.util.money.Money;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 外汇交易请求
 *
 * <AUTHOR>
 * @date 2022/7/31 10:22
 */
@Data
public class ForexTradeRequest implements BaseRequest {

    private static final long serialVersionUID = -6941415642768096011L;
    /**
     * 渠道编码
     */
    @NotBlank(message = "[channelCode] is mandatory")
    private String channelCode;

    /**
     * 请求单号
     */
    @NotBlank(message = "[transactionOrderNo] is mandatory")
    private String transactionOrderNo;

    /**
     * 客户号
     */
    private String customerNo;

    /**
     * 期间
     */
    @NotBlank(message = "[duration] is mandatory")
    private String duration;
    /**
     * 起息日
     */
    private Long valueDate;
    /**
     * 基准货币
     */
    @NotBlank(message = "[baseCurrency] is mandatory")
    private String baseCurrency;

    /**
     * 交易货币
     */
    @NotBlank(message = "[transactionCurrency] is mandatory")
    private String transactionCurrency;

    /**
     * 货币对
     */
    @NotBlank(message = "[currencyPairFlag] is mandatory")
    private String currencyPairFlag;

    /**
     * 客户实际支付金额
     */
    @NotNull(message = "[transactionAmount] is mandatory")
    private Money transactionAmount;

    /**
     * 交易方向
     */
    @NotBlank(message = "[transactionSide] is mandatory")
    private String transactionSide;

    /**
     * 交易类型
     */
    @NotBlank(message = "[transactionType] is mandatory")
    private String transactionType;

    /**
     * 机构汇率版本号
     */
    private String rateSheetRef;

    /**
     * 交易日期
     */
    private Long tradeDate;

    /**
     * 渠道账户别名
     */
    private String channelAccountAlias;

    /**
     * 买入账号Id
     */
    private String buyAccountId;

    /**
     * 卖出账号Id
     */
    private String sellAccountId;

    /**
     * 汇率值
     */
    private BigDecimal rateSheet;

    /**
     * 对手方币种
     */
    private String counterCcy;

    /**
     * 预计对手方金额
     */
    private Money expectCounterAmount;

    /**
     * 客户身份标识
     */
    private String customIdentify;

}
