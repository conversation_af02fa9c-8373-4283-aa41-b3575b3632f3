package com.payermax.fin.exchange.service.enums;

/**
 * <AUTHOR>
 * @date Create on 2021/8/25 17:53
 */
public enum AuthorizationStatus4OutEnum {
    /**
     * 对外展示的授权结果
     */
    INIT,
    SUCCESS,
    FAILED,
    PENDING,
    REVOKE,
    EXPIRED;

    /**
     * 状态映射
     *
     * @param value
     * @return
     */
    public static AuthorizationStatus4OutEnum mappingStatus4Out(String value) {
        AuthorizationStatus4OutEnum[] enums = AuthorizationStatus4OutEnum.values();
        for (AuthorizationStatus4OutEnum item : enums) {
            if (item.name().equals(value)) {
                return item;
            }
        }
        return PENDING;
    }
}
