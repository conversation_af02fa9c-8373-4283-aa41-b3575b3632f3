package com.payermax.fin.exchange.service.facade;

import com.payermax.fin.exchange.service.request.InquiryAuthorizationUserInfoRequest;
import com.payermax.fin.exchange.service.response.ApplyAuthorizationResponse;
import com.payermax.fin.exchange.service.response.InquiryAuthorizationResponse;
import com.payermax.fin.exchange.service.response.InquiryAuthorizationUserInfoResponse;
import com.payermax.fin.exchange.service.response.RevokeAuthorizationResponse;
import com.payermax.fin.exchange.service.request.ApplyAuthorizationRequest;
import com.payermax.fin.exchange.service.request.InquiryAuthorizationRequest;
import com.payermax.fin.exchange.service.request.RevokeAuthorizationRequest;
import com.payermax.common.lang.model.dto.Result;

/**
 * 授权服务
 *
 * <AUTHOR>
 * @date 2021/7/15 21:10
 * @dubbo
 */
public interface AuthorizationFacade {

    /**
     * 授权申请
     *
     * @param request
     * @return
     */
    Result<ApplyAuthorizationResponse> apply(ApplyAuthorizationRequest request);

    /**
     * 授权解约
     *
     * @param request
     * @return
     */
    Result<RevokeAuthorizationResponse> revoke(RevokeAuthorizationRequest request);

    /**
     * 查询授权单信息
     *
     * @param request
     * @return
     */
    Result<InquiryAuthorizationResponse> inquiry(InquiryAuthorizationRequest request);

    /**
     * 查询绑定的用户信息
     *
     * @param request
     * @return
     */
    Result<InquiryAuthorizationUserInfoResponse> inquiryUserInfo(InquiryAuthorizationUserInfoRequest request);

}
