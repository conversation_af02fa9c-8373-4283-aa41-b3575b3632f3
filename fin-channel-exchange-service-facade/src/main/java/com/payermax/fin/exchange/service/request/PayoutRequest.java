package com.payermax.fin.exchange.service.request;

import com.payermax.fin.exchange.service.context.PayoutRequestContext;
import com.ushareit.fintech.common.model.dto.Money;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @date 2021/9/28 20:44
 */
@Getter
@Setter
public class PayoutRequest extends TransactionBaseRequest implements BaseRequest {

    private static final long serialVersionUID = -2374275352795258325L;

    /**
     * 支付请求单号，最大长度64
     */
    @NotBlank(message = "[payRequestNo] is mandatory")
    @Size(max = 64, message = "[payRequestNo] maximum 64 length")
    private String payRequestNo;

    /**
     * 支付引擎-支付单号,最大长度64
     */
    @NotBlank(message = "[payOrderNo] is mandatory")
    @Size(max = 64, message = "[payOrderNo] maximum 64 length")
    private String payOrderNo;

    /**
     * 业务身份
     *
     * @mock p201
     */
    @NotBlank(message = "[bizIdentify] is mandatory")
    private String bizIdentify;

    /**
     * 服务主体（商户签约主体）
     */
    private String serviceEntity;

    /**
     * 服务模式
     */
    private String serviceMode;

    /**
     * 业务产品编码
     */
    @NotBlank(message = "[productCode] is mandatory")
    private String productCode;

    /**
     * 支付方式类型,可用值:NetBanking,VirtualAccount,BarCode,Wallet,CardPay,CarrierBilling,PrePaid,PointsPay,PayLater,AutoDebit,DirectDebit
     *
     * @mock CarrierBilling
     */
    @NotBlank(message = "[paymentMethodType] is mandatory")
    private String paymentMethodType;

    /**
     * 目标机构
     *
     * @mock ********
     */
    @NotBlank(message = "[targetOrg] is mandatory")
    private String targetOrg;

    /**
     * 交易金额,单位:元
     */
    @NotNull(message = "[amount] is mandatory")
    private Money amount;

    /**
     * 请求上下文对象
     */
    @Valid
    private PayoutRequestContext requestContext;

    /**
     * 请求上下文JSON对象
     *
     * @ignore
     */
    private String requestContextJson;

}
