package com.payermax.fin.exchange.service.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 查询授权信息request
 *
 * <AUTHOR>
 * @date 2021/9/16 19:40
 */
@Data
public class InquiryAuthorizationRequest implements BaseRequest {

    private static final long serialVersionUID = -4976381899416665561L;

    /**
     * 授权码,内部金融交换系统生成
     */
    @NotBlank(message = "[authorizeCode] is mandatory")
    private String authorizeCode;

}
