package com.payermax.fin.exchange.service.response;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName InquiryAuthorizationUserInfoResponse
 * @Time 2024/11/18 17:02:01
 * @Description
 */
@Data
public class InquiryAuthorizationUserInfoResponse implements Serializable {
    private static final long serialVersionUID = 6442701687559364789L;

    /**
     * 账号信息：脱敏的手机号
     */
    private String accountDisplay;

    /**
     * 账户余额
     */
    private Balance balance;

    /**
     * 用户的附加信息
     */
    private Map<String, Object> additionalInfo;

    /**
     * token是否过期：true/false
     */
    private String tokenExpired;

    private String channelRespCode;

    private String channelRespMsg;

    @Data
    public static class Balance implements Serializable {

        private static final long serialVersionUID = -9174182239891313058L;

        /**
         * 金额
         */
        private String value;

        /**
         * 币种
         */
        private String currency;
    }

}
