package com.payermax.fin.exchange.service.enums;

/**
 * 请求类型
 *
 * <AUTHOR>
 * @date 2022/11/30 23:39
 */
public enum RequestTypeEnum {

    HEDGE_ADVICE("hedgeAdvice"),
    TRADE_ADVICE("tradeAdvice"),
    FOREX_TRADE("forexTrade"),
    QUERY_FOREX_TRADE("queryForexTrade"),
    QUERY_SPOT_RATE("querySpotRate"),
    DDC_APPLY("ddcApply"),
    AUTH("auth"),
    CAPTURE("capture"),
    ;

    private String code;

    RequestTypeEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

}
