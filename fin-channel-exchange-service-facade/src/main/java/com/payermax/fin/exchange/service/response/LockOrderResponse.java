package com.payermax.fin.exchange.service.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/3 15:20
 */
@Data
public class LockOrderResponse implements Serializable {

    private static final long serialVersionUID = -3327825512495261148L;

    /**
     * 锁单成功数量
     */
    public int successCount;

    /**
     * 锁单失败数量
     */
    private int failedCount;

    /**
     * 锁单失败详情
     */
    private List<FailedOrderDetail> failedOrderDetails;

    @Data
    public static class FailedOrderDetail implements Serializable {

        private static final long serialVersionUID = 133102829427528945L;

        /**
         * 渠道提交单号
         */
        public String channelPayCommitNo;

        /**
         * 失败原因
         */
        private String failedReason;

    }
}
