package com.payermax.fin.exchange.service.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/05/19 12:43
 * 展示品牌的能力详情
 **/
@Data
@EqualsAndHashCode
public class NewAvailabilityFilterResponse implements Serializable {

    /**
     * 品牌的能力集合
     */
    private List<Ability> abilityList;

    public NewAvailabilityFilterResponse() {
        this.abilityList = new ArrayList<>();
    }

    @Data
    @NoArgsConstructor
    public static class Ability implements Serializable {
        /**
         * 支付方式编号,来源于入参中的数据
         **/
        private String paymentMethodId;
        /**
         * 收银产品编码
         **/
        private String cashierProductNo;
        /**
         * 品牌
         **/
        private String brandCode;
        /**
         * 签约币种
         **/
        private String currency;
        /**
         * 渠道可用能力
         **/
        private List<ChannelAbility> channelAbilities;

        /**
         * 渠道不可用的原因
         **/
        private List<DisableChannelDetail> disableChannelDetails;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (!(o instanceof Ability)) return false;
            Ability that = (Ability) o;
            return Objects.equals(cashierProductNo, that.cashierProductNo) && Objects.equals(brandCode, that.brandCode) && Objects.equals(currency, that.currency);
        }

        @Override
        public int hashCode() {
            return Objects.hash(cashierProductNo, brandCode, currency);
        }

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class ChannelAbility implements Serializable {
            /**
             * 支付方式Code
             */
            private String channelMethodCode;
            /**
             * 渠道商户Mid
             */
            private String channelMerchantCode;
            /**
             * 单笔渠道限额
             */
            private Set<ExtendProperty> singleAmountLimit;

            @Override
            public boolean equals(Object o) {
                if (this == o) return true;
                if (!(o instanceof ChannelAbility)) return false;
                ChannelAbility that = (ChannelAbility) o;
                return Objects.equals(channelMethodCode, that.channelMethodCode) && Objects.equals(channelMerchantCode, that.channelMerchantCode);
            }

            @Override
            public int hashCode() {
                return Objects.hash(channelMethodCode, channelMerchantCode);
            }
        }

        @Data
        @EqualsAndHashCode
        public static class ExtendProperty implements Serializable {

            private static final long serialVersionUID = -3169516961547414808L;
            /**
             * 扩展属性value
             */
            private String value;
            /**
             * 逻辑操作符
             */
            private String logicKey;

        }

        @Data
        public static class DisableChannelDetail implements Serializable {
            /**
             * 支付方式Code
             */
            private String channelMethodCode;
            /**
             * 渠道商户Mid
             */
            private String channelMerchantCode;
            /**
             * 渠道ID
             */
            private String channelId;
            /**
             * 渠道主体
             */
            private String entity;
            /**
             * 错误码
             */
            private String errorCode;
            /**
             * 错误描述
             */
            private String errorMsg;

            @Override
            public boolean equals(Object o) {
                if (this == o) return true;
                if (!(o instanceof DisableChannelDetail)) return false;
                DisableChannelDetail that = (DisableChannelDetail) o;
                return Objects.equals(channelMethodCode, that.channelMethodCode) && Objects.equals(channelMerchantCode, that.channelMerchantCode);
            }

            @Override
            public int hashCode() {
                return Objects.hash(channelMethodCode, channelMerchantCode);
            }
        }
    }

}
