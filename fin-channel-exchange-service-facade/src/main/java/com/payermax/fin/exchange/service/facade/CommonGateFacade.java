package com.payermax.fin.exchange.service.facade;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.fin.exchange.service.request.CommonGateRequest;
import com.payermax.fin.exchange.service.response.CommonGateResponse;

/**
 * 通用网关转发服务
 *
 * <AUTHOR>
 * @date 2022/5/26 21:01
 * @dubbo
 */
public interface CommonGateFacade {

    /**
     * 通用网关转发服务
     *
     * @param request
     * @return
     */
    Result<CommonGateResponse> commonGateForward(CommonGateRequest request);
}
