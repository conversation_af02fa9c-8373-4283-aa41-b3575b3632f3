package com.payermax.fin.exchange.service.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 更新提交单请求
 *
 * <AUTHOR>
 * @date 2022/4/19 14:59
 */
@Data
public class UpdateCommitOrderRequest implements BaseRequest {

    private static final long serialVersionUID = -7389765149016696317L;

    /**
     * 渠道提交单号
     */
    @NotBlank(message = "[channelPayCommitNo] is mandatory")
    private String channelPayCommitNo;

    /**
     * 四方机构单号
     */
    private String thirdOrgOrderNo;

    /**
     * 渠道提交单号
     */
    private String fourthOrgOrderNo;

    /**
     * 支付类型,可用值:10-出,20-入,30-兑,40-退,50-转
     */
    private String paymentType;

    /**
     * 状态
     */
    private Byte status;

    /**
     * 响应返回码
     */
    private String respCode;

    /**
     * 响应返回msg
     */
    private String respMsg;

    /**
     * 扩展字段1
     */
    private String addField1;

    /**
     * 扩展字段2
     */
    private String addField2;

    /**
     * 扩展字段3
     */
    private String addField3;

    /**
     * 扩展字段4
     */
    private String addField4;

    /**
     * 扩展字段5
     */
    private String addField5;

    /**
     * 扩展字段6
     */
    private String addField6;
    
    /** 
     * 可重发类型 0:无可重发类型，1：可换单重发
     * */
    private Byte retryableType;

    /**
     * Acquirer Reference Number(ARN)
     */
    private String arn;

    /**
     * Retrieval Reference Number(RRN)
     */
    private String rrn;

    /**
     * OA流程相关数据
     */
    private OaProcessData oaProcessData;

    @Data
    public static class OaProcessData implements Serializable {

        private static final long serialVersionUID = -355599312340299L;

        /**
         * OA发起人
         * */
        private String oaSponsor;

        /**
         * OA编号
         * */
        private String oaNumber;
    }

}
