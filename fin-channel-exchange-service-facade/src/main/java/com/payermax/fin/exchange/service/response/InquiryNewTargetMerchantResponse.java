package com.payermax.fin.exchange.service.response;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date Create on 2023/4/18 10:33
 */
@Data
public class InquiryNewTargetMerchantResponse implements Serializable {

    private static final long serialVersionUID = 6954376977316363809L;

    /**
     * 渠道提交单号
     */
    private String channelPayCommitNo;

    /**
     * 二级商户信息
     */
    private TargetMerchantDTO targetMerchantDetail;


    @Data
    public static class TargetMerchantDTO implements Serializable {

        private static final long serialVersionUID = 7186461554621353489L;

        private String channelId;

        private String merchantId;

        private String targetMerchantId;

        private String targetMerchantMcc;

        private String targetMerchantName;

        private String targetMerchantAddress;

        private String targetMerchantCity;

        private String targetMerchantCountry;

        private String targetMerchantPostCode;

        private String targetMerchantPhone;

        private String targetMerchantEmail;

        private String targetMerchantSecretKey;

        private String configJson;

        private String targetMerchantInfo;

        @Override
        public String toString() {
            return JSON.toJSONString(this);
        }
    }

}
