package com.payermax.fin.exchange.service.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @ClassName InquiryAuthorizationUserInfoRequest
 * @Time 2024/11/18 17:00:30
 * @Description 查询绑定的用户信息
 */
@Data
public class InquiryAuthorizationUserInfoRequest implements BaseRequest {

    private static final long serialVersionUID = 2476657845742624332L;

    private String requestNo;

    /**
     * 授权码，内部金融交换系统生成
     */
    @NotBlank(message = "[authorizeCode] is mandatory")
    private String authorizeCode;

    /**
     * 外部用户号(UAT MOCK使用)
     */
    private String outUserId;

    private String merchantName;
}
