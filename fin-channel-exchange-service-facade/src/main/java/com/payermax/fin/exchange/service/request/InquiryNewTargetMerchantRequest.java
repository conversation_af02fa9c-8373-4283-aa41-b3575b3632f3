package com.payermax.fin.exchange.service.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date Create on 2023/4/18 10:29
 */
@Data
public class InquiryNewTargetMerchantRequest implements BaseRequest {

    private static final long serialVersionUID = 4273953188617264069L;

    /**
     * 渠道提交单号
     */
    @NotBlank(message = "[channelPayCommitNo] is mandatory")
    private String channelPayCommitNo;

    /**
     * 支付类型
     */
    private String paymentType;

    /**
     * 商户号
     */
    private String merchantId;
}
