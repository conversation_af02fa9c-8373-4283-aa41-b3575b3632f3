package com.payermax.fin.exchange.service.request;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class RevokeAuthorizationRequest extends TransactionBaseRequest {

    private static final long serialVersionUID = -2809866341973027553L;

    /**
     * 请求唯一标识,最大长度64
     *
     * @mock 1234567890
     */
    @NotBlank(message = "[requestNo] is mandatory")
    @Size(max = 64, message = "[requestNo] maximum 64 length")
    private String requestNo;

    /**
     * 商户用户标识,最大长度64
     *
     * @mock SP1234567_837629
     */
    @NotBlank(message = "[outUserId] is mandatory")
    @Size(max = 64, message = "[outUserId] maximum 64 length")
    private String outUserId;

    /**
     * 授权码,内部金融交换系统生成
     */
    @NotBlank(message = "[authorizeCode] is mandatory")
    private String authorizeCode;

    /**
     * 服务主体（商户签约主体）
     */
    private String serviceEntity;

    /**
     * 服务模式
     */
    private String serviceMode;

    /**
     * 备注,最大长度256
     */
    @Size(max = 256, message = "[remark] maximum 256 length")
    private String remark;

}