package com.payermax.fin.exchange.service.context;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/12/05
 **/

@Data
public class AuthorizationRequestContext implements Serializable {

    private static final long serialVersionUID = 3786601531784421976L;

    /**
     * 售卖收银产品编码
     */
    private String cashierProductNo;

    /**
     * 收银产品版本
     */
    private String cashierProductVersion;

    /**
     * 订单信息
     */
    @Valid
    @NotNull(message = "[orderInfo] is mandatory")
    private OrderInfo orderInfo;

    @Data
    public static class OrderInfo implements Serializable {

        private static final long serialVersionUID = -4557749020116701823L;

        /**
         * 客户ID
         */
        private String customId;

        /**
         * 商户店铺ID（外部商户的子商户）
         */
        private String shopId;

        /**
         * MCC信息
         */
        private String mcc;

        /**
         * 商户三级MCC
         */
        private String subMcc;

        /**
         * 用户会员ID
         */
        @Size(max = 64, message = "[userMemberId] maximum 64 length")
        private String userMemberId;

        /**
         * 消费信息,最大长度512
         */
        @Size(max = 512, message = "[purchaseInfo] maximum 512 length")
        private String purchaseInfo;

        /**
         * 用户使用语言
         */
        private String userLanguage;

    }
}
