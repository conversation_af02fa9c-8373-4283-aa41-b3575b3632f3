package com.payermax.fin.exchange.service.response;

import com.payermax.fin.exchange.service.enums.OrderStatus4OutEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 订单查询基础响应
 *
 * <AUTHOR>
 * @date 2021/8/30 20:58
 */
@Data
public abstract class InquiryBaseResponse implements Serializable {

    /**
     * 支付请求单号
     */
    private String payRequestNo;

    /**
     * 支付引擎-支付单号
     */
    private String payOrderNo;

    /**
     * 上游业务单号
     */
    private String bizOrderNo;

    /**
     * 渠道请求单号
     */
    private String channelPayRequestNo;

    /**
     * 渠道提交单号
     */
    private String channelPayCommitNo;

    /**
     * 国家二字码
     */
    private String country;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 标准渠道编码*
     */
    private String standardChannelCode;

    /**
     * 渠道签约主体,可用值:3-PMMax,2-Funtech,1-SHAREit
     */
    private String channelEntity;

    /**
     * 三方机构单号
     */
    private String thirdOrgOrderNo;

    /**
     * 四方机构单号
     */
    private String fourthOrgOrderNo;

    /**
     * 状态,可用值:PENDING-支付中,SUCCESS-成功,FAILED-失败
     */
    private OrderStatus4OutEnum status;

    /**
     * 响应映射code
     */
    private String mappingCode;

    /**
     * 响应映射msg
     */
    private String mappingMsg;

    /**
     * 完成时间
     */
    private Long completeTime;

    /**
     * 外部渠道完成时间
     */
    private Long channelCompleteTime;

    /**
     * 老系统一级支付方式编码
     */
    private String oldMethodCode;

    /**
     * 老系统二级支付方式编码
     */
    private String oldMethodSubCode;

    /**
     * 结算币种
     */
    private String settleCurrency;

    /**
     * 差错标识
     */
    private String correctionLogo;

    /**
     * 支付方式编码
     */
    private String channelMethodCode;

    /**
     * 渠道商户编码
     */
    private String channelMerchantCode;

    /**
     * 渠道业务类型
     * PREFUND：预存资金
     */
    private String channelBusinessType;

    /**
     * 卡组
     */
    private String cardOrg;

    /**
     * 我方头寸账号ID*
     */
    private String fundsAccountId;

    /**
     * 渠道响应码
     */
    private String channelRespCode;

    /**
     * 渠道响应消息
     */
    private String channelRespMsg;

    /**
     * 扩展返回数据,用于返回不同支付渠道的特定属性（返回给内部使用，如收银台）
     */
    private Map<String, Object> extendResult;

}
