package com.payermax.fin.exchange.service.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/2 21:00
 */
@Data
public class PayoutBatchRetryTransactionRequest implements BaseRequest {

    private static final long serialVersionUID = -5130821013731774740L;

    /**
     * 渠道提交单列表
     *
     * @mock P23456789
     */
    @NotEmpty(message = "[channelPayCommitNoList] is mandatory")
    private List<String> channelPayCommitNoList;

    /**
     * 操作人
     */
    @NotBlank(message = "[operator] is mandatory")
    private String operator;

}
