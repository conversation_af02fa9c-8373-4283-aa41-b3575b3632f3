package com.payermax.fin.exchange.service.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

/**
 * 转账请求
 *
 * <AUTHOR>
 * @date 2022/2/16 11:49
 */
@Data
public class TransferRequest implements BaseRequest {

    private static final long serialVersionUID = -20493077294626516L;

    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * 支付单号，最大长度64
     */
    @NotBlank(message = "[requestNo] is mandatory")
    @Size(min = 24, max = 64, message = "[requestNo] must be between 24 and 16 characters length")
    private String requestNo;

    /**
     * 业务源
     */
    @NotBlank(message = "[bizSource] is mandatory")
    @Size(max = 8, message = "[bizSource] maximum 8 length")
    private String bizSource;

    /**
     * 支付方式代码
     */
    @NotBlank(message = "[payMethodId] is mandatory")
    private String payMethodId;

    /**
     * 交易国家
     */
    @NotBlank(message = "[country] is mandatory")
    private String country;

    /**
     * 交易金额
     */
    @NotNull(message = "[amount] is mandatory")
    private BigDecimal amount;

    /**
     * 交易币种
     */
    @NotBlank(message = "[currency] is mandatory")
    private String currency;

    /**
     * 公司名称
     */
    @NotBlank(message = "[companyName] is mandatory")
    private String companyName;

    /**
     * 付款方账户
     */
    @NotBlank(message = "[payerAccount] is mandatory")
    private String payerAccount;

    /**
     * 付款方账户名称
     */
    @NotBlank(message = "[payerAccountName] is mandatory")
    @Pattern(regexp = "[^=!\"%&*<>;@#${}\\[\\]\\\\_^`|~]+$", message = "[payerAccountName] has special character")
    private String payerAccountName;

    /**
     * 付款方账户地址
     */
    @NotBlank(message = "[payerAccountAddress] is mandatory")
    @Size(max = 35, message = "[payerAccountAddress] maximum 35 length")
    @Pattern(regexp = "[^=!\"%&*<>;@#${}\\[\\]\\\\_^`|~]+$", message = "[payerAccountAddress] has special character")
    private String payerAccountAddress;

    /**
     * 付款方账户地址2
     */
    @Size(max = 35, message = "[payerAccountAddress2] maximum 35 length")
    @Pattern(regexp = "[^=!\"%&*<>;@#${}\\[\\]\\\\_^`|~]*$", message = "[payerAccountAddress2] has special character")
    private String payerAccountAddress2;

    /**
     * 付款方账户地址3
     */
    @Size(max = 35, message = "[payerAccountAddress2] maximum 35 length")
    @Pattern(regexp = "[^=!\"%&*<>;@#${}\\[\\]\\\\_^`|~]*$", message = "[payerAccountAddress3] has special character")
    private String payerAccountAddress3;

    /**
     * 付款方银行编码
     */
    @NotBlank(message = "[payerBankCode] is mandatory")
    private String payerBankCode;

    /**
     * 付款方银行国家
     */
    private String payerBankCountry;

    /**
     * 付款方分支行ID
     */
    private String payerBankBranchId;

    /**
     * 费用承担方
     */
    private String chargeBearer;

    /**
     * 收款方账户
     */
    private String payeeAccount;

    /**
     * 收款方IBAN账号
     */
    private String payeeIBanAccount;

    /**
     * 收款方账户名称
     */
    @NotBlank(message = "[payeeAccountName] is mandatory")
    private String payeeAccountName;

    /**
     * 收款方账户国家
     */
    private String payeeAccountCountry;

    /**
     * 收款方银行编码
     */
    private String payeeBankCode;

    /**
     * 收款方银行名称
     */
    @Pattern(regexp = "[^=!\"%&*<>;@#${}\\[\\]\\\\_^`|~]*$", message = "[payeeBankName] has special character")
    private String payeeBankName;

    /**
     * 收款方银行国家
     */
    @NotBlank(message = "[payeeBankCountry] is mandatory")
    private String payeeBankCountry;

    /**
     * 收款方邮箱
     */
    private String payeeEmail;

    /**
     * 支付执行时间
     */
    @NotBlank(message = "[payExecutionDate] is mandatory")
    private String payExecutionDate;

    /**
     * 支付汇率
     */
    private String payExchangeRate;

    /**
     * 支付外汇合同
     */
    private String payFxContract;

    /**
     * 清算系统会员号
     */
    private String clearMemberId;

    /**
     * 交易详情
     */
    @Size(max = 140, message = "[remark] maximum 140 length")
    @Pattern(regexp = "[^=!\"%&*<>;@#${}\\[\\]\\\\_^`|~]*$", message = "[paymentDetail] has special character")
    private String paymentDetail;

    /**
     * 发票详情
     */
    @Size(max = 63936, message = "[invoiceDetail] maximum 63936 length")
    @Pattern(regexp = "[^=!\"%&*<>;@#${}\\[\\]\\\\_^`|~]*$", message = "[invoiceDetail] has special character")
    private String invoiceDetail;

    /**
     * 交易备注
     */
    @Size(max = 512, message = "[remark] maximum 512 length")
    private String remark;

}
