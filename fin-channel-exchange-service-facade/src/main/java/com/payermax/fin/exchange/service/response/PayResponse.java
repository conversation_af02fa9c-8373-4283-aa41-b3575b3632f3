package com.payermax.fin.exchange.service.response;

import com.ushareit.fintech.common.model.dto.Money;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date Create on 2021/7/27 11:33
 */
@Data
public class PayResponse extends TransactionBaseResponse {

    private static final long serialVersionUID = -965432767069902821L;

    /**
     * 实际支付金额
     */
    private Money paidAmount;

    /**
     * 是否支持退款,可用值:1-支持,0-不支持
     */
    private String isSupportRefund;

    /**
     * 关单时间,单位:秒
     */
    private Integer expireTime;

    /**
     * 结果类型,可用值:1-联机类,2-跳转类,3-展示类,4-交互类
     *
     * @mock 2
     */
    private String resultType;

    /**
     * 异步标识，true则表示支付结果异步返回
     */
    private Boolean asyncFlag;

    /**
     * 异步处理完成标识
     */
    private Boolean asyncFinishFlag;

    /**
     * 返回参数
     */
    private ReturnParam returnParam;

    /**
     * 下一步接口请求标识,resultType=4时使用
     */
    private String nextRequestType;

    /**
     * 下一步接口类型,可用值:1-支付类,2-控制类；若为“支付类”,则调用“支付下单接口”,若为“控制类”,则调用“统一控制接口”, resultType=4时使用
     */
    private String nextApiType;

    /**
     * 扩展返回数据,用于返回不同支付渠道的特定属性（返回给内部使用，如收银台）
     */
    private Map<String, Object> extendResult;

    /**
     * 对外扩展返回数据,用于返回不同支付渠道的数据（返回给商户）
     */
    private Map<String, Object> outExtendResult;

    /**
     * 所用渠道是否支持3DS Y:是*
     */
    private String isSupport3ds;

    @Data
    public static class ReturnParam implements Serializable {

        private static final long serialVersionUID = 6995377160187590433L;

        /**
         * 跳转URL,若存在,则需收银台跳转,resultType=2时使用
         */
        private String redirectUrl;

        /**
         * 外部渠道原始跳转URL,若存在,则需收银台跳转,resultType=2时使用
         */
        private String origRedirectUrl;

        /**
         * 跳转页面信息（指定支付方式下单时使用）,若存在,则需收银台跳转,resultType=2时使用
         */
        private String redirectPageInfo;

        /**
         * 二维码,若存在,则需收银台渲染展示, resultType=3时使用
         */
        private String qrCode;

        /**
         * 二维码URL,若存在,则需收银台渲染展示, resultType=3时使用
         */
        private String qrCodeUrl;

        /**
         * 虚拟账户,若存在,则需收银台渲染展示, resultType=3时使用
         */
        private String virtualAccount;

        /**
         * 条形码,若存在,则需收银台渲染展示, resultType=3时使用
         */
        private String barCode;

        /**
         * 条形码URL,若存在,则需收银台渲染展示, resultType=3时使用
         */
        private String barCodeUrl;

        /**
         * 条形码,若存在,则需收银台渲染展示, resultType=3时使用,已按顺序排列
         */
        private List<String> barCodeList;

        /**
         * 采集参数集合,若存在,则说明此次下单需要后续交互,需收银台采集相关要素,采集完成后,调用“统一控制接口”, resultType=4时使用
         */
        private List<String> params;

        /**
         * 额外返回参数
         */
        private Map<String, Object> extraInfo;
    }

}
