package com.payermax.fin.exchange.service.request;

import com.payermax.common.lang.util.money.Money;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;


/**
 * 关单通知请求
 *
 * <AUTHOR>
 * @date 2023/8/17 19:31
 */
@Data
public class CloseOrderRequest {

    /**
     * 渠道编码
     */
    @NotBlank(message = "[channelCode] is mandatory")
    private String channelCode;

    /**
     * 支付方式
     */
    private String paymentType;

    /**
     * 账单订单
     */
    private List<BillRecordDO> billRecordDataList;

    @Data
    public static class BillRecordDO implements Serializable {

        private static final long serialVersionUID = -1776086339144187958L;

        /**
         * 外部账单订单号
         */
        private String channelOrderNo;

        /**
         * 外部账单金额
         */
        private Money amount;

    }

}
