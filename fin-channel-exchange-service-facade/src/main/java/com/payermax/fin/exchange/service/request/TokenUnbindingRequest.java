package com.payermax.fin.exchange.service.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @time 2025-04-15 10:49:37
 * @description TODO
 */
@Data
public class TokenUnbindingRequest extends TransactionBaseRequest{

    private static final long serialVersionUID = 764514973461194837L;

    /**
     * 卡标识
     */
    @NotBlank(message = "[cardIdentifierNo] is mandatory")
    private String cardIdentifierNo;

    /**
     * token id
     */
    @NotBlank(message = "[tokenId] is mandatory")
    private String tokenId;

    /**
     * token对应的渠道标识
     */
    @NotBlank(message = "[tokenChannel] is mandatory")
    private String tokenChannel;

    /**
     * token对应的渠道商户标识
     */
    @NotBlank(message = "[tokenChannelMid] is mandatory")
    private String tokenChannelMid;

    /**
     * token类型: GATEWAY_TOKEN、NETWORK_TOKEN
     */
    private String tokenType;

    /**
     * 业务单号
     */
    @NotBlank(message = "[bizOrderNo] is mandatory")
    @Size(max = 64, message = "[bizOrderNo] maximum 64 length")
    private String bizOrderNo;
}
