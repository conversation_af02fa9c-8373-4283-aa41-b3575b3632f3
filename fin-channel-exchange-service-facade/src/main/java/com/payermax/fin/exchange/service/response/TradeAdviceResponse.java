package com.payermax.fin.exchange.service.response;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import com.payermax.common.lang.util.money.Money;

/**
 * HA交易响应
 *
 * <AUTHOR>
 * @date 2022/5/30 11:22
 */
@Data
public class TradeAdviceResponse implements Serializable {

    private static final long serialVersionUID = 8065705711705623050L;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 渠道提交单号
     */
    private String channelPayCommitNo;

    /**
     * 请求单号
     */
    private String transactionOrderNo;

    /**
     * 基准货币
     */
    private String baseCurrency;

    /**
     * 交易货币
     */
    private String transactionCurrency;

    /**
     * 基准币种对应金额
     */
    private Money baseAmount;

    /**
     * 交易金额
     */
    private Money transactionAmount;

    /**
     * 结算金额
     */
    private Money settlementAmount;

    /**
     * 资金结算时间
     */
    private Long valueDate;

    /**
     * 实际使用汇率
     */
    private BigDecimal fxRateUsed;

    /**
     * 状态(SUCCESS/FAIL/INITIATE/PENDING)
     */
    private String status;

    /**
     * 响应映射code
     */
    private String mappingCode;

    /**
     * 响应映射msg
     */
    private String mappingMsg;

}
