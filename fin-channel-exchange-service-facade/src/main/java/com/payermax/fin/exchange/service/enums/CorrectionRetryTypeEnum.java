package com.payermax.fin.exchange.service.enums;

/**
 * <AUTHOR>
 * @date 2023/4/17 19:57
 **/
public enum CorrectionRetryTypeEnum {

    NOT_SUPPORT_RETRY("NOT_SUPPORT_RETRY", "不支持重试"),
    ORIG_REQUEST_ORDER_APPLY("ORIG_REQUEST_ORDER_RETRY", "请求单重试（换单重试）");

    private String type;

    private String desc;

    CorrectionRetryTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
