package com.payermax.fin.exchange.service.response;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/7/4 10:40
 **/
@Data
@EqualsAndHashCode
public class FundingChannelInfoResponse implements Serializable {

    private static final long serialVersionUID = 1846126456838561514L;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 标准渠道编码*
     */
    private String standardChannelCode;

    /**
     * 签约主体
     */
    private String entity;

    /**
     * 技术集成机构
     */
    private String technicalOrg;

    /**
     * 资金结算机构
     */
    private String fundsSettleOrg;

    /**
     * 业务处理机构
     */
    private String bizHandleOrg;

    /**
     * 渠道类型 I:入款 O:出款 T:资金调拨 F:外汇 R:风控合规
     */
    private String channelType;

    /**
     * 交互模式 0:正常渠道 1:异步渠道 2:人工渠道 3:虚拟渠道
     */
    private Integer interactiveMode;

    /**
     * 是否需要报备商户 1：是 0：否 2：可报可不报
     */
    private Byte isNeedReportMerchant;

    /**
     * 交易码映射配置组
     */
    private String transCodeMapGroupId;

    /**
     * 响应码映射配置组
     */
    private String responseCodeMapGroupId;

    /**
     * 扩展配置
     */
    private ChannelConfigJson configJson;

    /**
     * 状态 1：可用 0：不可用
     */
    private Byte status;

    /**
     * 业务处理机构标识-对应机构中心编码*
     */
    private String bizHandleInstCode;

    /**
     * 资金结算机构标识-对应机构中心编码*
     */
    private String fundsSettleInstCode;
    
    @Data
    public static class ChannelConfigJson implements Serializable {

        private static final long serialVersionUID = -5752782384893818L;

        /**
         * 渠道商户标识*
         */
        private String channelMerchantCode;

        /**
         * 下游服务BeanService名称
         */
        private String targetService;

        /**
         * 订单号生成规则
         */
        private String orderNoRuleCode;

        /**
         * 订单号是否加入环境标识
         */
        private String orderNoWithEnvPrefix;
        /**
         * 控制类请求需要特殊提交单号（是：Y 否：N）
         */
        private String isNeedSpecialCommitOrderNoForControlOrder;

        /**
         * 是否支持补偿查询 Y：是 N：否
         */
        private String isSupportInquiry;

        /**
         * 是否支持退款补偿查询 Y：是 N：否
         */
        private String isSupportRefundInquiry;

        /**
         * 支付中是否通知上游 Y：是 N：否
         */
        private String isPendingNotify;

        /**
         * 老渠道系统中的渠道标识（机构标识，类似于机构编码）
         */
        private String channelId;
        /**
         * 校验
         */
        private String validate;

        /**
         * 老渠道 BeanName
         */
        private String channelHandleName;

        /**
         * 补偿重试规则
         */
        private String compensateRetryRule;

        /**
         * 是否需要远程筛选支付方式（是：Y 否：N）
         */
        private String isNeedRemoteFilter;

        /**
         * 远程数据缓存时间（秒）
         */
        private Long remoteDataCacheTimeOut;

        /**
         * 是否支持重试（是：Y 否：N）
         */
        private String isSupportCompensate;

        /**
         * 结算币种
         */
        private String settleCurrency;

        /**
         * 是否支持重发
         */
        private Boolean isSupportRetry;

        /**
         * 是否需要机构资金账号*
         */
        private String isNeedFundsAccount;

        /**
         * 补偿查询pending情况下，是否保存三方单号（是：Y 否：N）*
         */
        private String isSaveThirdOrgNoWhenQueryPending;

        /**
         * 渠道允许退款次数
         */
        private String allowRefundTimes;

        /**
         * 退款时效内，渠道允许退款的退款时间范围
         */
        private String allowRefundTimeLimit;

        /**
         * callback是否需要校验
         */
        private Boolean callbackCheckSignature;

        @Override
        public String toString() {
            return JSON.toJSONString(this);
        }
    }


}
