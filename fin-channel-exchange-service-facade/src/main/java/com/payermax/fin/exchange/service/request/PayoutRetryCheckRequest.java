package com.payermax.fin.exchange.service.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/3 14:15
 */
@Data
public class PayoutRetryCheckRequest implements BaseRequest {

    private static final long serialVersionUID = 2325832725834514210L;

    /**
     * 渠道编码列表
     */
    @NotEmpty(message = "[channelCodeList] is mandatory")
    private List<String> channelCodeList;

}
