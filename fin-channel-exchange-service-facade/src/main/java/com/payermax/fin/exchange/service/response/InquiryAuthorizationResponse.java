package com.payermax.fin.exchange.service.response;

import com.payermax.fin.exchange.service.enums.AuthorizationStatus4OutEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * 查询授权信息response
 *
 * <AUTHOR>
 * @date 2021/9/16 20:15
 */
@Data
public class InquiryAuthorizationResponse implements Serializable {

    private static final long serialVersionUID = 3554097630896678486L;

    /**
     * 请求的唯一标识
     */
    private String requestNo;

    /**
     * 授权类型,可用值:AGREEMENT_PAYMENT-协议支付,SUBSCRIPTION_PAYMENT-订阅支付
     */
    private String authorizationType;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 授权码,内部金融交换系统生成
     */
    private String authorizeCode;

    /**
     * 授权描述
     */
    private String authorizationDesc;

    /**
     * 商户用户标识
     */
    private String outUserId;

    /**
     * 授权的结果状态
     * {@link AuthorizationStatus4OutEnum}
     */
    private AuthorizationStatus4OutEnum status;

    /**
     * 授权终止时间
     */
    private Long protocolEndTime;

    /**
     * 服务商用户ID
     */
    private String authorizePspCustomerId;

    /**
     * 用户登录ID
     */
    private String authorizeUserLoginId;

}
