package com.payermax.fin.exchange.service.facade;

import com.payermax.fin.exchange.service.response.InquiryPayoutResponse;
import com.payermax.fin.exchange.service.response.PayoutResponse;
import com.payermax.fin.exchange.service.request.InquiryPayoutRequest;
import com.payermax.fin.exchange.service.request.PayoutRequest;
import com.payermax.common.lang.model.dto.Result;

/**
 * 出款服务
 *
 * <AUTHOR>
 * @date 2021/7/15 21:01
 * @dubbo
 */
public interface PayoutFacade {

    /**
     * 出款申请
     *
     * @param request
     * @return
     */
    Result<PayoutResponse> withdraw(PayoutRequest request);

    /**
     * 查询出款单信息
     *
     * @param request
     * @return
     */
    Result<InquiryPayoutResponse> inquiry(InquiryPayoutRequest request);

}
