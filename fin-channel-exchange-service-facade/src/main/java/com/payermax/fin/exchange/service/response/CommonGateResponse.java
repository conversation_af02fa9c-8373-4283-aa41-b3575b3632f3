package com.payermax.fin.exchange.service.response;


import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 通用网关转发查询响应
 *
 * <AUTHOR>
 * @date 2022/5/25 22:22
 */
@Data
public class CommonGateResponse implements Serializable {

    private static final long serialVersionUID = 1728588376752807674L;

    /**
     * 请求单号
     */
    private String requestNo;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 渠道提交单号
     */
    private String channelPayCommitNo;

    /**
     * 状态(SUCCESS/FAIL)
     */
    private String status;

    /**
     * 响应映射code
     */
    private String mappingCode;

    /**
     * 响应映射msg
     */
    private String mappingMsg;

    /**
     * 额外返回参数
     */
    private Map<String, Object> data;

}
