package com.payermax.fin.exchange.service.response;

import com.payermax.fin.exchange.service.enums.OrderStatus4OutEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/3/13 17:40
 */
@Data
public class ApplyPaymentTokenResponse implements Serializable {

    private static final long serialVersionUID = -5811066290610129969L;

    /**
     * 业务单号
     */
    private String bizOrderNo;

    /**
     * 支付状态,可用值:PENDING-支付中,SUCCESS-成功,FAIL-失败
     */
    private OrderStatus4OutEnum status;

    /**
     * 渠道响应码
     */
    private String channelRespCode;

    /**
     * 渠道响应消息
     */
    private String channelRespMsg;

    /**
     * 响应映射code
     */
    private String mappingCode;

    /**
     * 响应映射msg
     */
    private String mappingMsg;

    /**
     * 跳转URL,若存在,则需收银台跳转,resultType=2时使用
     */
    private String redirectUrl;

    /**
     * 额外返回参数
     */
    private Map<String, Object> extendResult;

}
