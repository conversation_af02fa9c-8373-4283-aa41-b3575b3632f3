package com.payermax.fin.exchange.service.request;

import com.ushareit.fintech.common.model.dto.Money;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 退款请求
 *
 * <AUTHOR>
 * @date 2021/8/23 13:50
 */
@Data
public class VoidRequest extends TransactionBaseRequest {

    private static final long serialVersionUID = -5880741004640642789L;

    /**
     * 订单中心-撤销请求单号，最大长度64
     */
    private String payRequestNo;

    /**
     * 资产交换-撤销请求单号,最大长度64
     *
     * @mock P123456789
     */
    @NotBlank(message = "[payOrderNo] is mandatory")
    @Size(max = 64, message = "[payOrderNo] maximum 64 length")
    private String payOrderNo;

    /**
     * 资产交换-原auth请求单号,最大长度64
     *
     * @mock P123456789
     */
    @NotBlank(message = "[oriPayOrderNo] is mandatory")
    @Size(max = 64, message = "[oriPayOrderNo] maximum 64 length")
    private String oriPayOrderNo;

    /**
     * 撤销金额,单位:元
     */
    private Money amount;

    /**
     * 备注,最大长度512
     */
    @Size(max = 512, message = "[remark] maximum 512 length")
    private String remark;

}
