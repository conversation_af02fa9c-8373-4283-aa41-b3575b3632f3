package com.payermax.fin.exchange.service.request;

import com.payermax.infra.ionia.log.digest.core.annotation.Sensitive;
import com.payermax.infra.ionia.log.digest.core.common.SensitiveStrategy;
import lombok.Data;

import java.util.Map;

/**
 * 预筛选资金账户请求
 *
 * <AUTHOR>
 * @date 2023/1/30 14:42
 */
@Data
public class PreFilterAccountRequest extends PreFilterRequest {

    private static final long serialVersionUID = 1282295671520347867L;

    /**
     * 采集参数集合
     */
    @Sensitive(strategy = SensitiveStrategy.MAP)
    private Map<String, String> params;

}
