package com.payermax.fin.exchange.service.response;

import com.payermax.fin.exchange.service.enums.OrderStatus4OutEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 统一控制接口响应
 *
 * <AUTHOR>
 * @date 2021/7/30 15:27
 */
@Data
public class UnifyControlResponse implements Serializable {

    private static final long serialVersionUID = -4845434564728259601L;

    /**
     * 渠道请求单号
     */
    private String channelPayRequestNo;

    /**
     * 渠道提交单号
     */
    private String channelPayCommitNo;

    /**
     * 接口状态,可用值:PENDING-进行中,SUCCESS-成功,FAIL-失败
     */
    private OrderStatus4OutEnum status;

    /**
     * 支付状态,可用值:PENDING-进行中,SUCCESS-成功,FAIL-失败
     */
    private OrderStatus4OutEnum payStatus;

    /**
     * 结果类型,可用值:1-联机类,2-跳转类,3-展示类,4-交互类
     */
    private String resultType;

    /**
     * 返回参数
     */
    private ReturnParam returnParam;

    /**
     * 扩展返回数据,用于返回不同支付渠道的特定属性,如是否支付OTP重试等
     */
    private Map<String, Object> extendResult;

    /**
     * 下一步接口请求标识,若存在,则作为下一步请求类型传给金融交换
     */
    private String nextRequestType;

    /**
     * 下一步接口类型,可用值:1-支付类,2-控制类；若为“支付类”；则调用“支付下单接口”,若为“控制类”,则调用“统一控制接口”
     */
    private String nextApiType;

    @Data
    public static class ReturnParam implements Serializable {

        private static final long serialVersionUID = -6276016599707172386L;

        /**
         * 银行卡转账信息
         */
        private BankTransferInfo bankTransferInfo;

        /**
         * 扩展参数,若存在,则说明此次下单需要后续交互,需收银台采集相关要素,采集完成后,调用“统一控制接口”, resultType=4时使用
         */
        private List<String> params;

        /**
         * 条形码,若存在,则需收银台渲染展示, resultType=3时使用,已按顺序排列
         */
        private List<String> barCodeList;

        /**
         * 数据有效期截止时间,格式实例：2021-08-26T08:31:51Z
         */
        private String expiryTime;

        /**
         * 虚拟账户,若存在,则需收银台渲染展示, resultType=3时使用
         */
        private String virtualAccount;

        /**
         * 额外返回参数
         */
        private Map<String, Object> extraInfo;

        @Data
        public static class BankTransferInfo implements Serializable {

            private static final long serialVersionUID = 4072805919317384225L;

            /**
             * 银行代码
             */
            private String bankId;

            /**
             * 银行名称
             */
            private String bankName;

            /**
             * 账户名
             */
            private String accountHolder;

            /**
             * 账户号码
             */
            private String accountNumber;

            /**
             * 支行代码
             */
            private String branchNo;

            /**
             * 国际银行帐户号码
             */
            private String ibanNo;
        }
    }

}
