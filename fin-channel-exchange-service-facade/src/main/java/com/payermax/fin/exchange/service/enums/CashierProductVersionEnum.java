package com.payermax.fin.exchange.service.enums;

/**
 * 金融交换收银产品版本枚举
 *
 * <AUTHOR>
 * @date 2024/12/23 13:55
 */
public enum CashierProductVersionEnum {
    /**
     * 老收银产品；执行老筛选路由流程
     */
    V1("V1"),
    /**
     * 升级收银产品，上游未命中灰度；金融交换执行老筛选路由流程
     */
    V2_0("V2.0"),
    /**
     * 升级收银产品，上游命中灰度，结果不一致；金融交换执行老筛选路由流程
     */
    V2_1("V2.1"),
    /**
     * 升级收银产品，上游命中灰度，结果一致；金融交换同时老筛选路由流程和新筛选路由流程
     */
    V2_2("V2.2"),
    /**
     * 升级收银产品，上游命中灰度，新模式；金融交换执行新筛选路由流程
     */
    V2_3("V2.3"),
    /**
     * 新收银产品；金融交换执行新筛选路由流程
     */
    V3("V3"),
    ;

    private String version;

    CashierProductVersionEnum(String version) {
        this.version = version;
    }

    public String getVersion() {
        return version;
    }
}
