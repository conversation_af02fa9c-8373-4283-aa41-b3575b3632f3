package com.payermax.fin.exchange.service.response;

import com.payermax.fin.exchange.service.enums.OrderStatus4OutEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * 转账查询响应
 *
 * <AUTHOR>
 * @date 2022/2/16 15:14
 */
@Data
public class InquiryTransferResponse implements Serializable {

    private static final long serialVersionUID = 5146059225339004114L;

    /**
     * 支付单号（请求流水号）
     */
    private String requestNo;

    /**
     * 渠道提交单号
     */
    private String channelOrderNo;

    /**
     * 状态
     */
    private OrderStatus4OutEnum status;

    /**
     * 渠道响应code
     */
    private String respCode;

    /**
     * 渠道响应信息
     */
    private String respMsg;

}
