package com.payermax.fin.exchange.service.facade;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.fin.exchange.service.response.ChannelMerchantResponse;

import java.util.Map;

public interface ChannelMerchantFacade {

    Result<Map<String, String>> getAccountJsonInfoByCode(String code);

    Result<ChannelMerchantResponse> getByMerchantCodeIgnoreStatus(String merchantCode);

}
