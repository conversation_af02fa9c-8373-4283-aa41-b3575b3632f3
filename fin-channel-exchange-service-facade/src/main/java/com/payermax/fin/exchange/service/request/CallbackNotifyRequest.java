package com.payermax.fin.exchange.service.request;

import com.payermax.common.lang.util.money.Money;
import com.payermax.infra.ionia.log.digest.core.annotation.Sensitive;
import com.payermax.infra.ionia.log.digest.core.common.SensitiveStrategy;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 渠道callback请求实体
 *
 * <AUTHOR>
 * @date 2021/8/27 17:53
 */
@Data
public class CallbackNotifyRequest implements BaseRequest {

    private static final long serialVersionUID = -5510887882634890441L;

    /**
     * 渠道端关联流水号
     */
    @NotBlank(message = "[referenceNo] is mandatory")
    private String referenceNo;

    /**
     * 通知类别
     */
    @NotNull(message = "[notifyType] is mandatory")
    private NotifyTypeEnum notifyType;

    /**
     * 通知状态
     */
    @NotNull(message = "[status] is mandatory")
    private NotifyStatusEnum status;

    /**
     * 当前订单状态
     */
    private NotifyStatusEnum curStatus;

    /**
     * 三方银行流水号
     */
    private String payThirdBankTxnId;

    /**
     * 三方渠道流水号
     */
    private String payThirdChannelNo;

    /**
     * 渠道返回码
     */
    private String respCode;

    /**
     * 渠道返回msg
     */
    private String respMsg;

    /**
     * 扩展字段
     */
    private String addField1;

    /**
     * 扩展字段
     */
    private String addField2;

    /**
     * 扩展字段
     */
    private String addField3;

    /**
     * 扩展字段
     */
    private String addField4;

    /**
     * 扩展字段
     */
    private String addField5;

    /**
     * 扩展字段
     */
    private String addField6;

    /**
     * 渠道token
     */
    private String channelToken;

    /**
     * 扩展数据
     */
    @Deprecated
    @Sensitive(strategy = SensitiveStrategy.MAP)
    private String extraJson;

    /**
     * 是否转换错误信息
     */
    @Deprecated
    private boolean translateMsg = false;

    /**
     * 交易时间 yyyy-MM-dd hh:mm:ss
     */
    @Deprecated
    private String transactionTime;

    /**
     * 备注
     */
    @Deprecated
    private String txnNote;

    /* 授权相关数据start */
    /**
     * 授权码
     */
    @Sensitive(strategy = SensitiveStrategy.DEFAULT_TOP_THREE)
    private String authorizeCode;

    /**
     * 授权码时效
     */
    private String authorizeCodeExpiryTime;

    /**
     * 授权刷新码
     */
    @Sensitive(strategy = SensitiveStrategy.DEFAULT_TOP_THREE)
    private String authorizeRefreshCode;

    /**
     * 授权刷新码时效
     */
    private String authorizeRefreshCodeExpiryTime;

    /**
     * 授权客户ID
     */
    private String authorizePspCustomerId;

    private String phoneNumber;

    private String email;

    /* 授权相关数据end */

    /* 出款相关数据start */
    /**
     * 收款人账户
     */
    @Deprecated
    private String payeeAccountId;

    /**
     * 收款人银行账号名称
     */
    @Deprecated
    private String payeeAccountName;

    /**
     * 收款人银行账号
     */
    @Deprecated
    private String payeeAccount;

    /**
     * 收款人银行代码
     */
    @Deprecated
    private String payeeBankCode;

    /**
     * 收款人名称
     */
    @Deprecated
    private String payeeName;

    /**
     * 收款人银行名称
     */
    @Deprecated
    private String payeeBankName;

    /**
     * 是否退票通知
     */
    private boolean hasBounceBack = false;

    /**
     * 可重发类型 0:无，1：可换单重发
     * */
    private Byte retryableType;
    /* 出款相关数据end */

    /* 入款相关数据start */
    /**
     * 付款人账户ID
     */
    @Deprecated
    private String payerAccountId;

    /**
     * 付款人银行账号名称
     */
    @Deprecated
    private String payerAccountName;

    /**
     * 付款人银行账号
     */
    @Deprecated
    private String payerAccount;

    /**
     * 付款人账户编码
     */
    @Deprecated
    private String payerAccountCode;

    /**
     * 付款人名称
     */
    @Deprecated
    private String payerName;

    /**
     * 付款人银行名称
     */
    @Deprecated
    private String payerBankName;
    /* 入款相关数据end */

    /**
     * 3ds相关信息
     */
    private ThreeDSecureInfo threeDSecureInfo;

    /**
     * callback金额
     */
    private Money payAmount;

    /**
     * Acquirer Reference Number(ARN)
     */
    private String arn;

    /**
     * 渠道响应的AVS结果
     */
    private String avsResultCode;

    /**
     * Retrieval Reference Number(RRN)
     */
    private String rrn;

    @Data
    public static class ThreeDSecureInfo implements Serializable {
        private static final long serialVersionUID = 3555403050457575799L;
        private String threeDSVersion;
        private String enrolled;
        private String eci;
        private String cavv;
        private String authenticationStatus;
        private String signatureVerification;
        private String xid;
        private String dsTransactionId;
        /**
         * Indicator. example: spa, vbv
         */
        private String indicator;
        /**
         * The card type returned by 3DS result. example: MASTERCARD
         */
        private String cardType;
        /**
         * 实际使用的认证方式
         */
        private String authenticationMethodType;
        /**
         * 验证结果
         */
        private String authenticationResult;
    }
    
    /**
     * OA流程相关数据
     * */
    private OaProcessData oaProcessData;

    @Data
    public static class OaProcessData implements Serializable {
        
        private static final long serialVersionUID = 355544673542399L;

        /**
         * OA发起人
         * */
        private String oaSponsor;

        /**
         * OA编号
         * */
        private String oaNumber;
    }

    /* 外汇相关数据 */

    /**
     * 三方报文
     */
    @Sensitive(strategy = SensitiveStrategy.MAP)
    private String callbackContent;

    /**
     * 远期汇率业务数据
     */
    private List<CurrencyPair> CurrencyPairs;
    @Data
    public static class CurrencyPair{
        private String rateVersion;
        private String baseCurrency;
        private String duration;
        private BigDecimal bidRate;
        private BigDecimal offerRate;
        private BigDecimal midRate;
        private Long expirationTime;
        private String currencyPairFlag;
        private Map<String, Object> extraInfo;
    }

    /**
     * 外汇交易业务数据
     */
    private List<TransData> transDatas;

    @Data
    public static class TransData{
        private String transactionOrderNo;
        private String customerNo;
        private String channelTransactionId;
        private String ordStatus;
        private String currencyPairFlag;
        private String duration;
        private Long valueDate;
        private String transactionCurrency;
        private String transactionSide;
        private Money transactionAmount;
        private String rateSheetRef;
        private String counterCcy;
        private BigDecimal contAmtValue;
        private BigDecimal lastSpotRate;
        private BigDecimal lastForwardPoints;
        private BigDecimal outrightRate;
        private String rejectCode;
        private String rejectReason;
        private String transactionType;
    }

    /**
     * 通知类型
     */
    public enum NotifyTypeEnum {
        PAYMENT,
        CAPTURE,
        PAYOUT,
        AUTHORIZATION,
        REFUND,
        VOID,
        PAYMENT_CONTROL,
        TRANSFER,
        RATE_RESPONSE,
        FOREX_RESPONSE,
        PAYOUT_CONTROL,
        CONTROL
    }

    /**
     * 通知状态
     */
    public enum NotifyStatusEnum {
        SUCCESS,
        FAILED,
        PENDING,
        INITIATE,
        BOUNCEBACK;
    }

}
