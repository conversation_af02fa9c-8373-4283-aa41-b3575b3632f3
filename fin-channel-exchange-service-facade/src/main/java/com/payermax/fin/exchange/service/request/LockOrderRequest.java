package com.payermax.fin.exchange.service.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 锁单请求
 *
 * <AUTHOR>
 * @date 2025/1/5 14:41
 */
@Data
public class LockOrderRequest implements BaseRequest {

    private static final long serialVersionUID = -3591460789576641824L;

    /**
     * 渠道提交单号集合
     */
    @NotEmpty(message = "[channelPayCommitNos] is mandatory")
    private List<String> channelPayCommitNos;

    /**
     * 锁单操作人
     */
    private String operator;

}
