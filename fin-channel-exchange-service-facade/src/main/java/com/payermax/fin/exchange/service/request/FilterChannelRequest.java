package com.payermax.fin.exchange.service.request;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/14 16:13
 */
@Data
public class FilterChannelRequest implements BaseRequest {

    private static final long serialVersionUID = -6263606643565423778L;

    /**
     * 客户ID
     */
    @NotBlank(message = "[customId] is mandatory")
    private String customId;

    /**
     * 内部商户号
     *
     * @mock SP1234567
     */
    @NotBlank(message = "[merchantNo] is mandatory")
    private String merchantNo;

    /**
     * 商户类型*
     */
    private String merchantType;

    /**
     * 商户MCC
     */
    @NotBlank(message = "[mcc] is mandatory")
    private String mcc;

    /**
     * 商户三级MCC
     */
    @NotBlank(message = "[subMcc] is mandatory")
    private String subMcc;

    /**
     * 支付方式集合
     */
    @Valid
    @NotEmpty(message = "[paymentMethods] is mandatory")
    private List<PaymentMethod> paymentMethods;

    @Data
    public static class PaymentMethod implements Serializable {

        private static final long serialVersionUID = -8767644301987352862L;

        /**
         * 此条支付方式唯一标识，调用方自定义，金融交换不使用，仅在接口响应时会原值返回
         */
        private String paymentMethodNo;

        /**
         * 售卖收银产品编码
         */
        @NotBlank(message = "[cashierProductNo] is mandatory")
        private String cashierProductNo;

        /**
         * 售卖收银产品版本
         */
        @NotBlank(message = "[cashierProductVersion] is mandatory")
        private String cashierProductVersion;

        /**
         * 支付类型,可用值:10-出款,20-入款,40-退款
         *
         * @mock 20
         */
        @NotBlank(message = "[paymentType] is mandatory")
        private String paymentType;

        /**
         * 支付方式类型,可用值:NetBanking,VirtualAccount,BarCode,Wallet,CardPay,CarrierBilling,PrePaid,PointsPay,PayLater,AutoDebit,DirectDebit
         *
         * @mock CarrierBilling
         */
        @NotBlank(message = "[paymentMethodType] is mandatory")
        private String paymentMethodType;

        /**
         * 国家二字码
         *
         * @mock ID
         */
        @Size(min = 2, max = 2, message = "[country] maximum 2 length")
        @Pattern(regexp = "^[A-Za-z]+$", message = "[country] value is invalid")
        private String country;

        /**
         * 交易币种
         */
        private String currency;

        /**
         * 目标机构
         *
         * @mock ********
         */
        private String targetOrg;

        /**
         * 卡组
         */
        private String cardOrg;

    }
}
