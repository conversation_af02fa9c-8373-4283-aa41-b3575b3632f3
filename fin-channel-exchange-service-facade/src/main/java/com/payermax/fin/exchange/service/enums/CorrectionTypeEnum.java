package com.payermax.fin.exchange.service.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/8 16:26
 **/
public enum CorrectionTypeEnum {

    /**
     * 重复支付交易
     * */
    DUPLICATE_PAYMENT("DUPLICATE_PAYMENT", "重复支付交易"),
    /**
     * 先失败后成功支付交易
     * */
    FAIL_BEFORE_SUCCEED_PAYMENT("FAIL_BEFORE_SUCCEED_PAYMENT", "先失败后成功支付交易");
    

    private String code;

    private String desc;

    CorrectionTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
    
    public static CorrectionTypeEnum getByCode(String value) {
        CorrectionTypeEnum[] enums = CorrectionTypeEnum.values();
        for (CorrectionTypeEnum item : enums) {
            if (Objects.equals(item.getCode(), value)) {
                return item;
            }
        }
        return null;
    }
}
