package com.payermax.fin.exchange.service.context;

import org.apache.commons.lang3.StringUtils;

/**
 * 上下文key工具类
 *
 * <AUTHOR>
 * @date 2021/12/13 21:44
 */
public class ContextKeyUtils {

    private static final String REQUEST_KEY = "REQUEST:%s";

    private static final String RESPONSE_KEY = "RESPONSE:%s";

    private static final String CALLBACK_KEY = "CALLBACK:%s";

    /**
     * 获取请求上下文key
     *
     * @param payRequestNo
     * @return
     */
    public static String getRequestKey(String payRequestNo) {
        if (StringUtils.isBlank(payRequestNo)) {
            throw new IllegalArgumentException("[payRequestNo] is mandatory");
        }
        return String.format(REQUEST_KEY, payRequestNo);
    }

    /**
     * 获取响应上下文key
     *
     * @param payRequestNo
     * @return
     */
    public static String getResponseKey(String payRequestNo) {
        if (StringUtils.isBlank(payRequestNo)) {
            throw new IllegalArgumentException("[payRequestNo] is mandatory");
        }
        return String.format(RESPONSE_KEY, payRequestNo);
    }

    /**
     * 获取callback上下文key
     * @param payRequestNo
     * @return
     */
    public static String getCallbackKey(String payRequestNo) {
        if (StringUtils.isBlank(payRequestNo)) {
            throw new IllegalArgumentException("[payRequestNo] is mandatory");
        }
        return String.format(CALLBACK_KEY, payRequestNo);
    }

}
