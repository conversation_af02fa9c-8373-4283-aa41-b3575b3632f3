package com.payermax.fin.exchange.service.request;

import com.payermax.common.lang.util.money.Money;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/8/15 14:11
 */

@Data
public class CompensateStatusRequest implements BaseRequest {

    private static final long serialVersionUID = 24145483211506L;
    
    /**
     * 渠道提交单号
     */
    @NotBlank(message = "[channelPayCommitNo] is mandatory")
    private String channelPayCommitNo;

    /**
     * 支付类型
     */
    @NotBlank(message = "[paymentType] is mandatory")
    private String paymentType;

    /**
     * 当前状态
     */
    @NotBlank(message = "[currentStatus] is mandatory")
    private String currentStatus;

    /**
     * 目标状态
     */
    @NotBlank(message = "[targetStatus] is mandatory")
    private String targetStatus;

    /**
     * 三方单号
     */
    private String thirdOrgOrderNo;

    /**
     * 四方单号
     */
    private String fourthOrgOrderNo;

    /**
     * 金额
     */
    @NotNull(message = "[payAmount] is mandatory")
    private Money payAmount;

    /**
     * 响应编码
     */
    private String respCode;

    /**
     * 响应描述
     */
    private String respMsg;

    /**
     * 是否忽略补偿查询 默认：否
     */
    private Boolean ignoreCompensateInquiry = Boolean.FALSE;

    /**
     * 是否忽略退票查询 默认：否
     */
    private Boolean ignoreBounceBackInquiry = Boolean.FALSE;

    /**
     * OA流程相关数据
     * */
    private OaProcessData oaProcessData;

    @Data
    public static class OaProcessData implements Serializable {

        private static final long serialVersionUID = 177144187958L;

        /**
         * OA发起人
         * */
        private String oaSponsor;

        /**
         * OA编号
         * */
        private String oaNumber;
    }

}
