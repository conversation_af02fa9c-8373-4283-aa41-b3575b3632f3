package com.payermax.fin.exchange.service.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/2/16 15:13
 */
@Data
public class InquiryTransferRequest implements BaseRequest {

    private static final long serialVersionUID = -6089507543214992854L;

    /**
     * 支付单号，最大长度64
     */
    @NotBlank(message = "[requestNo] is mandatory")
    private String requestNo;

}
