package com.payermax.fin.exchange.service.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2021/10/21 14:59
 */
@Data
public class PaymentDataCorrectionRequest implements BaseRequest {

    private static final long serialVersionUID = -4658676446125644775L;

    /**
     * 渠道提交单
     *
     * @mock P123456789
     */
    @NotBlank(message = "[channelPayCommitNo] is mandatory")
    private String channelPayCommitNo;

}
