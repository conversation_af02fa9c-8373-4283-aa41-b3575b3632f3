package com.payermax.fin.exchange.service.response;

import com.ushareit.fintech.common.model.dto.Money;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/10/11 17:29
 */
@Data
public class InquiryPayoutResponse extends InquiryBaseResponse {

    private static final long serialVersionUID = 8190266706841723637L;

    /**
     * 实际支付金额
     */
    private Money paidAmount;

    /**
     * 提交单扩展字段
     */
    private String commitAddField1;
    /**
     * 提交单扩展字段
     */
    private String commitAddField2;
    /**
     * 提交单扩展字段
     */
    private String commitAddField3;
    /**
     * 提交单扩展字段
     */
    private String commitAddField4;
    /**
     * 提交单扩展字段
     */
    private String commitAddField5;
    /**
     * 提交单扩展字段
     */
    private String commitAddField6;

}
