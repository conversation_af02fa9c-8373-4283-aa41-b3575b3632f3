package com.payermax.fin.exchange.service.response;

import com.payermax.fin.exchange.service.enums.OrderStatus4OutEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * 主动关单响应
 */
@Data
public class CloseResponse implements Serializable {

    private static final long serialVersionUID = -1662543543395161465L;

    /**
     * 支付状态,可用值:PENDING-支付中,SUCCESS-成功,FAIL-失败
     */
    private OrderStatus4OutEnum status;

    /**
     * 响应映射code
     */
    private String mappingCode;

    /**
     * 响应映射msg
     */
    private String mappingMsg;

    /**
     * 渠道响应码
     */
    private String channelRespCode;

    /**
     * 渠道响应消息
     */
    private String channelRespMsg;

    /**
     * 支付完成时间
     */
    private Long completeTime;

    /**
     * 外部渠道完成时间
     */
    private Long channelCompleteTime;
}
