package com.payermax.fin.exchange.service.request;

import com.ushareit.fintech.common.model.dto.Money;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 申请DDC请求
 *
 * <AUTHOR>
 * @date 2023/2/15 14:10
 */
@Data
public class ApplyDDCRequest implements BaseRequest {

    private static final long serialVersionUID = 8960304721713665242L;

    /**
     * 交易token（仅用于日志查看）
     */
    private String tradeToken;

    /**
     * 请求单号
     */
    @NotBlank(message = "[requestNo] is mandatory")
    private String requestNo;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * DDC机构
     */
    private String ddcOrg;

    /**
     * 商户号
     */
    @NotBlank(message = "[merchantNo] is mandatory")
    private String merchantNo;

    /**
     * 子商户号（外部商户的子商户）
     */
    private String subMerchantNo;

    /**
     * 内部二级商户信息
     */
    private SubMerchantInfo subMerchant;

    /**
     * 商户类型*
     */
    private String merchantType;

    /**
     * 服务主体（商户签约主体）
     */
    private String serviceEntity;

    /**
     * 服务模式
     */
    private String serviceMode;

    /**
     * 集成模式
     */
    private String integrate;

    /**
     * 商户是否开通拒付预警 Y：是 N：否
     */
    private String isOpenCbWarning;

    /**
     * 国家
     */
    @NotBlank(message = "[country] is mandatory")
    private String country;

    /**
     * 币种
     */
    private String currency;

    /**
     * 交易金额,单位:元
     */
    private Money amount;

    /**
     * 卡token
     */
    @NotBlank(message = "[cardPaymentToken] is mandatory")
    private String cardPaymentToken;

    /**
     * 卡bin（特殊路由使用）
     */
    private String realCardBin;

    /**
     * 发卡行（特殊路由使用）*
     */
    private String cardIssueBank;

    /**
     * 售卖收银产品编码
     */
    private String cashierProductNo;

    /**
     * 收银产品版本
     */
    private String cashierProductVersion;

    /**
     * 业务身份
     *
     * @mock p201
     */
    private String bizIdentify;

    /**
     * 业务产品编码
     */
    private String productCode;

    /**
     * 支付方式类型,可用值:NetBanking,VirtualAccount,BarCode,Wallet,CardPay,CarrierBilling,PrePaid,PointsPay,PayLater,AutoDebit,DirectDebit
     *
     * @mock CarrierBilling
     */
    private String paymentMethodType;

    /**
     * 客户类型,可用值:1-2C,2-2B
     *
     * @mock 1
     */
    private String customerType;

    /**
     * 目标机构,最大长度64
     *
     * @mock ********
     */
    @Size(max = 64, message = "[targetOrg] maximum 64 length")
    private String targetOrg;

    /**
     * 支付方式扩展属性集合
     */
    private List<ExtendProperty> extendProperties;

    /**
     * 客户ID
     */
    private String customId;

    /**
     * MCC信息
     */
    private String mcc;

    /**
     * 商户三级MCC
     */
    private String subMcc;

    /**
     * 商户用户标识,最大长度64
     *
     * @mock SP1234567_837629
     */
    @Size(max = 64, message = "[outUserId] maximum 64 length")
    private String outUserId;

    /**
     * 用户会员ID
     */
    @Size(max = 64, message = "[userMemberId] maximum 64 length")
    private String userMemberId;

    /**
     * capture模式：automatic/manual
     */
    private String captureMode;

    /**
     * 授权类型：finalAuth/preAuth
     */
    private String authorizationType;

    /**
     * 采集参数集合
     */
    private List<String> paramKeys;

    @Data
    public static class ExtendProperty implements Serializable {

        private static final long serialVersionUID = -2099770871849536257L;

        /**
         * 扩展属性key
         */
        private String key;
        /**
         * 扩展属性value
         */
        private String value;
        /**
         * 逻辑操作符
         */
        private String logicKey;
    }

    /**
     * 场景信息
     */
    private ScenarioInfo scenarioInfo;

    @Data
    public static class ScenarioInfo implements Serializable{

        private static final long serialVersionUID = -8316874198474984736L;

        // 代扣
        private String recurringType;
        // 是否商户发起
        private Boolean merchantInitiated;
    }

}
