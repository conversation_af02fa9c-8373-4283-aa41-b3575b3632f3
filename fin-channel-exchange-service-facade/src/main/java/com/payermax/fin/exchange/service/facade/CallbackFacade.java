package com.payermax.fin.exchange.service.facade;

import com.payermax.fin.exchange.service.request.CallbackNotifyRequest;
import com.payermax.common.lang.model.dto.Result;

/**
 * 通知服务
 *
 * <AUTHOR>
 * @date 2021/8/27 17:53
 * @dubbo
 */
public interface CallbackFacade {

    /**
     * callback通知
     *
     * @param request
     * @return
     */
    Result<String> callbackNotify(CallbackNotifyRequest request);

}
