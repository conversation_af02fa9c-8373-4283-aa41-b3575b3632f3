package com.payermax.fin.exchange.service.response;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date Create on 2021/10/19 11:18
 */
@Data
public class RevokeAuthorizationResponse implements Serializable {

    private static final long serialVersionUID = -706872892219216000L;

    /**
     * 请求的唯一标识
     */
    private String requestNo;

    /**
     * 渠道请求单号
     */
    private String channelPayRequestNo;

    /**
     * 渠道提交单号
     */
    private String channelPayCommitNo;

    /**
     * 商户用户标识
     */
    protected String outUserId;

    /**
     * 授权码,内部金融交换系统生成
     */
    private String authorizeCode;

    /**
     * 是否撤销成功,可用值:true-撤销成功,false-撤销失败
     */
    private Boolean isRevokeSuccess;

}
