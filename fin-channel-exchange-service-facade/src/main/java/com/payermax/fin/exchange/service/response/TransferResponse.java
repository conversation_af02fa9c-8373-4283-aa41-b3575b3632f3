package com.payermax.fin.exchange.service.response;

import com.payermax.fin.exchange.service.enums.OrderStatus4OutEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * 转账响应
 *
 * <AUTHOR>
 * @date 2022/2/16 15:09
 */
@Data
public class TransferResponse implements Serializable {

    private static final long serialVersionUID = 3175603652580845732L;

    /**
     * 支付单号（请求流水号）
     */
    private String requestNo;

    /**
     * 渠道提交单号
     */
    private String channelOrderNo;

    /**
     * 状态
     */
    private OrderStatus4OutEnum status;

}
