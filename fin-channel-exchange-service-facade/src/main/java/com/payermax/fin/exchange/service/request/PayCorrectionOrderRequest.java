package com.payermax.fin.exchange.service.request;

import com.payermax.fin.exchange.service.enums.CorrectionTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR> at 2022/9/21 13:42
 **/
@Data
public class PayCorrectionOrderRequest extends PayRequest {

    private static final long serialVersionUID = 76451872343245449L;

    /**
     * 支付请求单号，最大长度64（原）
     */
    @NotBlank(message = "[payRequestNoOriginal] is mandatory")
    @Size(max = 64, message = "[payRequestNoOriginal] maximum 64 length")
    private String payRequestNoOriginal;

    /**
     * 支付引擎-支付单号,最大长度64（原）
     *
     * @mock P123456789
     */
    @NotBlank(message = "[payOrderNoOriginal] is mandatory")
    @Size(max = 64, message = "[payOrderNoOriginal] maximum 64 length")
    private String payOrderNoOriginal;

    /**
     * 差错处理类型 
     * 
     * DUPLICATE_PAYMENT : 重复支付交易
     * FAIL_BEFORE_SUCCEED_PAYMENT: 先失败后成功支付交易
     */
    @NotNull(message = "[correctionType] is mandatory")
    private CorrectionTypeEnum correctionType;


}
