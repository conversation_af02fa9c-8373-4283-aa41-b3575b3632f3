package com.payermax.fin.exchange.service.request;

import com.payermax.infra.ionia.log.digest.core.annotation.Sensitive;
import com.payermax.infra.ionia.log.digest.core.common.SensitiveStrategy;
import com.ushareit.fintech.common.model.dto.Money;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date Create on 2021/8/24 11:29
 */
@Getter
@Setter
public class ApplyAuthorizationRequest extends TransactionBaseRequest {

    private static final long serialVersionUID = 1438557987357703851L;

    /**
     * 请求的唯一标识,最大长度64
     *
     * @mock A123456789
     */
    @NotBlank(message = "[requestNo] is mandatory")
    @Size(max = 64, message = "[requestNo] maximum 64 length")
    private String requestNo;

    /**
     * 授权类型,可用值:AGREEMENT_PAYMENT-协议扣款,SUBSCRIPTION_PAYMENT-订阅扣款
     * {@link AuthorizationTypeEnum}
     */
    @NotBlank(message = "[authorizationType] is mandatory")
    private String authorizationType;

    /**
     * 未授权过期时间间隔,单位:秒
     *
     * @mock 60
     */
    @NotNull(message = "[unauthorizedExpirationInterval] is mandatory")
    private Integer unauthorizedExpirationInterval;

    /**
     * 授权完成页面跳转地址,最大长度1024
     */
    @NotBlank(message = "[redirectUrl] is mandatory")
    private String redirectUrl;

    /**
     * 支付方式信息
     */
    @Valid
    @NotNull(message = "[paymentMethod] is mandatory")
    private PaymentMethod paymentMethod;

    /**
     * 授权信息
     */
    @Valid
    @NotNull(message = "[authorizationInfo] is mandatory")
    private AuthorizationInfo authorizationInfo;

    /**
     * 采集参数集合
     */
    @Sensitive(strategy = SensitiveStrategy.MAP)
    private Map<String, String> params;

    @Data
    public static class AuthorizationInfo implements Serializable {

        private static final long serialVersionUID = 3037809627580998796L;

        /**
         * 内部授权码
         */
        private String authorizeCode;

        /**
         * 客户ID
         */
        private String customId;

        /**
         * 内部商户号,最大长度64
         *
         * @mock SP1234567
         */
        @NotBlank(message = "[merchantNo] is mandatory")
        @Size(max = 64, message = "[merchantNo] maximum 64 length")
        private String merchantNo;

        /**
         * 商户店铺ID（外部商户的子商户）
         */
        private String shopId;

        /**
         * 二级商户信息
         */
        private SubMerchantInfo subMerchant;

        /**
         * 商户类型*
         */
        private String merchantType;

        /**
         * 商户MCC
         */
        private String mcc;

        /**
         * 商户三级MCC
         */
        private String subMcc;

        /**
         * 服务主体（商户签约主体）
         */
        private String serviceEntity;

        /**
         * 服务模式
         */
        private String serviceMode;

        /**
         * 商户用户标识,最大长度64
         *
         * @mock SP1234567_837629
         */
        @NotBlank(message = "[outUserId] is mandatory")
        @Size(max = 64, message = "[outUserId] maximum 64 length")
        private String outUserId;

        /**
         * 用户会员ID
         */
        @Size(max = 64, message = "[userMemberId] maximum 64 length")
        private String userMemberId;

        /**
         * 交易金额,单位:元
         */
        @NotNull(message = "[amount] is mandatory")
        private Money amount;

        /**
         * 用户使用语言
         */
        private String userLanguage;

        /**
         * 消费信息,最大长度512
         */
        @Size(max = 512, message = "[purchaseInfo] maximum 512 length")
        private String purchaseInfo;

        /**
         * 交易备注,最大长度512
         */
        @Size(max = 512, message = "[remark] maximum 512 length")
        private String remark;

        /**
         * 环境信息
         */
        private EnvInfo envInfo;

        @Data
        public static class EnvInfo implements Serializable {

            private static final long serialVersionUID = -3018700301692782966L;

            /**
             * 客户端IP
             *
             * @mock 127.0.0.1
             */
            private String clientIp;

            /**
             * 设备终端类型,可用值:WEB-PC浏览器,WAP-移动端WAP,APP-移动端APP
             *
             * @mock WEB
             */
            private String terminalType;

            /**
             * 设备操作系统类型,可用值:IOS-苹果IOS,ANDROID-安卓
             *
             * @mock IOS
             */
            private String osType;

            /**
             * 是否在webview，可用值：T,F
             */
            private String inWebview;

            /**
             * 是否安装APP，可用值：T,F
             */
            private String hasApp;

        }

    }

    @Data
    public static class PaymentMethod implements Serializable {

        private static final long serialVersionUID = -7969324401477864386L;

        /**
         * 售卖收银产品编码
         */
        private String cashierProductNo;

        /**
         * 收银产品版本
         */
        private String cashierProductVersion;

        /**
         * 支付方式类型,可用值:NetBanking,VirtualAccount,BarCode,Wallet,CardPay,CarrierBilling,PrePaid,PointsPay,PayLater,AutoDebit,DirectDebit
         *
         * @mock CarrierBilling
         */
        @NotBlank(message = "[paymentMethodType] is mandatory")
        private String paymentMethodType;

        /**
         * 支付类型,可用值:20-入款
         *
         * @mock 20
         */
        @NotBlank(message = "[paymentType] is mandatory")
        private String paymentType;

        /**
         * 国家二字码
         *
         * @mock ID
         */
        @NotBlank(message = "[country] is mandatory")
        @Size(min = 2, max = 2, message = "[country] maximum 2 length")
        @Pattern(regexp = "^[A-Za-z]+$", message = "[country] value is invalid")
        private String country;

        /**
         * 目标机构
         *
         * @mock ********
         */
        @NotBlank(message = "[targetOrg] is mandatory")
        private String targetOrg;

        /**
         * 业务身份
         *
         * @mock p201
         */
        @NotBlank(message = "[bizIdentify] is mandatory")
        private String bizIdentify;

        /**
         * 客户类型,可用值:1-2C,2-2B
         *
         * @mock 1
         */
        private String customerType;

        /**
         * 支付流程,可用值:DIRECT-联机,REDIRECT-跳转,OTP-OTP,REDIRECT_OTP-跳转+OTP,DIRECT_COLLECT-联机+后采集
         *
         * @mock REDIRECT_OTP
         */
        private String paymentFlow;

        /**
         * 支付方式扩展属性集合
         */
        private List<ExtendProperty> extendProperties;

        @Data
        public static class ExtendProperty implements Serializable {

            private static final long serialVersionUID = 5265318095835682391L;

            /**
             * 扩展属性key
             */
            private String key;
            /**
             * 扩展属性value
             */
            private String value;
            /**
             * 逻辑操作符
             */
            private String logicKey;

        }
    }

    public enum AuthorizationTypeEnum {
        /**
         * 协议支付
         */
        AGREEMENT_PAYMENT,
        /**
         * 订阅支付
         */
        SUBSCRIPTION_PAYMENT;
    }

}
