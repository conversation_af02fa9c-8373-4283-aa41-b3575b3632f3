package com.payermax.fin.exchange.service.response;

import com.payermax.fin.exchange.service.enums.OrderStatus4OutEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @time 2025-04-15 10:49:37
 * @description TODO
 */
@Data
public class TokenUnbindingResponse implements Serializable {

    private static final long serialVersionUID = 764514973461194838L;

    /**
     * 渠道提交单号
     */
    private String channelPayCommitNo;

    /**
     * 业务单号
     */
    private String bizOrderNo;

    /**
     * 状态,可用值:PENDING-支付中,SUCCESS-成功,FAILED-失败
     */
    private OrderStatus4OutEnum status;
}
