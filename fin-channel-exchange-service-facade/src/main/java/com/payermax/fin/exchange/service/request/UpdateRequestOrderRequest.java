package com.payermax.fin.exchange.service.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 更新请求单请求
 *
 * <AUTHOR>
 */
@Data
public class UpdateRequestOrderRequest implements BaseRequest {

    private static final long serialVersionUID = -7389765149016696317L;

    /**
     * 渠道请求单号
     */
    @NotBlank(message = "[channelPayRequestNo] is mandatory")
    private String channelPayRequestNo;

    /**
     * 支付类型,可用值:10-出,20-入,30-兑,40-退,50-转
     */
    private String paymentType;

    /**
     * 状态
     */
    private Byte status;

    /**
     * 当前订单状态
     */
    private Byte curStatus;

    /**
     * 扩展字段1
     */
    private String addField1;

    /**
     * 扩展字段2
     */
    private String addField2;

    /**
     * 扩展字段3
     */
    private String addField3;

    /**
     * 扩展字段4
     */
    private String addField4;

    /**
     * 扩展字段5
     */
    private String addField5;

    /**
     * 扩展字段6
     */
    private String addField6;
    
    /**
     * OA流程相关数据
     */
    private OaProcessData oaProcessData;

    @Data
    public static class OaProcessData implements Serializable {

        private static final long serialVersionUID = -355599312340299L;

        /**
         * OA发起人
         * */
        private String oaSponsor;

        /**
         * OA编号
         * */
        private String oaNumber;
    }

}
