package com.payermax.fin.exchange.service.request;

import com.payermax.infra.ionia.log.digest.core.annotation.Sensitive;
import com.payermax.infra.ionia.log.digest.core.common.SensitiveStrategy;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/3/13 17:31
 */
@Data
public class ApplyPaymentTokenRequest implements Serializable {

    private static final long serialVersionUID = 647183106237876900L;

    /**
     * 业务单号
     */
    @NotBlank(message = "[bizOrderNo] is mandatory")
    @Size(max = 64, message = "[bizOrderNo] maximum 64 length")
    private String bizOrderNo;

    /**
     * 采集参数集合
     */
    @Sensitive(strategy = SensitiveStrategy.MAP)
    private Map<String, String> params;

    /**
     * 支付方式信息
     */
    @Valid
    @NotNull(message = "[paymentMethod] is mandatory")
    private PaymentMethod paymentMethod;

    /**
     * 订单信息
     */
    @Valid
    @NotNull(message = "[orderInfo] is mandatory")
    private OrderInfo orderInfo;

    /**
     * 前端调整地址
     */
    private String frontRedirectUrl;

    /**
     * 场景类型，用于后续MIT交易
     */
    private String recurringType;

    @Data
    public static class PaymentMethod implements Serializable {

        private static final long serialVersionUID = 2403476201166754496L;

        /**
         * 售卖收银产品编码
         */
        private String cashierProductNo;

        /**
         * 收银产品版本
         */
        private String cashierProductVersion;

        /**
         * 支付类型,可用值:20-入款
         *
         * @mock 20
         */
        @NotBlank(message = "[paymentType] is mandatory")
        private String paymentType;

        /**
         * 支付方式类型
         *
         * @mock CarrierBilling
         */
        @NotBlank(message = "[paymentMethodType] is mandatory")
        private String paymentMethodType;

        /**
         * 目标机构,最大长度64
         *
         * @mock 10000111
         */
        @NotBlank(message = "[targetOrg] is mandatory")
        @Size(max = 64, message = "[targetOrg] maximum 64 length")
        private String targetOrg;

        /**
         * 卡组,最大长度64
         *
         * @mock 10000111
         */
        @Size(max = 64, message = "[cardOrg] maximum 64 length")
        private String cardOrg;

        /**
         * 国家
         *
         * @mock ID
         */
        @NotBlank(message = "[country] is mandatory")
        @Size(min = 2, max = 2, message = "[country] maximum 2 length")
        @Pattern(regexp = "^[A-Za-z]+$", message = "[country] value is invalid")
        private String country;

        /**
         * 币种
         */
        @NotBlank(message = "[currency] is mandatory")
        @Size(min = 3, max = 3, message = "[currency] maximum 3 length")
        private String currency;

        /**
         * 支付方式扩展属性集合
         */
        private List<ExtendProperty> extendProperties;

        @Data
        public static class ExtendProperty implements Serializable {

            private static final long serialVersionUID = -2099770871849536257L;

            /**
             * 扩展属性key
             */
            private String key;
            /**
             * 扩展属性value
             */
            private String value;
            /**
             * 逻辑操作符
             */
            private String logicKey;
        }
    }

    @Data
    public static class OrderInfo implements Serializable {

        private static final long serialVersionUID = -577570983731022534L;

        /**
         * 内部商户号,最大长度64
         *
         * @mock SP1234567
         */
        @NotBlank(message = "[merchantNo] is mandatory")
        @Size(max = 64, message = "[merchantNo] maximum 64 length")
        private String merchantNo;

        /**
         * 子商户号（外部商户的子商户）
         */
        private String subMerchantNo;

        /**
         * 服务主体（商户签约主体）
         */
        private String serviceEntity;

        /**
         * 服务模式
         */
        private String serviceMode;

        /**
         * 集成模式
         */
        private String integrate;

        /**
         * 客户ID
         */
        private String customId;

        /**
         * 商户店铺ID（外部商户的子商户）
         */
        private String shopId;

        /**
         * MCC信息
         */
        private String mcc;

        /**
         * 商户三级MCC
         */
        private String subMcc;

        /**
         * 商户用户标识,最大长度64
         *
         * @mock SP1234567_837629
         */
        @NotBlank(message = "[outUserId] is mandatory")
        @Size(max = 64, message = "[outUserId] maximum 64 length")
        private String outUserId;

        /**
         * 用户会员ID
         */
        @Size(max = 64, message = "[userMemberId] maximum 64 length")
        private String userMemberId;

        /**
         * 交易备注,最大长度512
         */
        @Size(max = 512, message = "[remark] maximum 512 length")
        private String remark;

        /**
         * 卡bin（特殊路由使用）
         */
        private String realCardBin;

        /**
         * 发卡行（特殊路由使用）*
         */
        private String cardIssueBank;

        /**
         * 环境信息
         */
        private EnvInfo envInfo;

        @Data
        public static class EnvInfo implements Serializable {

            private static final long serialVersionUID = 7592410664605802876L;

            /**
             * 客户端IP
             *
             * @mock 127.0.0.1
             */
            private String clientIp;

            /**
             * 设备终端类型,可用值:WEB-PC浏览器,WAP-移动端WAP,APP-移动端APP
             *
             * @mock WEB
             */
            private String terminalType;

            /**
             * 设备操作系统类型,可用值:IOS-苹果IOS,ANDROID-安卓
             *
             * @mock IOS
             */
            private String osType;

            /**
             * accept
             */
            private String acceptHeader;

            /**
             * user-agent
             */
            private String userAgentHeader;

            /**
             * 浏览器语言
             */
            private String browserLanguage;

            /**
             * 浏览器是否支持Java*
             */
            private String browserJavaEnabled;

            /**
             * 瀏覽器支援螢幕色彩位數
             */
            private String browserColorDepth;

            /**
             * 浏览器屏幕宽度
             */
            private String browserScreenWidth;

            /**
             * 浏览器屏幕高度
             */
            private String browserScreenHeight;

            /**
             * 浏览器时区
             */
            private String browserTZ;
        }
    }

}
