package com.payermax.fin.exchange.service.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2021/10/22 14:52
 */
@Data
public class PayoutCompensateRequest implements BaseRequest {

    private static final long serialVersionUID = 170363947860170786L;

    /**
     * 渠道提交单
     *
     * @mock P23456789
     */
    @NotBlank(message = "[channelPayCommitNo] is mandatory")
    private String channelPayCommitNo;

    /**
     * 出款使用：是否触发重发,默认触发
     */
    private Boolean toFinalState = Boolean.FALSE;

}
