package com.payermax.fin.exchange.service.response;

import com.payermax.fin.exchange.service.enums.AuthorizationStatus4OutEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date Create on 2021/8/24 11:47
 */
@Data
public class ApplyAuthorizationResponse implements Serializable {

    private static final long serialVersionUID = -3631533272421591596L;

    /**
     * 请求的唯一标识
     */
    private String requestNo;

    /**
     * 渠道请求单号
     */
    private String channelPayRequestNo;

    /**
     * 渠道提交单号
     */
    private String channelPayCommitNo;

    /**
     * 授权码,内部金融交换系统生成
     */
    private String authorizeCode;

    /**
     * 授权状态,可用值:INIT-初始,PENDING-授权中,SUCCESS-成功,FAILED-失败,REVOKE-解约,EXPIRED-过期
     * {@link AuthorizationStatus4OutEnum}
     */
    private String status;

    /**
     * 结果类型,可用值:2-跳转类
     */
    private String resultType;

    /**
     * 返回参数
     */
    private ReturnParam returnParam;

    /**
     * 扩展返回数据,用于返回不同渠道的特定属性
     */
    private Map<String, Object> extendResult;

    @Data
    public static class ReturnParam implements Serializable {

        private static final long serialVersionUID = -125313524379440886L;

        /**
         * 跳转URL,resultType=2时使用
         */
        private String redirectUrl;
    }

}
