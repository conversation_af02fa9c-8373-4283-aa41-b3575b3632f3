package com.payermax.fin.exchange.service.response;

import com.ushareit.fintech.common.model.dto.Money;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @date Create on 2021/8/23 11:33
 */
@Data
public class InquiryPaymentResponse extends InquiryBaseResponse {

    private static final long serialVersionUID = -591696162652840491L;

    /**
     * 实际支付金额
     */
    private Money paidAmount;

    /**
     * 是否支持退款,可用值:0-不支持,1-支持
     */
    private String isSupportRefund;

    /**
     * 对外扩展返回数据,用于返回不同支付渠道的数据（返回给商户）
     */
    private Map<String, Object> outExtendResult;

    /**
     * 所用渠道是否支持3DS Y:是*
     */
    private String isSupport3ds;

}
