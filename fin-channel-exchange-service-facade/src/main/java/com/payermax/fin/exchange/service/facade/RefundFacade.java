package com.payermax.fin.exchange.service.facade;

import com.payermax.fin.exchange.service.request.InquiryVoidRequest;
import com.payermax.fin.exchange.service.request.VoidRequest;
import com.payermax.fin.exchange.service.response.InquiryVoidResponse;
import com.payermax.fin.exchange.service.response.InquiryRefundResponse;
import com.payermax.fin.exchange.service.response.RefundResponse;
import com.payermax.fin.exchange.service.request.InquiryRefundRequest;
import com.payermax.fin.exchange.service.request.RefundRequest;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.fin.exchange.service.response.VoidResponse;

/**
 * 退款服务
 *
 * <AUTHOR>
 * @date 2021/7/15 21:02
 * @dubbo
 */
public interface RefundFacade {

    /**
     * 撤销申请
     *
     * @param request
     * @return
     */
    Result<VoidResponse> voidApply(VoidRequest request);

    /**
     * 查询退款单信息
     *
     * @param request
     * @return
     */
    Result<InquiryVoidResponse> inquiryVoid(InquiryVoidRequest request);

    /**
     * 退款申请
     *
     * @param request
     * @return
     */
    Result<RefundResponse> refundApply(RefundRequest request);

    /**
     * 查询退款单信息
     *
     * @param request
     * @return
     */
    Result<InquiryRefundResponse> inquiryRefund(InquiryRefundRequest request);

}
