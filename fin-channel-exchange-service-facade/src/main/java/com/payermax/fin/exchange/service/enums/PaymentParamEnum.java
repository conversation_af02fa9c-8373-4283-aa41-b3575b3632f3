package com.payermax.fin.exchange.service.enums;

/**
 * 支付要素key枚举
 */
public enum PaymentParamEnum {

    /**
     * address
     */
    REGION("region", "The two-character ISO 3166 country/region code"),
    STATE("state", "State, country, or province"),
    CITY("city", "City, district, suburb, town, or village"),
    ADDRESS1("address1", "Address line 1 (such as street, PO Box, or company name )"),
    ADDRESS2("address2", "Address line 2 (such as apartment, suite, unit, or building)"),
    HOUSE_NUMBER("houseNumber", "User's address number"),
    ZIP_CODE("zipCode", "ZIP or postal code"),
    STREET("street", "User's address street"),
    PAYER_STATE("payerState", "Payer state"),
    PAYER_ZIP_CODE("payerZipCode", "Payer zipCode"),

    /**
     * env: Order environment information, such as device information.
     */
    TERMINAL_TYPE("terminalType", "Terminal type"),
    OS_TYPE("osType", "Operation system type."),
    CLIENT_IP("clientIp", "IP address of the client device "),
    DEVICE_ID("deviceId", "Unique device identification "),
    FINGERPRINT("fingerprint", "Device fingerprint "),

    /**
     * buyer
     */
    BUYER_IDENTIFICATION_ID("buyerIdentificationId", "buyer identification id"),
    BUYER_IDENTIFICATION_ID_TYPE("buyerIdentificationIdType", "buyer identification id's type"),
    REFERENCE_BUYER_ID("referenceBuyerId", ""),
    BUYER_FIRST_NAME("buyerFirstName", "buyer's firstName"),
    BUYER_MIDDLE_NAME("buyerMiddleName", "buyer's middleName"),
    BUYER_LAST_NAME("buyerLastName", "buyer's lastName"),
    BUYER_FULL_NAME("buyerFullName", "buyer's fullName"),
    BUYER_PHONE_NO("buyerPhoneNo", "buyer's phoneNo"),
    PAYEE_NAME("payeeName", "payee's name"),
    BUYER_PHONE_NO_REGION("buyerPhoneNoRegion", "buyer's phoneNoRegion"),
    BUYER_EMAIL("buyerEmail", "buyer's email"),
    BUYER_BIRTHDAY("buyerBirthday", "buyer's birthday"),

    /**
     * card
     */
    CVV("cvv", "cvv"),
    CARD_NO("cardNo", "buyer's card no"),
    CARD_EXPIRATION_MONTH("cardExpirationMonth", "buyer's card expiration month"),
    CARD_EXPIRATION_YEAR("cardExpirationYear", "buyer's card expiration year"),
    CARD_HOLDER_NAME("cardHolderName", "buyer's cardHolder full name"),
    CARD_HOLDER_FIRST_NAME("cardHolderFirstName", "buyer's cardHolder firstName"),
    CARD_HOLDER_LAST_NAME("cardHolderLastName", "buyer's cardHolder lastName"),
    CARD_CATEGORY("cardCategory", "card category"),
    CARD_ORG("cardOrg", "Card organization"),
    ISSUING_BANK_CODE("issuingBankCode", "issuing country code"),

    /**
     * 支持多张预付卡号采集
     */
    PRE_PAID_CARD_0("prePaidCard0", "prepaid card number"),
    PRE_PAID_CARD_1("prePaidCard1", "prepaid card number"),
    PRE_PAID_CARD_2("prePaidCard2", "prepaid card number"),
    PRE_PAID_CARD_3("prePaidCard3", "prepaid card number"),

    /**
     * transfer
     */
    TRANSFER_DATE("transferDate", "Transfer date"),
    TRANSFER_REMARK("transferRemark", "Transfer remark"),

    /**
     * 各个国家税类型
     */
    PAN("pan", "India(IN) tax types"),
    CPF("cpf", "Brazil(BR) personal tax types"),
    CNPJ("cnpj", "Brazil(BR) corporate tax types"),
    CPF_CNPJ("cpf_cnpj", "Brazil(BR) personal/corporate tax types"),
    CI("ci", "Bolivia(BO)/Chile(CL)/Ecuador(EC)/Paraguay(PY)/Uruguay(UY) tax types"),
    CC("cc", "Colombia(CO) tax types"),
    CURP("curp", "Mexico(MX) tax types"),
    CUIT("cuit", "Argentina(AR) tax types"),
    DNI_CUIT("dni_cuit", "Argentina(AR) tax types"),

    /**
     * 各个国家证件类型
     */
    DNI("dni", "Peru(PE) type of certificate"),
    CNIC("cnic", "Pakistan(PK) type of certificate"),
    ID_CARD("idCard", "South Africa(ZA) type of certificate"),

    /**
     * others
     */
    TAX_NO("taxNo", "Taxpayer Identification Number"),
    TAX_TYPE("taxType", "Tax type"),

    /**
     * 收银台H5特殊转换(收银台专用)
     */
    AREACODE_PHONE("areacode-phone", "如果是区号和手机，那么合成数组格式给到前端"),

    /**
     * billing信息
     */
    BILLING_FIRST_NAME("billingFirstName", "billing firstName"),
    BILLING_MIDDLE_NAME("billingMiddleName", "billing middleName"),
    BILLING_LAST_NAME("billingLastName", "billing lastName"),
    BILLING_EMAIL("billingEmail", "billing email"),
    BILLING_ADDRESS("billingAddress", "billing address"),
    BILLING_ADDRESS2("billingAddress2", "billing address2"),
    BILLING_ADDRESS3("billingAddress3", "billing address3"),
    BILLING_CITY("billingCity", "billing city"),
    BILLING_REGION("billingRegion", "billing region"),
    BILLING_STATE("billingState", "billing state"),
    BILLING_COUNTRY("billingCountry", "billing country"),
    BILLING_ZIP("billingZip", "billing zip code"),
    BILLING_PHONE("billingPhone", "billing phone"),

    /**
     * shipping信息
     */
    SHIPPING_FIRST_NAME("shippingFirstName", "shipping first name"),
    SHIPPING_LAST_NAME("shippingLastName", "shipping last name"),
    SHIPPING_ADDRESS("shippingAddress", "shipping address"),
    SHIPPING_ADDRESS2("shippingAddress2", "shipping address 2"),
    SHIPPING_ADDRESS3("shippingAddress3", "shipping address 3"),
    SHIPPING_REGION("shippingRegion", "shipping region"),
    SHIPPING_ZIP_CODE("shippingZipCode", "shipping zip code"),
    SHIPPING_CITY("shippingCity", "shipping city"),
    SHIPPING_STATE("shippingState", "shipping state"),
    SHIPPING_COUNTRY("shippingCountry", "shipping country"),
    SHIPPING_PHONE_NUMBER("shippingPhoneNumber", "shipping phone number"),
    SHIPPING_EMAIL("shippingEmail", "shipping email")
    ;

    PaymentParamEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private String code;

    private String desc;

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
