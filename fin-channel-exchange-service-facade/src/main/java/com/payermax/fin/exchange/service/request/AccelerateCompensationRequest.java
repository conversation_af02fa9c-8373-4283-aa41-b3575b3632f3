package com.payermax.fin.exchange.service.request;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/4/28 16:36
 */

@Data
public class AccelerateCompensationRequest {

    /**
     * 新渠道编码
     */
    private String channelCode;

    /**
     * 老渠道编码
     */
    private String oldChannelCode;

    /**
     * 补偿类型
     */
    private String retryType = "ABNORMAL_APPLY";

    /**
     * 当前补偿次数
     */
    private Integer retryCurrentCount = 0;

}
