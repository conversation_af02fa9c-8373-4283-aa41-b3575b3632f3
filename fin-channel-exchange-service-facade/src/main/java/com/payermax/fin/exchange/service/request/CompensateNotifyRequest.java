package com.payermax.fin.exchange.service.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 补偿通知请求
 *
 * <AUTHOR>
 * @date 2022/7/7 17:54
 */
@Data
public class CompensateNotifyRequest implements BaseRequest  {

    private static final long serialVersionUID = 2414547531125211506L;

    /**
     * 渠道提交单
     */
    private String channelPayCommitNo;

    /**
     * 支付类型
     */
    @NotBlank(message = "[paymentType] is mandatory")
    private String paymentType;

    /**
     * 渠道请求单
     */
    private String channelPayRequestNo;
}
