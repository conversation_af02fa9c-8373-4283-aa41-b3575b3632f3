package com.payermax.fin.exchange.service.facade;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.fin.exchange.service.request.*;
import com.payermax.fin.exchange.service.response.LockOrderResponse;

import java.util.List;
import java.util.Map;

/**
 * OMC-金融交换订单服务
 *
 * <AUTHOR>
 * @date 2022/1/22 1:39
 */
public interface OmcExchangeFacade {

    /**
     * 补偿查询
     *
     * @param request
     * @return
     */
    Result<String> compensateInquiry(CompensateInquiryRequest request);

    /**
     * 渠道路由
     *
     * @param request
     * @return
     */
    Result<Map> channelRouting(ChannelRoutingRequest request);

    /**
     * 补偿通知
     *
     * @param request
     * @return
     */
    Result<Void> compensateNotify(List<CompensateNotifyRequest> request);

    /**
     * 锁定订单
     *
     * @param request
     * @return
     */
    Result<LockOrderResponse> lockOrder(LockOrderRequest request);

    /**
     * 补推3ds通知
     *
     * @param request
     * @return
     */
    Result<Void> compensateThreeDSNotify(List<CompensateThreeDSNotifyRequest> request);
}
