package com.payermax.fin.exchange.service.context;

import com.ushareit.fintech.common.model.dto.Money;
import com.payermax.infra.ionia.log.digest.core.annotation.Sensitive;
import com.payermax.infra.ionia.log.digest.core.common.SensitiveStrategy;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 入款请求上下文
 *
 * <AUTHOR>
 * @date 2021/9/24 14:04
 */
@Data
public class PayRequestContext implements Serializable {

    private static final long serialVersionUID = 3837162276829190697L;

    /**
     * 服务机房
     */
    private String idcRegion;

    /**
     * 业务源
     */
    private String bizSource;

    /**
     * 请求类型,可用值:pay-入款,authorizationPay-授权支付,默认值pay
     *
     * @mock 1
     */
    private String requestType;

    /**
     * 上一步的渠道请求单号,最大长度64
     *
     * @mock P123456789
     */
    @Size(max = 64, message = "[quoteChannelPayRequestNo] maximum 64 length")
    private String quoteChannelPayRequestNo;

    /**
     * 上一步的渠道提交单号,最大长度64
     *
     * @mock P123456789
     */
    @Size(max = 64, message = "[quoteChannelPayCommitNo] maximum 64 length")
    private String quoteChannelPayCommitNo;

    /**
     * 3ds token（换取ddcSessionId等信息）
     */
    private String threeDomainSecureToken;

    /**
     * 支付方式信息
     */
    @Valid
    @NotNull(message = "[paymentMethod] is mandatory")
    private PaymentMethod paymentMethod;

    /**
     * 新支付方式信息
     */
    @Valid
    private NewPaymentMethod newPaymentMethod;

    /**
     * 订单信息
     */
    @Valid
    @NotNull(message = "[orderInfo] is mandatory")
    private OrderInfo orderInfo;

    /**
     * 商户信息
     */
    private MerchantInfo merchantInfo;

    /**
     * 用户信息
     */
    private OutUserInfo outUserInfo;

    /**
     * 配送信息--【电商场景必输】
     */
    private ShippingInfo shippingInfo;

    /**
     * 信用卡账单信息--【电商场景必输】
     */
    private BillingInfo billingInfo;

    /**
     * 风控安全信息
     */
    private RiskInfo riskInfo;

    /**
     * 采集参数集合
     */
    @Sensitive(strategy = SensitiveStrategy.MAP)
    private Map<String, String> params;

    /**
     * 支付Token信息
     */
    private List<TokenInfo> tokenInfos;

    /**
     * 扩展采集参数（防止有些不可预期的参数）
     */
    private Map<String, Object> extParams;

    /**
     * 内部授权码,requestType=4时使用
     */
    private String authorizeCode;

    /**
     * 内部支付token
     */
    private String paymentTokenID;

    /**
     * 支付集成
     * 引用：PaymentIntegrationEnum类
     * 值：CASHIER_CHECKOUT/API_ONLY
     */
    private String paymentIntegration;

    /**
     * 若是异步，用于存放收银台后端发起异步的机器ip
     */
    private String asyncPaySourceIp;

    /**
     * 渠道订单完成时间（用于充值场景，不需要调用外部渠道，直接完成，上游下发完成时间）
     */
    private Long channelCompleteTime;

    /**
     * 由上游传递下来的特殊支付标记，用来标记订单，做特殊业务处理
     * NETWORK_TOKEN
     * CARD_ON_FILE
     */
    private String additionalPaymentTag;

    /**
     * 场景信息
     * 后续考虑将additionalPaymentTag中的NETWORK_TOKEN，CARD_ON_FILE迁移合并
     */
    private ScenarioInfo scenarioInfo;

    /**
     * 3ds相关信息
     */
    private ThreeDSecureInfo threeDSecureInfo;

    /**
     * 商品信息
     */
    private List<GoodsDetail> goodsDetails;

    /***
     * 三方机构单号
     */
    private String thirdOrgOrderNo;

    @Data
    public static class PaymentMethod implements Serializable {

        private static final long serialVersionUID = 2403476201166754496L;

        /**
         * 售卖收银产品编码
         */
        private String cashierProductNo;

        /**
         * 收银产品版本
         */
        private String cashierProductVersion;

        /**
         * 银行列表
         */
        private String bankCode;

        /**
         * 支付类型,可用值:20-入款
         *
         * @mock 20
         */
        @NotBlank(message = "[paymentType] is mandatory")
        private String paymentType;

        /**
         * 国家二字码
         *
         * @mock ID
         */
        @NotBlank(message = "[country] is mandatory")
        @Size(min = 2, max = 2, message = "[country] maximum 2 length")
        @Pattern(regexp = "^[A-Za-z]+$", message = "[country] value is invalid")
        private String country;

        /**
         * 客户类型,可用值:1-2C,2-2B
         *
         * @mock 1
         */
        private String customerType;

        /**
         * 支付流程,可用值:DIRECT-联机,REDIRECT-跳转,OTP-OTP,REDIRECT_OTP-跳转+OTP,DIRECT_COLLECT-联机+后采集
         *
         * @mock REDIRECT_OTP
         */
        private String paymentFlow;

        /**
         * 支付方式扩展属性集合
         */
        private List<ExtendProperty> extendProperties;

        /**
         * 渠道附加税标识
         *
         * @mock VAT_4_IDR_10
         */
        private String additionalTaxCode;

        /**
         * 支付扩展参数
         */
        private List<String> paymentParams;

        /**
         * 是否匹配签约主体 Y:是 N:否
         */
        private String isMatchEntity;

        @Data
        public static class ExtendProperty implements Serializable {

            private static final long serialVersionUID = -2099770871849536257L;

            /**
             * 扩展属性key
             */
            private String key;
            /**
             * 扩展属性value
             */
            private String value;
            /**
             * 逻辑操作符
             */
            private String logicKey;
        }
    }

    @Data
    public static class NewPaymentMethod implements Serializable {

        private static final long serialVersionUID = -1419328264633136583L;

        /**
         * 售卖收银产品编码
         */
        @NotBlank(message = "[cashierProductNo] is mandatory")
        private String cashierProductNo;

        /**
         * 支付方式类型
         */
        @NotBlank(message = "[paymentMethodType] is mandatory")
        private String paymentMethodType;

        /**
         * 目标机构
         */
        @NotBlank(message = "[targetOrg] is mandatory")
        private String targetOrg;
    }

    @Data
    public static class OrderInfo implements Serializable {

        private static final long serialVersionUID = -577570983731022534L;

        /**
         * 内部商户号,最大长度64
         *
         * @mock SP1234567
         */
        @NotBlank(message = "[merchantNo] is mandatory")
        @Size(max = 64, message = "[merchantNo] maximum 64 length")
        private String merchantNo;

        /**
         * 子商户号（外部商户的子商户）
         */
        private String subMerchantNo;

        /**
         * 二级商户信息
         */
        private SubMerchantInfo subMerchant;

        /**
         * 商户类型
         */
        private String merchantType;

        /**
         * 集成模式
         */
        private String integrate;

        /**
         * 商户是否开通拒付预警 Y：是 N：否
         */
        private String isOpenCbWarning;

        /**
         * 客户ID
         */
        private String customId;

        /**
         * 商户店铺ID（外部商户的子商户）
         */
        private String shopId;

        /**
         * MCC信息
         */
        private String mcc;

        /**
         * 商户三级MCC
         */
        private String subMcc;

        /**
         * 商户用户标识,最大长度64
         *
         * @mock SP1234567_837629
         */
        @NotBlank(message = "[outUserId] is mandatory")
        @Size(max = 64, message = "[outUserId] maximum 64 length")
        private String outUserId;

        /**
         * 用户会员ID
         */
        @Size(max = 64, message = "[userMemberId] maximum 64 length")
        private String userMemberId;

        /**
         * 消费信息,最大长度512
         */
        @Size(max = 512, message = "[purchaseInfo] maximum 512 length")
        private String purchaseInfo;

        /**
         * 交易备注,最大长度512
         */
        @Size(max = 512, message = "[remark] maximum 512 length")
        private String remark;

        /**
         * capture模式：automatic/manual
         */
        private String captureMode;

        /**
         * 授权类型：finalAuth/preAuth
         */
        private String authorizationType;

        /**
         * 用户使用语言
         */
        private String userLanguage;

        /**
         * 卡bin（特殊路由使用）
         */
        private String realCardBin;

        /**
         * 发卡行（特殊路由使用）*
         */
        private String cardIssueBank;

        /**
         * 订单过期时间戳，单位毫秒，由订单中心放入上下文
         */
        private Long orderExpireTimeStamp;

        /**
         * 分润金额
         */
        private Money dividendAmount;

        /**
         * 环境信息
         */
        private EnvInfo envInfo;

        @Data
        public static class EnvInfo implements Serializable {

            private static final long serialVersionUID = 7592410664605802876L;

            /**
             * 客户端IP
             *
             * @mock 127.0.0.1
             */
            private String clientIp;

            /**
             * 设备终端类型,可用值:WEB-PC浏览器,WAP-移动端WAP,APP-移动端APP
             *
             * @mock WEB
             */
            private String terminalType;

            /**
             * 设备操作系统类型,可用值:IOS-苹果IOS,ANDROID-安卓
             *
             * @mock IOS
             */
            private String osType;

            /**
             * accept
             */
            private String acceptHeader;

            /**
             * user-agent
             */
            private String userAgentHeader;

            /**
             * 浏览器语言
             */
            private String browserLanguage;

            /**
             * 浏览器是否支持Java*
             */
            private String browserJavaEnabled;

            /**
             * 瀏覽器支援螢幕色彩位數
             */
            private String browserColorDepth;

            /**
             * 浏览器屏幕宽度
             */
            private String browserScreenWidth;

            /**
             * 浏览器屏幕高度
             */
            private String browserScreenHeight;

            /**
             * 浏览器时区
             */
            private String browserTZ;

            /**
             * 是否在Webview
             */
            private String inWebview;

            /**
             * 是否安装APP
             */
            private String hasApp;
        }
    }

    @Data
    public static class MerchantInfo implements Serializable {

        private static final long serialVersionUID = -8687243156959778798L;

        /**
         * 商户ID
         */
        private String merchantNo;

        /**
         * 商户名称
         */
        private String merchantName;

        /**
         * 商户国家
         */
        private String merchantCountry;

        /**
         * 商户注册地址*
         */
        private String merchantStreet;

        /**
         * 商户签约协议支付
         */
        private String isSupportAgreementPay;
    }

    @Data
    public static class OutUserInfo implements Serializable {

        private static final long serialVersionUID = 271842322684158599L;

        /**
         * 用户邮箱
         */
        private String userEmail;
    }

    @Data
    public static class ShippingInfo implements Serializable {

        private static final long serialVersionUID = 6135602173640186770L;

        /**
         * 配送人姓名
         *
         * @mock 张三
         */
        private String shippingFirstName;
        /**
         * 配送人姓名
         *
         * @mock 张三
         */
        private String shippingLastName;
        /**
         * 配送地址1
         *
         * @mock 二仙桥
         */
        private String shippingAddress;
        /**
         * 配送地址2
         */
        private String shippingAddress2;
        /**
         * 配送地址3
         */
        private String shippingAddress3;
        /**
         * 配送地址所在城市
         *
         * @mock 成都市
         */
        private String shippingCity;
        /**
         * 配送地址所在区域
         *
         * @mock 武侯区
         */
        private String shippingRegion;
        /**
         * 配送地址所在州
         */
        private String shippingState;
        /**
         * 配送所在国家
         *
         * @mock 中国
         */
        private String shippingCountry;
        /**
         * 配送地邮编
         *
         * @mock 000000
         */
        private String shippingZip;

        /**
         * 配送email
         *
         */
        private String shippingEmail;
        /**
         * 配送人手机
         *
         * @mock 13009090980
         */
        @Sensitive(strategy = SensitiveStrategy.PHONE)
        private String shippingPhone;
    }

    @Data
    public static class BillingInfo implements Serializable {

        private static final long serialVersionUID = -1331179352696688742L;

        /**
         * 账单姓名
         */
        private String billingFirstName;
        /**
         * 账单姓名
         */
        private String billingMiddleName;
        /**
         * 账单姓名
         */
        private String billingLastName;
        /**
         * 账单邮箱
         */
        @Sensitive(strategy = SensitiveStrategy.EMAIL)
        private String billingEmail;
        /**
         * 账单地址1
         */
        private String billingAddress;
        /**
         * 账单地址1
         */
        private String billingAddress2;
        /**
         * 账单地址1
         */
        private String billingAddress3;
        /**
         * 账单地址所在城市
         */
        private String billingCity;
        /**
         * 账单地址所在区域
         */
        private String billingRegion;
        /**
         * 账单地址所在州
         */
        private String billingState;
        /**
         * 账单地址所在国家
         */
        private String billingCountry;
        /**
         * 账单地址邮编
         */
        private String billingZip;
        /**
         * 账单手机号
         */
        @Sensitive(strategy = SensitiveStrategy.PHONE)
        private String billingPhone;
    }

    @Data
    public static class RiskInfo implements Serializable {

        private static final long serialVersionUID = -3793334203078277181L;

        /**
         * 风控结果码
         */
        private String riskResult;

        /**
         * 卡bin
         */
        private String cardBin;

        /**
         * ddc session id
         */
        private String ddcSessionId;

        /**
         * amazon seesion id
         */
        private String amazonPaySessionId;

        /**
         * cardinal ddcSessionId：卡bin为key，ddcSessionId为value*
         */
        private Map<String, String> cardinalDdcSessionMap;

        /**
         * mpgs ddcSessionId：卡bin为key，ddcSessionId为value*
         */
        private Map<String, String> mpgsDdcSessionMap;

        /**
         * cybersource ddcSessionId：卡bin为key，ddcSessionId为value*
         */
        private Map<String, String> cybsDdcSessionMap;

        /**
         * hitrust ddcSessionId：卡bin为key，ddcSessionId为value*
         */
        private Map<String, String> hitrustDdcSessionMap;

        /**
         * ddcSessionId集合：第一层key为ddcOrg，第二层key为卡bin，ddcSessionId为value*
         * 示例：{"CYBS":{"123456":"ddcSessionId1","654321":"ddcSessionId2"}, "MPGS":{"123456":"ddcSessionId1","654321":"ddcSessionId2"}}
         */
        private Map<String, Map<String, String>> ddcSessionMap;
    }

    @Data
    public static class ThreeDSecureInfo implements Serializable {
        private static final long serialVersionUID = 3555403050457575799L;

        private String threeDSVersion;
        private String enrolled;
        private String eci;
        private String cavv;
        private String signatureVerification;
        private String xid;
        private String dsTransactionId;
        private Boolean useExt3DS;
    }

    @Data
    public static class GoodsDetail implements Serializable {
        private static final long serialVersionUID = -8316874156904843566L;

        private String goodsId;
        private String goodsName;
        private String quantity;
        private String price;
        private String goodsCurrency;
        private String showUrl;
    }

    /**
     * 场景信息对象
     */
    @Data
    public static class ScenarioInfo implements Serializable{
        private static final long serialVersionUID = -8316874198474844577L;

        // 代扣
        private String recurringType;
        private Boolean merchantInitiated;
        // COF
        private Boolean cardholderInitiated;
        // 外部network token
        private Boolean extraNetworkToken;
        // 绑定后交易: FIRST/SUBSEQUENT
        private String tradeAfterAuth;
    }

    /**
     * 内部二级商户信息
     */
    @Data
    public static class SubMerchantInfo implements Serializable {

        /**
         * 二级商户号
         */
        private String subMerchantNo;
        /**
         * 二级商户名称
         */
        private String subMerchantName;
        /**
         * 二级商户MCC
         */
        private String mcc;
        /**
         * 二级商户子MCC
         */
        private String subMcc;

    }

    @Data
    public static class TokenInfo implements Serializable {

        private static final long serialVersionUID = 2403476201166759837L;

        /**
         * network token id
         */
        private String tokenId;

        /**
         * token对应的渠道标识
         */
        private String tokenChannel;

        /**
         * token对应的渠道商户标识
         */
        private String tokenChannelMid;

        /**
         * 支持的授权认证类型
         */
        private List<String> authenticationMethodTypes;

        /**
         * token记录状态
         */
        private String tokenStatus;

        /**
         * token类型: GATEWAY_TOKEN、NETWORK_TOKEN
         */
        private String tokenType;
    }
}
