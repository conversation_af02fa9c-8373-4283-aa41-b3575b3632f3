package com.payermax.fin.exchange.service.request;

import com.ushareit.fintech.common.model.dto.Money;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 渠道路由请求
 *
 * <AUTHOR>
 * @date 2022/5/7 15:40
 */
@Data
public class ChannelRoutingRequest implements BaseRequest {

    private static final long serialVersionUID = -2094911022725157216L;

    /**
     * 支付方式
     */
    @Valid
    @NotNull(message = "[paymentMethod] is mandatory")
    private PaymentMethod paymentMethod;

    /**
     * 订单信息
     */
    @Valid
    @NotNull(message = "[orderInfo] is mandatory")
    private OrderInfo orderInfo;

    /**
     * 实际采集数据
     */
    private Map<String, String> params;

    @Data
    public static class PaymentMethod implements Serializable {

        private static final long serialVersionUID = -8581255577142272618L;

        /**
         * 支付方式类型,可用值:NetBanking,VirtualAccount,BarCode,Wallet,CardPay,CarrierBilling,PrePaid,PointsPay,PayLater,AutoDebit,DirectDebit
         *
         * @mock CarrierBilling
         */
        @NotBlank(message = "[paymentMethodType] is mandatory")
        private String paymentMethodType;

        /**
         * 支付类型,可用值:20-入款
         *
         * @mock 20
         */
        @NotBlank(message = "[paymentType] is mandatory")
        private String paymentType;

        /**
         * 国家二字码
         *
         * @mock ID
         */
        @NotBlank(message = "[country] is mandatory")
        private String country;

        /**
         * 交易金额,单位:元
         */
        @NotNull(message = "[amount] is mandatory")
        private Money amount;

        /**
         * 是否忽略交易金额 为true时，忽略对amount.value的校验
         */
        private Boolean isIgnoreAmountValue;

        /**
         * 目标机构
         *
         * @mock ********
         */
        @NotBlank(message = "[targetOrg] is mandatory")
        private String targetOrg;

        /**
         * 业务身份
         *
         * @mock p201
         */
        private String bizIdentify;

        /**
         * 是否匹配签约主体 Y:是 N:否
         */
        private String isMatchEntity;

        /**
         * 业务产品编码
         */
        private String productCode;

        /**
         * 客户类型,可用值:1-2C,2-2B
         *
         * @mock 1
         */
        private String customerType;

        /**
         * 支付流程,可用值:DIRECT-联机,REDIRECT-跳转,OTP-OTP,REDIRECT_OTP-跳转+OTP,DIRECT_COLLECT-联机+后采集
         *
         * @mock REDIRECT_OTP
         */
        private String paymentFlow;

        /**
         * 渠道附加税标识
         *
         * @mock VAT_4_IDR_10
         */
        private String additionalTaxCode;

        /**
         * 采集参数集合
         */
        private List<String> params;

        /**
         * 支付方式扩展属性集合（卡组织{"logicKey":"INCLUDE","value":"SETTLEORG","key":"SUPPORT_CARD_ORG"}）
         */
        private List<ExtendProperty> extendProperties;

        /**
         * 支付扩展参数
         */
        private List<String> paymentParams;

        @Data
        public static class ExtendProperty implements Serializable {
            private static final long serialVersionUID = -3690945485241654449L;
            /**
             * 扩展属性key
             */
            private String key;
            /**
             * 扩展属性value
             */
            private String value;
            /**
             * 逻辑操作符
             */
            private String logicKey;

        }
    }

    @Data
    public static class OrderInfo implements Serializable {

        private static final long serialVersionUID = -4330566017182830302L;

        /**
         * 交易步骤*
         */
        private String transStep;

        /**
         * 内部商户号,最大长度64
         *
         * @mock SP1234567
         */
        @NotBlank(message = "[merchantNo] is mandatory")
        private String merchantNo;

        /**
         * 商户店铺ID（外部商户的子商户）
         */
        private String shopId;

        /**
         * 商户用户标识,最大长度64
         *
         * @mock SP1234567_837629
         */
        private String outUserId;

        /**
         * 用户会员ID
         */
        private String userMemberId;

        /**
         * MCC信息
         */
        private String mcc;

        /**
         * 消费信息,最大长度512
         */
        private String purchaseInfo;

        /**
         * 交易备注,最大长度512
         */
        private String remark;

        /**
         * 用户使用语言
         */
        private String userLanguage;

        /**
         * 卡bin（特殊路由使用）
         */
        private String realCardBin;

        /**
         * 发卡行（特殊路由使用）*
         */
        private String cardIssueBank;

    }
}
