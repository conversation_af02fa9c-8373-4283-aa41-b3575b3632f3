{
  "isStrict": false, //是否开启严格模式
  "allInOne": true,
  "createDebugPage": true, //生成测试页
  "framework": "dubbo",
  "outPath": "src/main/resources/static/doc", //指定文档的输出路径
  "projectName": "fin-channel-exchange",//配置自己的项目名称
  "rpcApiDependencies":[{ // 项目开放的dubbo api接口模块依赖，配置后输出到文档方便使用者集成
    "artifactId":"fin-channel-exchange-service-facade",
    "groupId":"com.payermax",
    "version":"1.0-SNAPSHOT"
  }],
  "appToken": "86cd8b403e864cf991f6d7a850fc6a57", // torna平台对接appKey
  "openUrl": "http://torna-dev.payermax.com/api",//torna平台地址，
  "replace": true
}